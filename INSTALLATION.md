# 代码块增强功能安装说明

## 概述

此更新为Romanticism主题添加了以下功能：
1. 对没有指定语言类型的Markdown代码块的支持，确保所有代码块都能正确显示为标准格式
2. 代码块右上角显示语言类型标签，智能定位不遮挡复制按钮
3. 移除了后台废弃的配置选项

## 更新的文件

### 1. 新增文件
- `config/js/codeblock-enhancer.js` - 代码块增强器（新增）

### 2. 修改的文件
- `config/style/prism.highlight.css` - 添加了无语言类代码块的CSS样式
- `config/js/thememode.js` - 集成了主题切换时的代码块样式更新
- `config/footer.php` - 引入了新的JavaScript文件
- `README.md` - 更新了功能说明

## 安装步骤

### 方法一：手动更新（推荐）

1. **备份现有文件**
   ```bash
   cp config/style/prism.highlight.css config/style/prism.highlight.css.backup
   cp config/js/thememode.js config/js/thememode.js.backup
   cp config/footer.php config/footer.php.backup
   ```

2. **添加新文件**
   - 将 `config/js/codeblock-enhancer.js` 上传到主题的 `config/js/` 目录

3. **更新现有文件**
   - 用更新后的内容替换以下文件：
     - `config/style/prism.highlight.css`
     - `config/js/thememode.js`
     - `config/footer.php`
     - `README.md`

### 方法二：完整替换

1. 备份整个主题目录
2. 下载更新后的完整主题包
3. 替换主题目录

## 验证安装

1. **检查文件是否存在**
   - 确认 `config/js/codeblock-enhancer.js` 文件存在
   - 确认其他文件已正确更新

2. **测试功能**
   - 创建一个包含没有语言标识的代码块的测试文章
   - 检查代码块是否正确显示为代码格式
   - 验证代码块右上角是否显示语言类型标签
   - 测试明暗主题切换时代码块样式是否正确
   - 确认语言标签不遮挡复制按钮

3. **浏览器控制台检查**
   - 打开浏览器开发者工具
   - 检查是否有JavaScript错误
   - 确认 `window.CodeBlockEnhancer` 对象存在

## 测试用例

创建一个测试文章，包含以下内容：

```markdown
# 代码块测试

## 没有语言标识的代码块（应显示"Text"标签）
```
function test() {
    console.log("这应该显示为代码块");
}
```

## 有语言标识的代码块（应显示"JavaScript"标签）
```javascript
function test() {
    console.log("这应该有语法高亮");
}
```

## Python代码块（应显示"Python"标签）
```python
def hello():
    print("Hello World!")
```
```

## 故障排除

### 问题1：代码块仍然显示为普通文本
**解决方案：**
1. 检查 `config/js/codeblock-enhancer.js` 是否正确加载
2. 检查浏览器控制台是否有JavaScript错误
3. 确认 `config/footer.php` 中已添加了对新JS文件的引用

### 问题2：主题切换时代码块样式不更新
**解决方案：**
1. 检查 `config/js/thememode.js` 是否已正确更新
2. 确认主题切换功能正常工作
3. 检查浏览器控制台是否有错误

### 问题3：代码块样式与主题不匹配
**解决方案：**
1. 检查 `config/style/prism.highlight.css` 是否已正确更新
2. 清除浏览器缓存
3. 检查是否有其他CSS文件覆盖了样式

## 兼容性说明

- 此更新完全向后兼容
- 不会影响现有的代码高亮功能
- 支持所有现代浏览器
- 适配移动设备

## 回滚说明

如果需要回滚到之前的版本：

1. 删除 `config/js/codeblock-enhancer.js`
2. 恢复备份的文件：
   ```bash
   cp config/style/prism.highlight.css.backup config/style/prism.highlight.css
   cp config/js/thememode.js.backup config/js/thememode.js
   cp config/footer.php.backup config/footer.php
   ```
3. 清除浏览器缓存

## 技术支持

如果在安装过程中遇到问题，请：

1. 检查所有文件是否正确更新
2. 查看浏览器控制台的错误信息
3. 确认主题版本兼容性
4. 联系主题开发者获取支持
