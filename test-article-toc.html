<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文章目录测试页面</title>
    <link rel="stylesheet" href="config/mdui/css/mdui.min.css">
    <link rel="stylesheet" href="config/style/romanticism.aka.css?v=3.5">
    <link rel="stylesheet" href="config/style/prism.highlight.css?v=2.1">
</head>
<body class="mdui-theme-layout-light">
    <div class="mdui-container">
        <div class="mdui-row">
            <div class="mdui-col-md-8 mdui-col-offset-md-2">
                <h1>文章目录功能测试</h1>
                
                <button id="switch-theme" class="mdui-btn mdui-btn-raised">切换主题</button>
                <button id="toggle-toc" class="mdui-btn mdui-btn-raised mdui-color-blue">切换目录</button>
                
                <br><br>
                
                <!-- 模拟文章内容 -->
                <div class="yuan mdui-col-md-12 mdui-card-primary toup">
                    <div class="article mdui-typo">
                        <h1>第一章：智能生成自动扫描文章标题无需手动配置层级结构支持多级标题自动缩进显示</h1>
                        <p>这是第一章的内容。文章目录应该能够自动生成并显示在右侧。当你滚动页面时，目录中的当前章节会高亮显示。这个标题很长，用来测试截断功能。</p>
                        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>

                        <h2>1.1 背景介绍和项目概述</h2>
                        <p>这是第一章第一节的内容。目录应该显示层级结构，子标题会有适当的缩进。</p>
                        <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>

                        <h2>1.2 目标和范围以及详细的实施计划和时间安排</h2>
                        <p>这是第一章第二节的内容。这个标题也比较长，用来测试截断效果。</p>
                        <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.</p>

                        <h3>1.2.1 具体目标和关键绩效指标KPI设定</h3>
                        <p>这是一个三级标题的内容。目录应该能够正确显示三级标题的层级。这个标题用来测试三级标题的截断。</p>
                        <p>Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.</p>
                        
                        <h1>第二章：实现方法和技术栈选择以及详细的开发流程规划</h1>
                        <p>这是第二章的内容。当你点击目录中的链接时，页面应该平滑滚动到对应的章节。这个标题也很长，测试一级标题的截断。</p>
                        <p>Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit, sed quia non numquam eius modi tempora incidunt ut labore et dolore magnam aliquam quaerat voluptatem.</p>

                        <h2>2.1 技术选择和框架对比分析</h2>
                        <p>这是第二章第一节的内容。</p>
                        <p>Ut enim ad minima veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur?</p>

                        <h2>2.2 架构设计和系统模块划分</h2>
                        <p>这是第二章第二节的内容。</p>
                        <p>Quis autem vel eum iure reprehenderit qui in ea voluptate velit esse quam nihil molestiae consequatur, vel illum qui dolorem eum fugiat quo voluptas nulla pariatur?</p>

                        <h3>2.2.1 前端架构设计和组件化开发模式</h3>
                        <p>这是关于前端架构的内容。这个三级标题很长，用来测试深层级标题的截断效果。</p>
                        <p>At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque corrupti quos dolores et quas molestias excepturi sint occaecati cupiditate non provident.</p>

                        <h3>2.2.2 后端架构设计和微服务拆分策略</h3>
                        <p>这是关于后端架构的内容。</p>
                        <p>Similique sunt in culpa qui officia deserunt mollitia animi, id est laborum et dolorum fuga. Et harum quidem rerum facilis est et expedita distinctio.</p>
                        
                        <h1>第三章：测试和验证</h1>
                        <p>这是第三章的内容。目录功能应该在所有现代浏览器中正常工作。</p>
                        <p>Nam libero tempore, cum soluta nobis est eligendi optio cumque nihil impedit quo minus id quod maxime placeat facere possimus, omnis voluptas assumenda est, omnis dolor repellendus.</p>
                        
                        <h2>3.1 单元测试</h2>
                        <p>这是关于单元测试的内容。</p>
                        <p>Temporibus autem quibusdam et aut officiis debitis aut rerum necessitatibus saepe eveniet ut et voluptates repudiandae sint et molestiae non recusandae.</p>
                        
                        <h2>3.2 集成测试</h2>
                        <p>这是关于集成测试的内容。</p>
                        <p>Itaque earum rerum hic tenetur a sapiente delectus, ut aut reiciendis voluptatibus maiores alias consequatur aut perferendis doloribus asperiores repellat.</p>
                        
                        <h1>第四章：结论</h1>
                        <p>这是结论章节的内容。文章目录功能应该能够提供良好的用户体验，帮助读者快速导航到感兴趣的章节。</p>
                        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
                        
                        <h2>4.1 总结</h2>
                        <p>这是总结部分的内容。</p>
                        <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
                        
                        <h2>4.2 未来工作</h2>
                        <p>这是关于未来工作的内容。</p>
                        <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.</p>
                        
                        <br><br>
                        <p><strong>测试说明：</strong></p>
                        <ul>
                            <li>右侧应该显示文章目录，宽度为320px</li>
                            <li>目录应该显示所有标题的层级结构</li>
                            <li>长标题应该自动截断，超过25个中文字符显示省略号</li>
                            <li>标题不应该换行显示，保持单行</li>
                            <li>鼠标悬停在截断的标题上应该显示完整标题</li>
                            <li>点击目录项应该平滑滚动到对应章节</li>
                            <li>滚动页面时，当前章节应该在目录中高亮显示</li>
                            <li>点击目录标题可以折叠/展开目录</li>
                            <li>在移动设备上目录应该隐藏</li>
                            <li>主题切换时目录样式应该相应改变</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="config/js/jquery.min.js"></script>
    <script src="config/mdui/js/mdui.min.js"></script>
    <script src="config/js/article-toc.js?v=3.0"></script>
    <script src="config/js/thememode.js"></script>
    
    <script>
        // 测试功能
        document.getElementById('toggle-toc').addEventListener('click', function() {
            if (window.ArticleTOC) {
                window.ArticleTOC.toggle();
            }
        });
        
        // 功能检查脚本
        setTimeout(function() {
            console.log('=== 文章目录功能检查报告 ===');
            
            // 检查目录是否生成
            const tocContainer = document.querySelector('.article-toc-fixed');
            console.log('目录容器:', tocContainer ? '✅ 已生成' : '❌ 未生成');
            
            if (tocContainer) {
                const tocItems = tocContainer.querySelectorAll('.article-toc-list li');
                console.log('目录项数量:', tocItems.length);
                
                tocItems.forEach((item, index) => {
                    const link = item.querySelector('a');
                    console.log(`目录项 ${index + 1}:`, {
                        text: link ? link.textContent : 'N/A',
                        href: link ? link.getAttribute('href') : 'N/A',
                        indent: item.style.paddingLeft
                    });
                });
            }
            
            // 检查标题ID
            const headings = document.querySelectorAll('.article h1, .article h2, .article h3, .article h4, .article h5, .article h6');
            console.log('文章标题数量:', headings.length);
            
            headings.forEach((heading, index) => {
                console.log(`标题 ${index + 1}:`, {
                    tag: heading.tagName,
                    text: heading.textContent,
                    id: heading.id
                });
            });
            
            // 检查ArticleTOC是否加载
            if (window.ArticleTOC) {
                console.log('✅ ArticleTOC 已加载');
                console.log('可用方法:', Object.keys(window.ArticleTOC));
            } else {
                console.log('❌ ArticleTOC 未加载');
            }
            
            console.log('=== 检查完成 ===');
        }, 1000);
    </script>
</body>
</html>
