<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Romanticism 主题功能验证</title>
    <link rel="stylesheet" href="config/mdui/css/mdui.min.css">
    <link rel="stylesheet" href="config/style/romanticism.aka.css?v=2.1">
    <link rel="stylesheet" href="config/style/prism.highlight.css?v=2.1">
    <style>
        .status-item {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            border-left: 4px solid #ccc;
        }
        .status-success {
            background-color: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .status-error {
            background-color: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .status-warning {
            background-color: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
    </style>
</head>
<body class="mdui-theme-layout-light">
    <div class="mdui-container">
        <div class="mdui-row">
            <div class="mdui-col-md-10 mdui-col-offset-md-1">
                <h1>Romanticism 主题功能验证</h1>
                
                <button id="switch-theme" class="mdui-btn mdui-btn-raised">切换主题测试</button>
                <button id="run-check" class="mdui-btn mdui-btn-raised mdui-color-blue">运行功能检查</button>
                
                <div id="status-report" class="mdui-typo" style="margin-top: 20px;">
                    <h3>功能检查报告</h3>
                    <p>点击"运行功能检查"按钮开始验证...</p>
                </div>
                
                <div class="article mdui-typo">
                    <h2>测试内容</h2>
                    
                    <h3>1. 代码块测试</h3>
                    <p>以下代码块应该显示One Dark主题和语言标签：</p>
                    
                    <h4>JavaScript代码（应显示"JavaScript"标签）：</h4>
                    <pre><code class="language-javascript">function testFunction() {
    console.log("这是JavaScript代码");
    return "success";
}</code></pre>
                    
                    <h4>无语言类型代码（应显示"Text"标签）：</h4>
                    <pre><code>这是没有指定语言的代码块
应该显示为One Dark主题
右上角应该有"Text"标签</code></pre>
                    
                    <h4>Python代码（应显示"Python"标签）：</h4>
                    <pre><code class="language-python">def test_function():
    print("这是Python代码")
    return True</code></pre>
                    
                    <h3>2. 无序列表测试</h3>
                    <p>以下列表应该显示多级标识符：</p>
                    <ul>
                        <li>第一级项目 - 应该是实心圆点 (•)
                            <ul>
                                <li>第二级项目 - 应该是空心圆点 (◦)
                                    <ul>
                                        <li>第三级项目 - 应该是空心方块 (▫)
                                            <ul>
                                                <li>第四级项目 - 应该回到实心圆点 (•)</li>
                                            </ul>
                                        </li>
                                    </ul>
                                </li>
                            </ul>
                        </li>
                        <li>另一个第一级项目</li>
                    </ul>

                    <h3>3. 超链接颜色测试</h3>
                    <p>以下链接应该显示正确的颜色：</p>
                    <ul>
                        <li><a href="https://www.example.com">未访问链接</a> - 应该是蓝色</li>
                        <li><a href="https://www.google.com">可能已访问的链接</a> - 如果访问过应该是紫色</li>
                        <li><a href="#test">页面内链接</a> - 测试内部链接</li>
                    </ul>


                </div>
            </div>
        </div>
    </div>
    
    <script src="config/js/jquery.min.js"></script>
    <script src="config/mdui/js/mdui.min.js"></script>
    <script src="config/js/prism.highlight.js"></script>
    <script src="config/js/codeblock-enhancer.js?v=2.1"></script>
    <script src="config/js/list-enhancer.js?v=2.1"></script>
    <script src="config/js/link-enhancer.js?v=2.1"></script>
    <script src="config/js/thememode.js"></script>
    
    <script>
        document.getElementById('run-check').addEventListener('click', function() {
            setTimeout(runFunctionCheck, 500);
        });
        
        function runFunctionCheck() {
            const report = document.getElementById('status-report');
            let html = '<h3>功能检查报告</h3>';
            
            // 检查文件加载
            html += checkFileLoading();
            
            // 检查代码块功能
            html += checkCodeBlocks();
            
            // 检查列表样式
            html += checkListStyles();
            
            // 检查主题切换
            html += checkThemeSwitch();

            // 检查超链接颜色
            html += checkLinkColors();



            // 总结
            html += '<h4>检查完成</h4>';
            html += '<p>如果发现问题，请参考 <code>故障排除指南.md</code> 文件。</p>';
            
            report.innerHTML = html;
        }
        
        function checkFileLoading() {
            let html = '<h4>1. 文件加载检查</h4>';
            
            // 检查CodeBlockEnhancer
            if (window.CodeBlockEnhancer) {
                html += '<div class="status-item status-success">✅ CodeBlockEnhancer 已加载</div>';
            } else {
                html += '<div class="status-item status-error">❌ CodeBlockEnhancer 未加载</div>';
            }
            
            // 检查Prism
            if (window.Prism) {
                html += '<div class="status-item status-success">✅ Prism 已加载</div>';
            } else {
                html += '<div class="status-item status-error">❌ Prism 未加载</div>';
            }

            // 检查ListEnhancer
            if (window.ListEnhancer) {
                html += '<div class="status-item status-success">✅ ListEnhancer 已加载</div>';
            } else {
                html += '<div class="status-item status-error">❌ ListEnhancer 未加载</div>';
            }

            // 检查LinkEnhancer
            if (window.LinkEnhancer) {
                html += '<div class="status-item status-success">✅ LinkEnhancer 已加载</div>';
            } else {
                html += '<div class="status-item status-error">❌ LinkEnhancer 未加载</div>';
            }
            
            return html;
        }
        
        function checkCodeBlocks() {
            let html = '<h4>2. 代码块功能检查</h4>';
            
            const codeBlocks = document.querySelectorAll('pre');
            html += `<div class="status-item">找到代码块数量: ${codeBlocks.length}</div>`;
            
            const languageLabels = document.querySelectorAll('.code-language-label');
            if (languageLabels.length > 0) {
                html += `<div class="status-item status-success">✅ 找到语言标签: ${languageLabels.length} 个</div>`;
            } else {
                html += '<div class="status-item status-error">❌ 未找到语言标签</div>';
            }
            
            // 检查One Dark主题
            let oneDarkCount = 0;
            codeBlocks.forEach(block => {
                const bgColor = getComputedStyle(block).backgroundColor;
                if (bgColor.includes('40, 44, 52') || bgColor.includes('#282c34')) {
                    oneDarkCount++;
                }
            });
            
            if (oneDarkCount > 0) {
                html += `<div class="status-item status-success">✅ One Dark主题已应用: ${oneDarkCount} 个代码块</div>`;
            } else {
                html += '<div class="status-item status-error">❌ One Dark主题未应用</div>';
            }
            
            return html;
        }
        
        function checkListStyles() {
            let html = '<h4>3. 列表样式检查</h4>';
            
            const listItems = document.querySelectorAll('.mdui-typo ul li');
            html += `<div class="status-item">找到列表项数量: ${listItems.length}</div>`;
            
            if (listItems.length > 0) {
                let customBulletCount = 0;
                let jsBulletCount = 0;

                // 检查CSS伪元素标识符
                listItems.forEach(li => {
                    const beforeContent = getComputedStyle(li, '::before').content;
                    if (beforeContent && beforeContent !== 'none' && beforeContent !== '""') {
                        customBulletCount++;
                    }
                });

                // 检查JavaScript生成的标识符
                const jsBullets = document.querySelectorAll('.custom-bullet');
                jsBulletCount = jsBullets.length;

                // 检查是否有重复标识符
                let duplicateCount = 0;
                listItems.forEach(li => {
                    const beforeContent = getComputedStyle(li, '::before').content;
                    const hasCSSBullet = beforeContent && beforeContent !== 'none' && beforeContent !== '""';
                    const hasJSBullet = li.querySelector('.custom-bullet') !== null;

                    if (hasCSSBullet && hasJSBullet) {
                        duplicateCount++;
                    }
                });

                if (customBulletCount > 0) {
                    html += `<div class="status-item status-success">✅ CSS列表标识符已应用: ${customBulletCount} 个</div>`;
                } else if (jsBulletCount > 0) {
                    html += `<div class="status-item status-success">✅ JavaScript列表标识符已应用: ${jsBulletCount} 个</div>`;
                } else {
                    html += '<div class="status-item status-error">❌ 列表标识符未应用（CSS和JavaScript都未生效）</div>';
                }

                // 检查缺失的标识符
                let missingCount = 0;
                listItems.forEach(li => {
                    const beforeContent = getComputedStyle(li, '::before').content;
                    const beforeDisplay = getComputedStyle(li, '::before').display;
                    const hasCSSBullet = beforeContent &&
                                        beforeContent !== 'none' &&
                                        beforeContent !== '""' &&
                                        beforeDisplay !== 'none';
                    const hasJSBullet = li.querySelector('.custom-bullet') !== null;

                    if (!hasCSSBullet && !hasJSBullet) {
                        missingCount++;
                    }
                });

                if (duplicateCount > 0) {
                    html += `<div class="status-item status-warning">⚠️ 检测到重复标识符: ${duplicateCount} 个（需要清理）</div>`;
                } else {
                    html += '<div class="status-item status-success">✅ 没有重复标识符</div>';
                }

                if (missingCount > 0) {
                    html += `<div class="status-item status-error">❌ 缺失标识符: ${missingCount} 个（需要修复）</div>`;
                } else {
                    html += '<div class="status-item status-success">✅ 所有列表项都有标识符</div>';
                }

                // 检查列表样式重置
                let resetCount = 0;
                listItems.forEach(li => {
                    const listStyle = getComputedStyle(li).listStyle;
                    if (listStyle === 'none' || listStyle.includes('none')) {
                        resetCount++;
                    }
                });

                if (resetCount > 0) {
                    html += `<div class="status-item status-success">✅ 列表样式重置成功: ${resetCount} 个</div>`;
                } else {
                    html += '<div class="status-item status-warning">⚠️ 列表样式可能未完全重置</div>';
                }
            }
            
            return html;
        }
        
        function checkThemeSwitch() {
            let html = '<h4>4. 主题切换检查</h4>';
            
            const switchBtn = document.getElementById('switch-theme');
            if (switchBtn) {
                html += '<div class="status-item status-success">✅ 主题切换按钮存在</div>';
                
                // 检查主题切换功能
                const isDark = document.body.classList.contains('mdui-theme-layout-dark');
                html += `<div class="status-item">当前主题: ${isDark ? '深色' : '浅色'}</div>`;
            } else {
                html += '<div class="status-item status-error">❌ 主题切换按钮不存在</div>';
            }
            
            return html;
        }

        function checkLinkColors() {
            let html = '<h4>5. 超链接颜色检查</h4>';

            const links = document.querySelectorAll('.mdui-typo a');
            html += `<div class="status-item">找到超链接数量: ${links.length}</div>`;

            if (links.length > 0) {
                let correctColorCount = 0;
                let enhancedCount = 0;

                links.forEach(link => {
                    const color = getComputedStyle(link).color;
                    // 检查是否为蓝色系（未访问）或紫色系（已访问）
                    if (color.includes('33, 150, 243') || // #2196F3
                        color.includes('156, 39, 176') ||  // #9C27B0
                        color.includes('100, 181, 246') || // #64B5F6 (dark theme)
                        color.includes('206, 147, 216')) { // #CE93D8 (dark theme)
                        correctColorCount++;
                    }

                    // 检查是否被JavaScript增强器处理过
                    if (link.classList.contains('link-enhanced')) {
                        enhancedCount++;
                    }
                });

                if (correctColorCount > 0) {
                    html += `<div class="status-item status-success">✅ 超链接颜色正确: ${correctColorCount} 个</div>`;
                } else {
                    html += '<div class="status-item status-error">❌ 超链接颜色未正确应用</div>';
                }

                if (enhancedCount > 0) {
                    html += `<div class="status-item status-success">✅ JavaScript增强的链接: ${enhancedCount} 个</div>`;
                } else {
                    html += '<div class="status-item status-warning">⚠️ 没有JavaScript增强的链接</div>';
                }
            }

            return html;
        }



        // 自动运行检查
        setTimeout(runFunctionCheck, 2000);
    </script>
</body>
</html>
