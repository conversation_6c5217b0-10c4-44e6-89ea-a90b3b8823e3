{"version": 3, "file": "mdui.js", "sources": ["../../node_modules/mdn-polyfills/MouseEvent.js", "../../node_modules/mdn-polyfills/CustomEvent.js", "../../node_modules/promise-polyfill/src/finally.js", "../../node_modules/promise-polyfill/src/allSettled.js", "../../node_modules/promise-polyfill/src/index.js", "../../node_modules/promise-polyfill/src/polyfill.js", "../../node_modules/mdui.jq/es/utils.js", "../../node_modules/mdui.jq/es/functions/each.js", "../../node_modules/mdui.jq/es/JQ.js", "../../node_modules/mdui.jq/es/$.js", "../../src/mdui.ts", "../../node_modules/mdui.jq/es/methods/each.js", "../../node_modules/mdui.jq/es/functions/contains.js", "../../node_modules/mdui.jq/es/functions/merge.js", "../../node_modules/mdui.jq/es/methods/get.js", "../../node_modules/mdui.jq/es/methods/find.js", "../../node_modules/mdui.jq/es/methods/utils/event.js", "../../node_modules/mdui.jq/es/methods/trigger.js", "../../node_modules/mdui.jq/es/functions/extend.js", "../../node_modules/mdui.jq/es/functions/param.js", "../../node_modules/mdui.jq/es/functions/utils/ajax.js", "../../node_modules/mdui.jq/es/functions/ajax.js", "../../node_modules/mdui.jq/es/static/ajax.js", "../../node_modules/mdui.jq/es/functions/ajaxSetup.js", "../../node_modules/mdui.jq/es/static/ajaxSetup.js", "../../node_modules/mdui.jq/es/static/contains.js", "../../node_modules/mdui.jq/es/functions/utils/data.js", "../../node_modules/mdui.jq/es/functions/data.js", "../../node_modules/mdui.jq/es/static/data.js", "../../node_modules/mdui.jq/es/static/each.js", "../../node_modules/mdui.jq/es/static/extend.js", "../../node_modules/mdui.jq/es/functions/map.js", "../../node_modules/mdui.jq/es/static/map.js", "../../node_modules/mdui.jq/es/static/merge.js", "../../node_modules/mdui.jq/es/static/param.js", "../../node_modules/mdui.jq/es/functions/removeData.js", "../../node_modules/mdui.jq/es/static/removeData.js", "../../node_modules/mdui.jq/es/functions/unique.js", "../../node_modules/mdui.jq/es/static/unique.js", "../../node_modules/mdui.jq/es/methods/add.js", "../../node_modules/mdui.jq/es/methods/addClass.js", "../../node_modules/mdui.jq/es/methods/insertBefore.js", "../../node_modules/mdui.jq/es/methods/before.js", "../../node_modules/mdui.jq/es/methods/off.js", "../../node_modules/mdui.jq/es/methods/on.js", "../../node_modules/mdui.jq/es/methods/ajaxStart.js", "../../node_modules/mdui.jq/es/methods/map.js", "../../node_modules/mdui.jq/es/methods/clone.js", "../../node_modules/mdui.jq/es/methods/is.js", "../../node_modules/mdui.jq/es/methods/remove.js", "../../node_modules/mdui.jq/es/methods/append.js", "../../node_modules/mdui.jq/es/methods/appendTo.js", "../../node_modules/mdui.jq/es/methods/attr.js", "../../node_modules/mdui.jq/es/methods/children.js", "../../node_modules/mdui.jq/es/methods/slice.js", "../../node_modules/mdui.jq/es/methods/eq.js", "../../node_modules/mdui.jq/es/methods/utils/dir.js", "../../node_modules/mdui.jq/es/methods/parent.js", "../../node_modules/mdui.jq/es/methods/closest.js", "../../node_modules/mdui.jq/es/methods/data.js", "../../node_modules/mdui.jq/es/methods/empty.js", "../../node_modules/mdui.jq/es/methods/extend.js", "../../node_modules/mdui.jq/es/methods/filter.js", "../../node_modules/mdui.jq/es/methods/first.js", "../../node_modules/mdui.jq/es/methods/has.js", "../../node_modules/mdui.jq/es/methods/hasClass.js", "../../node_modules/mdui.jq/es/methods/width.js", "../../node_modules/mdui.jq/es/methods/hide.js", "../../node_modules/mdui.jq/es/methods/val.js", "../../node_modules/mdui.jq/es/methods/index.js", "../../node_modules/mdui.jq/es/methods/last.js", "../../node_modules/mdui.jq/es/methods/next.js", "../../node_modules/mdui.jq/es/methods/not.js", "../../node_modules/mdui.jq/es/methods/offsetParent.js", "../../node_modules/mdui.jq/es/methods/position.js", "../../node_modules/mdui.jq/es/methods/offset.js", "../../node_modules/mdui.jq/es/methods/one.js", "../../node_modules/mdui.jq/es/methods/prev.js", "../../node_modules/mdui.jq/es/methods/removeAttr.js", "../../node_modules/mdui.jq/es/methods/removeData.js", "../../node_modules/mdui.jq/es/methods/removeProp.js", "../../node_modules/mdui.jq/es/methods/replaceWith.js", "../../node_modules/mdui.jq/es/methods/replaceAll.js", "../../node_modules/mdui.jq/es/methods/serializeArray.js", "../../node_modules/mdui.jq/es/methods/serialize.js", "../../node_modules/mdui.jq/es/methods/show.js", "../../node_modules/mdui.jq/es/methods/siblings.js", "../../node_modules/mdui.jq/es/methods/toggle.js", "../../src/jq_extends/methods/reflow.ts", "../../src/jq_extends/methods/transition.ts", "../../src/jq_extends/methods/transitionEnd.ts", "../../src/jq_extends/methods/transformOrigin.ts", "../../src/jq_extends/methods/transform.ts", "../../src/utils/mutation.ts", "../../src/jq_extends/methods/mutation.ts", "../../src/jq_extends/static/showOverlay.ts", "../../src/jq_extends/static/hideOverlay.ts", "../../src/jq_extends/static/lockScreen.ts", "../../src/jq_extends/static/unlockScreen.ts", "../../src/jq_extends/static/throttle.ts", "../../src/jq_extends/static/guid.ts", "../../src/global/mutation.ts", "../../src/utils/componentEvent.ts", "../../src/utils/dom.ts", "../../src/components/headroom/index.ts", "../../src/utils/parseOptions.ts", "../../src/components/headroom/customAttr.ts", "../../src/components/collapse/collapseAbstract.ts", "../../src/components/collapse/index.ts", "../../src/components/collapse/customAttr.ts", "../../src/components/panel/index.ts", "../../src/components/panel/customAttr.ts", "../../src/components/table/index.ts", "../../src/utils/touchHandler.ts", "../../src/components/ripple/index.ts", "../../src/components/textfield/index.ts", "../../src/components/slider/index.ts", "../../src/components/fab/index.ts", "../../src/components/fab/customAttr.ts", "../../src/components/select/index.ts", "../../src/components/select/customAttr.ts", "../../src/components/appbar/index.ts", "../../src/components/tab/index.ts", "../../src/components/tab/customAttr.ts", "../../src/components/drawer/index.ts", "../../src/components/drawer/customAttr.ts", "../../src/utils/queue.ts", "../../src/components/dialog/class.ts", "../../src/components/dialog/index.ts", "../../src/components/dialog/customAttr.ts", "../../src/components/dialog/dialog.ts", "../../src/components/dialog/alert.ts", "../../src/components/dialog/confirm.ts", "../../src/components/dialog/prompt.ts", "../../src/components/tooltip/index.ts", "../../src/components/tooltip/customAttr.ts", "../../src/components/snackbar/index.ts", "../../src/components/bottom_nav/index.ts", "../../src/components/progress/spinner.ts", "../../src/components/menu/index.ts", "../../src/components/menu/customAttr.ts"], "sourcesContent": ["!function(){try{return new MouseEvent(\"test\")}catch(e){}var e=function(e,t){t=t||{bubbles:!1,cancelable:!1};var n=document.createEvent(\"MouseEvent\");return n.initMouseEvent(e,t.bubbles,t.cancelable,window,0,t.screenX||0,t.screenY||0,t.clientX||0,t.clientY||0,t.ctrlKey||!1,t.altKey||!1,t.shiftKey||!1,t.metaKey||!1,t.button||0,t.relatedTarget||null),n};e.prototype=Event.prototype,window.MouseEvent=e}();\n", "!function(){function t(t,e){e=e||{bubbles:!1,cancelable:!1,detail:void 0};var n=document.createEvent(\"CustomEvent\");return n.initCustomEvent(t,e.bubbles,e.cancelable,e.detail),n}\"function\"!=typeof window.CustomEvent&&(t.prototype=window.Event.prototype,window.CustomEvent=t)}();\n", "/**\n * @this {Promise}\n */\nfunction finallyConstructor(callback) {\n  var constructor = this.constructor;\n  return this.then(\n    function(value) {\n      // @ts-ignore\n      return constructor.resolve(callback()).then(function() {\n        return value;\n      });\n    },\n    function(reason) {\n      // @ts-ignore\n      return constructor.resolve(callback()).then(function() {\n        // @ts-ignore\n        return constructor.reject(reason);\n      });\n    }\n  );\n}\n\nexport default finallyConstructor;\n", "function allSettled(arr) {\n  var P = this;\n  return new P(function(resolve, reject) {\n    if (!(arr && typeof arr.length !== 'undefined')) {\n      return reject(\n        new TypeError(\n          typeof arr +\n            ' ' +\n            arr +\n            ' is not iterable(cannot read property Symbol(Symbol.iterator))'\n        )\n      );\n    }\n    var args = Array.prototype.slice.call(arr);\n    if (args.length === 0) return resolve([]);\n    var remaining = args.length;\n\n    function res(i, val) {\n      if (val && (typeof val === 'object' || typeof val === 'function')) {\n        var then = val.then;\n        if (typeof then === 'function') {\n          then.call(\n            val,\n            function(val) {\n              res(i, val);\n            },\n            function(e) {\n              args[i] = { status: 'rejected', reason: e };\n              if (--remaining === 0) {\n                resolve(args);\n              }\n            }\n          );\n          return;\n        }\n      }\n      args[i] = { status: 'fulfilled', value: val };\n      if (--remaining === 0) {\n        resolve(args);\n      }\n    }\n\n    for (var i = 0; i < args.length; i++) {\n      res(i, args[i]);\n    }\n  });\n}\n\nexport default allSettled;\n", "import promiseFinally from './finally';\nimport allSettled from './allSettled';\n\n// Store setTimeout reference so promise-polyfill will be unaffected by\n// other code modifying setTimeout (like sinon.useFakeTimers())\nvar setTimeoutFunc = setTimeout;\n\nfunction isArray(x) {\n  return Boolean(x && typeof x.length !== 'undefined');\n}\n\nfunction noop() {}\n\n// Polyfill for Function.prototype.bind\nfunction bind(fn, thisArg) {\n  return function() {\n    fn.apply(thisArg, arguments);\n  };\n}\n\n/**\n * @constructor\n * @param {Function} fn\n */\nfunction Promise(fn) {\n  if (!(this instanceof Promise))\n    throw new TypeError('Promises must be constructed via new');\n  if (typeof fn !== 'function') throw new TypeError('not a function');\n  /** @type {!number} */\n  this._state = 0;\n  /** @type {!boolean} */\n  this._handled = false;\n  /** @type {Promise|undefined} */\n  this._value = undefined;\n  /** @type {!Array<!Function>} */\n  this._deferreds = [];\n\n  doResolve(fn, this);\n}\n\nfunction handle(self, deferred) {\n  while (self._state === 3) {\n    self = self._value;\n  }\n  if (self._state === 0) {\n    self._deferreds.push(deferred);\n    return;\n  }\n  self._handled = true;\n  Promise._immediateFn(function() {\n    var cb = self._state === 1 ? deferred.onFulfilled : deferred.onRejected;\n    if (cb === null) {\n      (self._state === 1 ? resolve : reject)(deferred.promise, self._value);\n      return;\n    }\n    var ret;\n    try {\n      ret = cb(self._value);\n    } catch (e) {\n      reject(deferred.promise, e);\n      return;\n    }\n    resolve(deferred.promise, ret);\n  });\n}\n\nfunction resolve(self, newValue) {\n  try {\n    // Promise Resolution Procedure: https://github.com/promises-aplus/promises-spec#the-promise-resolution-procedure\n    if (newValue === self)\n      throw new TypeError('A promise cannot be resolved with itself.');\n    if (\n      newValue &&\n      (typeof newValue === 'object' || typeof newValue === 'function')\n    ) {\n      var then = newValue.then;\n      if (newValue instanceof Promise) {\n        self._state = 3;\n        self._value = newValue;\n        finale(self);\n        return;\n      } else if (typeof then === 'function') {\n        doResolve(bind(then, newValue), self);\n        return;\n      }\n    }\n    self._state = 1;\n    self._value = newValue;\n    finale(self);\n  } catch (e) {\n    reject(self, e);\n  }\n}\n\nfunction reject(self, newValue) {\n  self._state = 2;\n  self._value = newValue;\n  finale(self);\n}\n\nfunction finale(self) {\n  if (self._state === 2 && self._deferreds.length === 0) {\n    Promise._immediateFn(function() {\n      if (!self._handled) {\n        Promise._unhandledRejectionFn(self._value);\n      }\n    });\n  }\n\n  for (var i = 0, len = self._deferreds.length; i < len; i++) {\n    handle(self, self._deferreds[i]);\n  }\n  self._deferreds = null;\n}\n\n/**\n * @constructor\n */\nfunction Handler(onFulfilled, onRejected, promise) {\n  this.onFulfilled = typeof onFulfilled === 'function' ? onFulfilled : null;\n  this.onRejected = typeof onRejected === 'function' ? onRejected : null;\n  this.promise = promise;\n}\n\n/**\n * Take a potentially misbehaving resolver function and make sure\n * onFulfilled and onRejected are only called once.\n *\n * Makes no guarantees about asynchrony.\n */\nfunction doResolve(fn, self) {\n  var done = false;\n  try {\n    fn(\n      function(value) {\n        if (done) return;\n        done = true;\n        resolve(self, value);\n      },\n      function(reason) {\n        if (done) return;\n        done = true;\n        reject(self, reason);\n      }\n    );\n  } catch (ex) {\n    if (done) return;\n    done = true;\n    reject(self, ex);\n  }\n}\n\nPromise.prototype['catch'] = function(onRejected) {\n  return this.then(null, onRejected);\n};\n\nPromise.prototype.then = function(onFulfilled, onRejected) {\n  // @ts-ignore\n  var prom = new this.constructor(noop);\n\n  handle(this, new Handler(onFulfilled, onRejected, prom));\n  return prom;\n};\n\nPromise.prototype['finally'] = promiseFinally;\n\nPromise.all = function(arr) {\n  return new Promise(function(resolve, reject) {\n    if (!isArray(arr)) {\n      return reject(new TypeError('Promise.all accepts an array'));\n    }\n\n    var args = Array.prototype.slice.call(arr);\n    if (args.length === 0) return resolve([]);\n    var remaining = args.length;\n\n    function res(i, val) {\n      try {\n        if (val && (typeof val === 'object' || typeof val === 'function')) {\n          var then = val.then;\n          if (typeof then === 'function') {\n            then.call(\n              val,\n              function(val) {\n                res(i, val);\n              },\n              reject\n            );\n            return;\n          }\n        }\n        args[i] = val;\n        if (--remaining === 0) {\n          resolve(args);\n        }\n      } catch (ex) {\n        reject(ex);\n      }\n    }\n\n    for (var i = 0; i < args.length; i++) {\n      res(i, args[i]);\n    }\n  });\n};\n\nPromise.allSettled = allSettled;\n\nPromise.resolve = function(value) {\n  if (value && typeof value === 'object' && value.constructor === Promise) {\n    return value;\n  }\n\n  return new Promise(function(resolve) {\n    resolve(value);\n  });\n};\n\nPromise.reject = function(value) {\n  return new Promise(function(resolve, reject) {\n    reject(value);\n  });\n};\n\nPromise.race = function(arr) {\n  return new Promise(function(resolve, reject) {\n    if (!isArray(arr)) {\n      return reject(new TypeError('Promise.race accepts an array'));\n    }\n\n    for (var i = 0, len = arr.length; i < len; i++) {\n      Promise.resolve(arr[i]).then(resolve, reject);\n    }\n  });\n};\n\n// Use polyfill for setImmediate for performance gains\nPromise._immediateFn =\n  // @ts-ignore\n  (typeof setImmediate === 'function' &&\n    function(fn) {\n      // @ts-ignore\n      setImmediate(fn);\n    }) ||\n  function(fn) {\n    setTimeoutFunc(fn, 0);\n  };\n\nPromise._unhandledRejectionFn = function _unhandledRejectionFn(err) {\n  if (typeof console !== 'undefined' && console) {\n    console.warn('Possible Unhandled Promise Rejection:', err); // eslint-disable-line no-console\n  }\n};\n\nexport default Promise;\n", "import Promise from './index';\nimport promiseFinally from './finally';\nimport allSettled from './allSettled';\n\n/** @suppress {undefinedVars} */\nvar globalNS = (function() {\n  // the only reliable means to get the global object is\n  // `Function('return this')()`\n  // However, this causes CSP violations in Chrome apps.\n  if (typeof self !== 'undefined') {\n    return self;\n  }\n  if (typeof window !== 'undefined') {\n    return window;\n  }\n  if (typeof global !== 'undefined') {\n    return global;\n  }\n  throw new Error('unable to locate global object');\n})();\n\n// Expose the polyfill if Promise is undefined or set to a\n// non-function value. The latter can be due to a named HTMLElement\n// being exposed by browsers for legacy reasons.\n// https://github.com/taylorhakes/promise-polyfill/issues/114\nif (typeof globalNS['Promise'] !== 'function') {\n  globalNS['Promise'] = Promise;\n} else if (!globalNS.Promise.prototype['finally']) {\n  globalNS.Promise.prototype['finally'] = promiseFinally;\n} else if (!globalNS.Promise.allSettled) {\n  globalNS.Promise.allSettled = allSettled;\n}\n", "function isNodeName(element, name) {\n    return element.nodeName.toLowerCase() === name.toLowerCase();\n}\nfunction isFunction(target) {\n    return typeof target === 'function';\n}\nfunction isString(target) {\n    return typeof target === 'string';\n}\nfunction isNumber(target) {\n    return typeof target === 'number';\n}\nfunction isBoolean(target) {\n    return typeof target === 'boolean';\n}\nfunction isUndefined(target) {\n    return typeof target === 'undefined';\n}\nfunction isNull(target) {\n    return target === null;\n}\nfunction isWindow(target) {\n    return target instanceof Window;\n}\nfunction isDocument(target) {\n    return target instanceof Document;\n}\nfunction isElement(target) {\n    return target instanceof Element;\n}\nfunction isNode(target) {\n    return target instanceof Node;\n}\n/**\n * 是否是 IE 浏览器\n */\nfunction isIE() {\n    // @ts-ignore\n    return !!window.document.documentMode;\n}\nfunction isArrayLike(target) {\n    if (isFunction(target) || isWindow(target)) {\n        return false;\n    }\n    return isNumber(target.length);\n}\nfunction isObjectLike(target) {\n    return typeof target === 'object' && target !== null;\n}\nfunction toElement(target) {\n    return isDocument(target) ? target.documentElement : target;\n}\n/**\n * 把用 - 分隔的字符串转为驼峰（如 box-sizing 转换为 boxSizing）\n * @param string\n */\nfunction toCamelCase(string) {\n    return string\n        .replace(/^-ms-/, 'ms-')\n        .replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());\n}\n/**\n * 把驼峰法转为用 - 分隔的字符串（如 boxSizing 转换为 box-sizing）\n * @param string\n */\nfunction toKebabCase(string) {\n    return string.replace(/[A-Z]/g, (replacer) => '-' + replacer.toLowerCase());\n}\n/**\n * 获取元素的样式值\n * @param element\n * @param name\n */\nfunction getComputedStyleValue(element, name) {\n    return window.getComputedStyle(element).getPropertyValue(toKebabCase(name));\n}\n/**\n * 检查元素的 box-sizing 是否是 border-box\n * @param element\n */\nfunction isBorderBox(element) {\n    return getComputedStyleValue(element, 'box-sizing') === 'border-box';\n}\n/**\n * 获取元素的 padding, border, margin 宽度（两侧宽度的和，单位为px）\n * @param element\n * @param direction\n * @param extra\n */\nfunction getExtraWidth(element, direction, extra) {\n    const position = direction === 'width' ? ['Left', 'Right'] : ['Top', 'Bottom'];\n    return [0, 1].reduce((prev, _, index) => {\n        let prop = extra + position[index];\n        if (extra === 'border') {\n            prop += 'Width';\n        }\n        return prev + parseFloat(getComputedStyleValue(element, prop) || '0');\n    }, 0);\n}\n/**\n * 获取元素的样式值，对 width 和 height 进行过处理\n * @param element\n * @param name\n */\nfunction getStyle(element, name) {\n    // width、height 属性使用 getComputedStyle 得到的值不准确，需要使用 getBoundingClientRect 获取\n    if (name === 'width' || name === 'height') {\n        const valueNumber = element.getBoundingClientRect()[name];\n        if (isBorderBox(element)) {\n            return `${valueNumber}px`;\n        }\n        return `${valueNumber -\n            getExtraWidth(element, name, 'border') -\n            getExtraWidth(element, name, 'padding')}px`;\n    }\n    return getComputedStyleValue(element, name);\n}\n/**\n * 获取子节点组成的数组\n * @param target\n * @param parent\n */\nfunction getChildNodesArray(target, parent) {\n    const tempParent = document.createElement(parent);\n    tempParent.innerHTML = target;\n    return [].slice.call(tempParent.childNodes);\n}\n/**\n * 始终返回 false 的函数\n */\nfunction returnFalse() {\n    return false;\n}\n/**\n * 数值单位的 CSS 属性\n */\nconst cssNumber = [\n    'animationIterationCount',\n    'columnCount',\n    'fillOpacity',\n    'flexGrow',\n    'flexShrink',\n    'fontWeight',\n    'gridArea',\n    'gridColumn',\n    'gridColumnEnd',\n    'gridColumnStart',\n    'gridRow',\n    'gridRowEnd',\n    'gridRowStart',\n    'lineHeight',\n    'opacity',\n    'order',\n    'orphans',\n    'widows',\n    'zIndex',\n    'zoom',\n];\nexport { isNodeName, isArrayLike, isObjectLike, isFunction, isString, isNumber, isBoolean, isUndefined, isNull, isWindow, isDocument, isElement, isNode, isIE, toElement, toCamelCase, toKebabCase, getComputedStyleValue, isBorderBox, getExtraWidth, getStyle, getChildNodesArray, returnFalse, cssNumber, };\n", "import { isArrayLike } from '../utils';\nfunction each(target, callback) {\n    if (isArrayLike(target)) {\n        for (let i = 0; i < target.length; i += 1) {\n            if (callback.call(target[i], i, target[i]) === false) {\n                return target;\n            }\n        }\n    }\n    else {\n        const keys = Object.keys(target);\n        for (let i = 0; i < keys.length; i += 1) {\n            if (callback.call(target[keys[i]], keys[i], target[keys[i]]) === false) {\n                return target;\n            }\n        }\n    }\n    return target;\n}\nexport default each;\n", "import each from './functions/each';\n/**\n * 为了使用模块扩充，这里不能使用默认导出\n */\nexport class JQ {\n    constructor(arr) {\n        this.length = 0;\n        if (!arr) {\n            return this;\n        }\n        each(arr, (i, item) => {\n            // @ts-ignore\n            this[i] = item;\n        });\n        this.length = arr.length;\n        return this;\n    }\n}\n", "import each from './functions/each';\nimport { JQ } from './JQ';\nimport { getChildNodesArray, isArrayLike, isFunction, isNode, isString, } from './utils';\nfunction get$() {\n    const $ = function (selector) {\n        if (!selector) {\n            return new JQ();\n        }\n        // JQ\n        if (selector instanceof JQ) {\n            return selector;\n        }\n        // function\n        if (isFunction(selector)) {\n            if (/complete|loaded|interactive/.test(document.readyState) &&\n                document.body) {\n                selector.call(document, $);\n            }\n            else {\n                document.addEventListener('DOMContentLoaded', () => selector.call(document, $), false);\n            }\n            return new JQ([document]);\n        }\n        // String\n        if (isString(selector)) {\n            const html = selector.trim();\n            // 根据 HTML 字符串创建 JQ 对象\n            if (html[0] === '<' && html[html.length - 1] === '>') {\n                let toCreate = 'div';\n                const tags = {\n                    li: 'ul',\n                    tr: 'tbody',\n                    td: 'tr',\n                    th: 'tr',\n                    tbody: 'table',\n                    option: 'select',\n                };\n                each(tags, (childTag, parentTag) => {\n                    if (html.indexOf(`<${childTag}`) === 0) {\n                        toCreate = parentTag;\n                        return false;\n                    }\n                    return;\n                });\n                return new JQ(getChildNodesArray(html, toCreate));\n            }\n            // 根据 CSS 选择器创建 JQ 对象\n            const isIdSelector = selector[0] === '#' && !selector.match(/[ .<>:~]/);\n            if (!isIdSelector) {\n                return new JQ(document.querySelectorAll(selector));\n            }\n            const element = document.getElementById(selector.slice(1));\n            if (element) {\n                return new JQ([element]);\n            }\n            return new JQ();\n        }\n        if (isArrayLike(selector) && !isNode(selector)) {\n            return new JQ(selector);\n        }\n        return new JQ([selector]);\n    };\n    $.fn = JQ.prototype;\n    return $;\n}\nconst $ = get$();\nexport default $;\n", "import { MduiStatic } from './interfaces/MduiStatic';\nimport $ from 'mdui.jq/es/$';\n\n// 避免页面加载完后直接执行css动画\n// https://css-tricks.com/transitions-only-after-page-load/\nsetTimeout(() => $('body').addClass('mdui-loaded'));\n\nconst mdui = {\n  $: $,\n} as MduiStatic;\n\nexport default mdui;\n", "import $ from '../$';\nimport each from '../functions/each';\n$.fn.each = function (callback) {\n    return each(this, callback);\n};\n", "import { toElement } from '../utils';\n/**\n * 检查 container 元素内是否包含 contains 元素\n * @param container 父元素\n * @param contains 子元素\n * @example\n```js\ncontains( document, document.body ); // true\ncontains( document.getElementById('test'), document ); // false\ncontains( $('.container').get(0), $('.contains').get(0) ); // false\n```\n */\nfunction contains(container, contains) {\n    return container !== contains && toElement(container).contains(contains);\n}\nexport default contains;\n", "import each from './each';\n/**\n * 把第二个数组的元素追加到第一个数组中，并返回合并后的数组\n * @param first 第一个数组\n * @param second 该数组的元素将被追加到第一个数组中\n * @example\n```js\nmerge( [ 0, 1, 2 ], [ 2, 3, 4 ] )\n// [ 0, 1, 2, 2, 3, 4 ]\n```\n */\nfunction merge(first, second) {\n    each(second, (_, value) => {\n        first.push(value);\n    });\n    return first;\n}\nexport default merge;\n", "import $ from '../$';\n$.fn.get = function (index) {\n    return index === undefined\n        ? [].slice.call(this)\n        : this[index >= 0 ? index : index + this.length];\n};\n", "import $ from '../$';\nimport merge from '../functions/merge';\nimport { JQ } from '../JQ';\nimport './each';\nimport './get';\n$.fn.find = function (selector) {\n    const foundElements = [];\n    this.each((_, element) => {\n        merge(foundElements, $(element.querySelectorAll(selector)).get());\n    });\n    return new JQ(foundElements);\n};\n", "import $ from '../../$';\nimport contains from '../../functions/contains';\nimport { isObjectLike } from '../../utils';\nimport '../find';\n// 存储事件\nconst handlers = {};\n// 元素ID\nlet mduiElementId = 1;\n/**\n * 为元素赋予一个唯一的ID\n */\nfunction getElementId(element) {\n    const key = '_mduiEventId';\n    // @ts-ignore\n    if (!element[key]) {\n        // @ts-ignore\n        element[key] = ++mduiElementId;\n    }\n    // @ts-ignore\n    return element[key];\n}\n/**\n * 解析事件名中的命名空间\n */\nfunction parse(type) {\n    const parts = type.split('.');\n    return {\n        type: parts[0],\n        ns: parts.slice(1).sort().join(' '),\n    };\n}\n/**\n * 命名空间匹配规则\n */\nfunction matcherFor(ns) {\n    return new RegExp('(?:^| )' + ns.replace(' ', ' .* ?') + '(?: |$)');\n}\n/**\n * 获取匹配的事件\n * @param element\n * @param type\n * @param func\n * @param selector\n */\nfunction getHandlers(element, type, func, selector) {\n    const event = parse(type);\n    return (handlers[getElementId(element)] || []).filter((handler) => handler &&\n        (!event.type || handler.type === event.type) &&\n        (!event.ns || matcherFor(event.ns).test(handler.ns)) &&\n        (!func || getElementId(handler.func) === getElementId(func)) &&\n        (!selector || handler.selector === selector));\n}\n/**\n * 添加事件监听\n * @param element\n * @param types\n * @param func\n * @param data\n * @param selector\n */\nfunction add(element, types, func, data, selector) {\n    const elementId = getElementId(element);\n    if (!handlers[elementId]) {\n        handlers[elementId] = [];\n    }\n    // 传入 data.useCapture 来设置 useCapture: true\n    let useCapture = false;\n    if (isObjectLike(data) && data.useCapture) {\n        useCapture = true;\n    }\n    types.split(' ').forEach((type) => {\n        if (!type) {\n            return;\n        }\n        const event = parse(type);\n        function callFn(e, elem) {\n            // 因为鼠标事件模拟事件的 detail 属性是只读的，因此在 e._detail 中存储参数\n            const result = func.apply(elem, \n            // @ts-ignore\n            e._detail === undefined ? [e] : [e].concat(e._detail));\n            if (result === false) {\n                e.preventDefault();\n                e.stopPropagation();\n            }\n        }\n        function proxyFn(e) {\n            // @ts-ignore\n            if (e._ns && !matcherFor(e._ns).test(event.ns)) {\n                return;\n            }\n            // @ts-ignore\n            e._data = data;\n            if (selector) {\n                // 事件代理\n                $(element)\n                    .find(selector)\n                    .get()\n                    .reverse()\n                    .forEach((elem) => {\n                    if (elem === e.target ||\n                        contains(elem, e.target)) {\n                        callFn(e, elem);\n                    }\n                });\n            }\n            else {\n                // 不使用事件代理\n                callFn(e, element);\n            }\n        }\n        const handler = {\n            type: event.type,\n            ns: event.ns,\n            func,\n            selector,\n            id: handlers[elementId].length,\n            proxy: proxyFn,\n        };\n        handlers[elementId].push(handler);\n        element.addEventListener(handler.type, proxyFn, useCapture);\n    });\n}\n/**\n * 移除事件监听\n * @param element\n * @param types\n * @param func\n * @param selector\n */\nfunction remove(element, types, func, selector) {\n    const handlersInElement = handlers[getElementId(element)] || [];\n    const removeEvent = (handler) => {\n        delete handlersInElement[handler.id];\n        element.removeEventListener(handler.type, handler.proxy, false);\n    };\n    if (!types) {\n        handlersInElement.forEach((handler) => removeEvent(handler));\n    }\n    else {\n        types.split(' ').forEach((type) => {\n            if (type) {\n                getHandlers(element, type, func, selector).forEach((handler) => removeEvent(handler));\n            }\n        });\n    }\n}\nexport { parse, add, remove };\n", "import $ from '../$';\nimport './each';\nimport { parse } from './utils/event';\n$.fn.trigger = function (type, extraParameters) {\n    const event = parse(type);\n    let eventObject;\n    const eventParams = {\n        bubbles: true,\n        cancelable: true,\n    };\n    const isMouseEvent = ['click', 'mousedown', 'mouseup', 'mousemove'].indexOf(event.type) > -1;\n    if (isMouseEvent) {\n        // Note: MouseEvent 无法传入 detail 参数\n        eventObject = new MouseEvent(event.type, eventParams);\n    }\n    else {\n        eventParams.detail = extraParameters;\n        eventObject = new CustomEvent(event.type, eventParams);\n    }\n    // @ts-ignore\n    eventObject._detail = extraParameters;\n    // @ts-ignore\n    eventObject._ns = event.ns;\n    return this.each(function () {\n        this.dispatchEvent(eventObject);\n    });\n};\n", "import each from '../functions/each';\nimport { isUndefined } from '../utils';\nfunction extend(target, object1, ...objectN) {\n    objectN.unshift(object1);\n    each(objectN, (_, object) => {\n        each(object, (prop, value) => {\n            if (!isUndefined(value)) {\n                target[prop] = value;\n            }\n        });\n    });\n    return target;\n}\nexport default extend;\n", "import { isObjectLike } from '../utils';\nimport each from './each';\n/**\n * 将数组或对象序列化，序列化后的字符串可作为 URL 查询字符串使用\n *\n * 若传入数组，则格式必须和 serializeArray 方法的返回值一样\n * @param obj 对象或数组\n * @example\n```js\nparam({ width: 1680, height: 1050 });\n// width=1680&height=1050\n```\n * @example\n```js\nparam({ foo: { one: 1, two: 2 }})\n// foo[one]=1&foo[two]=2\n```\n * @example\n```js\nparam({ids: [1, 2, 3]})\n// ids[]=1&ids[]=2&ids[]=3\n```\n * @example\n```js\nparam([\n  {\"name\":\"name\",\"value\":\"mdui\"},\n  {\"name\":\"password\",\"value\":\"123456\"}\n])\n// name=mdui&password=123456\n```\n */\nfunction param(obj) {\n    if (!isObjectLike(obj) && !Array.isArray(obj)) {\n        return '';\n    }\n    const args = [];\n    function destructure(key, value) {\n        let keyTmp;\n        if (isObjectLike(value)) {\n            each(value, (i, v) => {\n                if (Array.isArray(value) && !isObjectLike(v)) {\n                    keyTmp = '';\n                }\n                else {\n                    keyTmp = i;\n                }\n                destructure(`${key}[${keyTmp}]`, v);\n            });\n        }\n        else {\n            if (value == null || value === '') {\n                keyTmp = '=';\n            }\n            else {\n                keyTmp = `=${encodeURIComponent(value)}`;\n            }\n            args.push(encodeURIComponent(key) + keyTmp);\n        }\n    }\n    if (Array.isArray(obj)) {\n        each(obj, function () {\n            destructure(this.name, this.value);\n        });\n    }\n    else {\n        each(obj, destructure);\n    }\n    return args.join('&');\n}\nexport default param;\n", "// 全局配置参数\nconst globalOptions = {};\n// 全局事件名\nconst ajaxEvents = {\n    ajaxStart: 'start.mdui.ajax',\n    ajaxSuccess: 'success.mdui.ajax',\n    ajaxError: 'error.mdui.ajax',\n    ajaxComplete: 'complete.mdui.ajax',\n};\nexport { globalOptions, ajaxEvents };\n", "import $ from '../$';\nimport '../methods/trigger';\nimport { isString, isUndefined } from '../utils';\nimport each from './each';\nimport extend from './extend';\nimport param from './param';\nimport { ajaxEvents, globalOptions } from './utils/ajax';\n/**\n * 判断此请求方法是否通过查询字符串提交参数\n * @param method 请求方法，大写\n */\nfunction isQueryStringData(method) {\n    return ['GET', 'HEAD'].indexOf(method) >= 0;\n}\n/**\n * 添加参数到 URL 上，且 URL 中不存在 ? 时，自动把第一个 & 替换为 ?\n * @param url\n * @param query\n */\nfunction appendQuery(url, query) {\n    return `${url}&${query}`.replace(/[&?]{1,2}/, '?');\n}\n/**\n * 合并请求参数，参数优先级：options > globalOptions > defaults\n * @param options\n */\nfunction mergeOptions(options) {\n    // 默认参数\n    const defaults = {\n        url: '',\n        method: 'GET',\n        data: '',\n        processData: true,\n        async: true,\n        cache: true,\n        username: '',\n        password: '',\n        headers: {},\n        xhrFields: {},\n        statusCode: {},\n        dataType: 'text',\n        contentType: 'application/x-www-form-urlencoded',\n        timeout: 0,\n        global: true,\n    };\n    // globalOptions 中的回调函数不合并\n    each(globalOptions, (key, value) => {\n        const callbacks = [\n            'beforeSend',\n            'success',\n            'error',\n            'complete',\n            'statusCode',\n        ];\n        // @ts-ignore\n        if (callbacks.indexOf(key) < 0 && !isUndefined(value)) {\n            defaults[key] = value;\n        }\n    });\n    return extend({}, defaults, options);\n}\n/**\n * 发送 ajax 请求\n * @param options\n * @example\n```js\najax({\n  method: \"POST\",\n  url: \"some.php\",\n  data: { name: \"John\", location: \"Boston\" }\n}).then(function( msg ) {\n  alert( \"Data Saved: \" + msg );\n});\n```\n */\nfunction ajax(options) {\n    // 是否已取消请求\n    let isCanceled = false;\n    // 事件参数\n    const eventParams = {};\n    // 参数合并\n    const mergedOptions = mergeOptions(options);\n    let url = mergedOptions.url || window.location.toString();\n    const method = mergedOptions.method.toUpperCase();\n    let data = mergedOptions.data;\n    const processData = mergedOptions.processData;\n    const async = mergedOptions.async;\n    const cache = mergedOptions.cache;\n    const username = mergedOptions.username;\n    const password = mergedOptions.password;\n    const headers = mergedOptions.headers;\n    const xhrFields = mergedOptions.xhrFields;\n    const statusCode = mergedOptions.statusCode;\n    const dataType = mergedOptions.dataType;\n    const contentType = mergedOptions.contentType;\n    const timeout = mergedOptions.timeout;\n    const global = mergedOptions.global;\n    // 需要发送的数据\n    // GET/HEAD 请求和 processData 为 true 时，转换为查询字符串格式，特殊格式不转换\n    if (data &&\n        (isQueryStringData(method) || processData) &&\n        !isString(data) &&\n        !(data instanceof ArrayBuffer) &&\n        !(data instanceof Blob) &&\n        !(data instanceof Document) &&\n        !(data instanceof FormData)) {\n        data = param(data);\n    }\n    // 对于 GET、HEAD 类型的请求，把 data 数据添加到 URL 中\n    if (data && isQueryStringData(method)) {\n        // 查询字符串拼接到 URL 中\n        url = appendQuery(url, data);\n        data = null;\n    }\n    /**\n     * 触发事件和回调函数\n     * @param event\n     * @param params\n     * @param callback\n     * @param args\n     */\n    function trigger(event, params, callback, ...args) {\n        // 触发全局事件\n        if (global) {\n            $(document).trigger(event, params);\n        }\n        // 触发 ajax 回调和事件\n        let result1;\n        let result2;\n        if (callback) {\n            // 全局回调\n            if (callback in globalOptions) {\n                // @ts-ignore\n                result1 = globalOptions[callback](...args);\n            }\n            // 自定义回调\n            if (mergedOptions[callback]) {\n                // @ts-ignore\n                result2 = mergedOptions[callback](...args);\n            }\n            // beforeSend 回调返回 false 时取消 ajax 请求\n            if (callback === 'beforeSend' &&\n                (result1 === false || result2 === false)) {\n                isCanceled = true;\n            }\n        }\n    }\n    // XMLHttpRequest 请求\n    function XHR() {\n        let textStatus;\n        return new Promise((resolve, reject) => {\n            // GET/HEAD 请求的缓存处理\n            if (isQueryStringData(method) && !cache) {\n                url = appendQuery(url, `_=${Date.now()}`);\n            }\n            // 创建 XHR\n            const xhr = new XMLHttpRequest();\n            xhr.open(method, url, async, username, password);\n            if (contentType ||\n                (data && !isQueryStringData(method) && contentType !== false)) {\n                xhr.setRequestHeader('Content-Type', contentType);\n            }\n            // 设置 Accept\n            if (dataType === 'json') {\n                xhr.setRequestHeader('Accept', 'application/json, text/javascript');\n            }\n            // 添加 headers\n            if (headers) {\n                each(headers, (key, value) => {\n                    // undefined 值不发送，string 和 null 需要发送\n                    if (!isUndefined(value)) {\n                        xhr.setRequestHeader(key, value + ''); // 把 null 转换成字符串\n                    }\n                });\n            }\n            // 检查是否是跨域请求，跨域请求时不添加 X-Requested-With\n            const crossDomain = /^([\\w-]+:)?\\/\\/([^/]+)/.test(url) &&\n                RegExp.$2 !== window.location.host;\n            if (!crossDomain) {\n                xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');\n            }\n            if (xhrFields) {\n                each(xhrFields, (key, value) => {\n                    // @ts-ignore\n                    xhr[key] = value;\n                });\n            }\n            eventParams.xhr = xhr;\n            eventParams.options = mergedOptions;\n            let xhrTimeout;\n            xhr.onload = function () {\n                if (xhrTimeout) {\n                    clearTimeout(xhrTimeout);\n                }\n                // AJAX 返回的 HTTP 响应码是否表示成功\n                const isHttpStatusSuccess = (xhr.status >= 200 && xhr.status < 300) ||\n                    xhr.status === 304 ||\n                    xhr.status === 0;\n                let responseData;\n                if (isHttpStatusSuccess) {\n                    if (xhr.status === 204 || method === 'HEAD') {\n                        textStatus = 'nocontent';\n                    }\n                    else if (xhr.status === 304) {\n                        textStatus = 'notmodified';\n                    }\n                    else {\n                        textStatus = 'success';\n                    }\n                    if (dataType === 'json') {\n                        try {\n                            responseData =\n                                method === 'HEAD' ? undefined : JSON.parse(xhr.responseText);\n                            eventParams.data = responseData;\n                        }\n                        catch (err) {\n                            textStatus = 'parsererror';\n                            trigger(ajaxEvents.ajaxError, eventParams, 'error', xhr, textStatus);\n                            reject(new Error(textStatus));\n                        }\n                        if (textStatus !== 'parsererror') {\n                            trigger(ajaxEvents.ajaxSuccess, eventParams, 'success', responseData, textStatus, xhr);\n                            resolve(responseData);\n                        }\n                    }\n                    else {\n                        responseData =\n                            method === 'HEAD'\n                                ? undefined\n                                : xhr.responseType === 'text' || xhr.responseType === ''\n                                    ? xhr.responseText\n                                    : xhr.response;\n                        eventParams.data = responseData;\n                        trigger(ajaxEvents.ajaxSuccess, eventParams, 'success', responseData, textStatus, xhr);\n                        resolve(responseData);\n                    }\n                }\n                else {\n                    textStatus = 'error';\n                    trigger(ajaxEvents.ajaxError, eventParams, 'error', xhr, textStatus);\n                    reject(new Error(textStatus));\n                }\n                // statusCode\n                each([globalOptions.statusCode, statusCode], (_, func) => {\n                    if (func && func[xhr.status]) {\n                        if (isHttpStatusSuccess) {\n                            func[xhr.status](responseData, textStatus, xhr);\n                        }\n                        else {\n                            func[xhr.status](xhr, textStatus);\n                        }\n                    }\n                });\n                trigger(ajaxEvents.ajaxComplete, eventParams, 'complete', xhr, textStatus);\n            };\n            xhr.onerror = function () {\n                if (xhrTimeout) {\n                    clearTimeout(xhrTimeout);\n                }\n                trigger(ajaxEvents.ajaxError, eventParams, 'error', xhr, xhr.statusText);\n                trigger(ajaxEvents.ajaxComplete, eventParams, 'complete', xhr, 'error');\n                reject(new Error(xhr.statusText));\n            };\n            xhr.onabort = function () {\n                let statusText = 'abort';\n                if (xhrTimeout) {\n                    statusText = 'timeout';\n                    clearTimeout(xhrTimeout);\n                }\n                trigger(ajaxEvents.ajaxError, eventParams, 'error', xhr, statusText);\n                trigger(ajaxEvents.ajaxComplete, eventParams, 'complete', xhr, statusText);\n                reject(new Error(statusText));\n            };\n            // ajax start 回调\n            trigger(ajaxEvents.ajaxStart, eventParams, 'beforeSend', xhr);\n            if (isCanceled) {\n                reject(new Error('cancel'));\n                return;\n            }\n            // Timeout\n            if (timeout > 0) {\n                xhrTimeout = setTimeout(() => {\n                    xhr.abort();\n                }, timeout);\n            }\n            // 发送 XHR\n            xhr.send(data);\n        });\n    }\n    return XHR();\n}\nexport default ajax;\n", "import $ from '../$';\nimport ajax from '../functions/ajax';\n$.ajax = ajax;\n", "import extend from '../functions/extend';\nimport { globalOptions } from './utils/ajax';\n/**\n * 为 Ajax 请求设置全局配置参数\n * @param options 键值对参数\n * @example\n```js\najaxSetup({\n  dataType: 'json',\n  method: 'POST',\n});\n```\n */\nfunction ajaxSetup(options) {\n    return extend(globalOptions, options);\n}\nexport default ajaxSetup;\n", "import $ from '../$';\nimport ajaxSetup from '../functions/ajaxSetup';\n$.ajaxSetup = ajaxSetup;\n", "import $ from '../$';\nimport contains from '../functions/contains';\n$.contains = contains;\n", "const dataNS = '_mduiElementDataStorage';\nexport default dataNS;\n", "import { isObjectLike, isUndefined, toCamelCase } from '../utils';\nimport each from './each';\nimport dataNS from './utils/data';\n/**\n * 在元素上设置键值对数据\n * @param element\n * @param object\n */\nfunction setObjectToElement(element, object) {\n    // @ts-ignore\n    if (!element[dataNS]) {\n        // @ts-ignore\n        element[dataNS] = {};\n    }\n    each(object, (key, value) => {\n        // @ts-ignore\n        element[dataNS][toCamelCase(key)] = value;\n    });\n}\nfunction data(element, key, value) {\n    // 根据键值对设置值\n    // data(element, { 'key' : 'value' })\n    if (isObjectLike(key)) {\n        setObjectToElement(element, key);\n        return key;\n    }\n    // 根据 key、value 设置值\n    // data(element, 'key', 'value')\n    if (!isUndefined(value)) {\n        setObjectToElement(element, { [key]: value });\n        return value;\n    }\n    // 获取所有值\n    // data(element)\n    if (isUndefined(key)) {\n        // @ts-ignore\n        return element[dataNS] ? element[dataNS] : {};\n    }\n    // 从 dataNS 中获取指定值\n    // data(element, 'key')\n    key = toCamelCase(key);\n    // @ts-ignore\n    if (element[dataNS] && key in element[dataNS]) {\n        // @ts-ignore\n        return element[dataNS][key];\n    }\n    return undefined;\n}\nexport default data;\n", "import $ from '../$';\nimport data from '../functions/data';\n$.data = data;\n", "import $ from '../$';\nimport each from '../functions/each';\n$.each = each;\n", "import $ from '../$';\nimport each from '../functions/each';\nimport extend from '../functions/extend';\n$.extend = function (...objectN) {\n    if (objectN.length === 1) {\n        each(objectN[0], (prop, value) => {\n            this[prop] = value;\n        });\n        return this;\n    }\n    return extend(objectN.shift(), objectN.shift(), ...objectN);\n};\n", "import each from './each';\nfunction map(elements, callback) {\n    let value;\n    const ret = [];\n    each(elements, (i, element) => {\n        value = callback.call(window, element, i);\n        if (value != null) {\n            ret.push(value);\n        }\n    });\n    return [].concat(...ret);\n}\nexport default map;\n", "import $ from '../$';\nimport map from '../functions/map';\n$.map = map;\n", "import $ from '../$';\nimport merge from '../functions/merge';\n$.merge = merge;\n", "import $ from '../$';\nimport param from '../functions/param';\n$.param = param;\n", "import each from '../functions/each';\nimport { isUndefined, isString, toCamelCase } from '../utils';\nimport dataNS from './utils/data';\n/**\n * 移除指定元素上存放的数据\n * @param element 存放数据的元素\n * @param name\n * 数据键名\n *\n * 若未指定键名，将移除元素上所有数据\n *\n * 多个键名可以用空格分隔，或者用数组表示多个键名\n  @example\n```js\n// 移除元素上键名为 name 的数据\nremoveData(document.body, 'name');\n```\n * @example\n```js\n// 移除元素上键名为 name1 和 name2 的数据\nremoveData(document.body, 'name1 name2');\n```\n * @example\n```js\n// 移除元素上键名为 name1 和 name2 的数据\nremoveData(document.body, ['name1', 'name2']);\n```\n * @example\n```js\n// 移除元素上所有数据\nremoveData(document.body);\n```\n */\nfunction removeData(element, name) {\n    // @ts-ignore\n    if (!element[dataNS]) {\n        return;\n    }\n    const remove = (nameItem) => {\n        nameItem = toCamelCase(nameItem);\n        // @ts-ignore\n        if (element[dataNS][nameItem]) {\n            // @ts-ignore\n            element[dataNS][nameItem] = null;\n            // @ts-ignore\n            delete element[dataNS][nameItem];\n        }\n    };\n    if (isUndefined(name)) {\n        // @ts-ignore\n        element[dataNS] = null;\n        // @ts-ignore\n        delete element[dataNS];\n        // @ts-ignore\n    }\n    else if (isString(name)) {\n        name\n            .split(' ')\n            .filter((nameItem) => nameItem)\n            .forEach((nameItem) => remove(nameItem));\n    }\n    else {\n        each(name, (_, nameItem) => remove(nameItem));\n    }\n}\nexport default removeData;\n", "import $ from '../$';\nimport removeData from '../functions/removeData';\n$.removeData = removeData;\n", "import each from './each';\n/**\n * 过滤掉数组中的重复元素\n * @param arr 数组\n * @example\n```js\nunique([1, 2, 12, 3, 2, 1, 2, 1, 1]);\n// [1, 2, 12, 3]\n```\n */\nfunction unique(arr) {\n    const result = [];\n    each(arr, (_, val) => {\n        if (result.indexOf(val) === -1) {\n            result.push(val);\n        }\n    });\n    return result;\n}\nexport default unique;\n", "import $ from '../$';\nimport unique from '../functions/unique';\n$.unique = unique;\n", "import $ from '../$';\nimport merge from '../functions/merge';\nimport unique from '../functions/unique';\nimport { JQ } from '../JQ';\nimport './get';\n$.fn.add = function (selector) {\n    return new JQ(unique(merge(this.get(), $(selector).get())));\n};\n", "import $ from '../$';\nimport each from '../functions/each';\nimport { isElement, isFunction } from '../utils';\nimport './each';\neach(['add', 'remove', 'toggle'], (_, name) => {\n    $.fn[`${name}Class`] = function (className) {\n        if (name === 'remove' && !arguments.length) {\n            return this.each((_, element) => {\n                element.setAttribute('class', '');\n            });\n        }\n        return this.each((i, element) => {\n            if (!isElement(element)) {\n                return;\n            }\n            const classes = (isFunction(className)\n                ? className.call(element, i, element.getAttribute('class') || '')\n                : className)\n                .split(' ')\n                .filter((name) => name);\n            each(classes, (_, cls) => {\n                element.classList[name](cls);\n            });\n        });\n    };\n});\n", "import $ from '../$';\nimport each from '../functions/each';\nimport './each';\neach(['insertBefore', 'insertAfter'], (nameIndex, name) => {\n    $.fn[name] = function (target) {\n        const $element = nameIndex ? $(this.get().reverse()) : this; // 顺序和 jQuery 保持一致\n        const $target = $(target);\n        const result = [];\n        $target.each((index, target) => {\n            if (!target.parentNode) {\n                return;\n            }\n            $element.each((_, element) => {\n                const newItem = index\n                    ? element.cloneNode(true)\n                    : element;\n                const existingItem = nameIndex ? target.nextSibling : target;\n                result.push(newItem);\n                target.parentNode.insertBefore(newItem, existingItem);\n            });\n        });\n        return $(nameIndex ? result.reverse() : result);\n    };\n});\n", "import $ from '../$';\nimport each from '../functions/each';\nimport { getChildNodesArray, isFunction, isString, isElement } from '../utils';\nimport './each';\nimport './insertAfter';\nimport './insertBefore';\n/**\n * 是否不是 HTML 字符串（包裹在 <> 中）\n * @param target\n */\nfunction isPlainText(target) {\n    return (isString(target) && (target[0] !== '<' || target[target.length - 1] !== '>'));\n}\neach(['before', 'after'], (nameIndex, name) => {\n    $.fn[name] = function (...args) {\n        // after 方法，多个参数需要按参数顺序添加到元素后面，所以需要将参数顺序反向处理\n        if (nameIndex === 1) {\n            args = args.reverse();\n        }\n        return this.each((index, element) => {\n            const targets = isFunction(args[0])\n                ? [args[0].call(element, index, element.innerHTML)]\n                : args;\n            each(targets, (_, target) => {\n                let $target;\n                if (isPlainText(target)) {\n                    $target = $(getChildNodesArray(target, 'div'));\n                }\n                else if (index && isElement(target)) {\n                    $target = $(target.cloneNode(true));\n                }\n                else {\n                    $target = $(target);\n                }\n                $target[nameIndex ? 'insertAfter' : 'insertBefore'](element);\n            });\n        });\n    };\n});\n", "import $ from '../$';\nimport each from '../functions/each';\nimport { isFunction, isObjectLike, returnFalse } from '../utils';\nimport './each';\nimport { remove } from './utils/event';\n$.fn.off = function (types, selector, callback) {\n    // types 是对象\n    if (isObjectLike(types)) {\n        each(types, (type, fn) => {\n            // this.off('click', undefined, function () {})\n            // this.off('click', '.box', function () {})\n            this.off(type, selector, fn);\n        });\n        return this;\n    }\n    // selector 不存在\n    if (selector === false || isFunction(selector)) {\n        callback = selector;\n        selector = undefined;\n        // this.off('click', undefined, function () {})\n    }\n    // callback 传入 `false`，相当于 `return false`\n    if (callback === false) {\n        callback = returnFalse;\n    }\n    return this.each(function () {\n        remove(this, types, callback, selector);\n    });\n};\n", "import $ from '../$';\nimport each from '../functions/each';\nimport { isObjectLike, isString, returnFalse } from '../utils';\nimport './each';\nimport './off';\nimport { add } from './utils/event';\n$.fn.on = function (types, selector, data, callback, one) {\n    // types 可以是 type/func 对象\n    if (isObjectLike(types)) {\n        // (types-Object, selector, data)\n        if (!isString(selector)) {\n            // (types-Object, data)\n            data = data || selector;\n            selector = undefined;\n        }\n        each(types, (type, fn) => {\n            // selector 和 data 都可能是 undefined\n            // @ts-ignore\n            this.on(type, selector, data, fn, one);\n        });\n        return this;\n    }\n    if (data == null && callback == null) {\n        // (types, fn)\n        callback = selector;\n        data = selector = undefined;\n    }\n    else if (callback == null) {\n        if (isString(selector)) {\n            // (types, selector, fn)\n            callback = data;\n            data = undefined;\n        }\n        else {\n            // (types, data, fn)\n            callback = data;\n            data = selector;\n            selector = undefined;\n        }\n    }\n    if (callback === false) {\n        callback = returnFalse;\n    }\n    else if (!callback) {\n        return this;\n    }\n    // $().one()\n    if (one) {\n        // eslint-disable-next-line @typescript-eslint/no-this-alias\n        const _this = this;\n        const origCallback = callback;\n        callback = function (event) {\n            _this.off(event.type, selector, callback);\n            // eslint-disable-next-line prefer-rest-params\n            return origCallback.apply(this, arguments);\n        };\n    }\n    return this.each(function () {\n        add(this, types, callback, data, selector);\n    });\n};\n", "import $ from '../$';\nimport each from '../functions/each';\nimport { ajaxEvents } from '../functions/utils/ajax';\nimport './on';\neach(ajaxEvents, (name, eventName) => {\n    $.fn[name] = function (fn) {\n        return this.on(eventName, (e, params) => {\n            fn(e, params.xhr, params.options, params.data);\n        });\n    };\n});\n", "import $ from '../$';\nimport map from '../functions/map';\nimport { JQ } from '../JQ';\n$.fn.map = function (callback) {\n    return new JQ(map(this, (element, i) => callback.call(element, i, element)));\n};\n", "import $ from '../$';\nimport './map';\n$.fn.clone = function () {\n    return this.map(function () {\n        return this.cloneNode(true);\n    });\n};\n", "import $ from '../$';\nimport { isDocument, isFunction, isString, isWindow } from '../utils';\nimport './each';\n$.fn.is = function (selector) {\n    let isMatched = false;\n    if (isFunction(selector)) {\n        this.each((index, element) => {\n            if (selector.call(element, index, element)) {\n                isMatched = true;\n            }\n        });\n        return isMatched;\n    }\n    if (isString(selector)) {\n        this.each((_, element) => {\n            if (isDocument(element) || isWindow(element)) {\n                return;\n            }\n            // @ts-ignore\n            const matches = element.matches || element.msMatchesSelector;\n            if (matches.call(element, selector)) {\n                isMatched = true;\n            }\n        });\n        return isMatched;\n    }\n    const $compareWith = $(selector);\n    this.each((_, element) => {\n        $compareWith.each((_, compare) => {\n            if (element === compare) {\n                isMatched = true;\n            }\n        });\n    });\n    return isMatched;\n};\n", "import $ from '../$';\nimport './each';\nimport './is';\n$.fn.remove = function (selector) {\n    return this.each((_, element) => {\n        if (element.parentNode && (!selector || $(element).is(selector))) {\n            element.parentNode.removeChild(element);\n        }\n    });\n};\n", "import $ from '../$';\nimport each from '../functions/each';\nimport { isFunction, isString } from '../utils';\nimport './after';\nimport './before';\nimport './clone';\nimport './each';\nimport './map';\nimport './remove';\neach(['prepend', 'append'], (nameIndex, name) => {\n    $.fn[name] = function (...args) {\n        return this.each((index, element) => {\n            const childNodes = element.childNodes;\n            const childLength = childNodes.length;\n            const child = childLength\n                ? childNodes[nameIndex ? childLength - 1 : 0]\n                : document.createElement('div');\n            if (!childLength) {\n                element.appendChild(child);\n            }\n            let contents = isFunction(args[0])\n                ? [args[0].call(element, index, element.innerHTML)]\n                : args;\n            // 如果不是字符串，则仅第一个元素使用原始元素，其他的都克隆自第一个元素\n            if (index) {\n                contents = contents.map((content) => {\n                    return isString(content) ? content : $(content).clone();\n                });\n            }\n            $(child)[nameIndex ? 'after' : 'before'](...contents);\n            if (!childLength) {\n                element.removeChild(child);\n            }\n        });\n    };\n});\n", "import $ from '../$';\nimport each from '../functions/each';\nimport './insertAfter';\nimport './insertBefore';\nimport './map';\nimport './remove';\neach(['appendTo', 'prependTo'], (nameIndex, name) => {\n    $.fn[name] = function (target) {\n        const extraChilds = [];\n        const $target = $(target).map((_, element) => {\n            const childNodes = element.childNodes;\n            const childLength = childNodes.length;\n            if (childLength) {\n                return childNodes[nameIndex ? 0 : childLength - 1];\n            }\n            const child = document.createElement('div');\n            element.appendChild(child);\n            extraChilds.push(child);\n            return child;\n        });\n        const $result = this[nameIndex ? 'insertBefore' : 'insertAfter']($target);\n        $(extraChilds).remove();\n        return $result;\n    };\n});\n", "import $ from '../$';\nimport each from '../functions/each';\nimport { cssNumber, getStyle, isElement, isFunction, isNull, isNumber, isObjectLike, isUndefined, toCamelCase, } from '../utils';\nimport './each';\neach(['attr', 'prop', 'css'], (nameIndex, name) => {\n    function set(element, key, value) {\n        // 值为 undefined 时，不修改\n        if (isUndefined(value)) {\n            return;\n        }\n        switch (nameIndex) {\n            // attr\n            case 0:\n                if (isNull(value)) {\n                    element.removeAttribute(key);\n                }\n                else {\n                    element.setAttribute(key, value);\n                }\n                break;\n            // prop\n            case 1:\n                // @ts-ignore\n                element[key] = value;\n                break;\n            // css\n            default:\n                key = toCamelCase(key);\n                // @ts-ignore\n                element.style[key] = isNumber(value)\n                    ? `${value}${cssNumber.indexOf(key) > -1 ? '' : 'px'}`\n                    : value;\n                break;\n        }\n    }\n    function get(element, key) {\n        switch (nameIndex) {\n            // attr\n            case 0:\n                // 属性不存在时，原生 getAttribute 方法返回 null，而 jquery 返回 undefined。这里和 jquery 保持一致\n                const value = element.getAttribute(key);\n                return isNull(value) ? undefined : value;\n            // prop\n            case 1:\n                // @ts-ignore\n                return element[key];\n            // css\n            default:\n                return getStyle(element, key);\n        }\n    }\n    $.fn[name] = function (key, value) {\n        if (isObjectLike(key)) {\n            each(key, (k, v) => {\n                // @ts-ignore\n                this[name](k, v);\n            });\n            return this;\n        }\n        if (arguments.length === 1) {\n            const element = this[0];\n            return isElement(element) ? get(element, key) : undefined;\n        }\n        return this.each((i, element) => {\n            set(element, key, isFunction(value) ? value.call(element, i, get(element, key)) : value);\n        });\n    };\n});\n", "import $ from '../$';\nimport each from '../functions/each';\nimport unique from '../functions/unique';\nimport { JQ } from '../JQ';\nimport { isElement } from '../utils';\nimport './each';\nimport './is';\n$.fn.children = function (selector) {\n    const children = [];\n    this.each((_, element) => {\n        each(element.childNodes, (__, childNode) => {\n            if (!isElement(childNode)) {\n                return;\n            }\n            if (!selector || $(childNode).is(selector)) {\n                children.push(childNode);\n            }\n        });\n    });\n    return new JQ(unique(children));\n};\n", "import $ from '../$';\nimport { JQ } from '../JQ';\n$.fn.slice = function (...args) {\n    return new JQ([].slice.apply(this, args));\n};\n", "import $ from '../$';\nimport { JQ } from '../JQ';\nimport './slice';\n$.fn.eq = function (index) {\n    const ret = index === -1 ? this.slice(index) : this.slice(index, +index + 1);\n    return new JQ(ret);\n};\n", "import $ from '../../$';\nimport unique from '../../functions/unique';\nimport { JQ } from '../../JQ';\nimport { isElement } from '../../utils';\nimport '../each';\nimport '../is';\nexport default function dir($elements, nameIndex, node, selector, filter) {\n    const ret = [];\n    let target;\n    $elements.each((_, element) => {\n        target = element[node];\n        // 不能包含最顶层的 document 元素\n        while (target && isElement(target)) {\n            // prevUntil, nextUntil, parentsUntil\n            if (nameIndex === 2) {\n                if (selector && $(target).is(selector)) {\n                    break;\n                }\n                if (!filter || $(target).is(filter)) {\n                    ret.push(target);\n                }\n            }\n            // prev, next, parent\n            else if (nameIndex === 0) {\n                if (!selector || $(target).is(selector)) {\n                    ret.push(target);\n                }\n                break;\n            }\n            // prevAll, nextAll, parents\n            else {\n                if (!selector || $(target).is(selector)) {\n                    ret.push(target);\n                }\n            }\n            // @ts-ignore\n            target = target[node];\n        }\n    });\n    return new JQ(unique(ret));\n}\n", "import $ from '../$';\nimport each from '../functions/each';\nimport './get';\nimport dir from './utils/dir';\neach(['', 's', 'sUntil'], (nameIndex, name) => {\n    $.fn[`parent${name}`] = function (selector, filter) {\n        // parents、parentsUntil 需要把元素的顺序反向处理，以便和 jQuery 的结果一致\n        const $nodes = !nameIndex ? this : $(this.get().reverse());\n        return dir($nodes, nameIndex, 'parentNode', selector, filter);\n    };\n});\n", "import $ from '../$';\nimport { JQ } from '../JQ';\nimport './eq';\nimport './is';\nimport './parents';\n$.fn.closest = function (selector) {\n    if (this.is(selector)) {\n        return this;\n    }\n    const matched = [];\n    this.parents().each((_, element) => {\n        if ($(element).is(selector)) {\n            matched.push(element);\n            return false;\n        }\n    });\n    return new JQ(matched);\n};\n", "import $ from '../$';\nimport data from '../functions/data';\nimport { isObjectLike, isString, isUndefined, toCamelCase, toKebabCase, } from '../utils';\nimport './each';\nconst rbrace = /^(?:{[\\w\\W]*\\}|\\[[\\w\\W]*\\])$/;\n// 从 `data-*` 中获取的值，需要经过该函数转换\nfunction getData(value) {\n    if (value === 'true') {\n        return true;\n    }\n    if (value === 'false') {\n        return false;\n    }\n    if (value === 'null') {\n        return null;\n    }\n    if (value === +value + '') {\n        return +value;\n    }\n    if (rbrace.test(value)) {\n        return JSON.parse(value);\n    }\n    return value;\n}\n// 若 value 不存在，则从 `data-*` 中获取值\nfunction dataAttr(element, key, value) {\n    if (isUndefined(value) && element.nodeType === 1) {\n        const name = 'data-' + toKebabCase(key);\n        value = element.getAttribute(name);\n        if (isString(value)) {\n            try {\n                value = getData(value);\n            }\n            catch (e) { }\n        }\n        else {\n            value = undefined;\n        }\n    }\n    return value;\n}\n$.fn.data = function (key, value) {\n    // 获取所有值\n    if (isUndefined(key)) {\n        if (!this.length) {\n            return undefined;\n        }\n        const element = this[0];\n        const resultData = data(element);\n        // window, document 上不存在 `data-*` 属性\n        if (element.nodeType !== 1) {\n            return resultData;\n        }\n        // 从 `data-*` 中获取值\n        const attrs = element.attributes;\n        let i = attrs.length;\n        while (i--) {\n            if (attrs[i]) {\n                let name = attrs[i].name;\n                if (name.indexOf('data-') === 0) {\n                    name = toCamelCase(name.slice(5));\n                    resultData[name] = dataAttr(element, name, resultData[name]);\n                }\n            }\n        }\n        return resultData;\n    }\n    // 同时设置多个值\n    if (isObjectLike(key)) {\n        return this.each(function () {\n            data(this, key);\n        });\n    }\n    // value 传入了 undefined\n    if (arguments.length === 2 && isUndefined(value)) {\n        return this;\n    }\n    // 设置值\n    if (!isUndefined(value)) {\n        return this.each(function () {\n            data(this, key, value);\n        });\n    }\n    // 获取值\n    if (!this.length) {\n        return undefined;\n    }\n    return dataAttr(this[0], key, data(this[0], key));\n};\n", "import $ from '../$';\nimport './each';\n$.fn.empty = function () {\n    return this.each(function () {\n        this.innerHTML = '';\n    });\n};\n", "import $ from '../$';\nimport each from '../functions/each';\n$.fn.extend = function (obj) {\n    each(obj, (prop, value) => {\n        // 在 JQ 对象上扩展方法时，需要自己添加 typescript 的类型定义\n        $.fn[prop] = value;\n    });\n    return this;\n};\n", "import $ from '../$';\nimport { isFunction, isString } from '../utils';\nimport './is';\nimport './map';\n$.fn.filter = function (selector) {\n    if (isFunction(selector)) {\n        return this.map((index, element) => selector.call(element, index, element) ? element : undefined);\n    }\n    if (isString(selector)) {\n        return this.map((_, element) => $(element).is(selector) ? element : undefined);\n    }\n    const $selector = $(selector);\n    return this.map((_, element) => $selector.get().indexOf(element) > -1 ? element : undefined);\n};\n", "import $ from '../$';\nimport './eq';\n$.fn.first = function () {\n    return this.eq(0);\n};\n", "import $ from '../$';\nimport contains from '../functions/contains';\nimport { isString } from '../utils';\nimport './find';\n$.fn.has = function (selector) {\n    const $targets = isString(selector) ? this.find(selector) : $(selector);\n    const { length } = $targets;\n    return this.map(function () {\n        for (let i = 0; i < length; i += 1) {\n            if (contains(this, $targets[i])) {\n                return this;\n            }\n        }\n        return;\n    });\n};\n", "import $ from '../$';\n$.fn.hasClass = function (className) {\n    return this[0].classList.contains(className);\n};\n", "import $ from '../$';\nimport each from '../functions/each';\nimport { isBoolean, isDocument, isFunction, isWindow, toElement, isBorderBox, getExtraWidth, getComputedStyleValue, isIE, } from '../utils';\nimport './css';\nimport './each';\n/**\n * 值上面的 padding、border、margin 处理\n * @param element\n * @param name\n * @param value\n * @param funcIndex\n * @param includeMargin\n * @param multiply\n */\nfunction handleExtraWidth(element, name, value, funcIndex, includeMargin, multiply) {\n    // 获取元素的 padding, border, margin 宽度（两侧宽度的和）\n    const getExtraWidthValue = (extra) => {\n        return (getExtraWidth(element, name.toLowerCase(), extra) *\n            multiply);\n    };\n    if (funcIndex === 2 && includeMargin) {\n        value += getExtraWidthValue('margin');\n    }\n    if (isBorderBox(element)) {\n        // IE 为 box-sizing: border-box 时，得到的值不含 border 和 padding，这里先修复\n        // 仅获取时需要处理，multiply === 1 为 get\n        if (isIE() && multiply === 1) {\n            value += getExtraWidthValue('border');\n            value += getExtraWidthValue('padding');\n        }\n        if (funcIndex === 0) {\n            value -= getExtraWidthValue('border');\n        }\n        if (funcIndex === 1) {\n            value -= getExtraWidthValue('border');\n            value -= getExtraWidthValue('padding');\n        }\n    }\n    else {\n        if (funcIndex === 0) {\n            value += getExtraWidthValue('padding');\n        }\n        if (funcIndex === 2) {\n            value += getExtraWidthValue('border');\n            value += getExtraWidthValue('padding');\n        }\n    }\n    return value;\n}\n/**\n * 获取元素的样式值\n * @param element\n * @param name\n * @param funcIndex 0: innerWidth, innerHeight; 1: width, height; 2: outerWidth, outerHeight\n * @param includeMargin\n */\nfunction get(element, name, funcIndex, includeMargin) {\n    const clientProp = `client${name}`;\n    const scrollProp = `scroll${name}`;\n    const offsetProp = `offset${name}`;\n    const innerProp = `inner${name}`;\n    // $(window).width()\n    if (isWindow(element)) {\n        // outerWidth, outerHeight 需要包含滚动条的宽度\n        return funcIndex === 2\n            ? element[innerProp]\n            : toElement(document)[clientProp];\n    }\n    // $(document).width()\n    if (isDocument(element)) {\n        const doc = toElement(element);\n        return Math.max(\n        // @ts-ignore\n        element.body[scrollProp], doc[scrollProp], \n        // @ts-ignore\n        element.body[offsetProp], doc[offsetProp], doc[clientProp]);\n    }\n    const value = parseFloat(getComputedStyleValue(element, name.toLowerCase()) || '0');\n    return handleExtraWidth(element, name, value, funcIndex, includeMargin, 1);\n}\n/**\n * 设置元素的样式值\n * @param element\n * @param elementIndex\n * @param name\n * @param funcIndex 0: innerWidth, innerHeight; 1: width, height; 2: outerWidth, outerHeight\n * @param includeMargin\n * @param value\n */\nfunction set(element, elementIndex, name, funcIndex, includeMargin, value) {\n    let computedValue = isFunction(value)\n        ? value.call(element, elementIndex, get(element, name, funcIndex, includeMargin))\n        : value;\n    if (computedValue == null) {\n        return;\n    }\n    const $element = $(element);\n    const dimension = name.toLowerCase();\n    // 特殊的值，不需要计算 padding、border、margin\n    if (['auto', 'inherit', ''].indexOf(computedValue) > -1) {\n        $element.css(dimension, computedValue);\n        return;\n    }\n    // 其他值保留原始单位。注意：如果不使用 px 作为单位，则算出的值一般是不准确的\n    const suffix = computedValue.toString().replace(/\\b[0-9.]*/, '');\n    const numerical = parseFloat(computedValue);\n    computedValue =\n        handleExtraWidth(element, name, numerical, funcIndex, includeMargin, -1) +\n            (suffix || 'px');\n    $element.css(dimension, computedValue);\n}\neach(['Width', 'Height'], (_, name) => {\n    each([`inner${name}`, name.toLowerCase(), `outer${name}`], (funcIndex, funcName) => {\n        $.fn[funcName] = function (margin, value) {\n            // 是否是赋值操作\n            const isSet = arguments.length && (funcIndex < 2 || !isBoolean(margin));\n            const includeMargin = margin === true || value === true;\n            // 获取第一个元素的值\n            if (!isSet) {\n                return this.length\n                    ? get(this[0], name, funcIndex, includeMargin)\n                    : undefined;\n            }\n            // 设置每个元素的值\n            return this.each((index, element) => set(element, index, name, funcIndex, includeMargin, margin));\n        };\n    });\n});\n", "import $ from '../$';\nimport './each';\n$.fn.hide = function () {\n    return this.each(function () {\n        this.style.display = 'none';\n    });\n};\n", "import $ from '../$';\nimport each from '../functions/each';\nimport map from '../functions/map';\nimport { isElement, isFunction, isUndefined, toElement } from '../utils';\nimport './each';\nimport './is';\neach(['val', 'html', 'text'], (nameIndex, name) => {\n    const props = {\n        0: 'value',\n        1: 'innerHTML',\n        2: 'textContent',\n    };\n    const propName = props[nameIndex];\n    function get($elements) {\n        // text() 获取所有元素的文本\n        if (nameIndex === 2) {\n            // @ts-ignore\n            return map($elements, (element) => toElement(element)[propName]).join('');\n        }\n        // 空集合时，val() 和 html() 返回 undefined\n        if (!$elements.length) {\n            return undefined;\n        }\n        // val() 和 html() 仅获取第一个元素的内容\n        const firstElement = $elements[0];\n        // select multiple 返回数组\n        if (nameIndex === 0 && $(firstElement).is('select[multiple]')) {\n            return map($(firstElement).find('option:checked'), (element) => element.value);\n        }\n        // @ts-ignore\n        return firstElement[propName];\n    }\n    function set(element, value) {\n        // text() 和 html() 赋值为 undefined，则保持原内容不变\n        // val() 赋值为 undefined 则赋值为空\n        if (isUndefined(value)) {\n            if (nameIndex !== 0) {\n                return;\n            }\n            value = '';\n        }\n        if (nameIndex === 1 && isElement(value)) {\n            value = value.outerHTML;\n        }\n        // @ts-ignore\n        element[propName] = value;\n    }\n    $.fn[name] = function (value) {\n        // 获取值\n        if (!arguments.length) {\n            return get(this);\n        }\n        // 设置值\n        return this.each((i, element) => {\n            const computedValue = isFunction(value)\n                ? value.call(element, i, get($(element)))\n                : value;\n            // value 是数组，则选中数组中的元素，反选不在数组中的元素\n            if (nameIndex === 0 && Array.isArray(computedValue)) {\n                // select[multiple]\n                if ($(element).is('select[multiple]')) {\n                    map($(element).find('option'), (option) => (option.selected =\n                        computedValue.indexOf(option.value) >\n                            -1));\n                }\n                // 其他 checkbox, radio 等元素\n                else {\n                    element.checked =\n                        computedValue.indexOf(element.value) > -1;\n                }\n            }\n            else {\n                set(element, computedValue);\n            }\n        });\n    };\n});\n", "import $ from '../$';\nimport { isString } from '../utils';\nimport './children';\nimport './eq';\nimport './get';\nimport './parent';\n$.fn.index = function (selector) {\n    if (!arguments.length) {\n        return this.eq(0).parent().children().get().indexOf(this[0]);\n    }\n    if (isString(selector)) {\n        return $(selector).get().indexOf(this[0]);\n    }\n    return this.get().indexOf($(selector)[0]);\n};\n", "import $ from '../$';\nimport './eq';\n$.fn.last = function () {\n    return this.eq(-1);\n};\n", "import $ from '../$';\nimport each from '../functions/each';\nimport dir from './utils/dir';\neach(['', 'All', 'Until'], (nameIndex, name) => {\n    $.fn[`next${name}`] = function (selector, filter) {\n        return dir(this, nameIndex, 'nextElementSibling', selector, filter);\n    };\n});\n", "import $ from '../$';\nimport './filter';\nimport './map';\n$.fn.not = function (selector) {\n    const $excludes = this.filter(selector);\n    return this.map((_, element) => $excludes.index(element) > -1 ? undefined : element);\n};\n", "import $ from '../$';\nimport './css';\nimport './map';\n/**\n * 返回最近的用于定位的父元素\n */\n$.fn.offsetParent = function () {\n    return this.map(function () {\n        let offsetParent = this.offsetParent;\n        while (offsetParent && $(offsetParent).css('position') === 'static') {\n            offsetParent = offsetParent.offsetParent;\n        }\n        return offsetParent || document.documentElement;\n    });\n};\n", "import $ from '../$';\nimport './css';\nimport './eq';\nimport './offset';\nimport './offsetParent';\nfunction floatStyle($element, name) {\n    return parseFloat($element.css(name));\n}\n$.fn.position = function () {\n    if (!this.length) {\n        return undefined;\n    }\n    const $element = this.eq(0);\n    let currentOffset;\n    let parentOffset = {\n        left: 0,\n        top: 0,\n    };\n    if ($element.css('position') === 'fixed') {\n        currentOffset = $element[0].getBoundingClientRect();\n    }\n    else {\n        currentOffset = $element.offset();\n        const $offsetParent = $element.offsetParent();\n        parentOffset = $offsetParent.offset();\n        parentOffset.top += floatStyle($offsetParent, 'border-top-width');\n        parentOffset.left += floatStyle($offsetParent, 'border-left-width');\n    }\n    return {\n        top: currentOffset.top - parentOffset.top - floatStyle($element, 'margin-top'),\n        left: currentOffset.left -\n            parentOffset.left -\n            floatStyle($element, 'margin-left'),\n    };\n};\n", "import $ from '../$';\nimport extend from '../functions/extend';\nimport { isFunction } from '../utils';\nimport './css';\nimport './each';\nimport './position';\nfunction get(element) {\n    if (!element.getClientRects().length) {\n        return { top: 0, left: 0 };\n    }\n    const rect = element.getBoundingClientRect();\n    const win = element.ownerDocument.defaultView;\n    return {\n        top: rect.top + win.pageYOffset,\n        left: rect.left + win.pageXOffset,\n    };\n}\nfunction set(element, value, index) {\n    const $element = $(element);\n    const position = $element.css('position');\n    if (position === 'static') {\n        $element.css('position', 'relative');\n    }\n    const currentOffset = get(element);\n    const currentTopString = $element.css('top');\n    const currentLeftString = $element.css('left');\n    let currentTop;\n    let currentLeft;\n    const calculatePosition = (position === 'absolute' || position === 'fixed') &&\n        (currentTopString + currentLeftString).indexOf('auto') > -1;\n    if (calculatePosition) {\n        const currentPosition = $element.position();\n        currentTop = currentPosition.top;\n        currentLeft = currentPosition.left;\n    }\n    else {\n        currentTop = parseFloat(currentTopString);\n        currentLeft = parseFloat(currentLeftString);\n    }\n    const computedValue = isFunction(value)\n        ? value.call(element, index, extend({}, currentOffset))\n        : value;\n    $element.css({\n        top: computedValue.top != null\n            ? computedValue.top - currentOffset.top + currentTop\n            : undefined,\n        left: computedValue.left != null\n            ? computedValue.left - currentOffset.left + currentLeft\n            : undefined,\n    });\n}\n$.fn.offset = function (value) {\n    // 获取坐标\n    if (!arguments.length) {\n        if (!this.length) {\n            return undefined;\n        }\n        return get(this[0]);\n    }\n    // 设置坐标\n    return this.each(function (index) {\n        set(this, value, index);\n    });\n};\n", "import $ from '../$';\nimport './on';\n$.fn.one = function (types, selector, data, callback) {\n    // @ts-ignore\n    return this.on(types, selector, data, callback, true);\n};\n", "import $ from '../$';\nimport each from '../functions/each';\nimport './get';\nimport dir from './utils/dir';\neach(['', 'All', 'Until'], (nameIndex, name) => {\n    $.fn[`prev${name}`] = function (selector, filter) {\n        // prevAll、prevUntil 需要把元素的顺序倒序处理，以便和 jQuery 的结果一致\n        const $nodes = !nameIndex ? this : $(this.get().reverse());\n        return dir($nodes, nameIndex, 'previousElementSibling', selector, filter);\n    };\n});\n", "import $ from '../$';\nimport each from '../functions/each';\nimport './each';\n$.fn.removeAttr = function (attributeName) {\n    const names = attributeName.split(' ').filter((name) => name);\n    return this.each(function () {\n        each(names, (_, name) => {\n            this.removeAttribute(name);\n        });\n    });\n};\n", "import $ from '../$';\nimport removeData from '../functions/removeData';\nimport './each';\n$.fn.removeData = function (name) {\n    return this.each(function () {\n        removeData(this, name);\n    });\n};\n", "import $ from '../$';\nimport './each';\n$.fn.removeProp = function (name) {\n    return this.each(function () {\n        try {\n            // @ts-ignore\n            delete this[name];\n        }\n        catch (e) { }\n    });\n};\n", "import $ from '../$';\nimport './before';\nimport './clone';\nimport './each';\nimport './remove';\nimport { isFunction, isString } from '../utils';\n$.fn.replaceWith = function (newContent) {\n    this.each((index, element) => {\n        let content = newContent;\n        if (isFunction(content)) {\n            content = content.call(element, index, element.innerHTML);\n        }\n        else if (index && !isString(content)) {\n            content = $(content).clone();\n        }\n        $(element).before(content);\n    });\n    return this.remove();\n};\n", "import $ from '../$';\nimport './clone';\nimport './get';\nimport './map';\nimport './replaceWith';\n$.fn.replaceAll = function (target) {\n    return $(target).map((index, element) => {\n        $(element).replaceWith(index ? this.clone() : this);\n        return this.get();\n    });\n};\n", "import $ from '../$';\nimport './each';\nimport './val';\n/**\n * 将表单元素的值组合成键值对数组\n * @returns {Array}\n */\n$.fn.serializeArray = function () {\n    const result = [];\n    this.each((_, element) => {\n        const elements = element instanceof HTMLFormElement ? element.elements : [element];\n        $(elements).each((_, element) => {\n            const $element = $(element);\n            const type = element.type;\n            const nodeName = element.nodeName.toLowerCase();\n            if (nodeName !== 'fieldset' &&\n                element.name &&\n                !element.disabled &&\n                ['input', 'select', 'textarea', 'keygen'].indexOf(nodeName) > -1 &&\n                ['submit', 'button', 'image', 'reset', 'file'].indexOf(type) === -1 &&\n                (['radio', 'checkbox'].indexOf(type) === -1 ||\n                    element.checked)) {\n                const value = $element.val();\n                const valueArr = Array.isArray(value) ? value : [value];\n                valueArr.forEach((value) => {\n                    result.push({\n                        name: element.name,\n                        value,\n                    });\n                });\n            }\n        });\n    });\n    return result;\n};\n", "import $ from '../$';\nimport param from '../functions/param';\nimport './serializeArray';\n$.fn.serialize = function () {\n    return param(this.serializeArray());\n};\n", "import $ from '../$';\nimport { getStyle } from '../utils';\nimport './each';\nconst elementDisplay = {};\n/**\n * 获取元素的初始 display 值，用于 .show() 方法\n * @param nodeName\n */\nfunction defaultDisplay(nodeName) {\n    let element;\n    let display;\n    if (!elementDisplay[nodeName]) {\n        element = document.createElement(nodeName);\n        document.body.appendChild(element);\n        display = getStyle(element, 'display');\n        element.parentNode.removeChild(element);\n        if (display === 'none') {\n            display = 'block';\n        }\n        elementDisplay[nodeName] = display;\n    }\n    return elementDisplay[nodeName];\n}\n/**\n * 显示指定元素\n * @returns {JQ}\n */\n$.fn.show = function () {\n    return this.each(function () {\n        if (this.style.display === 'none') {\n            this.style.display = '';\n        }\n        if (getStyle(this, 'display') === 'none') {\n            this.style.display = defaultDisplay(this.nodeName);\n        }\n    });\n};\n", "import $ from '../$';\nimport './add';\nimport './nextAll';\nimport './prevAll';\n/**\n * 取得同辈元素的集合\n * @param selector {String=}\n * @returns {JQ}\n */\n$.fn.siblings = function (selector) {\n    return this.prevAll(selector).add(this.nextAll(selector));\n};\n", "import $ from '../$';\nimport { getStyle } from '../utils';\nimport './each';\nimport './hide';\nimport './show';\n/**\n * 切换元素的显示状态\n */\n$.fn.toggle = function () {\n    return this.each(function () {\n        getStyle(this, 'display') === 'none' ? $(this).show() : $(this).hide();\n    });\n};\n", "import $ from 'mdui.jq/es/$';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/each';\n\ndeclare module 'mdui.jq/es/JQ' {\n  interface JQ<T = HTMLElement> {\n    /**\n     * 强制重绘当前元素\n     *\n     * @example\n```js\n$('.box').reflow();\n```\n     */\n    reflow(): this;\n  }\n}\n\n$.fn.reflow = function (this: JQ): JQ {\n  return this.each(function () {\n    return this.clientLeft;\n  });\n};\n", "import $ from 'mdui.jq/es/$';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport { isNumber } from 'mdui.jq/es/utils';\nimport 'mdui.jq/es/methods/each';\n\ndeclare module 'mdui.jq/es/JQ' {\n  interface JQ<T = HTMLElement> {\n    /**\n     * 设置当前元素的 transition-duration 属性\n     * @param duration 可以是带单位的值；若不带单位，则自动添加 `ms` 作为单位\n     * @example\n```js\n$('.box').transition('300ms');\n$('.box').transition(300);\n```\n     */\n    transition(duration: string | number): this;\n  }\n}\n\n$.fn.transition = function (this: JQ, duration: string | number): JQ {\n  if (isNumber(duration)) {\n    duration = `${duration}ms`;\n  }\n\n  return this.each(function () {\n    this.style.webkitTransitionDuration = duration as string;\n    this.style.transitionDuration = duration as string;\n  });\n};\n", "import $ from 'mdui.jq/es/$';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport each from 'mdui.jq/es/functions/each';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/off';\n\ndeclare module 'mdui.jq/es/JQ' {\n  interface JQ<T = HTMLElement> {\n    /**\n     * 在当前元素上添加 transitionend 事件回调\n     * @param callback 回调函数的参数为 `transitionend` 事件对象；`this` 指向当前元素\n     * @example\n```js\n$('.box').transitionEnd(function() {\n  alert('.box 元素的 transitionend 事件已触发');\n});\n```\n     */\n    transitionEnd(callback: (this: T, e: Event) => void): this;\n  }\n}\n\n$.fn.transitionEnd = function (\n  this: JQ,\n  callback: (this: HTMLElement, e: Event) => void,\n): JQ {\n  // eslint-disable-next-line @typescript-eslint/no-this-alias\n  const that = this;\n  const events = ['webkitTransitionEnd', 'transitionend'];\n\n  function fireCallback(this: Element | Document | Window, e: Event): void {\n    if (e.target !== this) {\n      return;\n    }\n\n    // @ts-ignore\n    callback.call(this, e);\n\n    each(events, (_, event) => {\n      that.off(event, fireCallback);\n    });\n  }\n\n  each(events, (_, event) => {\n    that.on(event, fireCallback);\n  });\n\n  return this;\n};\n", "import $ from 'mdui.jq/es/$';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/each';\n\ndeclare module 'mdui.jq/es/JQ' {\n  interface JQ<T = HTMLElement> {\n    /**\n     * 设置当前元素的 transform-origin 属性\n     * @param transformOrigin\n     * @example\n```js\n$('.box').transformOrigin('top center');\n```\n     */\n    transformOrigin(transformOrigin: string): this;\n  }\n}\n\n$.fn.transformOrigin = function (this: JQ, transformOrigin: string): JQ {\n  return this.each(function () {\n    this.style.webkitTransformOrigin = transformOrigin;\n    this.style.transformOrigin = transformOrigin;\n  });\n};\n", "import $ from 'mdui.jq/es/$';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/each';\n\ndeclare module 'mdui.jq/es/JQ' {\n  interface JQ<T = HTMLElement> {\n    /**\n     * 设置当前元素的 transform 属性\n     * @param transform\n     * @example\n```js\n$('.box').transform('rotate(90deg)');\n```\n     */\n    transform(transform: string): this;\n  }\n}\n\n$.fn.transform = function (this: JQ, transform: string): JQ {\n  return this.each(function () {\n    this.style.webkitTransform = transform;\n    this.style.transform = transform;\n  });\n};\n", "import PlainObject from 'mdui.jq/es/interfaces/PlainObject';\nimport data from 'mdui.jq/es/functions/data';\n\ntype TYPE_API_INIT = (\n  this: HTMLElement,\n  i: number,\n  element: HTMLElement,\n) => void;\n\n/**\n * CSS 选择器和初始化函数组成的对象\n */\nconst entries: PlainObject<TYPE_API_INIT> = {};\n\n/**\n * 注册并执行初始化函数\n * @param selector CSS 选择器\n * @param apiInit 初始化函数\n * @param i 元素索引\n * @param element 元素\n */\nfunction mutation(\n  selector: string,\n  apiInit: TYPE_API_INIT,\n  i: number,\n  element: HTMLElement,\n): void {\n  let selectors = data(element, '_mdui_mutation');\n\n  if (!selectors) {\n    selectors = [];\n    data(element, '_mdui_mutation', selectors);\n  }\n\n  if (selectors.indexOf(selector) === -1) {\n    selectors.push(selector);\n    apiInit.call(element, i, element);\n  }\n}\n\nexport { TYPE_API_INIT, entries, mutation };\n", "import $ from 'mdui.jq/es/$';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport each from 'mdui.jq/es/functions/each';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/is';\nimport { entries, mutation } from '../../utils/mutation';\n\ndeclare module 'mdui.jq/es/JQ' {\n  interface JQ<T = HTMLElement> {\n    /**\n     * 执行在当前元素及其子元素内注册的初始化函数\n     */\n    mutation(): this;\n  }\n}\n\n$.fn.mutation = function (this: JQ): JQ {\n  return this.each((i, element) => {\n    const $this = $(element);\n\n    each(entries, (selector: string, apiInit) => {\n      if ($this.is(selector)) {\n        mutation(selector, apiInit, i, element);\n      }\n\n      $this.find(selector).each((i, element) => {\n        mutation(selector, apiInit, i, element);\n      });\n    });\n  });\n};\n", "import $ from 'mdui.jq/es/$';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport { isUndefined } from 'mdui.jq/es/utils';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/css';\nimport 'mdui.jq/es/methods/appendTo';\nimport 'mdui.jq/es/methods/addClass';\nimport '../methods/reflow';\n\ndeclare module 'mdui.jq/es/interfaces/JQStatic' {\n  interface JQStatic {\n    /**\n     * 创建并显示遮罩，返回遮罩层的 JQ 对象\n     * @param zIndex 遮罩层的 `z-index` 值，默认为 `2000`\n     * @example\n```js\n$.showOverlay();\n```\n     * @example\n```js\n$.showOverlay(3000);\n```\n     */\n    showOverlay(zIndex?: number): JQ;\n  }\n}\n\n$.showOverlay = function (zIndex?: number): JQ {\n  let $overlay = $('.mdui-overlay');\n\n  if ($overlay.length) {\n    $overlay.data('_overlay_is_deleted', false);\n\n    if (!isUndefined(zIndex)) {\n      $overlay.css('z-index', zIndex);\n    }\n  } else {\n    if (isUndefined(zIndex)) {\n      zIndex = 2000;\n    }\n\n    $overlay = $('<div class=\"mdui-overlay\">')\n      .appendTo(document.body)\n      .reflow()\n      .css('z-index', zIndex);\n  }\n\n  let level = $overlay.data('_overlay_level') || 0;\n\n  return $overlay.data('_overlay_level', ++level).addClass('mdui-overlay-show');\n};\n", "import $ from 'mdui.jq/es/$';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/removeClass';\nimport 'mdui.jq/es/methods/remove';\nimport '../methods/transitionEnd';\n\ndeclare module 'mdui.jq/es/interfaces/JQStatic' {\n  interface JQStatic {\n    /**\n     * 隐藏遮罩层\n     *\n     * 如果调用了多次 $.showOverlay() 来显示遮罩层，则也需要调用相同次数的 $.hideOverlay() 才能隐藏遮罩层。可以通过传入参数 true 来强制隐藏遮罩层。\n     * @param force 是否强制隐藏遮罩\n     * @example\n```js\n$.hideOverlay();\n```\n     * @example\n```js\n$.hideOverlay(true);\n```\n     */\n    hideOverlay(force?: boolean): void;\n  }\n}\n\n$.hideOverlay = function (force = false): void {\n  const $overlay = $('.mdui-overlay');\n\n  if (!$overlay.length) {\n    return;\n  }\n\n  let level = force ? 1 : $overlay.data('_overlay_level');\n\n  if (level > 1) {\n    $overlay.data('_overlay_level', --level);\n    return;\n  }\n\n  $overlay\n    .data('_overlay_level', 0)\n    .removeClass('mdui-overlay-show')\n    .data('_overlay_is_deleted', true)\n    .transitionEnd(() => {\n      if ($overlay.data('_overlay_is_deleted')) {\n        $overlay.remove();\n      }\n    });\n};\n", "import $ from 'mdui.jq/es/$';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/width';\n\ndeclare module 'mdui.jq/es/interfaces/JQStatic' {\n  interface JQStatic {\n    /**\n     * 锁定屏页面，禁止页面滚动\n     * @example\n```js\n$.lockScreen();\n```\n     */\n    lockScreen(): void;\n  }\n}\n\n$.lockScreen = function (): void {\n  const $body = $('body');\n\n  // 不直接把 body 设为 box-sizing: border-box，避免污染全局样式\n  const newBodyWidth = $body.width();\n  let level = $body.data('_lockscreen_level') || 0;\n\n  $body\n    .addClass('mdui-locked')\n    .width(newBodyWidth)\n    .data('_lockscreen_level', ++level);\n};\n", "import $ from 'mdui.jq/es/$';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/removeClass';\nimport 'mdui.jq/es/methods/width';\n\ndeclare module 'mdui.jq/es/interfaces/JQStatic' {\n  interface JQStatic {\n    /**\n     * 解除页面锁定\n     *\n     * 如果调用了多次 $.lockScreen() 来显示遮罩层，则也需要调用相同次数的 $.unlockScreen() 才能隐藏遮罩层。可以通过传入参数 true 来强制隐藏遮罩层。\n     * @param force 是否强制解除锁定\n     * @example\n```js\n$.unlockScreen();\n```\n     * @example\n```js\n$.unlockScreen(true);\n```\n     */\n    unlockScreen(force?: boolean): void;\n  }\n}\n\n$.unlockScreen = function (force = false): void {\n  const $body = $('body');\n  let level = force ? 1 : $body.data('_lockscreen_level');\n\n  if (level > 1) {\n    $body.data('_lockscreen_level', --level);\n    return;\n  }\n\n  $body.data('_lockscreen_level', 0).removeClass('mdui-locked').width('');\n};\n", "import $ from 'mdui.jq/es/$';\nimport { isNull } from 'mdui.jq/es/utils';\n\ndeclare module 'mdui.jq/es/interfaces/JQStatic' {\n  interface JQStatic {\n    /**\n     * 函数节流\n     * @param fn 执行的函数\n     * @param delay 最多多少毫秒执行一次\n     * @example\n```js\n$.throttle(function () {\n  console.log('这个函数最多 100ms 执行一次');\n}, 100)\n```\n     */\n    throttle(fn: () => void, delay: number): () => void;\n  }\n}\n\n$.throttle = function (fn: () => void, delay = 16): () => void {\n  let timer: any = null;\n\n  return function (this: any, ...args: any): void {\n    if (isNull(timer)) {\n      timer = setTimeout(() => {\n        fn.apply(this, args);\n        timer = null;\n      }, delay);\n    }\n  };\n};\n", "import $ from 'mdui.jq/es/$';\nimport { isUndefined } from 'mdui.jq/es/utils';\nimport PlainObject from 'mdui.jq/es/interfaces/PlainObject';\n\ndeclare module 'mdui.jq/es/interfaces/JQStatic' {\n  interface JQStatic {\n    /**\n     * 生成一个全局唯一的 ID\n     * @param name 当该参数值对应的 guid 不存在时，会生成一个新的 guid，并返回；当该参数对应的 guid 已存在，则直接返回已有 guid\n     * @example\n```js\n$.guid();\n```\n     * @example\n```js\n$.guid('test');\n```\n     */\n    guid(name?: string): string;\n  }\n}\n\nconst GUID: PlainObject<string> = {};\n\n$.guid = function (name?: string): string {\n  if (!isUndefined(name) && !isUndefined(GUID[name])) {\n    return GUID[name];\n  }\n\n  function s4(): string {\n    return Math.floor((1 + Math.random()) * 0x10000)\n      .toString(16)\n      .substring(1);\n  }\n\n  const guid =\n    '_' +\n    s4() +\n    s4() +\n    '-' +\n    s4() +\n    '-' +\n    s4() +\n    '-' +\n    s4() +\n    '-' +\n    s4() +\n    s4() +\n    s4();\n\n  if (!isUndefined(name)) {\n    GUID[name] = guid;\n  }\n\n  return guid;\n};\n", "import $ from 'mdui.jq/es/$';\nimport 'mdui.jq/es/methods/each';\nimport mdui from '../mdui';\nimport '../jq_extends/methods/mutation';\nimport { isUndefined } from 'mdui.jq/es/utils';\nimport { TYPE_API_INIT, entries, mutation } from '../utils/mutation';\n\ndeclare module '../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 传入了两个参数时，注册并执行初始化函数\n     *\n     * 没有传入参数时，执行初始化\n     * @param selector CSS 选择器\n     * @param apiInit 初始化函数\n     * @example\n```js\nmdui.mutation();\n```\n     * @example\n```js\nmdui.mutation();\n```\n     */\n    mutation(selector?: string, apiInit?: TYPE_API_INIT): void;\n  }\n}\n\nmdui.mutation = function (selector?: string, apiInit?: TYPE_API_INIT): void {\n  if (isUndefined(selector) || isUndefined(apiInit)) {\n    $(document).mutation();\n    return;\n  }\n\n  entries[selector] = apiInit!;\n  $(selector).each((i, element) => mutation(selector, apiInit, i, element));\n};\n", "import $ from 'mdui.jq/es/$';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport PlainObject from 'mdui.jq/es/interfaces/PlainObject';\nimport 'mdui.jq/es/methods/trigger';\n\n/**\n * 触发组件上的事件\n * @param eventName 事件名\n * @param componentName 组件名\n * @param target 在该元素上触发事件\n * @param instance 组件实例\n * @param parameters 事件参数\n */\nfunction componentEvent(\n  eventName: string,\n  componentName: string,\n  target: HTMLElement | HTMLElement[] | JQ,\n  instance?: any,\n  parameters?: PlainObject,\n): void {\n  if (!parameters) {\n    parameters = {};\n  }\n\n  // @ts-ignore\n  parameters.inst = instance;\n\n  const fullEventName = `${eventName}.mdui.${componentName}`;\n\n  // jQuery 事件\n  // @ts-ignore\n  if (typeof jQuery !== 'undefined') {\n    // @ts-ignore\n    jQuery(target).trigger(fullEventName, parameters);\n  }\n\n  const $target = $(target);\n\n  // mdui.jq 事件\n  $target.trigger(fullEventName, parameters);\n\n  // 原生事件，供使用 addEventListener 监听\n  type EventParams = {\n    detail?: any;\n    bubbles: boolean;\n    cancelable: boolean;\n  };\n\n  const eventParams: EventParams = {\n    bubbles: true,\n    cancelable: true,\n    detail: parameters,\n  };\n\n  const eventObject: CustomEvent = new CustomEvent(fullEventName, eventParams);\n\n  // @ts-ignore\n  eventObject._detail = parameters;\n\n  $target[0].dispatchEvent(eventObject);\n}\n\nexport { componentEvent };\n", "import $ from 'mdui.jq/es/$';\n\nconst $document = $(document);\nconst $window = $(window);\nconst $body = $('body');\n\nexport { $document, $window, $body };\n", "import $ from 'mdui.jq/es/$';\nimport extend from 'mdui.jq/es/functions/extend';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/removeClass';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport { isNumber } from 'mdui.jq/es/utils';\nimport mdui from '../../mdui';\nimport '../../jq_extends/methods/transitionEnd';\nimport { componentEvent } from '../../utils/componentEvent';\nimport { $window } from '../../utils/dom';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * Headroom 插件\n     *\n     * 请通过 `new mdui.Headroom()` 调用\n     */\n    Headroom: {\n      /**\n       * 实例化 Headroom 组件\n       * @param selector CSS 选择器、或 DOM 元素、或 JQ 对象\n       * @param options 配置参数\n       */\n      new (\n        selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n        options?: OPTIONS,\n      ): Headroom;\n    };\n  }\n}\n\ntype TOLERANCE = {\n  /**\n   * 滚动条向下滚动多少距离开始隐藏或显示元素\n   */\n  down: number;\n\n  /**\n   * 滚动条向上滚动多少距离开始隐藏或显示元素\n   */\n  up: number;\n};\n\ntype OPTIONS = {\n  /**\n   * 滚动条滚动多少距离开始隐藏或显示元素\n   */\n  tolerance?: TOLERANCE | number;\n\n  /**\n   * 在页面顶部多少距离内滚动不会隐藏元素\n   */\n  offset?: number;\n\n  /**\n   * 初始化时添加的类\n   */\n  initialClass?: string;\n\n  /**\n   * 元素固定时添加的类\n   */\n  pinnedClass?: string;\n\n  /**\n   * 元素隐藏时添加的类\n   */\n  unpinnedClass?: string;\n};\n\ntype STATE = 'pinning' | 'pinned' | 'unpinning' | 'unpinned';\ntype EVENT = 'pin' | 'pinned' | 'unpin' | 'unpinned';\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  tolerance: 5,\n  offset: 0,\n  initialClass: 'mdui-headroom',\n  pinnedClass: 'mdui-headroom-pinned-top',\n  unpinnedClass: 'mdui-headroom-unpinned-top',\n};\n\nclass Headroom {\n  /**\n   * headroom 元素的 JQ 对象\n   */\n  public $element: JQ;\n\n  /**\n   * 配置参数\n   */\n  public options: OPTIONS = extend({}, DEFAULT_OPTIONS);\n\n  /**\n   * 当前 headroom 的状态\n   */\n  private state: STATE = 'pinned';\n\n  /**\n   * 当前是否启用\n   */\n  private isEnable = false;\n\n  /**\n   * 上次滚动后，垂直方向的距离\n   */\n  private lastScrollY = 0;\n\n  /**\n   * AnimationFrame ID\n   */\n  private rafId = 0;\n\n  public constructor(\n    selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    options: OPTIONS = {},\n  ) {\n    this.$element = $(selector).first();\n\n    extend(this.options, options);\n\n    // tolerance 参数若为数值，转换为对象\n    const tolerance = this.options.tolerance;\n    if (isNumber(tolerance)) {\n      this.options.tolerance = {\n        down: tolerance,\n        up: tolerance,\n      };\n    }\n\n    this.enable();\n  }\n\n  /**\n   * 滚动时的处理\n   */\n  private onScroll(): void {\n    this.rafId = window.requestAnimationFrame(() => {\n      const currentScrollY = window.pageYOffset;\n      const direction = currentScrollY > this.lastScrollY ? 'down' : 'up';\n      const tolerance = (this.options.tolerance as TOLERANCE)[direction];\n      const scrolled = Math.abs(currentScrollY - this.lastScrollY);\n      const toleranceExceeded = scrolled >= tolerance;\n\n      if (\n        currentScrollY > this.lastScrollY &&\n        currentScrollY >= this.options.offset! &&\n        toleranceExceeded\n      ) {\n        this.unpin();\n      } else if (\n        (currentScrollY < this.lastScrollY && toleranceExceeded) ||\n        currentScrollY <= this.options.offset!\n      ) {\n        this.pin();\n      }\n\n      this.lastScrollY = currentScrollY;\n    });\n  }\n\n  /**\n   * 触发组件事件\n   * @param name\n   */\n  private triggerEvent(name: EVENT): void {\n    componentEvent(name, 'headroom', this.$element, this);\n  }\n\n  /**\n   * 动画结束的回调\n   */\n  private transitionEnd(): void {\n    if (this.state === 'pinning') {\n      this.state = 'pinned';\n      this.triggerEvent('pinned');\n    }\n\n    if (this.state === 'unpinning') {\n      this.state = 'unpinned';\n      this.triggerEvent('unpinned');\n    }\n  }\n\n  /**\n   * 使元素固定住\n   */\n  public pin(): void {\n    if (\n      this.state === 'pinning' ||\n      this.state === 'pinned' ||\n      !this.$element.hasClass(this.options.initialClass!)\n    ) {\n      return;\n    }\n\n    this.triggerEvent('pin');\n    this.state = 'pinning';\n    this.$element\n      .removeClass(this.options.unpinnedClass)\n      .addClass(this.options.pinnedClass!)\n      .transitionEnd(() => this.transitionEnd());\n  }\n\n  /**\n   * 使元素隐藏\n   */\n  public unpin(): void {\n    if (\n      this.state === 'unpinning' ||\n      this.state === 'unpinned' ||\n      !this.$element.hasClass(this.options.initialClass!)\n    ) {\n      return;\n    }\n\n    this.triggerEvent('unpin');\n    this.state = 'unpinning';\n    this.$element\n      .removeClass(this.options.pinnedClass)\n      .addClass(this.options.unpinnedClass!)\n      .transitionEnd(() => this.transitionEnd());\n  }\n\n  /**\n   * 启用 headroom 插件\n   */\n  public enable(): void {\n    if (this.isEnable) {\n      return;\n    }\n\n    this.isEnable = true;\n    this.state = 'pinned';\n    this.$element\n      .addClass(this.options.initialClass!)\n      .removeClass(this.options.pinnedClass)\n      .removeClass(this.options.unpinnedClass);\n    this.lastScrollY = window.pageYOffset;\n\n    $window.on('scroll', () => this.onScroll());\n  }\n\n  /**\n   * 禁用 headroom 插件\n   */\n  public disable(): void {\n    if (!this.isEnable) {\n      return;\n    }\n\n    this.isEnable = false;\n    this.$element\n      .removeClass(this.options.initialClass)\n      .removeClass(this.options.pinnedClass)\n      .removeClass(this.options.unpinnedClass);\n\n    $window.off('scroll', () => this.onScroll());\n    window.cancelAnimationFrame(this.rafId);\n  }\n\n  /**\n   * 获取当前状态。共包含四种状态：`pinning`、`pinned`、`unpinning`、`unpinned`\n   */\n  public getState(): STATE {\n    return this.state;\n  }\n}\n\nmdui.Headroom = Headroom;\n", "import $ from 'mdui.jq/es/$';\nimport 'mdui.jq/es/methods/attr';\nimport PlainObject from 'mdui.jq/es/interfaces/PlainObject';\n\n/**\n * 解析 DATA API 参数\n * @param element 元素\n * @param name 属性名\n */\nfunction parseOptions(element: HTMLElement, name: string): PlainObject {\n  const attr = $(element).attr(name);\n\n  if (!attr) {\n    return {};\n  }\n\n  return new Function(\n    '',\n    `var json = ${attr}; return JSON.parse(JSON.stringify(json));`,\n  )();\n}\n\nexport { parseOptions };\n", "import $ from 'mdui.jq/es/$';\nimport mdui from '../../mdui';\nimport '../../global/mutation';\nimport { parseOptions } from '../../utils/parseOptions';\nimport './index';\n\nconst customAttr = 'mdui-headroom';\n\n$(() => {\n  mdui.mutation(`[${customAttr}]`, function () {\n    new mdui.Headroom(this, parseOptions(this, customAttr));\n  });\n});\n", "import $ from 'mdui.jq/es/$';\nimport extend from 'mdui.jq/es/functions/extend';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/children';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/eq';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/height';\nimport 'mdui.jq/es/methods/is';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/parent';\nimport 'mdui.jq/es/methods/parents';\nimport 'mdui.jq/es/methods/removeClass';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport { isNumber } from 'mdui.jq/es/utils';\nimport '../../jq_extends/methods/reflow';\nimport '../../jq_extends/methods/transition';\nimport '../../jq_extends/methods/transitionEnd';\nimport { componentEvent } from '../../utils/componentEvent';\n\ntype OPTIONS = {\n  /**\n   * 是否启用手风琴效果\n   * 为 `true` 时，最多只能有一个面板项处于打开状态，打开一个面板项时会关闭其他面板项\n   * 为 `false` 时，可同时打开多个面板项\n   */\n  accordion?: boolean;\n};\n\ntype EVENT = 'open' | 'opened' | 'close' | 'closed';\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  accordion: false,\n};\n\nabstract class CollapseAbstract {\n  /**\n   * collapse 元素的 JQ 对象\n   */\n  public $element: JQ;\n\n  /**\n   * 配置参数\n   */\n  public options: OPTIONS = extend({}, DEFAULT_OPTIONS);\n\n  /**\n   * item 的 class 名\n   */\n  private classItem: string;\n\n  /**\n   * 打开状态的 item 的 class 名\n   */\n  private classItemOpen: string;\n\n  /**\n   * item-header 的 class 名\n   */\n  private classHeader: string;\n\n  /**\n   * item-body 的 class 名\n   */\n  private classBody: string;\n\n  /**\n   * 获取继承的组件名称\n   */\n  protected abstract getNamespace(): string;\n\n  public constructor(\n    selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    options: OPTIONS = {},\n  ) {\n    // CSS 类名\n    const classPrefix = `mdui-${this.getNamespace()}-item`;\n    this.classItem = classPrefix;\n    this.classItemOpen = `${classPrefix}-open`;\n    this.classHeader = `${classPrefix}-header`;\n    this.classBody = `${classPrefix}-body`;\n\n    this.$element = $(selector).first();\n\n    extend(this.options, options);\n\n    this.bindEvent();\n  }\n\n  /**\n   * 绑定事件\n   */\n  private bindEvent(): void {\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const that = this;\n\n    // 点击 header 时，打开/关闭 item\n    this.$element.on('click', `.${this.classHeader}`, function () {\n      const $header = $(this as HTMLElement);\n      const $item = $header.parent();\n      const $items = that.getItems();\n\n      $items.each((_, item) => {\n        if ($item.is(item)) {\n          that.toggle(item);\n        }\n      });\n    });\n\n    // 点击关闭按钮时，关闭 item\n    this.$element.on(\n      'click',\n      `[mdui-${this.getNamespace()}-item-close]`,\n      function () {\n        const $target = $(this as HTMLElement);\n        const $item = $target.parents(`.${that.classItem}`).first();\n\n        that.close($item);\n      },\n    );\n  }\n\n  /**\n   * 指定 item 是否处于打开状态\n   * @param $item\n   */\n  private isOpen($item: JQ): boolean {\n    return $item.hasClass(this.classItemOpen);\n  }\n\n  /**\n   * 获取所有 item\n   */\n  private getItems(): JQ {\n    return this.$element.children(`.${this.classItem}`);\n  }\n\n  /**\n   * 获取指定 item\n   * @param item\n   */\n  private getItem(\n    item: number | Selector | HTMLElement | ArrayLike<HTMLElement>,\n  ): JQ {\n    if (isNumber(item)) {\n      return this.getItems().eq(item);\n    }\n\n    return $(item).first();\n  }\n\n  /**\n   * 触发组件事件\n   * @param name 事件名\n   * @param $item 事件触发的目标 item\n   */\n  private triggerEvent(name: EVENT, $item: JQ): void {\n    componentEvent(name, this.getNamespace(), $item, this);\n  }\n\n  /**\n   * 动画结束回调\n   * @param $content body 元素\n   * @param $item item 元素\n   */\n  private transitionEnd($content: JQ, $item: JQ): void {\n    if (this.isOpen($item)) {\n      $content.transition(0).height('auto').reflow().transition('');\n\n      this.triggerEvent('opened', $item);\n    } else {\n      $content.height('');\n\n      this.triggerEvent('closed', $item);\n    }\n  }\n\n  /**\n   * 打开指定面板项\n   * @param item 面板项的索引号、或 CSS 选择器、或 DOM 元素、或 JQ 对象\n   */\n  public open(\n    item: number | Selector | HTMLElement | ArrayLike<HTMLElement>,\n  ): void {\n    const $item = this.getItem(item);\n\n    if (this.isOpen($item)) {\n      return;\n    }\n\n    // 关闭其他项\n    if (this.options.accordion) {\n      this.$element.children(`.${this.classItemOpen}`).each((_, element) => {\n        const $element = $(element);\n\n        if (!$element.is($item)) {\n          this.close($element);\n        }\n      });\n    }\n\n    const $content = $item.children(`.${this.classBody}`);\n\n    $content\n      .height($content[0].scrollHeight)\n      .transitionEnd(() => this.transitionEnd($content, $item));\n\n    this.triggerEvent('open', $item);\n\n    $item.addClass(this.classItemOpen);\n  }\n\n  /**\n   * 关闭指定面板项\n   * @param item 面板项的索引号、或 CSS 选择器、或 DOM 元素、或 JQ 对象\n   */\n  public close(\n    item: number | Selector | HTMLElement | ArrayLike<HTMLElement>,\n  ): void {\n    const $item = this.getItem(item);\n\n    if (!this.isOpen($item)) {\n      return;\n    }\n\n    const $content = $item.children(`.${this.classBody}`);\n\n    this.triggerEvent('close', $item);\n\n    $item.removeClass(this.classItemOpen);\n\n    $content\n      .transition(0)\n      .height($content[0].scrollHeight)\n      .reflow()\n      .transition('')\n      .height('')\n      .transitionEnd(() => this.transitionEnd($content, $item));\n  }\n\n  /**\n   * 切换指定面板项的打开状态\n   * @param item 面板项的索引号、或 CSS 选择器、或 DOM 元素、或 JQ 对象\n   */\n  public toggle(\n    item: number | Selector | HTMLElement | ArrayLike<HTMLElement>,\n  ): void {\n    const $item = this.getItem(item);\n\n    this.isOpen($item) ? this.close($item) : this.open($item);\n  }\n\n  /**\n   * 打开所有面板项\n   */\n  public openAll(): void {\n    this.getItems().each((_, element) => this.open(element));\n  }\n\n  /**\n   * 关闭所有面板项\n   */\n  public closeAll(): void {\n    this.getItems().each((_, element) => this.close(element));\n  }\n}\n\nexport { OPTIONS, CollapseAbstract };\n", "import Selector from 'mdui.jq/es/types/Selector';\nimport mdui from '../../mdui';\nimport { CollapseAbstract, OPTIONS } from './collapseAbstract';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 折叠内容块组件\n     *\n     * 请通过 `new mdui.Collapse()` 调用\n     */\n    Collapse: {\n      /**\n       * 实例化 Collapse 组件\n       * @param selector CSS 选择器或 DOM 元素\n       * @param options 配置参数\n       */\n      new (\n        selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n        options?: OPTIONS,\n      ): Collapse;\n    };\n  }\n}\n\nclass Collapse extends CollapseAbstract {\n  protected getNamespace(): string {\n    return 'collapse';\n  }\n}\n\nmdui.Collapse = Collapse;\n", "import $ from 'mdui.jq/es/$';\nimport mdui from '../../mdui';\nimport '../../global/mutation';\nimport { parseOptions } from '../../utils/parseOptions';\nimport './index';\n\nconst customAttr = 'mdui-collapse';\n\n$(() => {\n  mdui.mutation(`[${customAttr}]`, function () {\n    new mdui.Collapse(this, parseOptions(this, customAttr));\n  });\n});\n", "import Selector from 'mdui.jq/es/types/Selector';\nimport mdui from '../../mdui';\nimport { CollapseAbstract, OPTIONS } from '../collapse/collapseAbstract';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 可扩展面板组件\n     *\n     * 请通过 `new mdui.Panel()` 调用\n     */\n    Panel: {\n      /**\n       * 实例化 Panel 组件\n       * @param selector CSS 选择器或 DOM 元素\n       * @param options 配置参数\n       */\n      new (\n        selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n        options?: OPTIONS,\n      ): Panel;\n    };\n  }\n}\n\nclass Panel extends CollapseAbstract {\n  protected getNamespace(): string {\n    return 'panel';\n  }\n}\n\nmdui.Panel = Panel;\n", "import $ from 'mdui.jq/es/$';\nimport mdui from '../../mdui';\nimport '../../global/mutation';\nimport { parseOptions } from '../../utils/parseOptions';\nimport './index';\n\nconst customAttr = 'mdui-panel';\n\n$(() => {\n  mdui.mutation(`[${customAttr}]`, function () {\n    new mdui.Panel(this, parseOptions(this, customAttr));\n  });\n});\n", "import $ from 'mdui.jq/es/$';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/add';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/eq';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/prependTo';\nimport 'mdui.jq/es/methods/remove';\nimport 'mdui.jq/es/methods/removeClass';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport { isUndefined } from 'mdui.jq/es/utils';\nimport mdui from '../../mdui';\nimport '../../global/mutation';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 动态修改了表格后，需要调用该方法重新初始化表格。\n     *\n     * 若传入了参数，则只初始化该参数对应的表格。若没有传入参数，则重新初始化所有表格。\n     * @param selector CSS 选择器、或 DOM 元素、或 DOM 元素组成的数组、或 JQ 对象\n     */\n    updateTables(\n      selector?: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    ): void;\n  }\n}\n\nclass Table {\n  /**\n   * table 元素的 JQ 对象\n   */\n  public $element: JQ;\n\n  /**\n   * 表头 tr 元素\n   */\n  private $thRow: JQ = $();\n\n  /**\n   * 表格 body 中的 tr 元素\n   */\n  private $tdRows: JQ = $();\n\n  /**\n   * 表头的 checkbox 元素\n   */\n  private $thCheckbox: JQ<HTMLInputElement> = $();\n\n  /**\n   * 表格 body 中的 checkbox 元素\n   */\n  private $tdCheckboxs: JQ<HTMLInputElement> = $();\n\n  /**\n   * 表格行是否可选择\n   */\n  private selectable = false;\n\n  /**\n   * 已选中的行数\n   */\n  private selectedRow = 0;\n\n  public constructor(\n    selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n  ) {\n    this.$element = $(selector).first();\n    this.init();\n  }\n\n  /**\n   * 初始化表格\n   */\n  public init(): void {\n    this.$thRow = this.$element.find('thead tr');\n    this.$tdRows = this.$element.find('tbody tr');\n    this.selectable = this.$element.hasClass('mdui-table-selectable');\n\n    this.updateThCheckbox();\n    this.updateTdCheckbox();\n    this.updateNumericCol();\n  }\n\n  /**\n   * 生成 checkbox 的 HTML 结构\n   * @param tag 标签名\n   */\n  private createCheckboxHTML(tag: string): string {\n    return (\n      `<${tag} class=\"mdui-table-cell-checkbox\">` +\n      '<label class=\"mdui-checkbox\">' +\n      '<input type=\"checkbox\"/>' +\n      '<i class=\"mdui-checkbox-icon\"></i>' +\n      '</label>' +\n      `</${tag}>`\n    );\n  }\n\n  /**\n   * 更新表头 checkbox 的状态\n   */\n  private updateThCheckboxStatus(): void {\n    const checkbox = this.$thCheckbox[0];\n    const selectedRow = this.selectedRow;\n    const tdRowsLength = this.$tdRows.length;\n\n    checkbox.checked = selectedRow === tdRowsLength;\n    checkbox.indeterminate = !!selectedRow && selectedRow !== tdRowsLength;\n  }\n\n  /**\n   * 更新表格行的 checkbox\n   */\n  private updateTdCheckbox(): void {\n    const rowSelectedClass = 'mdui-table-row-selected';\n\n    this.$tdRows.each((_, row) => {\n      const $row = $(row);\n\n      // 移除旧的 checkbox\n      $row.find('.mdui-table-cell-checkbox').remove();\n\n      if (!this.selectable) {\n        return;\n      }\n\n      // 创建 DOM\n      const $checkbox = $(this.createCheckboxHTML('td'))\n        .prependTo($row)\n        .find('input[type=\"checkbox\"]') as JQ<HTMLInputElement>;\n\n      // 默认选中的行\n      if ($row.hasClass(rowSelectedClass)) {\n        $checkbox[0].checked = true;\n        this.selectedRow++;\n      }\n\n      this.updateThCheckboxStatus();\n\n      // 绑定事件\n      $checkbox.on('change', () => {\n        if ($checkbox[0].checked) {\n          $row.addClass(rowSelectedClass);\n          this.selectedRow++;\n        } else {\n          $row.removeClass(rowSelectedClass);\n          this.selectedRow--;\n        }\n\n        this.updateThCheckboxStatus();\n      });\n\n      this.$tdCheckboxs = this.$tdCheckboxs.add($checkbox);\n    });\n  }\n\n  /**\n   * 更新表头的 checkbox\n   */\n  private updateThCheckbox(): void {\n    // 移除旧的 checkbox\n    this.$thRow.find('.mdui-table-cell-checkbox').remove();\n\n    if (!this.selectable) {\n      return;\n    }\n\n    this.$thCheckbox = $(this.createCheckboxHTML('th'))\n      .prependTo(this.$thRow)\n      .find('input[type=\"checkbox\"]')\n      .on('change', () => {\n        const isCheckedAll = this.$thCheckbox[0].checked;\n        this.selectedRow = isCheckedAll ? this.$tdRows.length : 0;\n\n        this.$tdCheckboxs.each((_, checkbox) => {\n          checkbox.checked = isCheckedAll;\n        });\n\n        this.$tdRows.each((_, row) => {\n          isCheckedAll\n            ? $(row).addClass('mdui-table-row-selected')\n            : $(row).removeClass('mdui-table-row-selected');\n        });\n      }) as JQ<HTMLInputElement>;\n  }\n\n  /**\n   * 更新数值列\n   */\n  private updateNumericCol(): void {\n    const numericClass = 'mdui-table-col-numeric';\n\n    this.$thRow.find('th').each((i, th) => {\n      const isNumericCol = $(th).hasClass(numericClass);\n\n      this.$tdRows.each((_, row) => {\n        const $td = $(row).find('td').eq(i);\n\n        isNumericCol\n          ? $td.addClass(numericClass)\n          : $td.removeClass(numericClass);\n      });\n    });\n  }\n}\n\nconst dataName = '_mdui_table';\n\n$(() => {\n  mdui.mutation('.mdui-table', function () {\n    const $element = $(this);\n\n    if (!$element.data(dataName)) {\n      $element.data(dataName, new Table($element));\n    }\n  });\n});\n\nmdui.updateTables = function (\n  selector?: Selector | HTMLElement | ArrayLike<HTMLElement>,\n): void {\n  const $elements = isUndefined(selector) ? $('.mdui-table') : $(selector);\n\n  $elements.each((_, element) => {\n    const $element = $(element);\n    const instance = $element.data(dataName);\n\n    if (instance) {\n      instance.init();\n    } else {\n      $element.data(dataName, new Table($element));\n    }\n  });\n};\n", "/**\n * touch 事件后的 500ms 内禁用 mousedown 事件\n *\n * 不支持触控的屏幕上事件顺序为 mousedown -> mouseup -> click\n * 支持触控的屏幕上事件顺序为 touchstart -> touchend -> mousedown -> mouseup -> click\n *\n * 在每一个事件中都使用 TouchHandler.isAllow(event) 判断事件是否可执行\n * 在 touchstart 和 touchmove、touchend、touchcancel\n *\n * (function () {\n *   $document\n *     .on(start, function (e) {\n *       if (!isAllow(e)) {\n *         return;\n *       }\n *       register(e);\n *       console.log(e.type);\n *     })\n *     .on(move, function (e) {\n *       if (!isAllow(e)) {\n *         return;\n *       }\n *       console.log(e.type);\n *     })\n *     .on(end, function (e) {\n *       if (!isAllow(e)) {\n *         return;\n *       }\n *       console.log(e.type);\n *     })\n *     .on(unlock, register);\n * })();\n */\n\nconst startEvent = 'touchstart mousedown';\nconst moveEvent = 'touchmove mousemove';\nconst endEvent = 'touchend mouseup';\nconst cancelEvent = 'touchcancel mouseleave';\nconst unlockEvent = 'touchend touchmove touchcancel';\n\nlet touches = 0;\n\n/**\n * 该事件是否被允许，在执行事件前调用该方法判断事件是否可以执行\n * 若已触发 touch 事件，则阻止之后的鼠标事件\n * @param event\n */\nfunction isAllow(event: Event): boolean {\n  return !(\n    touches &&\n    [\n      'mousedown',\n      'mouseup',\n      'mousemove',\n      'click',\n      'mouseover',\n      'mouseout',\n      'mouseenter',\n      'mouseleave',\n    ].indexOf(event.type) > -1\n  );\n}\n\n/**\n * 在 touchstart 和 touchmove、touchend、touchcancel 事件中调用该方法注册事件\n * @param event\n */\nfunction register(event: Event): void {\n  if (event.type === 'touchstart') {\n    // 触发了 touch 事件\n    touches += 1;\n  } else if (\n    ['touchmove', 'touchend', 'touchcancel'].indexOf(event.type) > -1\n  ) {\n    // touch 事件结束 500ms 后解除对鼠标事件的阻止\n    setTimeout(function () {\n      if (touches) {\n        touches -= 1;\n      }\n    }, 500);\n  }\n}\n\nexport {\n  startEvent,\n  moveEvent,\n  endEvent,\n  cancelEvent,\n  unlockEvent,\n  isAllow,\n  register,\n};\n", "/**\n * Inspired by https://github.com/nolimits4web/Framework7/blob/master/src/js/fast-clicks.js\n * https://github.com/nolimits4web/Framework7/blob/master/LICENSE\n *\n * Inspired by https://github.com/fians/Waves\n */\n\nimport $ from 'mdui.jq/es/$';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/attr';\nimport 'mdui.jq/es/methods/children';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/innerHeight';\nimport 'mdui.jq/es/methods/innerWidth';\nimport 'mdui.jq/es/methods/off';\nimport 'mdui.jq/es/methods/offset';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/parents';\nimport 'mdui.jq/es/methods/prependTo';\nimport 'mdui.jq/es/methods/prop';\nimport 'mdui.jq/es/methods/remove';\nimport { isUndefined } from 'mdui.jq/es/utils';\nimport '../../jq_extends/methods/reflow';\nimport '../../jq_extends/methods/transform';\nimport '../../jq_extends/methods/transitionEnd';\nimport { $document } from '../../utils/dom';\nimport {\n  cancelEvent,\n  endEvent,\n  isAllow,\n  moveEvent,\n  register,\n  startEvent,\n  unlockEvent,\n} from '../../utils/touchHandler';\n\n/**\n * 显示涟漪动画\n * @param event\n * @param $ripple\n */\nfunction show(event: Event, $ripple: JQ): void {\n  // 鼠标右键不产生涟漪\n  if (event instanceof MouseEvent && event.button === 2) {\n    return;\n  }\n\n  // 点击位置坐标\n  const touchPosition =\n    typeof TouchEvent !== 'undefined' &&\n    event instanceof TouchEvent &&\n    event.touches.length\n      ? event.touches[0]\n      : (event as MouseEvent);\n\n  const touchStartX = touchPosition.pageX;\n  const touchStartY = touchPosition.pageY;\n\n  // 涟漪位置\n  const offset = $ripple.offset();\n  const height = $ripple.innerHeight();\n  const width = $ripple.innerWidth();\n  const center = {\n    x: touchStartX - offset.left,\n    y: touchStartY - offset.top,\n  };\n  const diameter = Math.max(\n    Math.pow(Math.pow(height, 2) + Math.pow(width, 2), 0.5),\n    48,\n  );\n\n  // 涟漪扩散动画\n  const translate =\n    `translate3d(${-center.x + width / 2}px,` +\n    `${-center.y + height / 2}px, 0) scale(1)`;\n\n  // 涟漪的 DOM 结构，并缓存动画效果\n  $(\n    `<div class=\"mdui-ripple-wave\" ` +\n      `style=\"width:${diameter}px;height:${diameter}px;` +\n      `margin-top:-${diameter / 2}px;margin-left:-${diameter / 2}px;` +\n      `left:${center.x}px;top:${center.y}px;\"></div>`,\n  )\n    .data('_ripple_wave_translate', translate)\n    .prependTo($ripple)\n    .reflow()\n    .transform(translate);\n}\n\n/**\n * 隐藏并移除涟漪\n * @param $wave\n */\nfunction removeRipple($wave: JQ): void {\n  if (!$wave.length || $wave.data('_ripple_wave_removed')) {\n    return;\n  }\n\n  $wave.data('_ripple_wave_removed', true);\n\n  let removeTimer = setTimeout(() => $wave.remove(), 400);\n  const translate = $wave.data('_ripple_wave_translate');\n\n  $wave\n    .addClass('mdui-ripple-wave-fill')\n    .transform(translate.replace('scale(1)', 'scale(1.01)'))\n    .transitionEnd(() => {\n      clearTimeout(removeTimer);\n\n      $wave\n        .addClass('mdui-ripple-wave-out')\n        .transform(translate.replace('scale(1)', 'scale(1.01)'));\n\n      removeTimer = setTimeout(() => $wave.remove(), 700);\n\n      setTimeout(() => {\n        $wave.transitionEnd(() => {\n          clearTimeout(removeTimer);\n          $wave.remove();\n        });\n      }, 0);\n    });\n}\n\n/**\n * 隐藏涟漪动画\n * @param this\n */\nfunction hide(this: any): void {\n  const $ripple = $(this as HTMLElement);\n\n  $ripple.children('.mdui-ripple-wave').each((_, wave) => {\n    removeRipple($(wave));\n  });\n\n  $ripple.off(`${moveEvent} ${endEvent} ${cancelEvent}`, hide);\n}\n\n/**\n * 显示涟漪，并绑定 touchend 等事件\n * @param event\n */\nfunction showRipple(event: Event): void {\n  if (!isAllow(event)) {\n    return;\n  }\n\n  register(event);\n\n  // Chrome 59 点击滚动条时，会在 document 上触发事件\n  if (event.target === document) {\n    return;\n  }\n\n  const $target = $(event.target as HTMLElement);\n\n  // 获取含 .mdui-ripple 类的元素\n  const $ripple = $target.hasClass('mdui-ripple')\n    ? $target\n    : $target.parents('.mdui-ripple').first();\n\n  if (!$ripple.length) {\n    return;\n  }\n\n  // 禁用状态的元素上不产生涟漪效果\n  if ($ripple.prop('disabled') || !isUndefined($ripple.attr('disabled'))) {\n    return;\n  }\n\n  if (event.type === 'touchstart') {\n    let hidden = false;\n\n    // touchstart 触发指定时间后开始涟漪动画，避免手指滑动时也触发涟漪\n    let timer = setTimeout(() => {\n      timer = 0;\n      show(event, $ripple);\n    }, 200);\n\n    const hideRipple = (): void => {\n      // 如果手指没有移动，且涟漪动画还没有开始，则开始涟漪动画\n      if (timer) {\n        clearTimeout(timer);\n        timer = 0;\n        show(event, $ripple);\n      }\n\n      if (!hidden) {\n        hidden = true;\n        hide.call($ripple);\n      }\n    };\n\n    // 手指移动后，移除涟漪动画\n    const touchMove = (): void => {\n      if (timer) {\n        clearTimeout(timer);\n        timer = 0;\n      }\n\n      hideRipple();\n    };\n\n    $ripple.on('touchmove', touchMove).on('touchend touchcancel', hideRipple);\n  } else {\n    show(event, $ripple);\n    $ripple.on(`${moveEvent} ${endEvent} ${cancelEvent}`, hide);\n  }\n}\n\n$(() => {\n  $document.on(startEvent, showRipple).on(unlockEvent, register);\n});\n", "import $ from 'mdui.jq/es/$';\nimport extend from 'mdui.jq/es/functions/extend';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/appendTo';\nimport 'mdui.jq/es/methods/attr';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/is';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/outerHeight';\nimport 'mdui.jq/es/methods/parent';\nimport 'mdui.jq/es/methods/parents';\nimport 'mdui.jq/es/methods/remove';\nimport 'mdui.jq/es/methods/removeClass';\nimport 'mdui.jq/es/methods/text';\nimport 'mdui.jq/es/methods/trigger';\nimport 'mdui.jq/es/methods/val';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport { isUndefined } from 'mdui.jq/es/utils';\nimport mdui from '../../mdui';\nimport '../../global/mutation';\nimport { $document } from '../../utils/dom';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 动态修改了文本框后，需要调用该方法重新初始化文本框。\n     *\n     * 若传入了参数，则只初始化该参数对应的文本框。若没有传入参数，则重新初始化所有文本框。\n     * @param selector CSS 选择器、或 DOM 元素、或 DOM 元素组成的数组、或 JQ 对象\n     */\n    updateTextFields(\n      selector?: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    ): void;\n  }\n}\n\ntype INPUT_EVENT_DATA = {\n  reInit?: boolean;\n  domLoadedEvent?: boolean;\n};\n\nconst defaultData: INPUT_EVENT_DATA = {\n  reInit: false,\n  domLoadedEvent: false,\n};\n\n/**\n * 输入框事件\n * @param event\n * @param data\n */\nfunction inputEvent(event: Event, data: INPUT_EVENT_DATA = {}): void {\n  data = extend({}, defaultData, data);\n\n  const input = event.target as HTMLInputElement;\n  const $input = $(input);\n  const eventType = event.type;\n  const value = $input.val() as string;\n\n  // 文本框类型\n  const inputType = $input.attr('type') || '';\n  if (\n    ['checkbox', 'button', 'submit', 'range', 'radio', 'image'].indexOf(\n      inputType,\n    ) > -1\n  ) {\n    return;\n  }\n\n  const $textfield = $input.parent('.mdui-textfield');\n\n  // 输入框是否聚焦\n  if (eventType === 'focus') {\n    $textfield.addClass('mdui-textfield-focus');\n  }\n\n  if (eventType === 'blur') {\n    $textfield.removeClass('mdui-textfield-focus');\n  }\n\n  // 输入框是否为空\n  if (eventType === 'blur' || eventType === 'input') {\n    value\n      ? $textfield.addClass('mdui-textfield-not-empty')\n      : $textfield.removeClass('mdui-textfield-not-empty');\n  }\n\n  // 输入框是否禁用\n  input.disabled\n    ? $textfield.addClass('mdui-textfield-disabled')\n    : $textfield.removeClass('mdui-textfield-disabled');\n\n  // 表单验证\n  if (\n    (eventType === 'input' || eventType === 'blur') &&\n    !data.domLoadedEvent &&\n    input.validity\n  ) {\n    input.validity.valid\n      ? $textfield.removeClass('mdui-textfield-invalid-html5')\n      : $textfield.addClass('mdui-textfield-invalid-html5');\n  }\n\n  // textarea 高度自动调整\n  if ($input.is('textarea')) {\n    // IE bug：textarea 的值仅为多个换行，不含其他内容时，textarea 的高度不准确\n    //         此时，在计算高度前，在值的开头加入一个空格，计算完后，移除空格\n    const inputValue = value;\n    let hasExtraSpace = false;\n\n    if (inputValue.replace(/[\\r\\n]/g, '') === '') {\n      $input.val(' ' + inputValue);\n      hasExtraSpace = true;\n    }\n\n    // 设置 textarea 高度\n    $input.outerHeight('');\n    const height = $input.outerHeight();\n    const scrollHeight = input.scrollHeight;\n\n    if (scrollHeight > height) {\n      $input.outerHeight(scrollHeight);\n    }\n\n    // 计算完，还原 textarea 的值\n    if (hasExtraSpace) {\n      $input.val(inputValue);\n    }\n  }\n\n  // 实时字数统计\n  if (data.reInit) {\n    $textfield.find('.mdui-textfield-counter').remove();\n  }\n\n  const maxLength = $input.attr('maxlength');\n  if (maxLength) {\n    if (data.reInit || data.domLoadedEvent) {\n      $(\n        '<div class=\"mdui-textfield-counter\">' +\n          `<span class=\"mdui-textfield-counter-inputed\"></span> / ${maxLength}` +\n          '</div>',\n      ).appendTo($textfield);\n    }\n\n    $textfield\n      .find('.mdui-textfield-counter-inputed')\n      .text(value.length.toString());\n  }\n\n  // 含 帮助文本、错误提示、字数统计 时，增加文本框底部内边距\n  if (\n    $textfield.find('.mdui-textfield-helper').length ||\n    $textfield.find('.mdui-textfield-error').length ||\n    maxLength\n  ) {\n    $textfield.addClass('mdui-textfield-has-bottom');\n  }\n}\n\n$(() => {\n  // 绑定事件\n  $document.on(\n    'input focus blur',\n    '.mdui-textfield-input',\n    { useCapture: true },\n    inputEvent,\n  );\n\n  // 可展开文本框展开\n  $document.on(\n    'click',\n    '.mdui-textfield-expandable .mdui-textfield-icon',\n    function () {\n      $(this as HTMLElement)\n        .parents('.mdui-textfield')\n        .addClass('mdui-textfield-expanded')\n        .find('.mdui-textfield-input')[0]\n        .focus();\n    },\n  );\n\n  // 可展开文本框关闭\n  $document.on(\n    'click',\n    '.mdui-textfield-expanded .mdui-textfield-close',\n    function () {\n      $(this)\n        .parents('.mdui-textfield')\n        .removeClass('mdui-textfield-expanded')\n        .find('.mdui-textfield-input')\n        .val('');\n    },\n  );\n\n  /**\n   * 初始化文本框\n   */\n  mdui.mutation('.mdui-textfield', function () {\n    $(this).find('.mdui-textfield-input').trigger('input', {\n      domLoadedEvent: true,\n    });\n  });\n});\n\nmdui.updateTextFields = function (\n  selector?: Selector | HTMLElement | ArrayLike<HTMLElement>,\n): void {\n  const $elements = isUndefined(selector) ? $('.mdui-textfield') : $(selector);\n\n  $elements.each((_, element) => {\n    $(element).find('.mdui-textfield-input').trigger('input', {\n      reInit: true,\n    });\n  });\n};\n", "import $ from 'mdui.jq/es/$';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/append';\nimport 'mdui.jq/es/methods/attr';\nimport 'mdui.jq/es/methods/css';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/empty';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/parent';\nimport 'mdui.jq/es/methods/remove';\nimport 'mdui.jq/es/methods/removeClass';\nimport 'mdui.jq/es/methods/text';\nimport 'mdui.jq/es/methods/val';\nimport 'mdui.jq/es/methods/width';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport { isUndefined } from 'mdui.jq/es/utils';\nimport mdui from '../../mdui';\nimport { $document } from '../../utils/dom';\nimport {\n  endEvent,\n  isAllow,\n  register,\n  startEvent,\n  unlockEvent,\n} from '../../utils/touchHandler';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 动态修改了滑块后，需要调用该方法重新初始化滑块\n     *\n     * 若传入了参数，则只初始化该参数对应的滑块。若没有传入参数，则重新初始化所有滑块。\n     * @param selector CSS 选择器、或 DOM 元素、或 DOM 元素组成的数组、或 JQ 对象\n     */\n    updateSliders(\n      selector?: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    ): void;\n  }\n}\n\n/**\n * 滑块的值改变后修改滑块样式\n * @param $slider\n */\nfunction updateValueStyle($slider: JQ): void {\n  const data = $slider.data();\n\n  const $track = data._slider_$track;\n  const $fill = data._slider_$fill;\n  const $thumb = data._slider_$thumb;\n  const $input = data._slider_$input;\n  const min = data._slider_min;\n  const max = data._slider_max;\n  const isDisabled = data._slider_disabled;\n  const isDiscrete = data._slider_discrete;\n  const $thumbText = data._slider_$thumbText;\n  const value = $input.val();\n  const percent = ((value - min) / (max - min)) * 100;\n\n  $fill.width(`${percent}%`);\n  $track.width(`${100 - percent}%`);\n\n  if (isDisabled) {\n    $fill.css('padding-right', '6px');\n    $track.css('padding-left', '6px');\n  }\n\n  $thumb.css('left', `${percent}%`);\n\n  if (isDiscrete) {\n    $thumbText.text(value);\n  }\n\n  percent === 0\n    ? $slider.addClass('mdui-slider-zero')\n    : $slider.removeClass('mdui-slider-zero');\n}\n\n/**\n * 重新初始化滑块\n * @param $slider\n */\nfunction reInit($slider: JQ): void {\n  const $track = $('<div class=\"mdui-slider-track\"></div>');\n  const $fill = $('<div class=\"mdui-slider-fill\"></div>');\n  const $thumb = $('<div class=\"mdui-slider-thumb\"></div>');\n  const $input = $slider.find('input[type=\"range\"]') as JQ<HTMLInputElement>;\n  const isDisabled = $input[0].disabled;\n  const isDiscrete = $slider.hasClass('mdui-slider-discrete');\n\n  // 禁用状态\n  isDisabled\n    ? $slider.addClass('mdui-slider-disabled')\n    : $slider.removeClass('mdui-slider-disabled');\n\n  // 重新填充 HTML\n  $slider.find('.mdui-slider-track').remove();\n  $slider.find('.mdui-slider-fill').remove();\n  $slider.find('.mdui-slider-thumb').remove();\n  $slider.append($track).append($fill).append($thumb);\n\n  // 间续型滑块\n  let $thumbText = $();\n  if (isDiscrete) {\n    $thumbText = $('<span></span>');\n    $thumb.empty().append($thumbText);\n  }\n\n  $slider.data('_slider_$track', $track);\n  $slider.data('_slider_$fill', $fill);\n  $slider.data('_slider_$thumb', $thumb);\n  $slider.data('_slider_$input', $input);\n  $slider.data('_slider_min', $input.attr('min'));\n  $slider.data('_slider_max', $input.attr('max'));\n  $slider.data('_slider_disabled', isDisabled);\n  $slider.data('_slider_discrete', isDiscrete);\n  $slider.data('_slider_$thumbText', $thumbText);\n\n  // 设置默认值\n  updateValueStyle($slider);\n}\n\nconst rangeSelector = '.mdui-slider input[type=\"range\"]';\n\n$(() => {\n  // 滑块滑动事件\n  $document.on('input change', rangeSelector, function () {\n    const $slider = $(this).parent() as JQ<HTMLElement>;\n\n    updateValueStyle($slider);\n  });\n\n  // 开始触摸滑块事件\n  $document.on(startEvent, rangeSelector, function (event: Event) {\n    if (!isAllow(event)) {\n      return;\n    }\n\n    register(event);\n\n    if ((this as HTMLInputElement).disabled) {\n      return;\n    }\n\n    const $slider = $(this).parent() as JQ<HTMLElement>;\n\n    $slider.addClass('mdui-slider-focus');\n  });\n\n  // 结束触摸滑块事件\n  $document.on(endEvent, rangeSelector, function (event: Event) {\n    if (!isAllow(event)) {\n      return;\n    }\n\n    if ((this as HTMLInputElement).disabled) {\n      return;\n    }\n\n    const $slider = $(this).parent() as JQ<HTMLElement>;\n\n    $slider.removeClass('mdui-slider-focus');\n  });\n\n  $document.on(unlockEvent, rangeSelector, register);\n\n  /**\n   * 初始化滑块\n   */\n  mdui.mutation('.mdui-slider', function () {\n    reInit($(this));\n  });\n});\n\nmdui.updateSliders = function (\n  selector?: Selector | HTMLElement | ArrayLike<HTMLElement>,\n): void {\n  const $elements = isUndefined(selector) ? $('.mdui-slider') : $(selector);\n\n  $elements.each((_, element) => {\n    reInit($(element));\n  });\n};\n", "import $ from 'mdui.jq/es/$';\nimport extend from 'mdui.jq/es/functions/extend';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/css';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/last';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/parents';\nimport 'mdui.jq/es/methods/removeClass';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport mdui from '../../mdui';\nimport '../../jq_extends/methods/transitionEnd';\nimport { componentEvent } from '../../utils/componentEvent';\nimport { $document } from '../../utils/dom';\nimport { startEvent } from '../../utils/touchHandler';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 浮动操作按钮组件\n     *\n     * 请通过 `new mdui.Fab()` 调用\n     */\n    Fab: {\n      /**\n       * 实例化 Fab 组件\n       * @param selector CSS 选择器、或 DOM 元素、或 JQ 对象\n       * @param options 配置参数\n       */\n      new (\n        selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n        options?: OPTIONS,\n      ): Fab;\n    };\n  }\n}\n\ntype OPTIONS = {\n  /**\n   * 触发方式。`hover`: 鼠标悬浮触发；`click`: 点击触发\n   *\n   * 默认为 `hover`\n   */\n  trigger?: 'click' | 'hover';\n};\n\ntype STATE = 'opening' | 'opened' | 'closing' | 'closed';\ntype EVENT = 'open' | 'opened' | 'close' | 'closed';\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  trigger: 'hover',\n};\n\nclass Fab {\n  /**\n   * Fab 元素的 JQ 对象\n   */\n  public $element: JQ;\n\n  /**\n   * 配置参数\n   */\n  public options: OPTIONS = extend({}, DEFAULT_OPTIONS);\n\n  /**\n   * 当前 fab 的状态\n   */\n  private state: STATE = 'closed';\n\n  /**\n   * 按钮元素\n   */\n  private $btn: JQ;\n\n  /**\n   * 拨号菜单元素\n   */\n  private $dial: JQ;\n\n  /**\n   * 拨号菜单内的按钮\n   */\n  private $dialBtns: JQ;\n\n  public constructor(\n    selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    options: OPTIONS = {},\n  ) {\n    this.$element = $(selector).first();\n\n    extend(this.options, options);\n\n    this.$btn = this.$element.find('.mdui-fab');\n    this.$dial = this.$element.find('.mdui-fab-dial');\n    this.$dialBtns = this.$dial.find('.mdui-fab');\n\n    if (this.options.trigger === 'hover') {\n      this.$btn.on('touchstart mouseenter', () => this.open());\n      this.$element.on('mouseleave', () => this.close());\n    }\n\n    if (this.options.trigger === 'click') {\n      this.$btn.on(startEvent, () => this.open());\n    }\n\n    // 触摸屏幕其他地方关闭快速拨号\n    $document.on(startEvent, (event) => {\n      if ($(event.target as HTMLElement).parents('.mdui-fab-wrapper').length) {\n        return;\n      }\n\n      this.close();\n    });\n  }\n\n  /**\n   * 触发组件事件\n   * @param name\n   */\n  private triggerEvent(name: EVENT): void {\n    componentEvent(name, 'fab', this.$element, this);\n  }\n\n  /**\n   * 当前是否为打开状态\n   */\n  private isOpen(): boolean {\n    return this.state === 'opening' || this.state === 'opened';\n  }\n\n  /**\n   * 打开快速拨号菜单\n   */\n  public open(): void {\n    if (this.isOpen()) {\n      return;\n    }\n\n    // 为菜单中的按钮添加不同的 transition-delay\n    this.$dialBtns.each((index, btn) => {\n      const delay = `${15 * (this.$dialBtns.length - index)}ms`;\n\n      btn.style.transitionDelay = delay;\n      btn.style.webkitTransitionDelay = delay;\n    });\n\n    this.$dial.css('height', 'auto').addClass('mdui-fab-dial-show');\n\n    // 如果按钮中存在 .mdui-fab-opened 的图标，则进行图标切换\n    if (this.$btn.find('.mdui-fab-opened').length) {\n      this.$btn.addClass('mdui-fab-opened');\n    }\n\n    this.state = 'opening';\n    this.triggerEvent('open');\n\n    // 打开顺序为从下到上逐个打开，最上面的打开后才表示动画完成\n    this.$dialBtns.first().transitionEnd(() => {\n      if (this.$btn.hasClass('mdui-fab-opened')) {\n        this.state = 'opened';\n        this.triggerEvent('opened');\n      }\n    });\n  }\n\n  /**\n   * 关闭快速拨号菜单\n   */\n  public close(): void {\n    if (!this.isOpen()) {\n      return;\n    }\n\n    // 为菜单中的按钮添加不同的 transition-delay\n    this.$dialBtns.each((index, btn) => {\n      const delay = `${15 * index}ms`;\n\n      btn.style.transitionDelay = delay;\n      btn.style.webkitTransitionDelay = delay;\n    });\n\n    this.$dial.removeClass('mdui-fab-dial-show');\n    this.$btn.removeClass('mdui-fab-opened');\n    this.state = 'closing';\n    this.triggerEvent('close');\n\n    // 从上往下依次关闭，最后一个关闭后才表示动画完成\n    this.$dialBtns.last().transitionEnd(() => {\n      if (this.$btn.hasClass('mdui-fab-opened')) {\n        return;\n      }\n\n      this.state = 'closed';\n      this.triggerEvent('closed');\n      this.$dial.css('height', 0);\n    });\n  }\n\n  /**\n   * 切换快速拨号菜单的打开状态\n   */\n  public toggle(): void {\n    this.isOpen() ? this.close() : this.open();\n  }\n\n  /**\n   * 以动画的形式显示整个浮动操作按钮\n   */\n  public show(): void {\n    this.$element.removeClass('mdui-fab-hide');\n  }\n\n  /**\n   * 以动画的形式隐藏整个浮动操作按钮\n   */\n  public hide(): void {\n    this.$element.addClass('mdui-fab-hide');\n  }\n\n  /**\n   * 返回当前快速拨号菜单的打开状态。共包含四种状态：`opening`、`opened`、`closing`、`closed`\n   */\n  public getState(): STATE {\n    return this.state;\n  }\n}\n\nmdui.Fab = Fab;\n", "import $ from 'mdui.jq/es/$';\nimport mdui from '../../mdui';\nimport { $document } from '../../utils/dom';\nimport { parseOptions } from '../../utils/parseOptions';\nimport './index';\n\nconst customAttr = 'mdui-fab';\n\n$(() => {\n  // mouseenter 不冒泡，无法进行事件委托，这里用 mouseover 代替。\n  // 不管是 click 、 mouseover 还是 touchstart ，都先初始化。\n\n  $document.on(\n    'touchstart mousedown mouseover',\n    `[${customAttr}]`,\n    function () {\n      new mdui.Fab(\n        this as HTMLElement,\n        parseOptions(this as HTMLElement, customAttr),\n      );\n    },\n  );\n});\n", "/**\n * 最终生成的元素结构为：\n *  <select class=\"mdui-select\" mdui-select=\"{position: 'top'}\" style=\"display: none;\"> // $native\n *    <option value=\"1\">State 1</option>\n *    <option value=\"2\">State 2</option>\n *    <option value=\"3\" disabled=\"\">State 3</option>\n *  </select>\n *  <div class=\"mdui-select mdui-select-position-top\" style=\"\" id=\"88dec0e4-d4a2-c6d0-0e7f-1ba4501e0553\"> // $element\n *    <span class=\"mdui-select-selected\">State 1</span> // $selected\n *    <div class=\"mdui-select-menu\" style=\"transform-origin: center 100% 0px;\"> // $menu\n *      <div class=\"mdui-select-menu-item mdui-ripple\" selected=\"\">State 1</div> // $items\n *      <div class=\"mdui-select-menu-item mdui-ripple\">State 2</div>\n *      <div class=\"mdui-select-menu-item mdui-ripple\" disabled=\"\">State 3</div>\n *    </div>\n *  </div>\n */\n\nimport $ from 'mdui.jq/es/$';\nimport contains from 'mdui.jq/es/functions/contains';\nimport extend from 'mdui.jq/es/functions/extend';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/add';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/after';\nimport 'mdui.jq/es/methods/append';\nimport 'mdui.jq/es/methods/appendTo';\nimport 'mdui.jq/es/methods/attr';\nimport 'mdui.jq/es/methods/css';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/height';\nimport 'mdui.jq/es/methods/hide';\nimport 'mdui.jq/es/methods/index';\nimport 'mdui.jq/es/methods/innerWidth';\nimport 'mdui.jq/es/methods/is';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/remove';\nimport 'mdui.jq/es/methods/removeAttr';\nimport 'mdui.jq/es/methods/removeClass';\nimport 'mdui.jq/es/methods/show';\nimport 'mdui.jq/es/methods/text';\nimport 'mdui.jq/es/methods/trigger';\nimport 'mdui.jq/es/methods/val';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport mdui from '../../mdui';\nimport '../../jq_extends/methods/transitionEnd';\nimport '../../jq_extends/static/guid';\nimport { componentEvent } from '../../utils/componentEvent';\nimport { $document, $window } from '../../utils/dom';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 下拉选择组件\n     *\n     * 请通过 `new mdui.Select()` 调用\n     */\n    Select: {\n      /**\n       * 实例化 Select 组件\n       * @param selector CSS 选择器、或 DOM 元素、或 JQ 对象\n       * @param options 配置参数\n       */\n      new (\n        selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n        options?: OPTIONS,\n      ): Select;\n    };\n  }\n}\n\ntype OPTIONS = {\n  /**\n   * 下拉框位置：`auto`、`top`、`bottom`\n   */\n  position?: 'auto' | 'top' | 'bottom';\n\n  /**\n   * 菜单与窗口上下边框至少保持多少间距\n   */\n  gutter?: number;\n};\n\ntype STATE = 'closing' | 'closed' | 'opening' | 'opened';\ntype EVENT = 'open' | 'opened' | 'close' | 'closed';\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  position: 'auto',\n  gutter: 16,\n};\n\nclass Select {\n  /**\n   * 原生 `<select>` 元素的 JQ 对象\n   */\n  public $native: JQ<HTMLSelectElement>;\n\n  /**\n   * 生成的 `<div class=\"mdui-select\">` 元素的 JQ 对象\n   */\n  public $element: JQ = $();\n\n  /**\n   * 配置参数\n   */\n  public options: OPTIONS = extend({}, DEFAULT_OPTIONS);\n\n  /**\n   * select 的 size 属性的值，根据该值设置 select 的高度\n   */\n  private size = 0;\n\n  /**\n   * 占位元素，显示已选中菜单项的文本\n   */\n  private $selected: JQ = $();\n\n  /**\n   * 菜单项的外层元素的 JQ 对象\n   */\n  private $menu: JQ = $();\n\n  /**\n   * 菜单项数组的 JQ 对象\n   */\n  private $items: JQ = $();\n\n  /**\n   * 当前选中的菜单项的索引号\n   */\n  private selectedIndex = 0;\n\n  /**\n   * 当前选中菜单项的文本\n   */\n  private selectedText = '';\n\n  /**\n   * 当前选中菜单项的值\n   */\n  private selectedValue = '';\n\n  /**\n   * 唯一 ID\n   */\n  private uniqueID: string;\n\n  /**\n   * 当前 select 的状态\n   */\n  private state: STATE = 'closed';\n\n  public constructor(\n    selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    options: OPTIONS = {},\n  ) {\n    this.$native = $(selector).first() as JQ<HTMLSelectElement>;\n    this.$native.hide();\n\n    extend(this.options, options);\n\n    // 为当前 select 生成唯一 ID\n    this.uniqueID = $.guid();\n\n    // 生成 select\n    this.handleUpdate();\n\n    // 点击 select 外面区域关闭\n    $document.on('click touchstart', (event: Event) => {\n      const $target = $(event.target as HTMLElement);\n\n      if (\n        this.isOpen() &&\n        !$target.is(this.$element) &&\n        !contains(this.$element[0], $target[0])\n      ) {\n        this.close();\n      }\n    });\n  }\n\n  /**\n   * 调整菜单位置\n   */\n  private readjustMenu(): void {\n    const windowHeight = $window.height();\n\n    // mdui-select 高度\n    const elementHeight = this.$element.height();\n\n    // 菜单项高度\n    const $itemFirst = this.$items.first();\n    const itemHeight = $itemFirst.height();\n    const itemMargin = parseInt($itemFirst.css('margin-top'));\n\n    // 菜单高度\n    const menuWidth = this.$element.innerWidth() + 0.01; // 必须比真实宽度多一点，不然会出现省略号\n    let menuHeight = itemHeight * this.size + itemMargin * 2;\n\n    // mdui-select 在窗口中的位置\n    const elementTop = this.$element[0].getBoundingClientRect().top;\n\n    let transformOriginY: string;\n    let menuMarginTop: number;\n\n    if (this.options.position === 'bottom') {\n      menuMarginTop = elementHeight;\n      transformOriginY = '0px';\n    } else if (this.options.position === 'top') {\n      menuMarginTop = -menuHeight - 1;\n      transformOriginY = '100%';\n    } else {\n      // 菜单高度不能超过窗口高度\n      const menuMaxHeight = windowHeight - this.options.gutter! * 2;\n      if (menuHeight > menuMaxHeight) {\n        menuHeight = menuMaxHeight;\n      }\n\n      // 菜单的 margin-top\n      menuMarginTop = -(\n        itemMargin +\n        this.selectedIndex * itemHeight +\n        (itemHeight - elementHeight) / 2\n      );\n\n      const menuMaxMarginTop = -(\n        itemMargin +\n        (this.size - 1) * itemHeight +\n        (itemHeight - elementHeight) / 2\n      );\n      if (menuMarginTop < menuMaxMarginTop) {\n        menuMarginTop = menuMaxMarginTop;\n      }\n\n      // 菜单不能超出窗口\n      const menuTop = elementTop + menuMarginTop;\n      if (menuTop < this.options.gutter!) {\n        // 不能超出窗口上方\n        menuMarginTop = -(elementTop - this.options.gutter!);\n      } else if (menuTop + menuHeight + this.options.gutter! > windowHeight) {\n        // 不能超出窗口下方\n        menuMarginTop = -(\n          elementTop +\n          menuHeight +\n          this.options.gutter! -\n          windowHeight\n        );\n      }\n\n      // transform 的 Y 轴坐标\n      transformOriginY = `${\n        this.selectedIndex * itemHeight + itemHeight / 2 + itemMargin\n      }px`;\n    }\n\n    // 设置样式\n    this.$element.innerWidth(menuWidth);\n    this.$menu\n      .innerWidth(menuWidth)\n      .height(menuHeight)\n      .css({\n        'margin-top': menuMarginTop + 'px',\n        'transform-origin': 'center ' + transformOriginY + ' 0',\n      });\n  }\n\n  /**\n   * select 是否为打开状态\n   */\n  private isOpen(): boolean {\n    return this.state === 'opening' || this.state === 'opened';\n  }\n\n  /**\n   * 对原生 select 组件进行了修改后，需要调用该方法\n   */\n  public handleUpdate(): void {\n    if (this.isOpen()) {\n      this.close();\n    }\n\n    this.selectedValue = this.$native.val() as string;\n\n    // 保存菜单项数据的数组\n    type typeItemsData = {\n      value: string;\n      text: string;\n      disabled: boolean;\n      selected: boolean;\n      index: number;\n    };\n    const itemsData: typeItemsData[] = [];\n    this.$items = $();\n\n    // 生成 HTML\n    this.$native.find('option').each((index, option) => {\n      const text = option.textContent || '';\n      const value = option.value;\n      const disabled = option.disabled;\n      const selected = this.selectedValue === value;\n\n      itemsData.push({\n        value,\n        text,\n        disabled,\n        selected,\n        index,\n      });\n\n      if (selected) {\n        this.selectedText = text;\n        this.selectedIndex = index;\n      }\n\n      this.$items = this.$items.add(\n        '<div class=\"mdui-select-menu-item mdui-ripple\"' +\n          (disabled ? ' disabled' : '') +\n          (selected ? ' selected' : '') +\n          `>${text}</div>`,\n      );\n    });\n\n    this.$selected = $(\n      `<span class=\"mdui-select-selected\">${this.selectedText}</span>`,\n    );\n\n    this.$element = $(\n      `<div class=\"mdui-select mdui-select-position-${this.options.position}\" ` +\n        `style=\"${this.$native.attr('style')}\" ` +\n        `id=\"${this.uniqueID}\"></div>`,\n    )\n      .show()\n      .append(this.$selected);\n\n    this.$menu = $('<div class=\"mdui-select-menu\"></div>')\n      .appendTo(this.$element)\n      .append(this.$items);\n\n    $(`#${this.uniqueID}`).remove();\n    this.$native.after(this.$element);\n\n    // 根据 select 的 size 属性设置高度\n    this.size = parseInt(this.$native.attr('size') || '0');\n\n    if (this.size <= 0) {\n      this.size = this.$items.length;\n\n      if (this.size > 8) {\n        this.size = 8;\n      }\n    }\n\n    // 点击选项时关闭下拉菜单\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const that = this;\n    this.$items.on('click', function () {\n      if (that.state === 'closing') {\n        return;\n      }\n\n      const $item = $(this);\n      const index = $item.index();\n      const data = itemsData[index];\n\n      if (data.disabled) {\n        return;\n      }\n\n      that.$selected.text(data.text);\n      that.$native.val(data.value);\n      that.$items.removeAttr('selected');\n      $item.attr('selected', '');\n      that.selectedIndex = data.index;\n      that.selectedValue = data.value;\n      that.selectedText = data.text;\n      that.$native.trigger('change');\n      that.close();\n    });\n\n    // 点击 $element 时打开下拉菜单\n    this.$element.on('click', (event: Event) => {\n      const $target = $(event.target as HTMLElement);\n\n      // 在菜单上点击时不打开\n      if (\n        $target.is('.mdui-select-menu') ||\n        $target.is('.mdui-select-menu-item')\n      ) {\n        return;\n      }\n\n      this.toggle();\n    });\n  }\n\n  /**\n   * 动画结束的回调\n   */\n  private transitionEnd(): void {\n    this.$element.removeClass('mdui-select-closing');\n\n    if (this.state === 'opening') {\n      this.state = 'opened';\n      this.triggerEvent('opened');\n      this.$menu.css('overflow-y', 'auto');\n    }\n\n    if (this.state === 'closing') {\n      this.state = 'closed';\n      this.triggerEvent('closed');\n\n      // 恢复样式\n      this.$element.innerWidth('');\n      this.$menu.css({\n        'margin-top': '',\n        height: '',\n        width: '',\n      });\n    }\n  }\n\n  /**\n   * 触发组件事件\n   * @param name\n   */\n  private triggerEvent(name: EVENT): void {\n    componentEvent(name, 'select', this.$native, this);\n  }\n\n  /**\n   * 切换下拉菜单的打开状态\n   */\n  public toggle(): void {\n    this.isOpen() ? this.close() : this.open();\n  }\n\n  /**\n   * 打开下拉菜单\n   */\n  public open(): void {\n    if (this.isOpen()) {\n      return;\n    }\n\n    this.state = 'opening';\n    this.triggerEvent('open');\n    this.readjustMenu();\n    this.$element.addClass('mdui-select-open');\n    this.$menu.transitionEnd(() => this.transitionEnd());\n  }\n\n  /**\n   * 关闭下拉菜单\n   */\n  public close(): void {\n    if (!this.isOpen()) {\n      return;\n    }\n\n    this.state = 'closing';\n    this.triggerEvent('close');\n    this.$menu.css('overflow-y', '');\n    this.$element\n      .removeClass('mdui-select-open')\n      .addClass('mdui-select-closing');\n    this.$menu.transitionEnd(() => this.transitionEnd());\n  }\n\n  /**\n   * 获取当前菜单的状态。共包含四种状态：`opening`、`opened`、`closing`、`closed`\n   */\n  public getState(): STATE {\n    return this.state;\n  }\n}\n\nmdui.Select = Select;\n", "import $ from 'mdui.jq/es/$';\nimport mdui from '../../mdui';\nimport '../../global/mutation';\nimport { parseOptions } from '../../utils/parseOptions';\nimport './index';\n\nconst customAttr = 'mdui-select';\n\n$(() => {\n  mdui.mutation(`[${customAttr}]`, function () {\n    new mdui.Select(this, parseOptions(this, customAttr));\n  });\n});\n", "import $ from 'mdui.jq/es/$';\nimport mdui from '../../mdui';\nimport '../../global/mutation';\nimport '../headroom';\n\n$(() => {\n  // 滚动时隐藏应用栏\n  mdui.mutation('.mdui-appbar-scroll-hide', function () {\n    new mdui.Headroom(this);\n  });\n\n  // 滚动时只隐藏应用栏中的工具栏\n  mdui.mutation('.mdui-appbar-scroll-toolbar-hide', function () {\n    new mdui.Headroom(this, {\n      pinnedClass: 'mdui-headroom-pinned-toolbar',\n      unpinnedClass: 'mdui-headroom-unpinned-toolbar',\n    });\n  });\n});\n", "import $ from 'mdui.jq/es/$';\nimport extend from 'mdui.jq/es/functions/extend';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/appendTo';\nimport 'mdui.jq/es/methods/attr';\nimport 'mdui.jq/es/methods/children';\nimport 'mdui.jq/es/methods/css';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/eq';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/get';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/hide';\nimport 'mdui.jq/es/methods/index';\nimport 'mdui.jq/es/methods/innerWidth';\nimport 'mdui.jq/es/methods/offset';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/removeClass';\nimport 'mdui.jq/es/methods/show';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport { isNumber } from 'mdui.jq/es/utils';\nimport mdui from '../../mdui';\nimport '../../jq_extends/static/throttle';\nimport { componentEvent } from '../../utils/componentEvent';\nimport { $window } from '../../utils/dom';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * Tab 选项卡组件\n     *\n     * 请通过 `new mdui.Tab()` 调用\n     */\n    Tab: {\n      /**\n       * 实例化 Tab 组件\n       * @param selector CSS 选择器、或 DOM 元素、或 JQ 对象\n       * @param options 配置参数\n       */\n      new (\n        selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n        options?: OPTIONS,\n      ): Tab;\n    };\n  }\n}\n\ntype OPTIONS = {\n  /**\n   * 切换选项卡的触发方式。`click`: 点击切换；`hover`: 鼠标悬浮切换\n   */\n  trigger?: 'click' | 'hover';\n\n  /**\n   * 是否启用循环切换，若为 `true`，则最后一个选项激活时调用 `next` 方法将回到第一个选项，第一个选项激活时调用 `prev` 方法将回到最后一个选项。\n   */\n  loop?: boolean;\n};\n\ntype EVENT = 'change' | 'show';\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  trigger: 'click',\n  loop: false,\n};\n\nclass Tab {\n  /**\n   * tab 元素的 JQ 对象\n   */\n  public $element: JQ;\n\n  /**\n   * 配置参数\n   */\n  public options: OPTIONS = extend({}, DEFAULT_OPTIONS);\n\n  /**\n   * 当前激活的 tab 的索引号。为 -1 时表示没有激活的选项卡，或不存在选项卡\n   */\n  public activeIndex = -1;\n\n  /**\n   * 选项数组 JQ 对象\n   */\n  private $tabs: JQ;\n\n  /**\n   * 激活状态的 tab 底部的指示符\n   */\n  private $indicator: JQ;\n\n  public constructor(\n    selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    options: OPTIONS = {},\n  ) {\n    this.$element = $(selector).first();\n\n    extend(this.options, options);\n\n    this.$tabs = this.$element.children('a');\n    this.$indicator = $('<div class=\"mdui-tab-indicator\"></div>').appendTo(\n      this.$element,\n    );\n\n    // 根据 url hash 获取默认激活的选项卡\n    const hash = window.location.hash;\n    if (hash) {\n      this.$tabs.each((index, tab) => {\n        if ($(tab).attr('href') === hash) {\n          this.activeIndex = index;\n          return false;\n        }\n\n        return true;\n      });\n    }\n\n    // 含 .mdui-tab-active 的元素默认激活\n    if (this.activeIndex === -1) {\n      this.$tabs.each((index, tab) => {\n        if ($(tab).hasClass('mdui-tab-active')) {\n          this.activeIndex = index;\n          return false;\n        }\n\n        return true;\n      });\n    }\n\n    // 存在选项卡时，默认激活第一个选项卡\n    if (this.$tabs.length && this.activeIndex === -1) {\n      this.activeIndex = 0;\n    }\n\n    // 设置激活状态选项卡\n    this.setActive();\n\n    // 监听窗口大小变化事件，调整指示器位置\n    $window.on(\n      'resize',\n      $.throttle(() => this.setIndicatorPosition(), 100),\n    );\n\n    // 监听点击选项卡事件\n    this.$tabs.each((_, tab) => {\n      this.bindTabEvent(tab);\n    });\n  }\n\n  /**\n   * 指定选项卡是否已禁用\n   * @param $tab\n   */\n  private isDisabled($tab: JQ): boolean {\n    return $tab.attr('disabled') !== undefined;\n  }\n\n  /**\n   * 绑定在 Tab 上点击或悬浮的事件\n   * @param tab\n   */\n  private bindTabEvent(tab: HTMLElement): void {\n    const $tab = $(tab);\n\n    // 点击或鼠标移入触发的事件\n    const clickEvent = (): void | false => {\n      // 禁用状态的选项卡无法选中\n      if (this.isDisabled($tab)) {\n        return false;\n      }\n\n      this.activeIndex = this.$tabs.index(tab);\n      this.setActive();\n    };\n\n    // 无论 trigger 是 click 还是 hover，都会响应 click 事件\n    $tab.on('click', clickEvent);\n\n    // trigger 为 hover 时，额外响应 mouseenter 事件\n    if (this.options.trigger === 'hover') {\n      $tab.on('mouseenter', clickEvent);\n    }\n\n    // 阻止链接的默认点击动作\n    $tab.on('click', (): void | false => {\n      if (($tab.attr('href') || '').indexOf('#') === 0) {\n        return false;\n      }\n    });\n  }\n\n  /**\n   * 触发组件事件\n   * @param name\n   * @param $element\n   * @param parameters\n   */\n  private triggerEvent(name: EVENT, $element: JQ, parameters = {}): void {\n    componentEvent(name, 'tab', $element, this, parameters);\n  }\n\n  /**\n   * 设置激活状态的选项卡\n   */\n  private setActive(): void {\n    this.$tabs.each((index, tab) => {\n      const $tab = $(tab);\n      const targetId = $tab.attr('href') || '';\n\n      // 设置选项卡激活状态\n      if (index === this.activeIndex && !this.isDisabled($tab)) {\n        if (!$tab.hasClass('mdui-tab-active')) {\n          this.triggerEvent('change', this.$element, {\n            index: this.activeIndex,\n            id: targetId.substr(1),\n          });\n          this.triggerEvent('show', $tab);\n\n          $tab.addClass('mdui-tab-active');\n        }\n\n        $(targetId).show();\n        this.setIndicatorPosition();\n      } else {\n        $tab.removeClass('mdui-tab-active');\n        $(targetId).hide();\n      }\n    });\n  }\n\n  /**\n   * 设置选项卡指示器的位置\n   */\n  private setIndicatorPosition(): void {\n    // 选项卡数量为 0 时，不显示指示器\n    if (this.activeIndex === -1) {\n      this.$indicator.css({\n        left: 0,\n        width: 0,\n      });\n\n      return;\n    }\n\n    const $activeTab = this.$tabs.eq(this.activeIndex);\n\n    if (this.isDisabled($activeTab)) {\n      return;\n    }\n\n    const activeTabOffset = $activeTab.offset();\n\n    this.$indicator.css({\n      left: `${\n        activeTabOffset.left +\n        this.$element[0].scrollLeft -\n        this.$element[0].getBoundingClientRect().left\n      }px`,\n      width: `${$activeTab.innerWidth()}px`,\n    });\n  }\n\n  /**\n   * 切换到下一个选项卡\n   */\n  public next(): void {\n    if (this.activeIndex === -1) {\n      return;\n    }\n\n    if (this.$tabs.length > this.activeIndex + 1) {\n      this.activeIndex++;\n    } else if (this.options.loop) {\n      this.activeIndex = 0;\n    }\n\n    this.setActive();\n  }\n\n  /**\n   * 切换到上一个选项卡\n   */\n  public prev(): void {\n    if (this.activeIndex === -1) {\n      return;\n    }\n\n    if (this.activeIndex > 0) {\n      this.activeIndex--;\n    } else if (this.options.loop) {\n      this.activeIndex = this.$tabs.length - 1;\n    }\n\n    this.setActive();\n  }\n\n  /**\n   * 显示指定索引号、或指定id的选项卡\n   * @param index 索引号、或id\n   */\n  public show(index: number | string): void {\n    if (this.activeIndex === -1) {\n      return;\n    }\n\n    if (isNumber(index)) {\n      this.activeIndex = index;\n    } else {\n      this.$tabs.each((i, tab): void | false => {\n        if (tab.id === index) {\n          this.activeIndex === i;\n          return false;\n        }\n      });\n    }\n\n    this.setActive();\n  }\n\n  /**\n   * 在父元素的宽度变化时，需要调用该方法重新调整指示器位置\n   * 在添加或删除选项卡时，需要调用该方法\n   */\n  public handleUpdate(): void {\n    const $oldTabs = this.$tabs; // 旧的 tabs JQ对象\n    const $newTabs = this.$element.children('a'); // 新的 tabs JQ对象\n    const oldTabsElement = $oldTabs.get(); // 旧的 tabs 元素数组\n    const newTabsElement = $newTabs.get(); // 新的 tabs 元素数组\n\n    if (!$newTabs.length) {\n      this.activeIndex = -1;\n      this.$tabs = $newTabs;\n      this.setIndicatorPosition();\n\n      return;\n    }\n\n    // 重新遍历选项卡，找出新增的选项卡\n    $newTabs.each((index, tab) => {\n      // 有新增的选项卡\n      if (oldTabsElement.indexOf(tab) < 0) {\n        this.bindTabEvent(tab);\n\n        if (this.activeIndex === -1) {\n          this.activeIndex = 0;\n        } else if (index <= this.activeIndex) {\n          this.activeIndex++;\n        }\n      }\n    });\n\n    // 找出被移除的选项卡\n    $oldTabs.each((index, tab) => {\n      // 有被移除的选项卡\n      if (newTabsElement.indexOf(tab) < 0) {\n        if (index < this.activeIndex) {\n          this.activeIndex--;\n        } else if (index === this.activeIndex) {\n          this.activeIndex = 0;\n        }\n      }\n    });\n\n    this.$tabs = $newTabs;\n\n    this.setActive();\n  }\n}\n\nmdui.Tab = Tab;\n", "import $ from 'mdui.jq/es/$';\nimport mdui from '../../mdui';\nimport '../../global/mutation';\nimport { parseOptions } from '../../utils/parseOptions';\nimport './index';\n\nconst customAttr = 'mdui-tab';\n\n$(() => {\n  mdui.mutation(`[${customAttr}]`, function () {\n    new mdui.Tab(this, parseOptions(this, customAttr));\n  });\n});\n", "/**\n * 在桌面设备上默认显示抽屉栏，不显示遮罩层\n * 在手机和平板设备上默认不显示抽屉栏，始终显示遮罩层，且覆盖导航栏\n */\n\nimport $ from 'mdui.jq/es/$';\nimport extend from 'mdui.jq/es/functions/extend';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/off';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/one';\nimport 'mdui.jq/es/methods/removeClass';\nimport 'mdui.jq/es/methods/width';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport mdui from '../../mdui';\nimport '../../jq_extends/methods/transitionEnd';\nimport '../../jq_extends/static/hideOverlay';\nimport '../../jq_extends/static/lockScreen';\nimport '../../jq_extends/static/showOverlay';\nimport '../../jq_extends/static/throttle';\nimport '../../jq_extends/static/unlockScreen';\nimport { componentEvent } from '../../utils/componentEvent';\nimport { $window } from '../../utils/dom';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * Drawer 组件\n     *\n     * 请通过 `new mdui.Drawer()` 调用\n     */\n    Drawer: {\n      /**\n       * 实例化 Drawer 组件\n       * @param selector CSS 选择器、或 DOM 元素、或 JQ 对象\n       * @param options 配置参数\n       */\n      new (\n        selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n        options?: OPTIONS,\n      ): Drawer;\n    };\n  }\n}\n\ntype OPTIONS = {\n  /**\n   * 打开抽屉栏时是否显示遮罩层。该参数只对中等屏幕及以上的设备有效，在超小屏和小屏设备上始终会显示遮罩层。\n   */\n  overlay?: boolean;\n\n  /**\n   * 是否启用滑动手势。\n   */\n  swipe?: boolean;\n};\n\ntype STATE = 'opening' | 'opened' | 'closing' | 'closed';\ntype EVENT = 'open' | 'opened' | 'close' | 'closed';\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  overlay: false,\n  swipe: false,\n};\n\nclass Drawer {\n  /**\n   * drawer 元素的 JQ 对象\n   */\n  public $element: JQ;\n\n  /**\n   * 配置参数\n   */\n  public options: OPTIONS = extend({}, DEFAULT_OPTIONS);\n\n  /**\n   * 当前是否显示着遮罩层\n   */\n  private overlay = false;\n\n  /**\n   * 抽屉栏的位置\n   */\n  private position: 'left' | 'right';\n\n  /**\n   * 当前抽屉栏状态\n   */\n  private state: STATE;\n\n  public constructor(\n    selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    options: OPTIONS = {},\n  ) {\n    this.$element = $(selector).first();\n\n    extend(this.options, options);\n\n    this.position = this.$element.hasClass('mdui-drawer-right')\n      ? 'right'\n      : 'left';\n\n    if (this.$element.hasClass('mdui-drawer-close')) {\n      this.state = 'closed';\n    } else if (this.$element.hasClass('mdui-drawer-open')) {\n      this.state = 'opened';\n    } else if (this.isDesktop()) {\n      this.state = 'opened';\n    } else {\n      this.state = 'closed';\n    }\n\n    // 浏览器窗口大小调整时\n    $window.on(\n      'resize',\n      $.throttle(() => {\n        if (this.isDesktop()) {\n          // 由手机平板切换到桌面时\n          // 如果显示着遮罩，则隐藏遮罩\n          if (this.overlay && !this.options.overlay) {\n            $.hideOverlay();\n            this.overlay = false;\n            $.unlockScreen();\n          }\n\n          // 没有强制关闭，则状态为打开状态\n          if (!this.$element.hasClass('mdui-drawer-close')) {\n            this.state = 'opened';\n          }\n        } else if (!this.overlay && this.state === 'opened') {\n          // 由桌面切换到手机平板时。如果抽屉栏是打开着的且没有遮罩层，则关闭抽屉栏\n          if (this.$element.hasClass('mdui-drawer-open')) {\n            $.showOverlay();\n            this.overlay = true;\n            $.lockScreen();\n\n            $('.mdui-overlay').one('click', () => this.close());\n          } else {\n            this.state = 'closed';\n          }\n        }\n      }, 100),\n    );\n\n    // 绑定关闭按钮事件\n    this.$element.find('[mdui-drawer-close]').each((_, close) => {\n      $(close).on('click', () => this.close());\n    });\n\n    this.swipeSupport();\n  }\n\n  /**\n   * 是否是桌面设备\n   */\n  private isDesktop(): boolean {\n    return $window.width() >= 1024;\n  }\n\n  /**\n   * 滑动手势支持\n   */\n  private swipeSupport(): void {\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const that = this;\n\n    // 抽屉栏滑动手势控制\n    let openNavEventHandler: (event: Event) => void;\n    let touchStartX: number;\n    let touchStartY: number;\n    let swipeStartX: number;\n    let swiping: null | 'opening' | 'closing' = null;\n    let maybeSwiping = false;\n    const $body = $('body');\n\n    // 手势触发的范围\n    const swipeAreaWidth = 24;\n\n    function setPosition(translateX: number): void {\n      const rtlTranslateMultiplier = that.position === 'right' ? -1 : 1;\n      const transformCSS = `translate(${\n        -1 * rtlTranslateMultiplier * translateX\n      }px, 0) !important;`;\n      const transitionCSS = 'initial !important;';\n\n      that.$element.css(\n        'cssText',\n        `transform: ${transformCSS}; transition: ${transitionCSS};`,\n      );\n    }\n\n    function cleanPosition(): void {\n      that.$element[0].style.transform = '';\n      that.$element[0].style.webkitTransform = '';\n      that.$element[0].style.transition = '';\n      that.$element[0].style.webkitTransition = '';\n    }\n\n    function getMaxTranslateX(): number {\n      return that.$element.width() + 10;\n    }\n\n    function getTranslateX(currentX: number): number {\n      return Math.min(\n        Math.max(\n          swiping === 'closing'\n            ? swipeStartX - currentX\n            : getMaxTranslateX() + swipeStartX - currentX,\n          0,\n        ),\n        getMaxTranslateX(),\n      );\n    }\n\n    function onBodyTouchEnd(event?: Event): void {\n      if (swiping) {\n        let touchX = (event as TouchEvent).changedTouches[0].pageX;\n        if (that.position === 'right') {\n          touchX = $body.width() - touchX;\n        }\n\n        const translateRatio = getTranslateX(touchX) / getMaxTranslateX();\n\n        maybeSwiping = false;\n        const swipingState = swiping;\n        swiping = null;\n\n        if (swipingState === 'opening') {\n          if (translateRatio < 0.92) {\n            cleanPosition();\n            that.open();\n          } else {\n            cleanPosition();\n          }\n        } else {\n          if (translateRatio > 0.08) {\n            cleanPosition();\n            that.close();\n          } else {\n            cleanPosition();\n          }\n        }\n\n        $.unlockScreen();\n      } else {\n        maybeSwiping = false;\n      }\n\n      $body.off({\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        touchmove: onBodyTouchMove,\n        touchend: onBodyTouchEnd,\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        touchcancel: onBodyTouchMove,\n      });\n    }\n\n    function onBodyTouchMove(event: Event): void {\n      let touchX = (event as TouchEvent).touches[0].pageX;\n      if (that.position === 'right') {\n        touchX = $body.width() - touchX;\n      }\n\n      const touchY = (event as TouchEvent).touches[0].pageY;\n\n      if (swiping) {\n        setPosition(getTranslateX(touchX));\n      } else if (maybeSwiping) {\n        const dXAbs = Math.abs(touchX - touchStartX);\n        const dYAbs = Math.abs(touchY - touchStartY);\n        const threshold = 8;\n\n        if (dXAbs > threshold && dYAbs <= threshold) {\n          swipeStartX = touchX;\n          swiping = that.state === 'opened' ? 'closing' : 'opening';\n          $.lockScreen();\n          setPosition(getTranslateX(touchX));\n        } else if (dXAbs <= threshold && dYAbs > threshold) {\n          onBodyTouchEnd();\n        }\n      }\n    }\n\n    function onBodyTouchStart(event: Event): void {\n      touchStartX = (event as TouchEvent).touches[0].pageX;\n      if (that.position === 'right') {\n        touchStartX = $body.width() - touchStartX;\n      }\n\n      touchStartY = (event as TouchEvent).touches[0].pageY;\n\n      if (that.state !== 'opened') {\n        if (\n          touchStartX > swipeAreaWidth ||\n          openNavEventHandler !== onBodyTouchStart\n        ) {\n          return;\n        }\n      }\n\n      maybeSwiping = true;\n\n      $body.on({\n        touchmove: onBodyTouchMove,\n        touchend: onBodyTouchEnd,\n        touchcancel: onBodyTouchMove,\n      });\n    }\n\n    function enableSwipeHandling(): void {\n      if (!openNavEventHandler) {\n        $body.on('touchstart', onBodyTouchStart);\n        openNavEventHandler = onBodyTouchStart;\n      }\n    }\n\n    if (this.options.swipe) {\n      enableSwipeHandling();\n    }\n  }\n\n  /**\n   * 触发组件事件\n   * @param name\n   */\n  private triggerEvent(name: EVENT): void {\n    componentEvent(name, 'drawer', this.$element, this);\n  }\n\n  /**\n   * 动画结束回调\n   */\n  private transitionEnd(): void {\n    if (this.$element.hasClass('mdui-drawer-open')) {\n      this.state = 'opened';\n      this.triggerEvent('opened');\n    } else {\n      this.state = 'closed';\n      this.triggerEvent('closed');\n    }\n  }\n\n  /**\n   * 是否处于打开状态\n   */\n  private isOpen(): boolean {\n    return this.state === 'opening' || this.state === 'opened';\n  }\n\n  /**\n   * 打开抽屉栏\n   */\n  public open(): void {\n    if (this.isOpen()) {\n      return;\n    }\n\n    this.state = 'opening';\n    this.triggerEvent('open');\n\n    if (!this.options.overlay) {\n      $('body').addClass(`mdui-drawer-body-${this.position}`);\n    }\n\n    this.$element\n      .removeClass('mdui-drawer-close')\n      .addClass('mdui-drawer-open')\n      .transitionEnd(() => this.transitionEnd());\n\n    if (!this.isDesktop() || this.options.overlay) {\n      this.overlay = true;\n      $.showOverlay().one('click', () => this.close());\n      $.lockScreen();\n    }\n  }\n\n  /**\n   * 关闭抽屉栏\n   */\n  public close(): void {\n    if (!this.isOpen()) {\n      return;\n    }\n\n    this.state = 'closing';\n    this.triggerEvent('close');\n\n    if (!this.options.overlay) {\n      $('body').removeClass(`mdui-drawer-body-${this.position}`);\n    }\n\n    this.$element\n      .addClass('mdui-drawer-close')\n      .removeClass('mdui-drawer-open')\n      .transitionEnd(() => this.transitionEnd());\n\n    if (this.overlay) {\n      $.hideOverlay();\n      this.overlay = false;\n      $.unlockScreen();\n    }\n  }\n\n  /**\n   * 切换抽屉栏打开/关闭状态\n   */\n  public toggle(): void {\n    this.isOpen() ? this.close() : this.open();\n  }\n\n  /**\n   * 返回当前抽屉栏的状态。共包含四种状态：`opening`、`opened`、`closing`、`closed`\n   */\n  public getState(): STATE {\n    return this.state;\n  }\n}\n\nmdui.Drawer = Drawer;\n", "import $ from 'mdui.jq/es/$';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/on';\nimport mdui from '../../mdui';\nimport '../../global/mutation';\nimport { parseOptions } from '../../utils/parseOptions';\nimport './index';\n\nconst customAttr = 'mdui-drawer';\n\ntype OPTIONS = {\n  target: string;\n  overlay?: boolean;\n  swipe?: boolean;\n};\n\n$(() => {\n  mdui.mutation(`[${customAttr}]`, function () {\n    const $element = $(this);\n    const options = parseOptions(this, customAttr) as OPTIONS;\n    const selector = options.target;\n    // @ts-ignore\n    delete options.target;\n\n    const $drawer = $(selector).first();\n    const instance = new mdui.Drawer($drawer, options);\n\n    $element.on('click', () => instance.toggle());\n  });\n});\n", "import { isUndefined } from 'mdui.jq/es/utils';\nimport PlainObject from 'mdui.jq/es/interfaces/PlainObject';\n\ntype Func = () => any;\n\nconst container: PlainObject<Func[]> = {};\n\n/**\n * 根据队列名，获取队列中所有函数\n * @param name 队列名\n */\nfunction queue(name: string): Func[];\n\n/**\n * 写入队列\n * @param name 队列名\n * @param func 函数\n */\nfunction queue(name: string, func: Func): void;\n\nfunction queue(name: string, func?: Func): void | Func[] {\n  if (isUndefined(container[name])) {\n    container[name] = [];\n  }\n\n  if (isUndefined(func)) {\n    return container[name];\n  }\n\n  container[name].push(func);\n}\n\n/**\n * 从队列中移除第一个函数，并执行该函数\n * @param name 队列满\n */\nfunction dequeue(name: string): void {\n  if (isUndefined(container[name])) {\n    return;\n  }\n\n  if (!container[name].length) {\n    return;\n  }\n\n  const func = container[name].shift()!;\n\n  func();\n}\n\nexport { queue, dequeue };\n", "import $ from 'mdui.jq/es/$';\nimport contains from 'mdui.jq/es/functions/contains';\nimport extend from 'mdui.jq/es/functions/extend';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/append';\nimport 'mdui.jq/es/methods/children';\nimport 'mdui.jq/es/methods/css';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/height';\nimport 'mdui.jq/es/methods/hide';\nimport 'mdui.jq/es/methods/innerHeight';\nimport 'mdui.jq/es/methods/off';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/remove';\nimport 'mdui.jq/es/methods/removeClass';\nimport 'mdui.jq/es/methods/show';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport '../../jq_extends/methods/transitionEnd';\nimport '../../jq_extends/static/hideOverlay';\nimport '../../jq_extends/static/lockScreen';\nimport '../../jq_extends/static/showOverlay';\nimport '../../jq_extends/static/throttle';\nimport '../../jq_extends/static/unlockScreen';\nimport { componentEvent } from '../../utils/componentEvent';\nimport { $window } from '../../utils/dom';\nimport { dequeue, queue } from '../../utils/queue';\n\ntype OPTIONS = {\n  /**\n   * 打开对话框时是否添加 url hash，若为 `true`，则打开对话框后可用过浏览器的后退按钮或 Android 的返回键关闭对话框。\n   */\n  history?: boolean;\n\n  /**\n   * 打开对话框时是否显示遮罩。\n   */\n  overlay?: boolean;\n\n  /**\n   * 是否模态化对话框。为 `false` 时点击对话框外面的区域时关闭对话框，否则不关闭。\n   */\n  modal?: boolean;\n\n  /**\n   * 按下 Esc 键时是否关闭对话框。\n   */\n  closeOnEsc?: boolean;\n\n  /**\n   * 按下取消按钮时是否关闭对话框。\n   */\n  closeOnCancel?: boolean;\n\n  /**\n   * 按下确认按钮时是否关闭对话框。\n   */\n  closeOnConfirm?: boolean;\n\n  /**\n   * 关闭对话框后是否自动销毁对话框。\n   */\n  destroyOnClosed?: boolean;\n};\n\ntype STATE = 'opening' | 'opened' | 'closing' | 'closed';\ntype EVENT = 'open' | 'opened' | 'close' | 'closed' | 'cancel' | 'confirm';\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  history: true,\n  overlay: true,\n  modal: false,\n  closeOnEsc: true,\n  closeOnCancel: true,\n  closeOnConfirm: true,\n  destroyOnClosed: false,\n};\n\n/**\n * 当前显示的对话框实例\n */\nlet currentInst: null | Dialog = null;\n\n/**\n * 队列名\n */\nconst queueName = '_mdui_dialog';\n\n/**\n * 窗口是否已锁定\n */\nlet isLockScreen = false;\n\n/**\n * 遮罩层元素\n */\nlet $overlay: null | JQ;\n\nclass Dialog {\n  /**\n   * dialog 元素的 JQ 对象\n   */\n  public $element: JQ;\n\n  /**\n   * 配置参数\n   */\n  public options: OPTIONS = extend({}, DEFAULT_OPTIONS);\n\n  /**\n   * 当前 dialog 的状态\n   */\n  public state: STATE = 'closed';\n\n  /**\n   * dialog 元素是否是动态添加的\n   */\n  private append = false;\n\n  public constructor(\n    selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    options: OPTIONS = {},\n  ) {\n    this.$element = $(selector).first();\n\n    // 如果对话框元素没有在当前文档中，则需要添加\n    if (!contains(document.body, this.$element[0])) {\n      this.append = true;\n      $('body').append(this.$element);\n    }\n\n    extend(this.options, options);\n\n    // 绑定取消按钮事件\n    this.$element.find('[mdui-dialog-cancel]').each((_, cancel) => {\n      $(cancel).on('click', () => {\n        this.triggerEvent('cancel');\n\n        if (this.options.closeOnCancel) {\n          this.close();\n        }\n      });\n    });\n\n    // 绑定确认按钮事件\n    this.$element.find('[mdui-dialog-confirm]').each((_, confirm) => {\n      $(confirm).on('click', () => {\n        this.triggerEvent('confirm');\n\n        if (this.options.closeOnConfirm) {\n          this.close();\n        }\n      });\n    });\n\n    // 绑定关闭按钮事件\n    this.$element.find('[mdui-dialog-close]').each((_, close) => {\n      $(close).on('click', () => this.close());\n    });\n  }\n\n  /**\n   * 触发组件事件\n   * @param name\n   */\n  private triggerEvent(name: EVENT): void {\n    componentEvent(name, 'dialog', this.$element, this);\n  }\n\n  /**\n   * 窗口宽度变化，或对话框内容变化时，调整对话框位置和对话框内的滚动条\n   */\n  private readjust(): void {\n    if (!currentInst) {\n      return;\n    }\n\n    const $element = currentInst.$element;\n    const $title = $element.children('.mdui-dialog-title');\n    const $content = $element.children('.mdui-dialog-content');\n    const $actions = $element.children('.mdui-dialog-actions');\n\n    // 调整 dialog 的 top 和 height 值\n    $element.height('');\n    $content.height('');\n\n    const elementHeight = $element.height();\n    $element.css({\n      top: `${($window.height() - elementHeight) / 2}px`,\n      height: `${elementHeight}px`,\n    });\n\n    // 调整 mdui-dialog-content 的高度\n    $content.innerHeight(\n      elementHeight -\n        ($title.innerHeight() || 0) -\n        ($actions.innerHeight() || 0),\n    );\n  }\n\n  /**\n   * hashchange 事件触发时关闭对话框\n   */\n  private hashchangeEvent(): void {\n    if (window.location.hash.substring(1).indexOf('mdui-dialog') < 0) {\n      currentInst!.close(true);\n    }\n  }\n\n  /**\n   * 点击遮罩层关闭对话框\n   * @param event\n   */\n  private overlayClick(event: Event): void {\n    if (\n      $(event.target as HTMLElement).hasClass('mdui-overlay') &&\n      currentInst\n    ) {\n      currentInst.close();\n    }\n  }\n\n  /**\n   * 动画结束回调\n   */\n  private transitionEnd(): void {\n    if (this.$element.hasClass('mdui-dialog-open')) {\n      this.state = 'opened';\n      this.triggerEvent('opened');\n    } else {\n      this.state = 'closed';\n      this.triggerEvent('closed');\n      this.$element.hide();\n\n      // 所有对话框都关闭，且当前没有打开的对话框时，解锁屏幕\n      if (!queue(queueName).length && !currentInst && isLockScreen) {\n        $.unlockScreen();\n        isLockScreen = false;\n      }\n\n      $window.off('resize', $.throttle(this.readjust, 100));\n\n      if (this.options.destroyOnClosed) {\n        this.destroy();\n      }\n    }\n  }\n\n  /**\n   * 打开指定对话框\n   */\n  private doOpen(): void {\n    currentInst = this;\n\n    if (!isLockScreen) {\n      $.lockScreen();\n      isLockScreen = true;\n    }\n\n    this.$element.show();\n    this.readjust();\n\n    $window.on('resize', $.throttle(this.readjust, 100));\n\n    // 打开消息框\n    this.state = 'opening';\n    this.triggerEvent('open');\n    this.$element\n      .addClass('mdui-dialog-open')\n      .transitionEnd(() => this.transitionEnd());\n\n    // 不存在遮罩层元素时，添加遮罩层\n    if (!$overlay) {\n      $overlay = $.showOverlay(5100);\n    }\n\n    // 点击遮罩层时是否关闭对话框\n    if (this.options.modal) {\n      $overlay.off('click', this.overlayClick);\n    } else {\n      $overlay.on('click', this.overlayClick);\n    }\n\n    // 是否显示遮罩层，不显示时，把遮罩层背景透明\n    $overlay.css('opacity', this.options.overlay ? '' : 0);\n\n    if (this.options.history) {\n      // 如果 hash 中原来就有 mdui-dialog，先删除，避免后退历史纪录后仍然有 mdui-dialog 导致无法关闭\n      // 包括 mdui-dialog 和 &mdui-dialog 和 ?mdui-dialog\n      let hash = window.location.hash.substring(1);\n      if (hash.indexOf('mdui-dialog') > -1) {\n        hash = hash.replace(/[&?]?mdui-dialog/g, '');\n      }\n\n      // 后退按钮关闭对话框\n      if (hash) {\n        window.location.hash = `${hash}${\n          hash.indexOf('?') > -1 ? '&' : '?'\n        }mdui-dialog`;\n      } else {\n        window.location.hash = 'mdui-dialog';\n      }\n\n      $window.on('hashchange', this.hashchangeEvent);\n    }\n  }\n\n  /**\n   * 当前对话框是否为打开状态\n   */\n  private isOpen(): boolean {\n    return this.state === 'opening' || this.state === 'opened';\n  }\n\n  /**\n   * 打开对话框\n   */\n  public open(): void {\n    if (this.isOpen()) {\n      return;\n    }\n\n    // 如果当前有正在打开或已经打开的对话框,或队列不为空，则先加入队列，等旧对话框开始关闭时再打开\n    if (\n      (currentInst &&\n        (currentInst.state === 'opening' || currentInst.state === 'opened')) ||\n      queue(queueName).length\n    ) {\n      queue(queueName, () => this.doOpen());\n\n      return;\n    }\n\n    this.doOpen();\n  }\n\n  /**\n   * 关闭对话框\n   */\n  public close(historyBack = false): void {\n    // historyBack 是否需要后退历史纪录，默认为 `false`。该参数仅内部使用\n    // 为 `false` 时是通过 js 关闭，需要后退一个历史记录\n    // 为 `true` 时是通过后退按钮关闭，不需要后退历史记录\n\n    // setTimeout 的作用是：\n    // 当同时关闭一个对话框，并打开另一个对话框时，使打开对话框的操作先执行，以使需要打开的对话框先加入队列\n    setTimeout(() => {\n      if (!this.isOpen()) {\n        return;\n      }\n\n      currentInst = null;\n\n      this.state = 'closing';\n      this.triggerEvent('close');\n\n      // 所有对话框都关闭，且当前没有打开的对话框时，隐藏遮罩\n      if (!queue(queueName).length && $overlay) {\n        $.hideOverlay();\n        $overlay = null;\n\n        // 若仍存在遮罩，恢复遮罩的 z-index\n        $('.mdui-overlay').css('z-index', 2000);\n      }\n\n      this.$element\n        .removeClass('mdui-dialog-open')\n        .transitionEnd(() => this.transitionEnd());\n\n      if (this.options.history && !queue(queueName).length) {\n        if (!historyBack) {\n          window.history.back();\n        }\n\n        $window.off('hashchange', this.hashchangeEvent);\n      }\n\n      // 关闭旧对话框，打开新对话框。\n      // 加一点延迟，仅仅为了视觉效果更好。不加延时也不影响功能\n      setTimeout(() => {\n        dequeue(queueName);\n      }, 100);\n    });\n  }\n\n  /**\n   * 切换对话框打开/关闭状态\n   */\n  public toggle(): void {\n    this.isOpen() ? this.close() : this.open();\n  }\n\n  /**\n   * 获取对话框状态。共包含四种状态：`opening`、`opened`、`closing`、`closed`\n   */\n  public getState(): STATE {\n    return this.state;\n  }\n\n  /**\n   * 销毁对话框\n   */\n  public destroy(): void {\n    if (this.append) {\n      this.$element.remove();\n    }\n\n    if (!queue(queueName).length && !currentInst) {\n      if ($overlay) {\n        $.hideOverlay();\n        $overlay = null;\n      }\n\n      if (isLockScreen) {\n        $.unlockScreen();\n        isLockScreen = false;\n      }\n    }\n  }\n\n  /**\n   * 对话框内容变化时，需要调用该方法来调整对话框位置和滚动条高度\n   */\n  public handleUpdate(): void {\n    this.readjust();\n  }\n}\n\nexport { currentInst, OPTIONS, Dialog };\n", "import Selector from 'mdui.jq/es/types/Selector';\nimport mdui from '../../mdui';\nimport 'mdui.jq/es/methods/on';\nimport { $document } from '../../utils/dom';\nimport { currentInst, OPTIONS, Dialog } from './class';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * Dialog 组件\n     *\n     * 请通过 `new mdui.Dialog()` 调用\n     */\n    Dialog: {\n      /**\n       * 实例化 Dialog 组件\n       * @param selector CSS 选择器、或 DOM 元素、或 JQ 对象\n       * @param options 配置参数\n       */\n      new (\n        selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n        options?: OPTIONS,\n      ): Dialog;\n    };\n  }\n}\n\n// esc 按下时关闭对话框\n$document.on('keydown', (event: Event) => {\n  if (\n    currentInst &&\n    currentInst.options.closeOnEsc &&\n    currentInst.state === 'opened' &&\n    (event as KeyboardEvent).keyCode === 27\n  ) {\n    currentInst.close();\n  }\n});\n\nmdui.Dialog = Dialog;\n", "import $ from 'mdui.jq/es/$';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/on';\nimport mdui from '../../mdui';\nimport { $document } from '../../utils/dom';\nimport { parseOptions } from '../../utils/parseOptions';\nimport './index';\n\nconst customAttr = 'mdui-dialog';\nconst dataName = '_mdui_dialog';\n\ntype OPTIONS = {\n  target: string;\n  history?: boolean;\n  overlay?: boolean;\n  modal?: boolean;\n  closeOnEsc?: boolean;\n  closeOnCancel?: boolean;\n  closeOnConfirm?: boolean;\n  destroyOnClosed?: boolean;\n};\n\n$(() => {\n  $document.on('click', `[${customAttr}]`, function () {\n    const options = parseOptions(this as HTMLElement, customAttr) as OPTIONS;\n    const selector = options.target;\n    // @ts-ignore\n    delete options.target;\n\n    const $dialog = $(selector).first();\n    let instance = $dialog.data(dataName);\n\n    if (!instance) {\n      instance = new mdui.Dialog($dialog, options);\n      $dialog.data(dataName, instance);\n    }\n\n    instance.open();\n  });\n});\n", "import $ from 'mdui.jq/es/$';\nimport each from 'mdui.jq/es/functions/each';\nimport extend from 'mdui.jq/es/functions/extend';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/on';\nimport mdui from '../../mdui';\nimport { Dialog } from './class';\nimport './index';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 打开一个对话框，标题、内容、按钮等都可以自定义\n     * @param options 配置参数\n     */\n    dialog(options: OPTIONS): Dialog;\n  }\n}\n\ntype BUTTON = {\n  /**\n   * 按钮文本\n   */\n  text?: string;\n\n  /**\n   * 按钮文本是否加粗，默认为 `false`\n   */\n  bold?: boolean;\n\n  /**\n   * 点击按钮后是否关闭对话框，默认为 `true`\n   */\n  close?: boolean;\n\n  /**\n   * 点击按钮的回调函数，参数为对话框的实例\n   */\n  onClick?: (dialog: Dialog) => void;\n};\n\ntype OPTIONS = {\n  /**\n   * 对话框的标题\n   */\n  title?: string;\n\n  /**\n   * 对话框的内容\n   */\n  content?: string;\n\n  /**\n   * 按钮数组，每个按钮都是一个带按钮参数的对象\n   */\n  buttons?: BUTTON[];\n\n  /**\n   * 按钮是否垂直排列，默认为 `false`\n   */\n  stackedButtons?: boolean;\n\n  /**\n   * 添加到 `.mdui-dialog` 上的 CSS 类\n   */\n  cssClass?: string;\n\n  /**\n   * 是否监听 `hashchange` 事件，为 `true` 时可以通过 Android 的返回键或浏览器后退按钮关闭对话框，默认为 `true`\n   */\n  history?: boolean;\n\n  /**\n   * 打开对话框后是否显示遮罩层，默认为 `true`\n   */\n  overlay?: boolean;\n\n  /**\n   * 是否模态化对话框。为 `false` 时点击对话框外面的区域时关闭对话框，否则不关闭\n   */\n  modal?: boolean;\n\n  /**\n   * 按下 Esc 键时是否关闭对话框，默认为 `true`\n   */\n  closeOnEsc?: boolean;\n\n  /**\n   * 关闭对话框后是否自动销毁对话框，默认为 `true`\n   */\n  destroyOnClosed?: boolean;\n\n  /**\n   * 打开动画开始时的回调，参数为对话框实例\n   */\n  onOpen?: (dialog: Dialog) => void;\n\n  /**\n   * 打开动画结束时的回调，参数为对话框实例\n   */\n  onOpened?: (dialog: Dialog) => void;\n\n  /**\n   * 关闭动画开始时的回调，参数为对话框实例\n   */\n  onClose?: (dialog: Dialog) => void;\n\n  /**\n   * 关闭动画结束时的回调，参数为对话框实例\n   */\n  onClosed?: (dialog: Dialog) => void;\n};\n\nconst DEFAULT_BUTTON: BUTTON = {\n  text: '',\n  bold: false,\n  close: true,\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onClick: () => {},\n};\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  title: '',\n  content: '',\n  buttons: [],\n  stackedButtons: false,\n  cssClass: '',\n  history: true,\n  overlay: true,\n  modal: false,\n  closeOnEsc: true,\n  destroyOnClosed: true,\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onOpen: () => {},\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onOpened: () => {},\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onClose: () => {},\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onClosed: () => {},\n};\n\nmdui.dialog = function (options: OPTIONS): Dialog {\n  // 合并配置参数\n  options = extend({}, DEFAULT_OPTIONS, options);\n\n  each(options.buttons!, (i, button) => {\n    options.buttons![i] = extend({}, DEFAULT_BUTTON, button);\n  });\n\n  // 按钮的 HTML\n  let buttonsHTML = '';\n  if (options.buttons?.length) {\n    buttonsHTML = `<div class=\"mdui-dialog-actions${\n      options.stackedButtons ? ' mdui-dialog-actions-stacked' : ''\n    }\">`;\n\n    each(options.buttons, (_, button) => {\n      buttonsHTML +=\n        '<a href=\"javascript:void(0)\" ' +\n        `class=\"mdui-btn mdui-ripple mdui-text-color-primary ${\n          button.bold ? 'mdui-btn-bold' : ''\n        }\">${button.text}</a>`;\n    });\n\n    buttonsHTML += '</div>';\n  }\n\n  // Dialog 的 HTML\n  const HTML =\n    `<div class=\"mdui-dialog ${options.cssClass}\">` +\n    (options.title\n      ? `<div class=\"mdui-dialog-title\">${options.title}</div>`\n      : '') +\n    (options.content\n      ? `<div class=\"mdui-dialog-content\">${options.content}</div>`\n      : '') +\n    buttonsHTML +\n    '</div>';\n\n  // 实例化 Dialog\n  const instance = new mdui.Dialog(HTML, {\n    history: options.history,\n    overlay: options.overlay,\n    modal: options.modal,\n    closeOnEsc: options.closeOnEsc,\n    destroyOnClosed: options.destroyOnClosed,\n  });\n\n  // 绑定按钮事件\n  if (options.buttons?.length) {\n    instance.$element\n      .find('.mdui-dialog-actions .mdui-btn')\n      .each((index, button) => {\n        $(button).on('click', () => {\n          options.buttons![index].onClick!(instance);\n\n          if (options.buttons![index].close) {\n            instance.close();\n          }\n        });\n      });\n  }\n\n  // 绑定打开关闭事件\n  instance.$element\n    .on('open.mdui.dialog', () => {\n      options.onOpen!(instance);\n    })\n    .on('opened.mdui.dialog', () => {\n      options.onOpened!(instance);\n    })\n    .on('close.mdui.dialog', () => {\n      options.onClose!(instance);\n    })\n    .on('closed.mdui.dialog', () => {\n      options.onClosed!(instance);\n    });\n\n  instance.open();\n\n  return instance;\n};\n", "import extend from 'mdui.jq/es/functions/extend';\nimport { isFunction, isUndefined } from 'mdui.jq/es/utils';\nimport mdui from '../../mdui';\nimport { Dialog } from './class';\nimport './dialog';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 打开一个警告框，可以包含标题、内容和一个确认按钮\n     * @param text 警告框内容\n     * @param title 警告框标题\n     * @param onConfirm 点击确认按钮的回调函数，参数为对话框实例\n     * @param options 配置参数\n     */\n    alert(\n      text: string,\n      title: string,\n      onConfirm?: (dialog: Dialog) => void,\n      options?: OPTIONS,\n    ): Dialog;\n\n    /**\n     * 打开一个警告框，可以包含内容，和一个确认按钮\n     * @param text 警告框内容\n     * @param onConfirm 点击确认按钮的回调函数，参数为对话框实例\n     * @param options 配置参数\n     */\n    alert(\n      text: string,\n      onConfirm?: (dialog: Dialog) => void,\n      options?: OPTIONS,\n    ): Dialog;\n  }\n}\n\ntype OPTIONS = {\n  /**\n   * 确认按钮的文本\n   */\n  confirmText?: string;\n\n  /**\n   * 是否监听 hashchange 事件，为 `true` 时可以通过 Android 的返回键或浏览器后退按钮关闭对话框，默认为 `true`\n   */\n  history?: boolean;\n\n  /**\n   * 是否模态化对话框。为 `false` 时点击对话框外面的区域时关闭对话框，否则不关闭，默认为 `false`\n   */\n  modal?: boolean;\n\n  /**\n   * 按下 Esc 键时是否关闭对话框，默认为 `true`\n   */\n  closeOnEsc?: boolean;\n\n  /**\n   * 是否在按下确认按钮时是否关闭对话框\n   */\n  closeOnConfirm?: boolean;\n};\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  confirmText: 'ok',\n  history: true,\n  modal: false,\n  closeOnEsc: true,\n  closeOnConfirm: true,\n};\n\nmdui.alert = function (\n  text: string,\n  title?: any,\n  onConfirm?: any,\n  options?: any,\n): Dialog {\n  if (isFunction(title)) {\n    options = onConfirm;\n    onConfirm = title;\n    title = '';\n  }\n\n  if (isUndefined(onConfirm)) {\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    onConfirm = (): void => {};\n  }\n\n  if (isUndefined(options)) {\n    options = {};\n  }\n\n  options = extend({}, DEFAULT_OPTIONS, options);\n\n  return mdui.dialog({\n    title: title,\n    content: text,\n    buttons: [\n      {\n        text: options.confirmText,\n        bold: false,\n        close: options.closeOnConfirm,\n        onClick: onConfirm,\n      },\n    ],\n    cssClass: 'mdui-dialog-alert',\n    history: options.history,\n    modal: options.modal,\n    closeOnEsc: options.closeOnEsc,\n  });\n};\n", "import extend from 'mdui.jq/es/functions/extend';\nimport { isFunction, isUndefined } from 'mdui.jq/es/utils';\nimport mdui from '../../mdui';\nimport { Dialog } from './class';\nimport './dialog';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 打开一个确认框，可以包含标题、内容、一个确认按钮和一个取消按钮\n     * @param text 确认框内容\n     * @param title 确认框标题\n     * @param onConfirm 点击确认按钮的回调函数，参数为对话框实例\n     * @param onCancel 点击取消按钮的回调函数，参数为对话框实例\n     * @param options 配置参数\n     */\n    confirm(\n      text: string,\n      title: string,\n      onConfirm?: (dialog: Dialog) => void,\n      onCancel?: (dialog: Dialog) => void,\n      options?: OPTIONS,\n    ): Dialog;\n\n    /**\n     * 打开一个确认框，可以包含内容、一个确认按钮和一个取消按钮\n     * @param text 确认框内容\n     * @param onConfirm 点击确认按钮的回调函数，参数为对话框实例\n     * @param onCancel 点击取消按钮的回调函数，参数为对话框实例\n     * @param options 配置参数\n     */\n    confirm(\n      text: string,\n      onConfirm?: (dialog: Dialog) => void,\n      onCancel?: (dialog: Dialog) => void,\n      options?: OPTIONS,\n    ): Dialog;\n  }\n}\n\ntype OPTIONS = {\n  /**\n   * 确认按钮的文本\n   */\n  confirmText?: string;\n\n  /**\n   * 取消按钮的文本\n   */\n  cancelText?: string;\n\n  /**\n   * 是否监听 hashchange 事件，为 `true` 时可以通过 Android 的返回键或浏览器后退按钮关闭对话框，默认为 `true`\n   */\n  history?: boolean;\n\n  /**\n   * 是否模态化对话框。为 `false` 时点击对话框外面的区域时关闭对话框，否则不关闭，默认为 `false`\n   */\n  modal?: boolean;\n\n  /**\n   * 按下 Esc 键时是否关闭对话框，默认为 `true`\n   */\n  closeOnEsc?: boolean;\n\n  /**\n   * 是否在按下取消按钮时是否关闭对话框\n   */\n  closeOnCancel?: boolean;\n\n  /**\n   * 是否在按下确认按钮时是否关闭对话框\n   */\n  closeOnConfirm?: boolean;\n};\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  confirmText: 'ok',\n  cancelText: 'cancel',\n  history: true,\n  modal: false,\n  closeOnEsc: true,\n  closeOnCancel: true,\n  closeOnConfirm: true,\n};\n\nmdui.confirm = function (\n  text: string,\n  title?: any,\n  onConfirm?: any,\n  onCancel?: any,\n  options?: any,\n): Dialog {\n  if (isFunction(title)) {\n    options = onCancel;\n    onCancel = onConfirm;\n    onConfirm = title;\n    title = '';\n  }\n\n  if (isUndefined(onConfirm)) {\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    onConfirm = (): void => {};\n  }\n\n  if (isUndefined(onCancel)) {\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    onCancel = (): void => {};\n  }\n\n  if (isUndefined(options)) {\n    options = {};\n  }\n\n  options = extend({}, DEFAULT_OPTIONS, options);\n\n  return mdui.dialog({\n    title: title,\n    content: text,\n    buttons: [\n      {\n        text: options.cancelText,\n        bold: false,\n        close: options.closeOnCancel,\n        onClick: onCancel,\n      },\n      {\n        text: options.confirmText,\n        bold: false,\n        close: options.closeOnConfirm,\n        onClick: onConfirm,\n      },\n    ],\n    cssClass: 'mdui-dialog-confirm',\n    history: options.history,\n    modal: options.modal,\n    closeOnEsc: options.closeOnEsc,\n  });\n};\n", "import extend from 'mdui.jq/es/functions/extend';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/val';\nimport { isFunction, isUndefined } from 'mdui.jq/es/utils';\nimport mdui from '../../mdui';\nimport '../textfield';\nimport { Dialog } from './class';\nimport './dialog';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 打开一个提示用户输入的对话框，可以包含标题、文本框标签、文本框、一个确认按钮和一个取消按钮\n     * @param label 文本框浮动标签的文本\n     * @param title 标题\n     * @param onConfirm 点击确认按钮的回调。含两个参数，分别为文本框的值和对话框实例\n     * @param onCancel 点击取消按钮的回调。含两个参数，分别为文本框的值和对话框实例\n     * @param options 配置参数\n     */\n    prompt(\n      label: string,\n      title: string,\n      onConfirm?: (value: string, dialog: Dialog) => void,\n      onCancel?: (value: string, dialog: Dialog) => void,\n      options?: OPTIONS,\n    ): Dialog;\n\n    /**\n     * 打开一个提示用户输入的对话框，可以包含文本框标签、文本框、一个确认按钮和一个取消按钮\n     * @param label 文本框浮动标签的文本\n     * @param onConfirm 点击确认按钮的回调。含两个参数，分别为文本框的值和对话框实例\n     * @param onCancel 点击取消按钮的回调，含两个参数，分别为文本框的值和对话框实例\n     * @param options 配置参数\n     */\n    prompt(\n      label: string,\n      onConfirm?: (value: string, dialog: Dialog) => void,\n      onCancel?: (value: string, dialog: Dialog) => void,\n      options?: OPTIONS,\n    ): Dialog;\n  }\n}\n\ntype OPTIONS = {\n  /**\n   * 确认按钮的文本\n   */\n  confirmText?: string;\n\n  /**\n   * 取消按钮的文本\n   */\n  cancelText?: string;\n\n  /**\n   * 是否监听 hashchange 事件，为 `true` 时可以通过 Android 的返回键或浏览器后退按钮关闭对话框，默认为 `true`\n   */\n  history?: boolean;\n\n  /**\n   * 是否模态化对话框。为 `false` 时点击对话框外面的区域时关闭对话框，否则不关闭，默认为 `false`\n   */\n  modal?: boolean;\n\n  /**\n   * 是否在按下 Esc 键时是否关闭对话框，默认为 `true`\n   */\n  closeOnEsc?: boolean;\n\n  /**\n   * 是否在按下取消按钮时是否关闭对话框\n   */\n  closeOnCancel?: boolean;\n\n  /**\n   * 是否在按下确认按钮时是否关闭对话框\n   */\n  closeOnConfirm?: boolean;\n\n  /**\n   * 是否在按下 Enter 键时触发 `onConfirm` 回调函数，默认为 `false`\n   */\n  confirmOnEnter?: boolean;\n\n  /**\n   * 文本框的类型。`text`: 单行文本框； `textarea`: 多行文本框\n   */\n  type?: 'text' | 'textarea';\n\n  /**\n   * 最大输入字符数量，为 0 时表示不限制\n   */\n  maxlength?: number;\n\n  /**\n   * 文本框的默认值\n   */\n  defaultValue?: string;\n};\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  confirmText: 'ok',\n  cancelText: 'cancel',\n  history: true,\n  modal: false,\n  closeOnEsc: true,\n  closeOnCancel: true,\n  closeOnConfirm: true,\n  type: 'text',\n  maxlength: 0,\n  defaultValue: '',\n  confirmOnEnter: false,\n};\n\nmdui.prompt = function (\n  label: string,\n  title?: any,\n  onConfirm?: any,\n  onCancel?: any,\n  options?: any,\n): Dialog {\n  if (isFunction(title)) {\n    options = onCancel;\n    onCancel = onConfirm;\n    onConfirm = title;\n    title = '';\n  }\n\n  if (isUndefined(onConfirm)) {\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    onConfirm = (): void => {};\n  }\n\n  if (isUndefined(onCancel)) {\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    onCancel = (): void => {};\n  }\n\n  if (isUndefined(options)) {\n    options = {};\n  }\n\n  options = extend({}, DEFAULT_OPTIONS, options);\n\n  const content =\n    '<div class=\"mdui-textfield\">' +\n    (label ? `<label class=\"mdui-textfield-label\">${label}</label>` : '') +\n    (options.type === 'text'\n      ? `<input class=\"mdui-textfield-input\" type=\"text\" value=\"${\n          options.defaultValue\n        }\" ${\n          options.maxlength ? 'maxlength=\"' + options.maxlength + '\"' : ''\n        }/>`\n      : '') +\n    (options.type === 'textarea'\n      ? `<textarea class=\"mdui-textfield-input\" ${\n          options.maxlength ? 'maxlength=\"' + options.maxlength + '\"' : ''\n        }>${options.defaultValue}</textarea>`\n      : '') +\n    '</div>';\n\n  const onCancelClick = (dialog: Dialog): void => {\n    const value = dialog.$element.find('.mdui-textfield-input').val();\n    onCancel(value, dialog);\n  };\n\n  const onConfirmClick = (dialog: Dialog): void => {\n    const value = dialog.$element.find('.mdui-textfield-input').val();\n    onConfirm(value, dialog);\n  };\n\n  return mdui.dialog({\n    title,\n    content,\n    buttons: [\n      {\n        text: options.cancelText,\n        bold: false,\n        close: options.closeOnCancel,\n        onClick: onCancelClick,\n      },\n      {\n        text: options.confirmText,\n        bold: false,\n        close: options.closeOnConfirm,\n        onClick: onConfirmClick,\n      },\n    ],\n    cssClass: 'mdui-dialog-prompt',\n    history: options.history,\n    modal: options.modal,\n    closeOnEsc: options.closeOnEsc,\n    onOpen: (dialog) => {\n      // 初始化输入框\n      const $input = dialog.$element.find('.mdui-textfield-input');\n      mdui.updateTextFields($input);\n\n      // 聚焦到输入框\n      $input[0].focus();\n\n      // 捕捉文本框回车键，在单行文本框的情况下触发回调\n      if (options.type !== 'textarea' && options.confirmOnEnter === true) {\n        $input.on('keydown', (event) => {\n          if ((event as KeyboardEvent).keyCode === 13) {\n            const value = dialog.$element.find('.mdui-textfield-input').val();\n            onConfirm(value, dialog);\n\n            if (options.closeOnConfirm) {\n              dialog.close();\n            }\n\n            return false;\n          }\n\n          return;\n        });\n      }\n\n      // 如果是多行输入框，监听输入框的 input 事件，更新对话框高度\n      if (options.type === 'textarea') {\n        $input.on('input', () => dialog.handleUpdate());\n      }\n\n      // 有字符数限制时，加载完文本框后 DOM 会变化，需要更新对话框高度\n      if (options.maxlength) {\n        dialog.handleUpdate();\n      }\n    },\n  });\n};\n", "import $ from 'mdui.jq/es/$';\nimport extend from 'mdui.jq/es/functions/extend';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/appendTo';\nimport 'mdui.jq/es/methods/attr';\nimport 'mdui.jq/es/methods/css';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/height';\nimport 'mdui.jq/es/methods/html';\nimport 'mdui.jq/es/methods/offset';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/removeClass';\nimport 'mdui.jq/es/methods/width';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport mdui from '../../mdui';\nimport '../../jq_extends/methods/transformOrigin';\nimport '../../jq_extends/methods/transitionEnd';\nimport '../../jq_extends/static/guid';\nimport { componentEvent } from '../../utils/componentEvent';\nimport { $window } from '../../utils/dom';\nimport { isAllow, register, unlockEvent } from '../../utils/touchHandler';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * Tooltip 组件\n     *\n     * 请通过 `new mdui.Tooltip()` 调用\n     */\n    Tooltip: {\n      /**\n       * 实例化 Tooltip 组件\n       * @param selector CSS 选择器、或 DOM 元素、或 JQ 对象\n       * @param options 配置参数\n       */\n      new (\n        selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n        options?: OPTIONS,\n      ): Tooltip;\n    };\n  }\n}\n\ntype POSITION = 'auto' | 'bottom' | 'top' | 'left' | 'right';\n\ntype OPTIONS = {\n  /**\n   * Tooltip 的位置。取值范围包括 `auto`、`bottom`、`top`、`left`、`right`。\n   * 为 `auto` 时，会自动判断位置。默认在下方。优先级为 `bottom` > `top` > `left` > `right`。\n   * 默认为 `auto`\n   */\n  position?: POSITION;\n\n  /**\n   * 延时触发，单位毫秒。默认为 `0`，即没有延时。\n   */\n  delay?: number;\n\n  /**\n   * Tooltip 的内容\n   */\n  content?: string;\n};\n\ntype STATE = 'opening' | 'opened' | 'closing' | 'closed';\ntype EVENT = 'open' | 'opened' | 'close' | 'closed';\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  position: 'auto',\n  delay: 0,\n  content: '',\n};\n\nclass Tooltip {\n  /**\n   * 触发 tooltip 元素的 JQ 对象\n   */\n  public $target: JQ;\n\n  /**\n   * tooltip 元素的 JQ 对象\n   */\n  public $element: JQ;\n\n  /**\n   * 配置参数\n   */\n  public options: OPTIONS = extend({}, DEFAULT_OPTIONS);\n\n  /**\n   * 当前 tooltip 的状态\n   */\n  private state: STATE = 'closed';\n\n  /**\n   * setTimeout 的返回值\n   */\n  private timeoutId: any = null;\n\n  public constructor(\n    selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    options: OPTIONS = {},\n  ) {\n    this.$target = $(selector).first();\n\n    extend(this.options, options);\n\n    // 创建 Tooltip HTML\n    this.$element = $(\n      `<div class=\"mdui-tooltip\" id=\"${$.guid()}\">${\n        this.options.content\n      }</div>`,\n    ).appendTo(document.body);\n\n    // 绑定事件。元素处于 disabled 状态时无法触发鼠标事件，为了统一，把 touch 事件也禁用\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const that = this;\n    this.$target\n      .on('touchstart mouseenter', function (event) {\n        if (that.isDisabled(this as HTMLElement)) {\n          return;\n        }\n\n        if (!isAllow(event)) {\n          return;\n        }\n\n        register(event);\n\n        that.open();\n      })\n      .on('touchend mouseleave', function (event) {\n        if (that.isDisabled(this as HTMLElement)) {\n          return;\n        }\n\n        if (!isAllow(event)) {\n          return;\n        }\n\n        that.close();\n      })\n      .on(unlockEvent, function (event) {\n        if (that.isDisabled(this as HTMLElement)) {\n          return;\n        }\n\n        register(event);\n      });\n  }\n\n  /**\n   * 元素是否已禁用\n   * @param element\n   */\n  private isDisabled(element: HTMLElement): boolean {\n    return (\n      (element as HTMLInputElement).disabled ||\n      $(element).attr('disabled') !== undefined\n    );\n  }\n\n  /**\n   * 是否是桌面设备\n   */\n  private isDesktop(): boolean {\n    return $window.width() > 1024;\n  }\n\n  /**\n   * 设置 Tooltip 的位置\n   */\n  private setPosition(): void {\n    let marginLeft: number;\n    let marginTop: number;\n\n    // 触发的元素\n    const targetProps = this.$target[0].getBoundingClientRect();\n\n    // 触发的元素和 Tooltip 之间的距离\n    const targetMargin = this.isDesktop() ? 14 : 24;\n\n    // Tooltip 的宽度和高度\n    const tooltipWidth = this.$element[0].offsetWidth;\n    const tooltipHeight = this.$element[0].offsetHeight;\n\n    // Tooltip 的方向\n    let position: POSITION = this.options.position!;\n\n    // 自动判断位置，加 2px，使 Tooltip 距离窗口边框至少有 2px 的间距\n    if (position === 'auto') {\n      if (\n        targetProps.top +\n          targetProps.height +\n          targetMargin +\n          tooltipHeight +\n          2 <\n        $window.height()\n      ) {\n        position = 'bottom';\n      } else if (targetMargin + tooltipHeight + 2 < targetProps.top) {\n        position = 'top';\n      } else if (targetMargin + tooltipWidth + 2 < targetProps.left) {\n        position = 'left';\n      } else if (\n        targetProps.width + targetMargin + tooltipWidth + 2 <\n        $window.width() - targetProps.left\n      ) {\n        position = 'right';\n      } else {\n        position = 'bottom';\n      }\n    }\n\n    // 设置位置\n    switch (position) {\n      case 'bottom':\n        marginLeft = -1 * (tooltipWidth / 2);\n        marginTop = targetProps.height / 2 + targetMargin;\n        this.$element.transformOrigin('top center');\n        break;\n\n      case 'top':\n        marginLeft = -1 * (tooltipWidth / 2);\n        marginTop =\n          -1 * (tooltipHeight + targetProps.height / 2 + targetMargin);\n        this.$element.transformOrigin('bottom center');\n        break;\n\n      case 'left':\n        marginLeft = -1 * (tooltipWidth + targetProps.width / 2 + targetMargin);\n        marginTop = -1 * (tooltipHeight / 2);\n        this.$element.transformOrigin('center right');\n        break;\n\n      case 'right':\n        marginLeft = targetProps.width / 2 + targetMargin;\n        marginTop = -1 * (tooltipHeight / 2);\n        this.$element.transformOrigin('center left');\n        break;\n    }\n\n    const targetOffset = this.$target.offset();\n\n    this.$element.css({\n      top: `${targetOffset.top + targetProps.height / 2}px`,\n      left: `${targetOffset.left + targetProps.width / 2}px`,\n      'margin-left': `${marginLeft}px`,\n      'margin-top': `${marginTop}px`,\n    });\n  }\n\n  /**\n   * 触发组件事件\n   * @param name\n   */\n  private triggerEvent(name: EVENT): void {\n    componentEvent(name, 'tooltip', this.$target, this);\n  }\n\n  /**\n   * 动画结束回调\n   */\n  private transitionEnd(): void {\n    if (this.$element.hasClass('mdui-tooltip-open')) {\n      this.state = 'opened';\n      this.triggerEvent('opened');\n    } else {\n      this.state = 'closed';\n      this.triggerEvent('closed');\n    }\n  }\n\n  /**\n   * 当前 tooltip 是否为打开状态\n   */\n  private isOpen(): boolean {\n    return this.state === 'opening' || this.state === 'opened';\n  }\n\n  /**\n   * 执行打开 tooltip\n   */\n  private doOpen(): void {\n    this.state = 'opening';\n    this.triggerEvent('open');\n\n    this.$element\n      .addClass('mdui-tooltip-open')\n      .transitionEnd(() => this.transitionEnd());\n  }\n\n  /**\n   * 打开 Tooltip\n   * @param options 允许每次打开时设置不同的参数\n   */\n  public open(options?: OPTIONS): void {\n    if (this.isOpen()) {\n      return;\n    }\n\n    const oldOptions = extend({}, this.options);\n\n    if (options) {\n      extend(this.options, options);\n    }\n\n    // tooltip 的内容有更新\n    if (oldOptions.content !== this.options.content) {\n      this.$element.html(this.options.content);\n    }\n\n    this.setPosition();\n\n    if (this.options.delay) {\n      this.timeoutId = setTimeout(() => this.doOpen(), this.options.delay);\n    } else {\n      this.timeoutId = null;\n      this.doOpen();\n    }\n  }\n\n  /**\n   * 关闭 Tooltip\n   */\n  public close(): void {\n    if (this.timeoutId) {\n      clearTimeout(this.timeoutId);\n      this.timeoutId = null;\n    }\n\n    if (!this.isOpen()) {\n      return;\n    }\n\n    this.state = 'closing';\n    this.triggerEvent('close');\n\n    this.$element\n      .removeClass('mdui-tooltip-open')\n      .transitionEnd(() => this.transitionEnd());\n  }\n\n  /**\n   * 切换 Tooltip 的打开状态\n   */\n  public toggle(): void {\n    this.isOpen() ? this.close() : this.open();\n  }\n\n  /**\n   * 获取 Tooltip 状态。共包含四种状态：`opening`、`opened`、`closing`、`closed`\n   */\n  public getState(): STATE {\n    return this.state;\n  }\n}\n\nmdui.Tooltip = Tooltip;\n", "import $ from 'mdui.jq/es/$';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/on';\nimport mdui from '../../mdui';\nimport { $document } from '../../utils/dom';\nimport { parseOptions } from '../../utils/parseOptions';\nimport './index';\n\nconst customAttr = 'mdui-tooltip';\nconst dataName = '_mdui_tooltip';\n\n$(() => {\n  // mouseenter 不能冒泡，所以这里用 mouseover 代替\n  $document.on('touchstart mouseover', `[${customAttr}]`, function () {\n    const $target = $(this);\n    let instance = $target.data(dataName);\n\n    if (!instance) {\n      instance = new mdui.Tooltip(\n        this as HTMLElement,\n        parseOptions(this as HTMLElement, customAttr),\n      );\n      $target.data(dataName, instance);\n    }\n  });\n});\n", "import $ from 'mdui.jq/es/$';\nimport extend from 'mdui.jq/es/functions/extend';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/appendTo';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/off';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/parents';\nimport 'mdui.jq/es/methods/remove';\nimport { isString } from 'mdui.jq/es/utils';\nimport mdui from '../../mdui';\nimport '../../jq_extends/methods/reflow';\nimport '../../jq_extends/methods/transform';\nimport '../../jq_extends/methods/transitionEnd';\nimport { $document } from '../../utils/dom';\nimport { dequeue, queue } from '../../utils/queue';\nimport { startEvent } from '../../utils/touchHandler';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 打开一个 Snackbar\n     * @param message Snackbar 的文本\n     * @param options 配置参数\n     */\n    snackbar(message: string, options?: OPTIONS): Snackbar;\n\n    /**\n     * 打开一个 Snackbar\n     * @param options 配置参数\n     */\n    snackbar(options: OPTIONS): Snackbar;\n  }\n}\n\ntype OPTIONS = {\n  /**\n   * Snackbar 的文本。通过 `mdui.snackbar(options)` 调用时，该参数不能为空\n   */\n  message?: string;\n\n  /**\n   * 在用户没有操作时多长时间自动隐藏，单位（毫秒）。为 `0` 时表示不自动关闭，默认为 `4000`\n   */\n  timeout?: number;\n\n  /**\n   * Snackbar 的位置，默认为 `bottom`。\n   * 取值范围包括：\n   *   `bottom`: 下方\n   *   `top`: 上方\n   *   `left-top`: 左上角\n   *   `left-bottom`: 左下角\n   *   `right-top`: 右上角\n   *   `right-bottom`: 右下角\n   */\n  position?:\n    | 'bottom'\n    | 'top'\n    | 'left-top'\n    | 'left-bottom'\n    | 'right-top'\n    | 'right-bottom';\n\n  /**\n   * 按钮的文本\n   */\n  buttonText?: string;\n\n  /**\n   * 按钮的文本颜色，可以是颜色名或颜色值，如 `red`、`#ffffff`、`rgba(255, 255, 255, 0.3)` 等。默认为 `#90CAF9`\n   */\n  buttonColor?: string;\n\n  /**\n   * 点击按钮时是否关闭 Snackbar，默认为 `true`\n   */\n  closeOnButtonClick?: boolean;\n\n  /**\n   * 点击或触摸 Snackbar 以外的区域时是否关闭 Snackbar，默认为 `true`\n   */\n  closeOnOutsideClick?: boolean;\n\n  /**\n   * 在 Snackbar 上点击的回调函数，参数为 Snackbar 的实例\n   */\n  onClick?: (snackbar: Snackbar) => void;\n\n  /**\n   * 点击 Snackbar 上的按钮时的回调函数，参数为 Snackbar 的实例\n   */\n  onButtonClick?: (snackbar: Snackbar) => void;\n\n  /**\n   * Snackbar 开始打开时的回调函数，参数为 Snackbar 的实例\n   */\n  onOpen?: (snackbar: Snackbar) => void;\n\n  /**\n   * Snackbar 打开后的回调函数，参数为 Snackbar 的实例\n   */\n  onOpened?: (snackbar: Snackbar) => void;\n\n  /**\n   * Snackbar 开始关闭时的回调函数，参数为 Snackbar 的实例\n   */\n  onClose?: (snackbar: Snackbar) => void;\n\n  /**\n   * Snackbar 关闭后的回调函数，参数为 Snackbar 的实例\n   */\n  onClosed?: (snackbar: Snackbar) => void;\n};\n\ntype STATE = 'opening' | 'opened' | 'closing' | 'closed';\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  message: '',\n  timeout: 4000,\n  position: 'bottom',\n  buttonText: '',\n  buttonColor: '',\n  closeOnButtonClick: true,\n  closeOnOutsideClick: true,\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onClick: () => {},\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onButtonClick: () => {},\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onOpen: () => {},\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onOpened: () => {},\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onClose: () => {},\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onClosed: () => {},\n};\n\n/**\n * 当前打开着的 Snackbar\n */\nlet currentInst: null | Snackbar = null;\n\n/**\n * 队列名\n */\nconst queueName = '_mdui_snackbar';\n\nclass Snackbar {\n  /**\n   * Snackbar 元素\n   */\n  public $element: JQ;\n  /**\n   * 配置参数\n   */\n  public options: OPTIONS = extend({}, DEFAULT_OPTIONS);\n\n  /**\n   * 当前 Snackbar 的状态\n   */\n  private state: STATE = 'closed';\n\n  /**\n   * setTimeout 的 ID\n   */\n  private timeoutId: any = null;\n\n  public constructor(options: OPTIONS) {\n    extend(this.options, options);\n\n    // 按钮颜色\n    let buttonColorStyle = '';\n    let buttonColorClass = '';\n\n    if (\n      this.options.buttonColor!.indexOf('#') === 0 ||\n      this.options.buttonColor!.indexOf('rgb') === 0\n    ) {\n      buttonColorStyle = `style=\"color:${this.options.buttonColor}\"`;\n    } else if (this.options.buttonColor !== '') {\n      buttonColorClass = `mdui-text-color-${this.options.buttonColor}`;\n    }\n\n    // 添加 HTML\n    this.$element = $(\n      '<div class=\"mdui-snackbar\">' +\n        `<div class=\"mdui-snackbar-text\">${this.options.message}</div>` +\n        (this.options.buttonText\n          ? `<a href=\"javascript:void(0)\" class=\"mdui-snackbar-action mdui-btn mdui-ripple mdui-ripple-white ${buttonColorClass}\" ${buttonColorStyle}>${this.options.buttonText}</a>`\n          : '') +\n        '</div>',\n    ).appendTo(document.body);\n\n    // 设置位置\n    this.setPosition('close');\n\n    this.$element.reflow().addClass(`mdui-snackbar-${this.options.position}`);\n  }\n\n  /**\n   * 点击 Snackbar 外面的区域关闭\n   * @param event\n   */\n  private closeOnOutsideClick(event: Event): void {\n    const $target = $(event.target as HTMLElement);\n\n    if (\n      !$target.hasClass('mdui-snackbar') &&\n      !$target.parents('.mdui-snackbar').length\n    ) {\n      currentInst!.close();\n    }\n  }\n\n  /**\n   * 设置 Snackbar 的位置\n   * @param state\n   */\n  private setPosition(state: 'open' | 'close'): void {\n    const snackbarHeight = this.$element[0].clientHeight;\n    const position = this.options.position;\n\n    let translateX;\n    let translateY;\n\n    // translateX\n    if (position === 'bottom' || position === 'top') {\n      translateX = '-50%';\n    } else {\n      translateX = '0';\n    }\n\n    // translateY\n    if (state === 'open') {\n      translateY = '0';\n    } else {\n      if (position === 'bottom') {\n        translateY = snackbarHeight;\n      }\n\n      if (position === 'top') {\n        translateY = -snackbarHeight;\n      }\n\n      if (position === 'left-top' || position === 'right-top') {\n        translateY = -snackbarHeight - 24;\n      }\n\n      if (position === 'left-bottom' || position === 'right-bottom') {\n        translateY = snackbarHeight + 24;\n      }\n    }\n\n    this.$element.transform(`translate(${translateX},${translateY}px`);\n  }\n\n  /**\n   * 打开 Snackbar\n   */\n  public open(): void {\n    if (this.state === 'opening' || this.state === 'opened') {\n      return;\n    }\n\n    // 如果当前有正在显示的 Snackbar，则先加入队列，等旧 Snackbar 关闭后再打开\n    if (currentInst) {\n      queue(queueName, () => this.open());\n      return;\n    }\n\n    currentInst = this;\n\n    // 开始打开\n    this.state = 'opening';\n    this.options.onOpen!(this);\n\n    this.setPosition('open');\n\n    this.$element.transitionEnd(() => {\n      if (this.state !== 'opening') {\n        return;\n      }\n\n      this.state = 'opened';\n      this.options.onOpened!(this);\n\n      // 有按钮时绑定事件\n      if (this.options.buttonText) {\n        this.$element.find('.mdui-snackbar-action').on('click', () => {\n          this.options.onButtonClick!(this);\n          if (this.options.closeOnButtonClick) {\n            this.close();\n          }\n        });\n      }\n\n      // 点击 snackbar 的事件\n      this.$element.on('click', (event) => {\n        if (!$(event.target as HTMLElement).hasClass('mdui-snackbar-action')) {\n          this.options.onClick!(this);\n        }\n      });\n\n      // 点击 Snackbar 外面的区域关闭\n      if (this.options.closeOnOutsideClick) {\n        $document.on(startEvent, this.closeOnOutsideClick);\n      }\n\n      // 超时后自动关闭\n      if (this.options.timeout) {\n        this.timeoutId = setTimeout(() => this.close(), this.options.timeout);\n      }\n    });\n  }\n\n  /**\n   * 关闭 Snackbar\n   */\n  public close(): void {\n    if (this.state === 'closing' || this.state === 'closed') {\n      return;\n    }\n\n    if (this.timeoutId) {\n      clearTimeout(this.timeoutId);\n    }\n\n    if (this.options.closeOnOutsideClick) {\n      $document.off(startEvent, this.closeOnOutsideClick);\n    }\n\n    this.state = 'closing';\n    this.options.onClose!(this);\n\n    this.setPosition('close');\n\n    this.$element.transitionEnd(() => {\n      if (this.state !== 'closing') {\n        return;\n      }\n\n      currentInst = null;\n      this.state = 'closed';\n      this.options.onClosed!(this);\n      this.$element.remove();\n      dequeue(queueName);\n    });\n  }\n}\n\nmdui.snackbar = function (message: any, options: any = {}): Snackbar {\n  if (isString(message)) {\n    options.message = message;\n  } else {\n    options = message;\n  }\n\n  const instance = new Snackbar(options);\n\n  instance.open();\n\n  return instance;\n};\n", "import $ from 'mdui.jq/es/$';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/children';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/is';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/parent';\nimport 'mdui.jq/es/methods/removeClass';\nimport mdui from '../../mdui';\nimport '../../global/mutation';\nimport { componentEvent } from '../../utils/componentEvent';\nimport { $document } from '../../utils/dom';\nimport '../headroom';\n\n$(() => {\n  // 切换导航项\n  $document.on('click', '.mdui-bottom-nav>a', function () {\n    const $item = $(this as HTMLElement);\n    const $bottomNav = $item.parent();\n\n    $bottomNav.children('a').each((index, item) => {\n      const isThis = $item.is(item);\n\n      if (isThis) {\n        componentEvent('change', 'bottomNav', $bottomNav[0], undefined, {\n          index,\n        });\n      }\n\n      isThis\n        ? $(item).addClass('mdui-bottom-nav-active')\n        : $(item).removeClass('mdui-bottom-nav-active');\n    });\n  });\n\n  // 滚动时隐藏 mdui-bottom-nav-scroll-hide\n  mdui.mutation('.mdui-bottom-nav-scroll-hide', function () {\n    new mdui.Headroom(this, {\n      pinnedClass: 'mdui-headroom-pinned-down',\n      unpinnedClass: 'mdui-headroom-unpinned-down',\n    });\n  });\n});\n", "import $ from 'mdui.jq/es/$';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/html';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport { isUndefined } from 'mdui.jq/es/utils';\nimport mdui from '../../mdui';\nimport '../../global/mutation';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 如果需要修改已有的圆形进度条组件，需要调用该方法来重新初始化组件。\n     *\n     * 若传入了参数，则只重新初始化该参数对应的圆形进度条。若没有传入参数，则重新初始化所有圆形进度条。\n     * @param selector CSS 选择器、或 DOM 元素、或 DOM 元素组成的数组、或 JQ 对象\n     */\n    updateSpinners(\n      selector?: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    ): void;\n  }\n}\n\n/**\n * layer 的 HTML 结构\n * @param index\n */\nfunction layerHTML(index: number | false = false): string {\n  return (\n    `<div class=\"mdui-spinner-layer ${\n      index ? `mdui-spinner-layer-${index}` : ''\n    }\">` +\n    '<div class=\"mdui-spinner-circle-clipper mdui-spinner-left\">' +\n    '<div class=\"mdui-spinner-circle\"></div>' +\n    '</div>' +\n    '<div class=\"mdui-spinner-gap-patch\">' +\n    '<div class=\"mdui-spinner-circle\"></div>' +\n    '</div>' +\n    '<div class=\"mdui-spinner-circle-clipper mdui-spinner-right\">' +\n    '<div class=\"mdui-spinner-circle\"></div>' +\n    '</div>' +\n    '</div>'\n  );\n}\n\n/**\n * 填充 HTML\n * @param spinner\n */\nfunction fillHTML(spinner: HTMLElement): void {\n  const $spinner = $(spinner);\n\n  const layer = $spinner.hasClass('mdui-spinner-colorful')\n    ? layerHTML(1) + layerHTML(2) + layerHTML(3) + layerHTML(4)\n    : layerHTML();\n\n  $spinner.html(layer);\n}\n\n$(() => {\n  // 页面加载完后自动填充 HTML 结构\n  mdui.mutation('.mdui-spinner', function () {\n    fillHTML(this);\n  });\n});\n\nmdui.updateSpinners = function (\n  selector?: Selector | HTMLElement | ArrayLike<HTMLElement>,\n): void {\n  const $elements = isUndefined(selector) ? $('.mdui-spinner') : $(selector);\n\n  $elements.each(function () {\n    fillHTML(this);\n  });\n};\n", "import $ from 'mdui.jq/es/$';\nimport contains from 'mdui.jq/es/functions/contains';\nimport extend from 'mdui.jq/es/functions/extend';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/attr';\nimport 'mdui.jq/es/methods/children';\nimport 'mdui.jq/es/methods/css';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/height';\nimport 'mdui.jq/es/methods/is';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/parent';\nimport 'mdui.jq/es/methods/parents';\nimport 'mdui.jq/es/methods/removeClass';\nimport 'mdui.jq/es/methods/width';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport mdui from '../../mdui';\nimport '../../jq_extends/methods/transformOrigin';\nimport '../../jq_extends/methods/transitionEnd';\nimport '../../jq_extends/static/throttle';\nimport { componentEvent } from '../../utils/componentEvent';\nimport { $document, $window } from '../../utils/dom';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * Menu 组件\n     *\n     * 请通过 `new mdui.Menu()` 调用\n     */\n    Menu: {\n      /**\n       * 实例化 Menu 组件\n       * @param anchorSelector 触发菜单的元素的 CSS 选择器、或 DOM 元素、或 JQ 对象\n       * @param menuSelector 菜单的 CSS 选择器、或 DOM 元素、或 JQ 对象\n       * @param options 配置参数\n       */\n      new (\n        anchorSelector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n        menuSelector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n        options?: OPTIONS,\n      ): Menu;\n    };\n  }\n}\n\ntype OPTIONS = {\n  /**\n   * 菜单相对于触发它的元素的位置，默认为 `auto`。\n   * 取值范围包括：\n   *   `top`: 菜单在触发它的元素的上方\n   *   `bottom`: 菜单在触发它的元素的下方\n   *   `center`: 菜单在窗口中垂直居中\n   *   `auto`: 自动判断位置。优先级为：`bottom` > `top` > `center`\n   */\n  position?: 'auto' | 'top' | 'bottom' | 'center';\n\n  /**\n   * 菜单与触发它的元素的对其方式，默认为 `auto`。\n   * 取值范围包括：\n   *   `left`: 菜单与触发它的元素左对齐\n   *   `right`: 菜单与触发它的元素右对齐\n   *   `center`: 菜单在窗口中水平居中\n   *   `auto`: 自动判断位置：优先级为：`left` > `right` > `center`\n   */\n  align?: 'auto' | 'left' | 'right' | 'center';\n\n  /**\n   * 菜单与窗口边框至少保持多少间距，单位为 px，默认为 `16`\n   */\n  gutter?: number;\n\n  /**\n   * 菜单的定位方式，默认为 `false`。\n   * 为 `true` 时，菜单使用 fixed 定位。在页面滚动时，菜单将保持在窗口固定位置，不随滚动条滚动。\n   * 为 `false` 时，菜单使用 absolute 定位。在页面滚动时，菜单将随着页面一起滚动。\n   */\n  fixed?: boolean;\n\n  /**\n   * 菜单是否覆盖在触发它的元素的上面，默认为 `auto`\n   * 为 `true` 时，使菜单覆盖在触发它的元素的上面\n   * 为 `false` 时，使菜单不覆盖触发它的元素\n   * 为 `auto` 时，简单菜单覆盖触发它的元素。级联菜单不覆盖触发它的元素\n   */\n  covered?: boolean | 'auto';\n\n  /**\n   * 子菜单的触发方式，默认为 `hover`\n   * 为 `click` 时，点击时触发子菜单\n   * 为 `hover` 时，鼠标悬浮时触发子菜单\n   */\n  subMenuTrigger?: 'click' | 'hover';\n\n  /**\n   * 子菜单的触发延迟时间（单位：毫秒），只有在 `subMenuTrigger: hover` 时，这个参数才有效，默认为 `200`\n   */\n  subMenuDelay?: number;\n};\n\ntype EVENT = 'open' | 'opened' | 'close' | 'closed';\ntype STATE = 'opening' | 'opened' | 'closing' | 'closed';\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  position: 'auto',\n  align: 'auto',\n  gutter: 16,\n  fixed: false,\n  covered: 'auto',\n  subMenuTrigger: 'hover',\n  subMenuDelay: 200,\n};\n\nclass Menu {\n  /**\n   * 触发菜单的元素的 JQ 对象\n   */\n  public $anchor: JQ;\n\n  /**\n   * 菜单元素的 JQ 对象\n   */\n  public $element: JQ;\n\n  /**\n   * 配置参数\n   */\n  public options: OPTIONS = extend({}, DEFAULT_OPTIONS);\n\n  /**\n   * 当前菜单状态\n   */\n  private state: STATE = 'closed';\n\n  /**\n   * 是否是级联菜单\n   */\n  private isCascade: boolean;\n\n  /**\n   * 菜单是否覆盖在触发它的元素的上面\n   */\n  private isCovered: boolean;\n\n  public constructor(\n    anchorSelector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    menuSelector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    options: OPTIONS = {},\n  ) {\n    this.$anchor = $(anchorSelector).first();\n    this.$element = $(menuSelector).first();\n\n    // 触发菜单的元素 和 菜单必须是同级的元素，否则菜单可能不能定位\n    if (!this.$anchor.parent().is(this.$element.parent())) {\n      throw new Error('anchorSelector and menuSelector must be siblings');\n    }\n\n    extend(this.options, options);\n\n    // 是否是级联菜单\n    this.isCascade = this.$element.hasClass('mdui-menu-cascade');\n\n    // covered 参数处理\n    this.isCovered =\n      this.options.covered === 'auto' ? !this.isCascade : this.options.covered!;\n\n    // 点击触发菜单切换\n    this.$anchor.on('click', () => this.toggle());\n\n    // 点击菜单外面区域关闭菜单\n    $document.on('click touchstart', (event: Event) => {\n      const $target = $(event.target as HTMLElement);\n\n      if (\n        this.isOpen() &&\n        !$target.is(this.$element) &&\n        !contains(this.$element[0], $target[0]) &&\n        !$target.is(this.$anchor) &&\n        !contains(this.$anchor[0], $target[0])\n      ) {\n        this.close();\n      }\n    });\n\n    // 点击不含子菜单的菜单条目关闭菜单\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const that = this;\n    $document.on('click', '.mdui-menu-item', function () {\n      const $item = $(this);\n\n      if (\n        !$item.find('.mdui-menu').length &&\n        $item.attr('disabled') === undefined\n      ) {\n        that.close();\n      }\n    });\n\n    // 绑定点击或鼠标移入含子菜单的条目的事件\n    this.bindSubMenuEvent();\n\n    // 窗口大小变化时，重新调整菜单位置\n    $window.on(\n      'resize',\n      $.throttle(() => this.readjust(), 100),\n    );\n  }\n\n  /**\n   * 是否为打开状态\n   */\n  private isOpen(): boolean {\n    return this.state === 'opening' || this.state === 'opened';\n  }\n\n  /**\n   * 触发组件事件\n   * @param name\n   */\n  private triggerEvent(name: EVENT): void {\n    componentEvent(name, 'menu', this.$element, this);\n  }\n\n  /**\n   * 调整主菜单位置\n   */\n  private readjust(): void {\n    let menuLeft;\n    let menuTop;\n\n    // 菜单位置和方向\n    let position: 'bottom' | 'top' | 'center';\n    let align: 'left' | 'right' | 'center';\n\n    // window 窗口的宽度和高度\n    const windowHeight = $window.height();\n    const windowWidth = $window.width();\n\n    // 配置参数\n    const gutter = this.options.gutter!;\n    const isCovered = this.isCovered;\n    const isFixed = this.options.fixed;\n\n    // 动画方向参数\n    let transformOriginX;\n    let transformOriginY;\n\n    // 菜单的原始宽度和高度\n    const menuWidth = this.$element.width();\n    const menuHeight = this.$element.height();\n\n    // 触发菜单的元素在窗口中的位置\n    const anchorRect = this.$anchor[0].getBoundingClientRect();\n    const anchorTop = anchorRect.top;\n    const anchorLeft = anchorRect.left;\n    const anchorHeight = anchorRect.height;\n    const anchorWidth = anchorRect.width;\n    const anchorBottom = windowHeight - anchorTop - anchorHeight;\n    const anchorRight = windowWidth - anchorLeft - anchorWidth;\n\n    // 触发元素相对其拥有定位属性的父元素的位置\n    const anchorOffsetTop = this.$anchor[0].offsetTop;\n    const anchorOffsetLeft = this.$anchor[0].offsetLeft;\n\n    // 自动判断菜单位置\n    if (this.options.position === 'auto') {\n      if (anchorBottom + (isCovered ? anchorHeight : 0) > menuHeight + gutter) {\n        // 判断下方是否放得下菜单\n        position = 'bottom';\n      } else if (\n        anchorTop + (isCovered ? anchorHeight : 0) >\n        menuHeight + gutter\n      ) {\n        // 判断上方是否放得下菜单\n        position = 'top';\n      } else {\n        // 上下都放不下，居中显示\n        position = 'center';\n      }\n    } else {\n      position = this.options.position!;\n    }\n\n    // 自动判断菜单对齐方式\n    if (this.options.align === 'auto') {\n      if (anchorRight + anchorWidth > menuWidth + gutter) {\n        // 判断右侧是否放得下菜单\n        align = 'left';\n      } else if (anchorLeft + anchorWidth > menuWidth + gutter) {\n        // 判断左侧是否放得下菜单\n        align = 'right';\n      } else {\n        // 左右都放不下，居中显示\n        align = 'center';\n      }\n    } else {\n      align = this.options.align!;\n    }\n\n    // 设置菜单位置\n    if (position === 'bottom') {\n      transformOriginY = '0';\n      menuTop =\n        (isCovered ? 0 : anchorHeight) +\n        (isFixed ? anchorTop : anchorOffsetTop);\n    } else if (position === 'top') {\n      transformOriginY = '100%';\n      menuTop =\n        (isCovered ? anchorHeight : 0) +\n        (isFixed ? anchorTop - menuHeight : anchorOffsetTop - menuHeight);\n    } else {\n      transformOriginY = '50%';\n\n      // =====================在窗口中居中\n      // 显示的菜单的高度，简单菜单高度不超过窗口高度，若超过了则在菜单内部显示滚动条\n      // 级联菜单内部不允许出现滚动条\n      let menuHeightTemp = menuHeight;\n\n      // 简单菜单比窗口高时，限制菜单高度\n      if (!this.isCascade) {\n        if (menuHeight + gutter * 2 > windowHeight) {\n          menuHeightTemp = windowHeight - gutter * 2;\n          this.$element.height(menuHeightTemp);\n        }\n      }\n\n      menuTop =\n        (windowHeight - menuHeightTemp) / 2 +\n        (isFixed ? 0 : anchorOffsetTop - anchorTop);\n    }\n\n    this.$element.css('top', `${menuTop}px`);\n\n    // 设置菜单对齐方式\n    if (align === 'left') {\n      transformOriginX = '0';\n      menuLeft = isFixed ? anchorLeft : anchorOffsetLeft;\n    } else if (align === 'right') {\n      transformOriginX = '100%';\n      menuLeft = isFixed\n        ? anchorLeft + anchorWidth - menuWidth\n        : anchorOffsetLeft + anchorWidth - menuWidth;\n    } else {\n      transformOriginX = '50%';\n\n      //=======================在窗口中居中\n      // 显示的菜单的宽度，菜单宽度不能超过窗口宽度\n      let menuWidthTemp = menuWidth;\n\n      // 菜单比窗口宽，限制菜单宽度\n      if (menuWidth + gutter * 2 > windowWidth) {\n        menuWidthTemp = windowWidth - gutter * 2;\n        this.$element.width(menuWidthTemp);\n      }\n\n      menuLeft =\n        (windowWidth - menuWidthTemp) / 2 +\n        (isFixed ? 0 : anchorOffsetLeft - anchorLeft);\n    }\n\n    this.$element.css('left', `${menuLeft}px`);\n\n    // 设置菜单动画方向\n    this.$element.transformOrigin(`${transformOriginX} ${transformOriginY}`);\n  }\n\n  /**\n   * 调整子菜单的位置\n   * @param $submenu\n   */\n  private readjustSubmenu($submenu: JQ): void {\n    const $item = $submenu.parent('.mdui-menu-item');\n\n    let submenuTop;\n    let submenuLeft;\n\n    // 子菜单位置和方向\n    let position: 'top' | 'bottom';\n    let align: 'left' | 'right';\n\n    // window 窗口的宽度和高度\n    const windowHeight = $window.height();\n    const windowWidth = $window.width();\n\n    // 动画方向参数\n    let transformOriginX;\n    let transformOriginY;\n\n    // 子菜单的原始宽度和高度\n    const submenuWidth = $submenu.width();\n    const submenuHeight = $submenu.height();\n\n    // 触发子菜单的菜单项的宽度高度\n    const itemRect = $item[0].getBoundingClientRect();\n    const itemWidth = itemRect.width;\n    const itemHeight = itemRect.height;\n    const itemLeft = itemRect.left;\n    const itemTop = itemRect.top;\n\n    // 判断菜单上下位置\n    if (windowHeight - itemTop > submenuHeight) {\n      // 判断下方是否放得下菜单\n      position = 'bottom';\n    } else if (itemTop + itemHeight > submenuHeight) {\n      // 判断上方是否放得下菜单\n      position = 'top';\n    } else {\n      // 默认放在下方\n      position = 'bottom';\n    }\n\n    // 判断菜单左右位置\n    if (windowWidth - itemLeft - itemWidth > submenuWidth) {\n      // 判断右侧是否放得下菜单\n      align = 'left';\n    } else if (itemLeft > submenuWidth) {\n      // 判断左侧是否放得下菜单\n      align = 'right';\n    } else {\n      // 默认放在右侧\n      align = 'left';\n    }\n\n    // 设置菜单位置\n    if (position === 'bottom') {\n      transformOriginY = '0';\n      submenuTop = '0';\n    } else if (position === 'top') {\n      transformOriginY = '100%';\n      submenuTop = -submenuHeight + itemHeight;\n    }\n\n    $submenu.css('top', `${submenuTop}px`);\n\n    // 设置菜单对齐方式\n    if (align === 'left') {\n      transformOriginX = '0';\n      submenuLeft = itemWidth;\n    } else if (align === 'right') {\n      transformOriginX = '100%';\n      submenuLeft = -submenuWidth;\n    }\n\n    $submenu.css('left', `${submenuLeft}px`);\n\n    // 设置菜单动画方向\n    $submenu.transformOrigin(`${transformOriginX} ${transformOriginY}`);\n  }\n\n  /**\n   * 打开子菜单\n   * @param $submenu\n   */\n  private openSubMenu($submenu: JQ): void {\n    this.readjustSubmenu($submenu);\n\n    $submenu\n      .addClass('mdui-menu-open')\n      .parent('.mdui-menu-item')\n      .addClass('mdui-menu-item-active');\n  }\n\n  /**\n   * 关闭子菜单，及其嵌套的子菜单\n   * @param $submenu\n   */\n  private closeSubMenu($submenu: JQ): void {\n    // 关闭子菜单\n    $submenu\n      .removeClass('mdui-menu-open')\n      .addClass('mdui-menu-closing')\n      .transitionEnd(() => $submenu.removeClass('mdui-menu-closing'))\n\n      // 移除激活状态的样式\n      .parent('.mdui-menu-item')\n      .removeClass('mdui-menu-item-active');\n\n    // 循环关闭嵌套的子菜单\n    $submenu.find('.mdui-menu').each((_, menu) => {\n      const $subSubmenu = $(menu);\n\n      $subSubmenu\n        .removeClass('mdui-menu-open')\n        .addClass('mdui-menu-closing')\n        .transitionEnd(() => $subSubmenu.removeClass('mdui-menu-closing'))\n        .parent('.mdui-menu-item')\n        .removeClass('mdui-menu-item-active');\n    });\n  }\n\n  /**\n   * 切换子菜单状态\n   * @param $submenu\n   */\n  private toggleSubMenu($submenu: JQ): void {\n    $submenu.hasClass('mdui-menu-open')\n      ? this.closeSubMenu($submenu)\n      : this.openSubMenu($submenu);\n  }\n\n  /**\n   * 绑定子菜单事件\n   */\n  private bindSubMenuEvent(): void {\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const that = this;\n\n    // 点击打开子菜单\n    this.$element.on('click', '.mdui-menu-item', function (event) {\n      const $item = $(this as HTMLElement);\n      const $target = $(event.target as HTMLElement);\n\n      // 禁用状态菜单不操作\n      if ($item.attr('disabled') !== undefined) {\n        return;\n      }\n\n      // 没有点击在子菜单的菜单项上时，不操作（点在了子菜单的空白区域、或分隔线上）\n      if ($target.is('.mdui-menu') || $target.is('.mdui-divider')) {\n        return;\n      }\n\n      // 阻止冒泡，点击菜单项时只在最后一级的 mdui-menu-item 上生效，不向上冒泡\n      if (!$target.parents('.mdui-menu-item').first().is($item)) {\n        return;\n      }\n\n      // 当前菜单的子菜单\n      const $submenu = $item.children('.mdui-menu');\n\n      // 先关闭除当前子菜单外的所有同级子菜单\n      $item\n        .parent('.mdui-menu')\n        .children('.mdui-menu-item')\n        .each((_, item) => {\n          const $tmpSubmenu = $(item).children('.mdui-menu');\n\n          if (\n            $tmpSubmenu.length &&\n            (!$submenu.length || !$tmpSubmenu.is($submenu))\n          ) {\n            that.closeSubMenu($tmpSubmenu);\n          }\n        });\n\n      // 切换当前子菜单\n      if ($submenu.length) {\n        that.toggleSubMenu($submenu);\n      }\n    });\n\n    if (this.options.subMenuTrigger === 'hover') {\n      // 临时存储 setTimeout 对象\n      let timeout: any = null;\n      let timeoutOpen: any = null;\n\n      this.$element.on('mouseover mouseout', '.mdui-menu-item', function (\n        event,\n      ) {\n        const $item = $(this as HTMLElement);\n        const eventType = event.type;\n        const $relatedTarget = $(\n          (event as MouseEvent).relatedTarget as HTMLElement,\n        );\n\n        // 禁用状态的菜单不操作\n        if ($item.attr('disabled') !== undefined) {\n          return;\n        }\n\n        // 用 mouseover 模拟 mouseenter\n        if (eventType === 'mouseover') {\n          if (\n            !$item.is($relatedTarget) &&\n            contains($item[0], $relatedTarget[0])\n          ) {\n            return;\n          }\n        }\n\n        // 用 mouseout 模拟 mouseleave\n        else if (eventType === 'mouseout') {\n          if (\n            $item.is($relatedTarget) ||\n            contains($item[0], $relatedTarget[0])\n          ) {\n            return;\n          }\n        }\n\n        // 当前菜单项下的子菜单，未必存在\n        const $submenu = $item.children('.mdui-menu');\n\n        // 鼠标移入菜单项时，显示菜单项下的子菜单\n        if (eventType === 'mouseover') {\n          if ($submenu.length) {\n            // 当前子菜单准备打开时，如果当前子菜单正准备着关闭，不用再关闭了\n            const tmpClose = $submenu.data('timeoutClose.mdui.menu');\n            if (tmpClose) {\n              clearTimeout(tmpClose);\n            }\n\n            // 如果当前子菜单已经打开，不操作\n            if ($submenu.hasClass('mdui-menu-open')) {\n              return;\n            }\n\n            // 当前子菜单准备打开时，其他准备打开的子菜单不用再打开了\n            clearTimeout(timeoutOpen);\n\n            // 准备打开当前子菜单\n            timeout = timeoutOpen = setTimeout(\n              () => that.openSubMenu($submenu),\n              that.options.subMenuDelay,\n            );\n\n            $submenu.data('timeoutOpen.mdui.menu', timeout);\n          }\n        }\n\n        // 鼠标移出菜单项时，关闭菜单项下的子菜单\n        else if (eventType === 'mouseout') {\n          if ($submenu.length) {\n            // 鼠标移出菜单项时，如果当前菜单项下的子菜单正准备打开，不用再打开了\n            const tmpOpen = $submenu.data('timeoutOpen.mdui.menu');\n            if (tmpOpen) {\n              clearTimeout(tmpOpen);\n            }\n\n            // 准备关闭当前子菜单\n            timeout = setTimeout(\n              () => that.closeSubMenu($submenu),\n              that.options.subMenuDelay,\n            );\n\n            $submenu.data('timeoutClose.mdui.menu', timeout);\n          }\n        }\n      });\n    }\n  }\n\n  /**\n   * 动画结束回调\n   */\n  private transitionEnd(): void {\n    this.$element.removeClass('mdui-menu-closing');\n\n    if (this.state === 'opening') {\n      this.state = 'opened';\n      this.triggerEvent('opened');\n    }\n\n    if (this.state === 'closing') {\n      this.state = 'closed';\n      this.triggerEvent('closed');\n\n      // 关闭后，恢复菜单样式到默认状态，并恢复 fixed 定位\n      this.$element.css({\n        top: '',\n        left: '',\n        width: '',\n        position: 'fixed',\n      });\n    }\n  }\n\n  /**\n   * 切换菜单状态\n   */\n  public toggle(): void {\n    this.isOpen() ? this.close() : this.open();\n  }\n\n  /**\n   * 打开菜单\n   */\n  public open(): void {\n    if (this.isOpen()) {\n      return;\n    }\n\n    this.state = 'opening';\n    this.triggerEvent('open');\n\n    this.readjust();\n\n    this.$element\n      // 菜单隐藏状态使用使用 fixed 定位。\n      .css('position', this.options.fixed ? 'fixed' : 'absolute')\n      .addClass('mdui-menu-open')\n      .transitionEnd(() => this.transitionEnd());\n  }\n\n  /**\n   * 关闭菜单\n   */\n  public close(): void {\n    if (!this.isOpen()) {\n      return;\n    }\n\n    this.state = 'closing';\n    this.triggerEvent('close');\n\n    // 菜单开始关闭时，关闭所有子菜单\n    this.$element.find('.mdui-menu').each((_, submenu) => {\n      this.closeSubMenu($(submenu));\n    });\n\n    this.$element\n      .removeClass('mdui-menu-open')\n      .addClass('mdui-menu-closing')\n      .transitionEnd(() => this.transitionEnd());\n  }\n}\n\nmdui.Menu = Menu;\n", "import $ from 'mdui.jq/es/$';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/on';\nimport mdui from '../../mdui';\nimport { $document } from '../../utils/dom';\nimport { parseOptions } from '../../utils/parseOptions';\nimport './index';\n\nconst customAttr = 'mdui-menu';\nconst dataName = '_mdui_menu';\n\ntype OPTIONS = {\n  target: string;\n  position?: 'auto' | 'top' | 'bottom' | 'center';\n  align?: 'auto' | 'left' | 'right' | 'center';\n  gutter?: number;\n  fixed?: boolean;\n  covered?: boolean | 'auto';\n  subMenuTrigger?: 'click' | 'hover';\n  subMenuDelay?: number;\n};\n\n$(() => {\n  $document.on('click', `[${customAttr}]`, function () {\n    const $this = $(this as HTMLElement);\n    let instance = $this.data(dataName);\n\n    if (!instance) {\n      const options = parseOptions(this as HTMLElement, customAttr) as OPTIONS;\n      const menuSelector = options.target;\n      // @ts-ignore\n      delete options.target;\n\n      instance = new mdui.Menu($this, menuSelector, options);\n      $this.data(dataName, instance);\n\n      instance.toggle();\n    }\n  });\n});\n"], "names": ["e", "Promise", "promiseFinally", "const", "let", "i", "this", "get", "set", "DEFAULT_OPTIONS", "customAttr", "dataName", "currentInst", "queueName"], "mappings": ";;;;;;;;;;;EAAA,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,MAAMA,GAAC,CAAC,EAAE,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,EAAC,CAAC,EAAE;;ECAnZ,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,OAAO,MAAM,CAAC,WAAW,GAAG,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,EAAC,CAAC,EAAE;;ECArR;EACA;EACA;EACA,SAAS,kBAAkB,CAAC,QAAQ,EAAE;EACtC,EAAE,IAAI,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;EACrC,EAAE,OAAO,IAAI,CAAC,IAAI;EAClB,IAAI,SAAS,KAAK,EAAE;EACpB;EACA,MAAM,OAAO,WAAW,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW;EAC7D,QAAQ,OAAO,KAAK,CAAC;EACrB,OAAO,CAAC,CAAC;EACT,KAAK;EACL,IAAI,SAAS,MAAM,EAAE;EACrB;EACA,MAAM,OAAO,WAAW,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW;EAC7D;EACA,QAAQ,OAAO,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;EAC1C,OAAO,CAAC,CAAC;EACT,KAAK;EACL,GAAG,CAAC;EACJ;;ECpBA,SAAS,UAAU,CAAC,GAAG,EAAE;EACzB,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC;EACf,EAAE,OAAO,IAAI,CAAC,CAAC,SAAS,OAAO,EAAE,MAAM,EAAE;EACzC,IAAI,IAAI,EAAE,GAAG,IAAI,OAAO,GAAG,CAAC,MAAM,KAAK,WAAW,CAAC,EAAE;EACrD,MAAM,OAAO,MAAM;EACnB,QAAQ,IAAI,SAAS;EACrB,UAAU,OAAO,GAAG;EACpB,YAAY,GAAG;EACf,YAAY,GAAG;EACf,YAAY,gEAAgE;EAC5E,SAAS;EACT,OAAO,CAAC;EACR,KAAK;EACL,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;EAC/C,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAE,OAAO,OAAO,CAAC,EAAE,CAAC,GAAC;EAC9C,IAAI,IAAI,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC;AAChC;EACA,IAAI,SAAS,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE;EACzB,MAAM,IAAI,GAAG,KAAK,OAAO,GAAG,KAAK,QAAQ,IAAI,OAAO,GAAG,KAAK,UAAU,CAAC,EAAE;EACzE,QAAQ,IAAI,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;EAC5B,QAAQ,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;EACxC,UAAU,IAAI,CAAC,IAAI;EACnB,YAAY,GAAG;EACf,YAAY,SAAS,GAAG,EAAE;EAC1B,cAAc,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;EAC1B,aAAa;EACb,YAAY,SAAS,CAAC,EAAE;EACxB,cAAc,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;EAC1D,cAAc,IAAI,EAAE,SAAS,KAAK,CAAC,EAAE;EACrC,gBAAgB,OAAO,CAAC,IAAI,CAAC,CAAC;EAC9B,eAAe;EACf,aAAa;EACb,WAAW,CAAC;EACZ,UAAU,OAAO;EACjB,SAAS;EACT,OAAO;EACP,MAAM,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;EACpD,MAAM,IAAI,EAAE,SAAS,KAAK,CAAC,EAAE;EAC7B,QAAQ,OAAO,CAAC,IAAI,CAAC,CAAC;EACtB,OAAO;EACP,KAAK;AACL;EACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EAC1C,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,KAAK;EACL,GAAG,CAAC,CAAC;EACL;;EC3CA;EACA;EACA,IAAI,cAAc,GAAG,UAAU,CAAC;AAChC;EACA,SAAS,OAAO,CAAC,CAAC,EAAE;EACpB,EAAE,OAAO,OAAO,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC;EACvD,CAAC;AACD;EACA,SAAS,IAAI,GAAG,EAAE;AAClB;EACA;EACA,SAAS,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE;EAC3B,EAAE,OAAO,WAAW;EACpB,IAAI,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;EACjC,GAAG,CAAC;EACJ,CAAC;AACD;EACA;EACA;EACA;EACA;EACA,SAASC,SAAO,CAAC,EAAE,EAAE;EACrB,EAAE,IAAI,EAAE,IAAI,YAAYA,SAAO,CAAC;EAChC,MAAI,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,GAAC;EAChE,EAAE,IAAI,OAAO,EAAE,KAAK,UAAU,IAAE,MAAM,IAAI,SAAS,CAAC,gBAAgB,CAAC,GAAC;EACtE;EACA,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;EAClB;EACA,EAAE,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;EACxB;EACA,EAAE,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;EAC1B;EACA,EAAE,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;AACvB;EACA,EAAE,SAAS,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;EACtB,CAAC;AACD;EACA,SAAS,MAAM,CAAC,IAAI,EAAE,QAAQ,EAAE;EAChC,EAAE,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;EAC5B,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;EACvB,GAAG;EACH,EAAE,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;EACzB,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EACnC,IAAI,OAAO;EACX,GAAG;EACH,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;EACvB,EAAEA,SAAO,CAAC,YAAY,CAAC,WAAW;EAClC,IAAI,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM,KAAK,CAAC,GAAG,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,UAAU,CAAC;EAC5E,IAAI,IAAI,EAAE,KAAK,IAAI,EAAE;EACrB,MAAM,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG,MAAM,EAAE,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;EAC5E,MAAM,OAAO;EACb,KAAK;EACL,IAAI,IAAI,GAAG,CAAC;EACZ,IAAI,IAAI;EACR,MAAM,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;EAC5B,KAAK,CAAC,OAAO,CAAC,EAAE;EAChB,MAAM,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;EAClC,MAAM,OAAO;EACb,KAAK;EACL,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;EACnC,GAAG,CAAC,CAAC;EACL,CAAC;AACD;EACA,SAAS,OAAO,CAAC,IAAI,EAAE,QAAQ,EAAE;EACjC,EAAE,IAAI;EACN;EACA,IAAI,IAAI,QAAQ,KAAK,IAAI;EACzB,QAAM,MAAM,IAAI,SAAS,CAAC,2CAA2C,CAAC,GAAC;EACvE,IAAI;EACJ,MAAM,QAAQ;EACd,OAAO,OAAO,QAAQ,KAAK,QAAQ,IAAI,OAAO,QAAQ,KAAK,UAAU,CAAC;EACtE,MAAM;EACN,MAAM,IAAI,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;EAC/B,MAAM,IAAI,QAAQ,YAAYA,SAAO,EAAE;EACvC,QAAQ,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;EACxB,QAAQ,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;EAC/B,QAAQ,MAAM,CAAC,IAAI,CAAC,CAAC;EACrB,QAAQ,OAAO;EACf,OAAO,MAAM,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;EAC7C,QAAQ,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,IAAI,CAAC,CAAC;EAC9C,QAAQ,OAAO;EACf,OAAO;EACP,KAAK;EACL,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;EACpB,IAAI,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;EAC3B,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC;EACjB,GAAG,CAAC,OAAO,CAAC,EAAE;EACd,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;EACpB,GAAG;EACH,CAAC;AACD;EACA,SAAS,MAAM,CAAC,IAAI,EAAE,QAAQ,EAAE;EAChC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;EAClB,EAAE,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;EACzB,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;EACf,CAAC;AACD;EACA,SAAS,MAAM,CAAC,IAAI,EAAE;EACtB,EAAE,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;EACzD,IAAIA,SAAO,CAAC,YAAY,CAAC,WAAW;EACpC,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;EAC1B,QAAQA,SAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;EACnD,OAAO;EACP,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;EAC9D,IAAI,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;EACrC,GAAG;EACH,EAAE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;EACzB,CAAC;AACD;EACA;EACA;EACA;EACA,SAAS,OAAO,CAAC,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE;EACnD,EAAE,IAAI,CAAC,WAAW,GAAG,OAAO,WAAW,KAAK,UAAU,GAAG,WAAW,GAAG,IAAI,CAAC;EAC5E,EAAE,IAAI,CAAC,UAAU,GAAG,OAAO,UAAU,KAAK,UAAU,GAAG,UAAU,GAAG,IAAI,CAAC;EACzE,EAAE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;EACzB,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,SAAS,CAAC,EAAE,EAAE,IAAI,EAAE;EAC7B,EAAE,IAAI,IAAI,GAAG,KAAK,CAAC;EACnB,EAAE,IAAI;EACN,IAAI,EAAE;EACN,MAAM,SAAS,KAAK,EAAE;EACtB,QAAQ,IAAI,IAAI,IAAE,SAAO;EACzB,QAAQ,IAAI,GAAG,IAAI,CAAC;EACpB,QAAQ,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;EAC7B,OAAO;EACP,MAAM,SAAS,MAAM,EAAE;EACvB,QAAQ,IAAI,IAAI,IAAE,SAAO;EACzB,QAAQ,IAAI,GAAG,IAAI,CAAC;EACpB,QAAQ,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;EAC7B,OAAO;EACP,KAAK,CAAC;EACN,GAAG,CAAC,OAAO,EAAE,EAAE;EACf,IAAI,IAAI,IAAI,IAAE,SAAO;EACrB,IAAI,IAAI,GAAG,IAAI,CAAC;EAChB,IAAI,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;EACrB,GAAG;EACH,CAAC;AACD;AACAA,WAAO,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,SAAS,UAAU,EAAE;EAClD,EAAE,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;EACrC,CAAC,CAAC;AACF;AACAA,WAAO,CAAC,SAAS,CAAC,IAAI,GAAG,SAAS,WAAW,EAAE,UAAU,EAAE;EAC3D;EACA,EAAE,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AACxC;EACA,EAAE,MAAM,CAAC,IAAI,EAAE,IAAI,OAAO,CAAC,WAAW,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC;EAC3D,EAAE,OAAO,IAAI,CAAC;EACd,CAAC,CAAC;AACF;AACAA,WAAO,CAAC,SAAS,CAAC,SAAS,CAAC,GAAGC,kBAAc,CAAC;AAC9C;AACAD,WAAO,CAAC,GAAG,GAAG,SAAS,GAAG,EAAE;EAC5B,EAAE,OAAO,IAAIA,SAAO,CAAC,SAAS,OAAO,EAAE,MAAM,EAAE;EAC/C,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;EACvB,MAAM,OAAO,MAAM,CAAC,IAAI,SAAS,CAAC,8BAA8B,CAAC,CAAC,CAAC;EACnE,KAAK;AACL;EACA,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;EAC/C,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAE,OAAO,OAAO,CAAC,EAAE,CAAC,GAAC;EAC9C,IAAI,IAAI,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC;AAChC;EACA,IAAI,SAAS,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE;EACzB,MAAM,IAAI;EACV,QAAQ,IAAI,GAAG,KAAK,OAAO,GAAG,KAAK,QAAQ,IAAI,OAAO,GAAG,KAAK,UAAU,CAAC,EAAE;EAC3E,UAAU,IAAI,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;EAC9B,UAAU,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;EAC1C,YAAY,IAAI,CAAC,IAAI;EACrB,cAAc,GAAG;EACjB,cAAc,SAAS,GAAG,EAAE;EAC5B,gBAAgB,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;EAC5B,eAAe;EACf,cAAc,MAAM;EACpB,aAAa,CAAC;EACd,YAAY,OAAO;EACnB,WAAW;EACX,SAAS;EACT,QAAQ,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;EACtB,QAAQ,IAAI,EAAE,SAAS,KAAK,CAAC,EAAE;EAC/B,UAAU,OAAO,CAAC,IAAI,CAAC,CAAC;EACxB,SAAS;EACT,OAAO,CAAC,OAAO,EAAE,EAAE;EACnB,QAAQ,MAAM,CAAC,EAAE,CAAC,CAAC;EACnB,OAAO;EACP,KAAK;AACL;EACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EAC1C,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,KAAK;EACL,GAAG,CAAC,CAAC;EACL,CAAC,CAAC;AACF;AACAA,WAAO,CAAC,UAAU,GAAG,UAAU,CAAC;AAChC;AACAA,WAAO,CAAC,OAAO,GAAG,SAAS,KAAK,EAAE;EAClC,EAAE,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,WAAW,KAAKA,SAAO,EAAE;EAC3E,IAAI,OAAO,KAAK,CAAC;EACjB,GAAG;AACH;EACA,EAAE,OAAO,IAAIA,SAAO,CAAC,SAAS,OAAO,EAAE;EACvC,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC;EACnB,GAAG,CAAC,CAAC;EACL,CAAC,CAAC;AACF;AACAA,WAAO,CAAC,MAAM,GAAG,SAAS,KAAK,EAAE;EACjC,EAAE,OAAO,IAAIA,SAAO,CAAC,SAAS,OAAO,EAAE,MAAM,EAAE;EAC/C,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC;EAClB,GAAG,CAAC,CAAC;EACL,CAAC,CAAC;AACF;AACAA,WAAO,CAAC,IAAI,GAAG,SAAS,GAAG,EAAE;EAC7B,EAAE,OAAO,IAAIA,SAAO,CAAC,SAAS,OAAO,EAAE,MAAM,EAAE;EAC/C,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;EACvB,MAAM,OAAO,MAAM,CAAC,IAAI,SAAS,CAAC,+BAA+B,CAAC,CAAC,CAAC;EACpE,KAAK;AACL;EACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;EACpD,MAAMA,SAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;EACpD,KAAK;EACL,GAAG,CAAC,CAAC;EACL,CAAC,CAAC;AACF;EACA;AACAA,WAAO,CAAC,YAAY;EACpB;EACA,EAAE,CAAC,OAAO,YAAY,KAAK,UAAU;EACrC,IAAI,SAAS,EAAE,EAAE;EACjB;EACA,MAAM,YAAY,CAAC,EAAE,CAAC,CAAC;EACvB,KAAK;EACL,EAAE,SAAS,EAAE,EAAE;EACf,IAAI,cAAc,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EAC1B,GAAG,CAAC;AACJ;AACAA,WAAO,CAAC,qBAAqB,GAAG,SAAS,qBAAqB,CAAC,GAAG,EAAE;EACpE,EAAE,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,EAAE;EACjD,IAAI,OAAO,CAAC,IAAI,CAAC,uCAAuC,EAAE,GAAG,CAAC,CAAC;EAC/D,GAAG;EACH,CAAC;;ECxPD;EACA,IAAI,QAAQ,GAAG,CAAC,WAAW;EAC3B;EACA;EACA;EACA,EAAE,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;EACnC,IAAI,OAAO,IAAI,CAAC;EAChB,GAAG;EACH,EAAE,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;EACrC,IAAI,OAAO,MAAM,CAAC;EAClB,GAAG;EACH,EAAE,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;EACrC,IAAI,OAAO,MAAM,CAAC;EAClB,GAAG;EACH,EAAE,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;EACpD,CAAC,GAAG,CAAC;AACL;EACA;EACA;EACA;EACA;EACA,IAAI,OAAO,QAAQ,CAAC,SAAS,CAAC,KAAK,UAAU,EAAE;EAC/C,EAAE,QAAQ,CAAC,SAAS,CAAC,GAAGA,SAAO,CAAC;EAChC,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE;EACnD,EAAE,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,GAAGC,kBAAc,CAAC;EACzD,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,EAAE;EACzC,EAAE,QAAQ,CAAC,OAAO,CAAC,UAAU,GAAG,UAAU,CAAC;EAC3C;;EC5BA,SAAS,UAAU,CAAC,MAAM,EAAE;EAC5B,IAAI,OAAO,OAAO,MAAM,KAAK,UAAU,CAAC;EACxC,CAAC;EACD,SAAS,QAAQ,CAAC,MAAM,EAAE;EAC1B,IAAI,OAAO,OAAO,MAAM,KAAK,QAAQ,CAAC;EACtC,CAAC;EACD,SAAS,QAAQ,CAAC,MAAM,EAAE;EAC1B,IAAI,OAAO,OAAO,MAAM,KAAK,QAAQ,CAAC;EACtC,CAAC;EACD,SAAS,SAAS,CAAC,MAAM,EAAE;EAC3B,IAAI,OAAO,OAAO,MAAM,KAAK,SAAS,CAAC;EACvC,CAAC;EACD,SAAS,WAAW,CAAC,MAAM,EAAE;EAC7B,IAAI,OAAO,OAAO,MAAM,KAAK,WAAW,CAAC;EACzC,CAAC;EACD,SAAS,MAAM,CAAC,MAAM,EAAE;EACxB,IAAI,OAAO,MAAM,KAAK,IAAI,CAAC;EAC3B,CAAC;EACD,SAAS,QAAQ,CAAC,MAAM,EAAE;EAC1B,IAAI,OAAO,MAAM,YAAY,MAAM,CAAC;EACpC,CAAC;EACD,SAAS,UAAU,CAAC,MAAM,EAAE;EAC5B,IAAI,OAAO,MAAM,YAAY,QAAQ,CAAC;EACtC,CAAC;EACD,SAAS,SAAS,CAAC,MAAM,EAAE;EAC3B,IAAI,OAAO,MAAM,YAAY,OAAO,CAAC;EACrC,CAAC;EACD,SAAS,MAAM,CAAC,MAAM,EAAE;EACxB,IAAI,OAAO,MAAM,YAAY,IAAI,CAAC;EAClC,CAAC;EACD;EACA;EACA;EACA,SAAS,IAAI,GAAG;EAChB;EACA,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC;EAC1C,CAAC;EACD,SAAS,WAAW,CAAC,MAAM,EAAE;EAC7B,IAAI,IAAI,UAAU,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,MAAM,CAAC,EAAE;EAChD,QAAQ,OAAO,KAAK,CAAC;EACrB,KAAK;EACL,IAAI,OAAO,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;EACnC,CAAC;EACD,SAAS,YAAY,CAAC,MAAM,EAAE;EAC9B,IAAI,OAAO,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,KAAK,IAAI,CAAC;EACzD,CAAC;EACD,SAAS,SAAS,CAAC,MAAM,EAAE;EAC3B,IAAI,OAAO,UAAU,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,eAAe,GAAG,MAAM,CAAC;EAChE,CAAC;EACD;EACA;EACA;EACA;EACA,SAAS,WAAW,CAAC,MAAM,EAAE;EAC7B,IAAI,OAAO,MAAM;EACjB,SAAS,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC;EAChC,SAAS,OAAO,CAAC,WAAW,YAAG,CAAC,EAAE,MAAM,WAAK,MAAM,CAAC,WAAW,KAAE,CAAC,CAAC;EACnE,CAAC;EACD;EACA;EACA;EACA;EACA,SAAS,WAAW,CAAC,MAAM,EAAE;EAC7B,IAAI,OAAO,MAAM,CAAC,OAAO,CAAC,QAAQ,YAAG,QAAQ,WAAK,GAAG,GAAG,QAAQ,CAAC,WAAW,KAAE,CAAC,CAAC;EAChF,CAAC;EACD;EACA;EACA;EACA;EACA;EACA,SAAS,qBAAqB,CAAC,OAAO,EAAE,IAAI,EAAE;EAC9C,IAAI,OAAO,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,gBAAgB,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;EAChF,CAAC;EACD;EACA;EACA;EACA;EACA,SAAS,WAAW,CAAC,OAAO,EAAE;EAC9B,IAAI,OAAO,qBAAqB,CAAC,OAAO,EAAE,YAAY,CAAC,KAAK,YAAY,CAAC;EACzE,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,aAAa,CAAC,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE;EAClD,IAAIC,IAAM,QAAQ,GAAG,SAAS,KAAK,OAAO,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;EACnF,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,WAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAK;EAC7C,QAAQC,IAAI,IAAI,GAAG,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;EAC3C,QAAQ,IAAI,KAAK,KAAK,QAAQ,EAAE;EAChC,YAAY,IAAI,IAAI,OAAO,CAAC;EAC5B,SAAS;EACT,QAAQ,OAAO,IAAI,GAAG,UAAU,CAAC,qBAAqB,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;EAC9E,KAAK,EAAE,CAAC,CAAC,CAAC;EACV,CAAC;EACD;EACA;EACA;EACA;EACA;EACA,SAAS,QAAQ,CAAC,OAAO,EAAE,IAAI,EAAE;EACjC;EACA,IAAI,IAAI,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,QAAQ,EAAE;EAC/C,QAAQD,IAAM,WAAW,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC,IAAI,CAAC,CAAC;EAClE,QAAQ,IAAI,WAAW,CAAC,OAAO,CAAC,EAAE;EAClC,YAAY,QAAU,WAAW,SAAK;EACtC,SAAS;EACT,QAAQ,SAAU,WAAW;EAC7B,YAAY,aAAa,CAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,CAAC;EAClD,YAAY,aAAa,CAAC,OAAO,EAAE,IAAI,EAAE,SAAS,WAAM;EACxD,KAAK;EACL,IAAI,OAAO,qBAAqB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;EAChD,CAAC;EACD;EACA;EACA;EACA;EACA;EACA,SAAS,kBAAkB,CAAC,MAAM,EAAE,MAAM,EAAE;EAC5C,IAAIA,IAAM,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;EACtD,IAAI,UAAU,CAAC,SAAS,GAAG,MAAM,CAAC;EAClC,IAAI,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;EAChD,CAAC;EACD;EACA;EACA;EACA,SAAS,WAAW,GAAG;EACvB,IAAI,OAAO,KAAK,CAAC;EACjB,CAAC;EACD;EACA;EACA;EACAA,IAAM,SAAS,GAAG;EAClB,IAAI,yBAAyB;EAC7B,IAAI,aAAa;EACjB,IAAI,aAAa;EACjB,IAAI,UAAU;EACd,IAAI,YAAY;EAChB,IAAI,YAAY;EAChB,IAAI,UAAU;EACd,IAAI,YAAY;EAChB,IAAI,eAAe;EACnB,IAAI,iBAAiB;EACrB,IAAI,SAAS;EACb,IAAI,YAAY;EAChB,IAAI,cAAc;EAClB,IAAI,YAAY;EAChB,IAAI,SAAS;EACb,IAAI,OAAO;EACX,IAAI,SAAS;EACb,IAAI,QAAQ;EACZ,IAAI,QAAQ;EACZ,IAAI,MAAM,EACT;;EC5JD,SAAS,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE;EAChC,IAAI,IAAI,WAAW,CAAC,MAAM,CAAC,EAAE;EAC7B,QAAQ,KAAKC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;EACnD,YAAY,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;EAClE,gBAAgB,OAAO,MAAM,CAAC;EAC9B,aAAa;EACb,SAAS;EACT,KAAK;EACL,SAAS;EACT,QAAQD,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;EACzC,QAAQ,KAAKC,IAAIC,GAAC,GAAG,CAAC,EAAEA,GAAC,GAAG,IAAI,CAAC,MAAM,EAAEA,GAAC,IAAI,CAAC,EAAE;EACjD,YAAY,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAACA,GAAC,CAAC,CAAC,EAAE,IAAI,CAACA,GAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAACA,GAAC,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;EACpF,gBAAgB,OAAO,MAAM,CAAC;EAC9B,aAAa;EACb,SAAS;EACT,KAAK;EACL,IAAI,OAAO,MAAM,CAAC;EAClB;;ECjBA;EACA;EACA;EACO,IAAM,EAAE,GACX,WAAW,CAAC,GAAG,EAAE;;AAAC;EACtB,IAAQ,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;EACxB,IAAQ,IAAI,CAAC,GAAG,EAAE;EAClB,QAAY,OAAO,IAAI,CAAC;EACxB,KAAS;EACT,IAAQ,IAAI,CAAC,GAAG,YAAG,CAAC,EAAE,IAAI,EAAK;EAC/B;EACA,QAAYC,MAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;EAC3B,KAAS,CAAC,CAAC;EACX,IAAQ,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;EACjC,IAAQ,OAAO,IAAI,CAAC;EAChB;;ECbJ,SAAS,IAAI,GAAG;EAChB,IAAIH,IAAM,CAAC,GAAG,UAAU,QAAQ,EAAE;EAClC,QAAQ,IAAI,CAAC,QAAQ,EAAE;EACvB,YAAY,OAAO,IAAI,EAAE,EAAE,CAAC;EAC5B,SAAS;EACT;EACA,QAAQ,IAAI,QAAQ,YAAY,EAAE,EAAE;EACpC,YAAY,OAAO,QAAQ,CAAC;EAC5B,SAAS;EACT;EACA,QAAQ,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE;EAClC,YAAY,IAAI,6BAA6B,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;EACvE,gBAAgB,QAAQ,CAAC,IAAI,EAAE;EAC/B,gBAAgB,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;EAC3C,aAAa;EACb,iBAAiB;EACjB,gBAAgB,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,uBAAQ,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAC,EAAE,KAAK,CAAC,CAAC;EACvG,aAAa;EACb,YAAY,OAAO,IAAI,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;EACtC,SAAS;EACT;EACA,QAAQ,IAAI,QAAQ,CAAC,QAAQ,CAAC,EAAE;EAChC,YAAYA,IAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;EACzC;EACA,YAAY,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;EAClE,gBAAgBC,IAAI,QAAQ,GAAG,KAAK,CAAC;EACrC,gBAAgBD,IAAM,IAAI,GAAG;EAC7B,oBAAoB,EAAE,EAAE,IAAI;EAC5B,oBAAoB,EAAE,EAAE,OAAO;EAC/B,oBAAoB,EAAE,EAAE,IAAI;EAC5B,oBAAoB,EAAE,EAAE,IAAI;EAC5B,oBAAoB,KAAK,EAAE,OAAO;EAClC,oBAAoB,MAAM,EAAE,QAAQ;EACpC,iBAAiB,CAAC;EAClB,gBAAgB,IAAI,CAAC,IAAI,YAAG,QAAQ,EAAE,SAAS,EAAK;EACpD,oBAAoB,IAAI,IAAI,CAAC,OAAO,QAAK,UAAW,KAAK,CAAC,EAAE;EAC5D,wBAAwB,QAAQ,GAAG,SAAS,CAAC;EAC7C,wBAAwB,OAAO,KAAK,CAAC;EACrC,qBAAqB;EACrB,oBAAoB,OAAO;EAC3B,iBAAiB,CAAC,CAAC;EACnB,gBAAgB,OAAO,IAAI,EAAE,CAAC,kBAAkB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;EAClE,aAAa;EACb;EACA,YAAYA,IAAM,YAAY,GAAG,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;EACpF,YAAY,IAAI,CAAC,YAAY,EAAE;EAC/B,gBAAgB,OAAO,IAAI,EAAE,CAAC,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC;EACnE,aAAa;EACb,YAAYA,IAAM,OAAO,GAAG,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;EACvE,YAAY,IAAI,OAAO,EAAE;EACzB,gBAAgB,OAAO,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;EACzC,aAAa;EACb,YAAY,OAAO,IAAI,EAAE,EAAE,CAAC;EAC5B,SAAS;EACT,QAAQ,IAAI,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;EACxD,YAAY,OAAO,IAAI,EAAE,CAAC,QAAQ,CAAC,CAAC;EACpC,SAAS;EACT,QAAQ,OAAO,IAAI,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;EAClC,KAAK,CAAC;EACN,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,SAAS,CAAC;EACxB,IAAI,OAAO,CAAC,CAAC;EACb,CAAC;EACDA,IAAM,CAAC,GAAG,IAAI,EAAE;;EC9DhB;EACA;EACA,UAAU,sBAAO,CAAC,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,aAAa,IAAC,CAAC,CAAC;MAE9C,IAAI,GAAG;MACX,CAAC,EAAE,CAAC;;;ECNN,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,UAAU,QAAQ,EAAE;EAChC,IAAI,OAAO,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;EAChC,CAAC;;ECHD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,QAAQ,CAAC,SAAS,EAAE,QAAQ,EAAE;EACvC,IAAI,OAAO,SAAS,KAAK,QAAQ,IAAI,SAAS,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;EAC7E;;ECbA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,KAAK,CAAC,KAAK,EAAE,MAAM,EAAE;EAC9B,IAAI,IAAI,CAAC,MAAM,YAAG,CAAC,EAAE,KAAK,EAAK;EAC/B,QAAQ,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EAC1B,KAAK,CAAC,CAAC;EACP,IAAI,OAAO,KAAK,CAAC;EACjB;;ECfA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,UAAU,KAAK,EAAE;EAC5B,IAAI,OAAO,KAAK,KAAK,SAAS;EAC9B,UAAU,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;EAC7B,UAAU,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;EACzD,CAAC;;ECAD,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,UAAU,QAAQ,EAAE;EAChC,IAAIA,IAAM,aAAa,GAAG,EAAE,CAAC;EAC7B,IAAI,IAAI,CAAC,IAAI,WAAE,CAAC,EAAE,OAAO,EAAK;EAC9B,QAAQ,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;EAC1E,KAAK,CAAC,CAAC;EACP,IAAI,OAAO,IAAI,EAAE,CAAC,aAAa,CAAC,CAAC;EACjC,CAAC;;ECPD;EACAA,IAAM,QAAQ,GAAG,EAAE,CAAC;EACpB;EACAC,IAAI,aAAa,GAAG,CAAC,CAAC;EACtB;EACA;EACA;EACA,SAAS,YAAY,CAAC,OAAO,EAAE;EAC/B,IAAID,IAAM,GAAG,GAAG,cAAc,CAAC;EAC/B;EACA,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;EACvB;EACA,QAAQ,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,aAAa,CAAC;EACvC,KAAK;EACL;EACA,IAAI,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;EACxB,CAAC;EACD;EACA;EACA;EACA,SAAS,KAAK,CAAC,IAAI,EAAE;EACrB,IAAIA,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;EAClC,IAAI,OAAO;EACX,QAAQ,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;EACtB,QAAQ,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC;EAC3C,KAAK,CAAC;EACN,CAAC;EACD;EACA;EACA;EACA,SAAS,UAAU,CAAC,EAAE,EAAE;EACxB,IAAI,OAAO,IAAI,MAAM,CAAC,SAAS,GAAG,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,SAAS,CAAC,CAAC;EACxE,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,WAAW,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE;EACpD,IAAIA,IAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;EAC9B,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,MAAM,WAAE,OAAO,WAAK,OAAO;EAC9E,SAAS,CAAC,KAAK,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,CAAC;EACpD,SAAS,CAAC,KAAK,CAAC,EAAE,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;EAC5D,SAAS,CAAC,IAAI,IAAI,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,YAAY,CAAC,IAAI,CAAC,CAAC;EACpE,SAAS,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,IAAC,CAAC,CAAC;EACtD,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE;EACnD,IAAIA,IAAM,SAAS,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;EAC5C,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;EAC9B,QAAQ,QAAQ,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;EACjC,KAAK;EACL;EACA,IAAIC,IAAI,UAAU,GAAG,KAAK,CAAC;EAC3B,IAAI,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE;EAC/C,QAAQ,UAAU,GAAG,IAAI,CAAC;EAC1B,KAAK;EACL,IAAI,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,WAAE,IAAI,EAAK;EACvC,QAAQ,IAAI,CAAC,IAAI,EAAE;EACnB,YAAY,OAAO;EACnB,SAAS;EACT,QAAQD,IAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;EAClC,QAAQ,SAAS,MAAM,CAAC,CAAC,EAAE,IAAI,EAAE;EACjC;EACA,YAAYA,IAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI;EAC1C;EACA,YAAY,CAAC,CAAC,OAAO,KAAK,SAAS,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;EACnE,YAAY,IAAI,MAAM,KAAK,KAAK,EAAE;EAClC,gBAAgB,CAAC,CAAC,cAAc,EAAE,CAAC;EACnC,gBAAgB,CAAC,CAAC,eAAe,EAAE,CAAC;EACpC,aAAa;EACb,SAAS;EACT,QAAQ,SAAS,OAAO,CAAC,CAAC,EAAE;EAC5B;EACA,YAAY,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE;EAC5D,gBAAgB,OAAO;EACvB,aAAa;EACb;EACA,YAAY,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC;EAC3B,YAAY,IAAI,QAAQ,EAAE;EAC1B;EACA,gBAAgB,CAAC,CAAC,OAAO,CAAC;EAC1B,qBAAqB,IAAI,CAAC,QAAQ,CAAC;EACnC,qBAAqB,GAAG,EAAE;EAC1B,qBAAqB,OAAO,EAAE;EAC9B,qBAAqB,OAAO,WAAE,IAAI,EAAK;EACvC,oBAAoB,IAAI,IAAI,KAAK,CAAC,CAAC,MAAM;EACzC,wBAAwB,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE;EAClD,wBAAwB,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;EACxC,qBAAqB;EACrB,iBAAiB,CAAC,CAAC;EACnB,aAAa;EACb,iBAAiB;EACjB;EACA,gBAAgB,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;EACnC,aAAa;EACb,SAAS;EACT,QAAQA,IAAM,OAAO,GAAG;EACxB,YAAY,IAAI,EAAE,KAAK,CAAC,IAAI;EAC5B,YAAY,EAAE,EAAE,KAAK,CAAC,EAAE;EACxB,kBAAY,IAAI;EAChB,sBAAY,QAAQ;EACpB,YAAY,EAAE,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC,MAAM;EAC1C,YAAY,KAAK,EAAE,OAAO;EAC1B,SAAS,CAAC;EACV,QAAQ,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;EAC1C,QAAQ,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;EACpE,KAAK,CAAC,CAAC;EACP,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE;EAChD,IAAIA,IAAM,iBAAiB,GAAG,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC;EACpE,IAAIA,IAAM,WAAW,aAAI,OAAO,EAAK;EACrC,QAAQ,OAAO,iBAAiB,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;EAC7C,QAAQ,OAAO,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;EACxE,KAAK,CAAC;EACN,IAAI,IAAI,CAAC,KAAK,EAAE;EAChB,QAAQ,iBAAiB,CAAC,OAAO,WAAE,OAAO,WAAK,WAAW,CAAC,OAAO,IAAC,CAAC,CAAC;EACrE,KAAK;EACL,SAAS;EACT,QAAQ,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,WAAE,IAAI,EAAK;EAC3C,YAAY,IAAI,IAAI,EAAE;EACtB,gBAAgB,WAAW,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,OAAO,WAAE,OAAO,WAAK,WAAW,CAAC,OAAO,IAAC,CAAC,CAAC;EACtG,aAAa;EACb,SAAS,CAAC,CAAC;EACX,KAAK;EACL;;EC9IA,CAAC,CAAC,EAAE,CAAC,OAAO,GAAG,UAAU,IAAI,EAAE,eAAe,EAAE;EAChD,IAAIA,IAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;EAC9B,IAAIC,IAAI,WAAW,CAAC;EACpB,IAAID,IAAM,WAAW,GAAG;EACxB,QAAQ,OAAO,EAAE,IAAI;EACrB,QAAQ,UAAU,EAAE,IAAI;EACxB,KAAK,CAAC;EACN,IAAIA,IAAM,YAAY,GAAG,CAAC,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;EACjG,IAAI,IAAI,YAAY,EAAE;EACtB;EACA,QAAQ,WAAW,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;EAC9D,KAAK;EACL,SAAS;EACT,QAAQ,WAAW,CAAC,MAAM,GAAG,eAAe,CAAC;EAC7C,QAAQ,WAAW,GAAG,IAAI,WAAW,CAAC,KAAK,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;EAC/D,KAAK;EACL;EACA,IAAI,WAAW,CAAC,OAAO,GAAG,eAAe,CAAC;EAC1C;EACA,IAAI,WAAW,CAAC,GAAG,GAAG,KAAK,CAAC,EAAE,CAAC;EAC/B,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;EACjC,QAAQ,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;EACxC,KAAK,CAAC,CAAC;EACP,CAAC;;ECxBD,SAAS,MAAM,CAAC,MAAM,EAAE,OAAmB,EAAE;;;AAAC;EAC9C,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;EAC7B,IAAI,IAAI,CAAC,OAAO,YAAG,CAAC,EAAE,MAAM,EAAK;EACjC,QAAQ,IAAI,CAAC,MAAM,YAAG,IAAI,EAAE,KAAK,EAAK;EACtC,YAAY,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;EACrC,gBAAgB,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;EACrC,aAAa;EACb,SAAS,CAAC,CAAC;EACX,KAAK,CAAC,CAAC;EACP,IAAI,OAAO,MAAM,CAAC;EAClB;;ECVA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,KAAK,CAAC,GAAG,EAAE;EACpB,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;EACnD,QAAQ,OAAO,EAAE,CAAC;EAClB,KAAK;EACL,IAAIA,IAAM,IAAI,GAAG,EAAE,CAAC;EACpB,IAAI,SAAS,WAAW,CAAC,GAAG,EAAE,KAAK,EAAE;EACrC,QAAQC,IAAI,MAAM,CAAC;EACnB,QAAQ,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE;EACjC,YAAY,IAAI,CAAC,KAAK,YAAG,CAAC,EAAE,CAAC,EAAK;EAClC,gBAAgB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;EAC9D,oBAAoB,MAAM,GAAG,EAAE,CAAC;EAChC,iBAAiB;EACjB,qBAAqB;EACrB,oBAAoB,MAAM,GAAG,CAAC,CAAC;EAC/B,iBAAiB;EACjB,gBAAgB,WAAW,EAAI,GAAG,SAAI,MAAM,SAAK,CAAC,CAAC,CAAC;EACpD,aAAa,CAAC,CAAC;EACf,SAAS;EACT,aAAa;EACb,YAAY,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,EAAE,EAAE;EAC/C,gBAAgB,MAAM,GAAG,GAAG,CAAC;EAC7B,aAAa;EACb,iBAAiB;EACjB,gBAAgB,MAAM,GAAG,OAAI,kBAAkB,CAAC,KAAK,EAAG,CAAC;EACzD,aAAa;EACb,YAAY,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC;EACxD,SAAS;EACT,KAAK;EACL,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;EAC5B,QAAQ,IAAI,CAAC,GAAG,EAAE,YAAY;EAC9B,YAAY,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;EAC/C,SAAS,CAAC,CAAC;EACX,KAAK;EACL,SAAS;EACT,QAAQ,IAAI,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;EAC/B,KAAK;EACL,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;EAC1B;;ECpEA;EACAD,IAAM,aAAa,GAAG,EAAE,CAAC;EACzB;EACAA,IAAM,UAAU,GAAG;EACnB,IAAI,SAAS,EAAE,iBAAiB;EAChC,IAAI,WAAW,EAAE,mBAAmB;EACpC,IAAI,SAAS,EAAE,iBAAiB;EAChC,IAAI,YAAY,EAAE,oBAAoB;EACtC,CAAC;;ECDD;EACA;EACA;EACA;EACA,SAAS,iBAAiB,CAAC,MAAM,EAAE;EACnC,IAAI,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;EAChD,CAAC;EACD;EACA;EACA;EACA;EACA;EACA,SAAS,WAAW,CAAC,GAAG,EAAE,KAAK,EAAE;EACjC,IAAI,QAAU,GAAG,SAAI,OAAQ,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;EACvD,CAAC;EACD;EACA;EACA;EACA;EACA,SAAS,YAAY,CAAC,OAAO,EAAE;EAC/B;EACA,IAAIA,IAAM,QAAQ,GAAG;EACrB,QAAQ,GAAG,EAAE,EAAE;EACf,QAAQ,MAAM,EAAE,KAAK;EACrB,QAAQ,IAAI,EAAE,EAAE;EAChB,QAAQ,WAAW,EAAE,IAAI;EACzB,QAAQ,KAAK,EAAE,IAAI;EACnB,QAAQ,KAAK,EAAE,IAAI;EACnB,QAAQ,QAAQ,EAAE,EAAE;EACpB,QAAQ,QAAQ,EAAE,EAAE;EACpB,QAAQ,OAAO,EAAE,EAAE;EACnB,QAAQ,SAAS,EAAE,EAAE;EACrB,QAAQ,UAAU,EAAE,EAAE;EACtB,QAAQ,QAAQ,EAAE,MAAM;EACxB,QAAQ,WAAW,EAAE,mCAAmC;EACxD,QAAQ,OAAO,EAAE,CAAC;EAClB,QAAQ,MAAM,EAAE,IAAI;EACpB,KAAK,CAAC;EACN;EACA,IAAI,IAAI,CAAC,aAAa,YAAG,GAAG,EAAE,KAAK,EAAK;EACxC,QAAQA,IAAM,SAAS,GAAG;EAC1B,YAAY,YAAY;EACxB,YAAY,SAAS;EACrB,YAAY,OAAO;EACnB,YAAY,UAAU;EACtB,YAAY,YAAY,EACf,CAAC;EACV;EACA,QAAQ,IAAI,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;EAC/D,YAAY,QAAQ,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;EAClC,SAAS;EACT,KAAK,CAAC,CAAC;EACP,IAAI,OAAO,MAAM,CAAC,EAAE,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;EACzC,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,IAAI,CAAC,OAAO,EAAE;EACvB;EACA,IAAIC,IAAI,UAAU,GAAG,KAAK,CAAC;EAC3B;EACA,IAAID,IAAM,WAAW,GAAG,EAAE,CAAC;EAC3B;EACA,IAAIA,IAAM,aAAa,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;EAChD,IAAIC,IAAI,GAAG,GAAG,aAAa,CAAC,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;EAC9D,IAAID,IAAM,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;EACtD,IAAIC,IAAI,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC;EAClC,IAAID,IAAM,WAAW,GAAG,aAAa,CAAC,WAAW,CAAC;EAClD,IAAIA,IAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;EACtC,IAAIA,IAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;EACtC,IAAIA,IAAM,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;EAC5C,IAAIA,IAAM,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;EAC5C,IAAIA,IAAM,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC;EAC1C,IAAIA,IAAM,SAAS,GAAG,aAAa,CAAC,SAAS,CAAC;EAC9C,IAAIA,IAAM,UAAU,GAAG,aAAa,CAAC,UAAU,CAAC;EAChD,IAAIA,IAAM,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;EAC5C,IAAIA,IAAM,WAAW,GAAG,aAAa,CAAC,WAAW,CAAC;EAClD,IAAIA,IAAM,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC;EAC1C,IAAIA,IAAM,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC;EACxC;EACA;EACA,IAAI,IAAI,IAAI;EACZ,SAAS,iBAAiB,CAAC,MAAM,CAAC,IAAI,WAAW,CAAC;EAClD,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC;EACvB,QAAQ,EAAE,IAAI,YAAY,WAAW,CAAC;EACtC,QAAQ,EAAE,IAAI,YAAY,IAAI,CAAC;EAC/B,QAAQ,EAAE,IAAI,YAAY,QAAQ,CAAC;EACnC,QAAQ,EAAE,IAAI,YAAY,QAAQ,CAAC,EAAE;EACrC,QAAQ,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;EAC3B,KAAK;EACL;EACA,IAAI,IAAI,IAAI,IAAI,iBAAiB,CAAC,MAAM,CAAC,EAAE;EAC3C;EACA,QAAQ,GAAG,GAAG,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;EACrC,QAAQ,IAAI,GAAG,IAAI,CAAC;EACpB,KAAK;EACL;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,SAAS,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,QAAiB,EAAE;;;AAAC;EACxD;EACA,QAAQ,IAAI,MAAM,EAAE;EACpB,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;EAC/C,SAAS;EACT;EACA,QAAQC,IAAI,OAAO,CAAC;EACpB,QAAQA,IAAI,OAAO,CAAC;EACpB,QAAQ,IAAI,QAAQ,EAAE;EACtB;EACA,YAAY,IAAI,QAAQ,IAAI,aAAa,EAAE;EAC3C;EACA,gBAAgB,OAAO,GAAG,aAAa,CAAC,QAAQ,OAAC,gBAAI,IAAI,CAAC,CAAC;EAC3D,aAAa;EACb;EACA,YAAY,IAAI,aAAa,CAAC,QAAQ,CAAC,EAAE;EACzC;EACA,gBAAgB,OAAO,GAAG,aAAa,CAAC,QAAQ,OAAC,gBAAI,IAAI,CAAC,CAAC;EAC3D,aAAa;EACb;EACA,YAAY,IAAI,QAAQ,KAAK,YAAY;EACzC,iBAAiB,OAAO,KAAK,KAAK,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE;EAC1D,gBAAgB,UAAU,GAAG,IAAI,CAAC;EAClC,aAAa;EACb,SAAS;EACT,KAAK;EACL;EACA,IAAI,SAAS,GAAG,GAAG;EACnB,QAAQA,IAAI,UAAU,CAAC;EACvB,QAAQ,OAAO,IAAI,OAAO,WAAE,OAAO,EAAE,MAAM,EAAK;EAChD;EACA,YAAY,IAAI,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE;EACrD,gBAAgB,GAAG,GAAG,WAAW,CAAC,GAAG,WAAO,IAAI,CAAC,GAAG,KAAK,CAAC;EAC1D,aAAa;EACb;EACA,YAAYD,IAAM,GAAG,GAAG,IAAI,cAAc,EAAE,CAAC;EAC7C,YAAY,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;EAC7D,YAAY,IAAI,WAAW;EAC3B,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,WAAW,KAAK,KAAK,CAAC,EAAE;EAC/E,gBAAgB,GAAG,CAAC,gBAAgB,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;EAClE,aAAa;EACb;EACA,YAAY,IAAI,QAAQ,KAAK,MAAM,EAAE;EACrC,gBAAgB,GAAG,CAAC,gBAAgB,CAAC,QAAQ,EAAE,mCAAmC,CAAC,CAAC;EACpF,aAAa;EACb;EACA,YAAY,IAAI,OAAO,EAAE;EACzB,gBAAgB,IAAI,CAAC,OAAO,YAAG,GAAG,EAAE,KAAK,EAAK;EAC9C;EACA,oBAAoB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;EAC7C,wBAAwB,GAAG,CAAC,gBAAgB,CAAC,GAAG,EAAE,KAAK,GAAG,EAAE,CAAC,CAAC;EAC9D,qBAAqB;EACrB,iBAAiB,CAAC,CAAC;EACnB,aAAa;EACb;EACA,YAAYA,IAAM,WAAW,GAAG,wBAAwB,CAAC,IAAI,CAAC,GAAG,CAAC;EAClE,gBAAgB,MAAM,CAAC,EAAE,KAAK,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;EACnD,YAAY,IAAI,CAAC,WAAW,EAAE;EAC9B,gBAAgB,GAAG,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;EAC3E,aAAa;EACb,YAAY,IAAI,SAAS,EAAE;EAC3B,gBAAgB,IAAI,CAAC,SAAS,YAAG,GAAG,EAAE,KAAK,EAAK;EAChD;EACA,oBAAoB,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;EACrC,iBAAiB,CAAC,CAAC;EACnB,aAAa;EACb,YAAY,WAAW,CAAC,GAAG,GAAG,GAAG,CAAC;EAClC,YAAY,WAAW,CAAC,OAAO,GAAG,aAAa,CAAC;EAChD,YAAYC,IAAI,UAAU,CAAC;EAC3B,YAAY,GAAG,CAAC,MAAM,GAAG,YAAY;EACrC,gBAAgB,IAAI,UAAU,EAAE;EAChC,oBAAoB,YAAY,CAAC,UAAU,CAAC,CAAC;EAC7C,iBAAiB;EACjB;EACA,gBAAgBD,IAAM,mBAAmB,GAAG,CAAC,GAAG,CAAC,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,MAAM,GAAG,GAAG;EAClF,oBAAoB,GAAG,CAAC,MAAM,KAAK,GAAG;EACtC,oBAAoB,GAAG,CAAC,MAAM,KAAK,CAAC,CAAC;EACrC,gBAAgBC,IAAI,YAAY,CAAC;EACjC,gBAAgB,IAAI,mBAAmB,EAAE;EACzC,oBAAoB,IAAI,GAAG,CAAC,MAAM,KAAK,GAAG,IAAI,MAAM,KAAK,MAAM,EAAE;EACjE,wBAAwB,UAAU,GAAG,WAAW,CAAC;EACjD,qBAAqB;EACrB,yBAAyB,IAAI,GAAG,CAAC,MAAM,KAAK,GAAG,EAAE;EACjD,wBAAwB,UAAU,GAAG,aAAa,CAAC;EACnD,qBAAqB;EACrB,yBAAyB;EACzB,wBAAwB,UAAU,GAAG,SAAS,CAAC;EAC/C,qBAAqB;EACrB,oBAAoB,IAAI,QAAQ,KAAK,MAAM,EAAE;EAC7C,wBAAwB,IAAI;EAC5B,4BAA4B,YAAY;EACxC,gCAAgC,MAAM,KAAK,MAAM,GAAG,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;EAC7F,4BAA4B,WAAW,CAAC,IAAI,GAAG,YAAY,CAAC;EAC5D,yBAAyB;EACzB,wBAAwB,OAAO,GAAG,EAAE;EACpC,4BAA4B,UAAU,GAAG,aAAa,CAAC;EACvD,4BAA4B,OAAO,CAAC,UAAU,CAAC,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;EACjG,4BAA4B,MAAM,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;EAC1D,yBAAyB;EACzB,wBAAwB,IAAI,UAAU,KAAK,aAAa,EAAE;EAC1D,4BAA4B,OAAO,CAAC,UAAU,CAAC,WAAW,EAAE,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;EACnH,4BAA4B,OAAO,CAAC,YAAY,CAAC,CAAC;EAClD,yBAAyB;EACzB,qBAAqB;EACrB,yBAAyB;EACzB,wBAAwB,YAAY;EACpC,4BAA4B,MAAM,KAAK,MAAM;EAC7C,kCAAkC,SAAS;EAC3C,kCAAkC,GAAG,CAAC,YAAY,KAAK,MAAM,IAAI,GAAG,CAAC,YAAY,KAAK,EAAE;EACxF,sCAAsC,GAAG,CAAC,YAAY;EACtD,sCAAsC,GAAG,CAAC,QAAQ,CAAC;EACnD,wBAAwB,WAAW,CAAC,IAAI,GAAG,YAAY,CAAC;EACxD,wBAAwB,OAAO,CAAC,UAAU,CAAC,WAAW,EAAE,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;EAC/G,wBAAwB,OAAO,CAAC,YAAY,CAAC,CAAC;EAC9C,qBAAqB;EACrB,iBAAiB;EACjB,qBAAqB;EACrB,oBAAoB,UAAU,GAAG,OAAO,CAAC;EACzC,oBAAoB,OAAO,CAAC,UAAU,CAAC,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;EACzF,oBAAoB,MAAM,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;EAClD,iBAAiB;EACjB;EACA,gBAAgB,IAAI,CAAC,CAAC,aAAa,CAAC,UAAU,EAAE,UAAU,CAAC,YAAG,CAAC,EAAE,IAAI,EAAK;EAC1E,oBAAoB,IAAI,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;EAClD,wBAAwB,IAAI,mBAAmB,EAAE;EACjD,4BAA4B,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;EAC5E,yBAAyB;EACzB,6BAA6B;EAC7B,4BAA4B,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;EAC9D,yBAAyB;EACzB,qBAAqB;EACrB,iBAAiB,CAAC,CAAC;EACnB,gBAAgB,OAAO,CAAC,UAAU,CAAC,YAAY,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;EAC3F,aAAa,CAAC;EACd,YAAY,GAAG,CAAC,OAAO,GAAG,YAAY;EACtC,gBAAgB,IAAI,UAAU,EAAE;EAChC,oBAAoB,YAAY,CAAC,UAAU,CAAC,CAAC;EAC7C,iBAAiB;EACjB,gBAAgB,OAAO,CAAC,UAAU,CAAC,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;EACzF,gBAAgB,OAAO,CAAC,UAAU,CAAC,YAAY,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;EACxF,gBAAgB,MAAM,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;EAClD,aAAa,CAAC;EACd,YAAY,GAAG,CAAC,OAAO,GAAG,YAAY;EACtC,gBAAgBA,IAAI,UAAU,GAAG,OAAO,CAAC;EACzC,gBAAgB,IAAI,UAAU,EAAE;EAChC,oBAAoB,UAAU,GAAG,SAAS,CAAC;EAC3C,oBAAoB,YAAY,CAAC,UAAU,CAAC,CAAC;EAC7C,iBAAiB;EACjB,gBAAgB,OAAO,CAAC,UAAU,CAAC,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;EACrF,gBAAgB,OAAO,CAAC,UAAU,CAAC,YAAY,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;EAC3F,gBAAgB,MAAM,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;EAC9C,aAAa,CAAC;EACd;EACA,YAAY,OAAO,CAAC,UAAU,CAAC,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,CAAC,CAAC;EAC1E,YAAY,IAAI,UAAU,EAAE;EAC5B,gBAAgB,MAAM,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;EAC5C,gBAAgB,OAAO;EACvB,aAAa;EACb;EACA,YAAY,IAAI,OAAO,GAAG,CAAC,EAAE;EAC7B,gBAAgB,UAAU,GAAG,UAAU,aAAO;EAC9C,oBAAoB,GAAG,CAAC,KAAK,EAAE,CAAC;EAChC,iBAAiB,EAAE,OAAO,CAAC,CAAC;EAC5B,aAAa;EACb;EACA,YAAY,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC3B,SAAS,CAAC,CAAC;EACX,KAAK;EACL,IAAI,OAAO,GAAG,EAAE,CAAC;EACjB;;EChSA,CAAC,CAAC,IAAI,GAAG,IAAI;;ECAb;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,SAAS,CAAC,OAAO,EAAE;EAC5B,IAAI,OAAO,MAAM,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;EAC1C;;ECbA,CAAC,CAAC,SAAS,GAAG,SAAS;;ECAvB,CAAC,CAAC,QAAQ,GAAG,QAAQ;;ECFrBD,IAAM,MAAM,GAAG,yBAAyB;;ECGxC;EACA;EACA;EACA;EACA;EACA,SAAS,kBAAkB,CAAC,OAAO,EAAE,MAAM,EAAE;EAC7C;EACA,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;EAC1B;EACA,QAAQ,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;EAC7B,KAAK;EACL,IAAI,IAAI,CAAC,MAAM,YAAG,GAAG,EAAE,KAAK,EAAK;EACjC;EACA,QAAQ,OAAO,CAAC,MAAM,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;EAClD,KAAK,CAAC,CAAC;EACP,CAAC;EACD,SAAS,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;;AAAC;EACpC;EACA;EACA,IAAI,IAAI,YAAY,CAAC,GAAG,CAAC,EAAE;EAC3B,QAAQ,kBAAkB,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;EACzC,QAAQ,OAAO,GAAG,CAAC;EACnB,KAAK;EACL;EACA;EACA,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;EAC7B,QAAQ,kBAAkB,CAAC,OAAO,UAAE,EAAC,KAAC,CAAC,GAAG,IAAG,aAAQ,CAAC;EACtD,QAAQ,OAAO,KAAK,CAAC;EACrB,KAAK;EACL;EACA;EACA,IAAI,IAAI,WAAW,CAAC,GAAG,CAAC,EAAE;EAC1B;EACA,QAAQ,OAAO,OAAO,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;EACtD,KAAK;EACL;EACA;EACA,IAAI,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;EAC3B;EACA,IAAI,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,OAAO,CAAC,MAAM,CAAC,EAAE;EACnD;EACA,QAAQ,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;EACpC,KAAK;EACL,IAAI,OAAO,SAAS,CAAC;EACrB;;EC7CA,CAAC,CAAC,IAAI,GAAG,IAAI;;ECAb,CAAC,CAAC,IAAI,GAAG,IAAI;;ECCb,CAAC,CAAC,MAAM,GAAG,YAAsB;;;;AAAC;EAClC,IAAI,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;EAC9B,QAAQ,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,YAAG,IAAI,EAAE,KAAK,EAAK;EAC1C,YAAYG,MAAI,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;EAC/B,SAAS,CAAC,CAAC;EACX,QAAQ,OAAO,IAAI,CAAC;EACpB,KAAK;EACL,IAAI,OAAO,YAAM,WAAC,OAAO,CAAC,KAAK,EAAE,EAAE,OAAO,CAAC,KAAK,EAAE,WAAK,SAAO,CAAC,CAAC;EAChE,CAAC;;ECVD,SAAS,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE;;AAAC;EAClC,IAAIF,IAAI,KAAK,CAAC;EACd,IAAID,IAAM,GAAG,GAAG,EAAE,CAAC;EACnB,IAAI,IAAI,CAAC,QAAQ,YAAG,CAAC,EAAE,OAAO,EAAK;EACnC,QAAQ,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;EAClD,QAAQ,IAAI,KAAK,IAAI,IAAI,EAAE;EAC3B,YAAY,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EAC5B,SAAS;EACT,KAAK,CAAC,CAAC;EACP,IAAI,cAAO,IAAG,YAAM,MAAI,GAAG,CAAC,CAAC;EAC7B;;ECTA,CAAC,CAAC,GAAG,GAAG,GAAG;;ECAX,CAAC,CAAC,KAAK,GAAG,KAAK;;ECAf,CAAC,CAAC,KAAK,GAAG,KAAK;;ECCf;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,UAAU,CAAC,OAAO,EAAE,IAAI,EAAE;EACnC;EACA,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;EAC1B,QAAQ,OAAO;EACf,KAAK;EACL,IAAIA,IAAM,MAAM,aAAI,QAAQ,EAAK;EACjC,QAAQ,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;EACzC;EACA,QAAQ,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,EAAE;EACvC;EACA,YAAY,OAAO,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;EAC7C;EACA,YAAY,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC;EAC7C,SAAS;EACT,KAAK,CAAC;EACN,IAAI,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE;EAC3B;EACA,QAAQ,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;EAC/B;EACA,QAAQ,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC;EAC/B;EACA,KAAK;EACL,SAAS,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE;EAC7B,QAAQ,IAAI;EACZ,aAAa,KAAK,CAAC,GAAG,CAAC;EACvB,aAAa,MAAM,WAAE,QAAQ,WAAK,WAAQ,CAAC;EAC3C,aAAa,OAAO,WAAE,QAAQ,WAAK,MAAM,CAAC,QAAQ,IAAC,CAAC,CAAC;EACrD,KAAK;EACL,SAAS;EACT,QAAQ,IAAI,CAAC,IAAI,YAAG,CAAC,EAAE,QAAQ,WAAK,MAAM,CAAC,QAAQ,IAAC,CAAC,CAAC;EACtD,KAAK;EACL;;EC9DA,CAAC,CAAC,UAAU,GAAG,UAAU;;ECDzB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,MAAM,CAAC,GAAG,EAAE;EACrB,IAAIA,IAAM,MAAM,GAAG,EAAE,CAAC;EACtB,IAAI,IAAI,CAAC,GAAG,YAAG,CAAC,EAAE,GAAG,EAAK;EAC1B,QAAQ,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;EACxC,YAAY,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;EAC7B,SAAS;EACT,KAAK,CAAC,CAAC;EACP,IAAI,OAAO,MAAM,CAAC;EAClB;;EChBA,CAAC,CAAC,MAAM,GAAG,MAAM;;ECGjB,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,UAAU,QAAQ,EAAE;EAC/B,IAAI,OAAO,IAAI,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;EAChE,CAAC;;ECHD,IAAI,CAAC,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAC,YAAG,CAAC,EAAE,IAAI,EAAK;EAC/C,IAAI,CAAC,CAAC,EAAE,EAAI,IAAI,YAAQ,GAAG,UAAU,SAAS,EAAE;EAChD,QAAQ,IAAI,IAAI,KAAK,QAAQ,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;EACpD,YAAY,OAAO,IAAI,CAAC,IAAI,WAAE,CAAC,EAAE,OAAO,EAAK;EAC7C,gBAAgB,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;EAClD,aAAa,CAAC,CAAC;EACf,SAAS;EACT,QAAQ,OAAO,IAAI,CAAC,IAAI,WAAE,CAAC,EAAE,OAAO,EAAK;EACzC,YAAY,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;EACrC,gBAAgB,OAAO;EACvB,aAAa;EACb,YAAYA,IAAM,OAAO,GAAG,CAAC,UAAU,CAAC,SAAS,CAAC;EAClD,kBAAkB,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;EACjF,kBAAkB,SAAS;EAC3B,iBAAiB,KAAK,CAAC,GAAG,CAAC;EAC3B,iBAAiB,MAAM,WAAE,IAAI,WAAK,OAAI,CAAC,CAAC;EACxC,YAAY,IAAI,CAAC,OAAO,YAAG,CAAC,EAAE,GAAG,EAAK;EACtC,gBAAgB,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;EAC7C,aAAa,CAAC,CAAC;EACf,SAAS,CAAC,CAAC;EACX,KAAK,CAAC;EACN,CAAC,CAAC;;ECtBF,IAAI,CAAC,CAAC,cAAc,EAAE,aAAa,CAAC,YAAG,SAAS,EAAE,IAAI,EAAK;EAC3D,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,UAAU,MAAM,EAAE;EACnC,QAAQA,IAAM,QAAQ,GAAG,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC;EACpE,QAAQA,IAAM,OAAO,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;EAClC,QAAQA,IAAM,MAAM,GAAG,EAAE,CAAC;EAC1B,QAAQ,OAAO,CAAC,IAAI,WAAE,KAAK,EAAE,MAAM,EAAK;EACxC,YAAY,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;EACpC,gBAAgB,OAAO;EACvB,aAAa;EACb,YAAY,QAAQ,CAAC,IAAI,WAAE,CAAC,EAAE,OAAO,EAAK;EAC1C,gBAAgBA,IAAM,OAAO,GAAG,KAAK;EACrC,sBAAsB,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC;EAC7C,sBAAsB,OAAO,CAAC;EAC9B,gBAAgBA,IAAM,YAAY,GAAG,SAAS,GAAG,MAAM,CAAC,WAAW,GAAG,MAAM,CAAC;EAC7E,gBAAgB,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;EACrC,gBAAgB,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;EACtE,aAAa,CAAC,CAAC;EACf,SAAS,CAAC,CAAC;EACX,QAAQ,OAAO,CAAC,CAAC,SAAS,GAAG,MAAM,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC,CAAC;EACxD,KAAK,CAAC;EACN,CAAC,CAAC;;ECjBF;EACA;EACA;EACA;EACA,SAAS,WAAW,CAAC,MAAM,EAAE;EAC7B,IAAI,QAAQ,QAAQ,CAAC,MAAM,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,EAAE;EAC1F,CAAC;EACD,IAAI,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,YAAG,SAAS,EAAE,IAAI,EAAK;EAC/C,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,YAAmB;;;AAAC;EACrC;EACA,QAAQ,IAAI,SAAS,KAAK,CAAC,EAAE;EAC7B,YAAY,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;EAClC,SAAS;EACT,QAAQ,OAAO,IAAI,CAAC,IAAI,WAAE,KAAK,EAAE,OAAO,EAAK;EAC7C,YAAYA,IAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;EAC/C,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;EACnE,kBAAkB,IAAI,CAAC;EACvB,YAAY,IAAI,CAAC,OAAO,YAAG,CAAC,EAAE,MAAM,EAAK;EACzC,gBAAgBC,IAAI,OAAO,CAAC;EAC5B,gBAAgB,IAAI,WAAW,CAAC,MAAM,CAAC,EAAE;EACzC,oBAAoB,OAAO,GAAG,CAAC,CAAC,kBAAkB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;EACnE,iBAAiB;EACjB,qBAAqB,IAAI,KAAK,IAAI,SAAS,CAAC,MAAM,CAAC,EAAE;EACrD,oBAAoB,OAAO,GAAG,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;EACxD,iBAAiB;EACjB,qBAAqB;EACrB,oBAAoB,OAAO,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;EACxC,iBAAiB;EACjB,gBAAgB,OAAO,CAAC,SAAS,GAAG,aAAa,GAAG,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC;EAC7E,aAAa,CAAC,CAAC;EACf,SAAS,CAAC,CAAC;EACX,KAAK,CAAC;EACN,CAAC,CAAC;;ECjCF,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,UAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE;;AAAC;EACjD;EACA,IAAI,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE;EAC7B,QAAQ,IAAI,CAAC,KAAK,YAAG,IAAI,EAAE,EAAE,EAAK;EAClC;EACA;EACA,YAAYE,MAAI,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAC;EACzC,SAAS,CAAC,CAAC;EACX,QAAQ,OAAO,IAAI,CAAC;EACpB,KAAK;EACL;EACA,IAAI,IAAI,QAAQ,KAAK,KAAK,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE;EACpD,QAAQ,QAAQ,GAAG,QAAQ,CAAC;EAC5B,QAAQ,QAAQ,GAAG,SAAS,CAAC;EAC7B;EACA,KAAK;EACL;EACA,IAAI,IAAI,QAAQ,KAAK,KAAK,EAAE;EAC5B,QAAQ,QAAQ,GAAG,WAAW,CAAC;EAC/B,KAAK;EACL,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;EACjC,QAAQ,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;EAChD,KAAK,CAAC,CAAC;EACP,CAAC;;ECtBD,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,EAAE;;AAAC;EAC3D;EACA,IAAI,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE;EAC7B;EACA,QAAQ,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;EACjC;EACA,YAAY,IAAI,GAAG,IAAI,IAAI,QAAQ,CAAC;EACpC,YAAY,QAAQ,GAAG,SAAS,CAAC;EACjC,SAAS;EACT,QAAQ,IAAI,CAAC,KAAK,YAAG,IAAI,EAAE,EAAE,EAAK;EAClC;EACA;EACA,YAAYA,MAAI,CAAC,EAAE,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;EACnD,SAAS,CAAC,CAAC;EACX,QAAQ,OAAO,IAAI,CAAC;EACpB,KAAK;EACL,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,QAAQ,IAAI,IAAI,EAAE;EAC1C;EACA,QAAQ,QAAQ,GAAG,QAAQ,CAAC;EAC5B,QAAQ,IAAI,GAAG,QAAQ,GAAG,SAAS,CAAC;EACpC,KAAK;EACL,SAAS,IAAI,QAAQ,IAAI,IAAI,EAAE;EAC/B,QAAQ,IAAI,QAAQ,CAAC,QAAQ,CAAC,EAAE;EAChC;EACA,YAAY,QAAQ,GAAG,IAAI,CAAC;EAC5B,YAAY,IAAI,GAAG,SAAS,CAAC;EAC7B,SAAS;EACT,aAAa;EACb;EACA,YAAY,QAAQ,GAAG,IAAI,CAAC;EAC5B,YAAY,IAAI,GAAG,QAAQ,CAAC;EAC5B,YAAY,QAAQ,GAAG,SAAS,CAAC;EACjC,SAAS;EACT,KAAK;EACL,IAAI,IAAI,QAAQ,KAAK,KAAK,EAAE;EAC5B,QAAQ,QAAQ,GAAG,WAAW,CAAC;EAC/B,KAAK;EACL,SAAS,IAAI,CAAC,QAAQ,EAAE;EACxB,QAAQ,OAAO,IAAI,CAAC;EACpB,KAAK;EACL;EACA,IAAI,IAAI,GAAG,EAAE;EACb;EACA,QAAQH,IAAM,KAAK,GAAG,IAAI,CAAC;EAC3B,QAAQA,IAAM,YAAY,GAAG,QAAQ,CAAC;EACtC,QAAQ,QAAQ,GAAG,UAAU,KAAK,EAAE;EACpC,YAAY,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;EACtD;EACA,YAAY,OAAO,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;EACvD,SAAS,CAAC;EACV,KAAK;EACL,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;EACjC,QAAQ,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;EACnD,KAAK,CAAC,CAAC;EACP,CAAC;;ECxDD,IAAI,CAAC,UAAU,YAAG,IAAI,EAAE,SAAS,EAAK;EACtC,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,UAAU,EAAE,EAAE;EAC/B,QAAQ,OAAO,IAAI,CAAC,EAAE,CAAC,SAAS,YAAG,CAAC,EAAE,MAAM,EAAK;EACjD,YAAY,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;EAC3D,SAAS,CAAC,CAAC;EACX,KAAK,CAAC;EACN,CAAC,CAAC;;ECPF,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,UAAU,QAAQ,EAAE;EAC/B,IAAI,OAAO,IAAI,EAAE,CAAC,GAAG,CAAC,IAAI,YAAG,OAAO,EAAE,CAAC,WAAK,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,OAAO,IAAC,CAAC,CAAC,CAAC;EACjF,CAAC;;ECHD,CAAC,CAAC,EAAE,CAAC,KAAK,GAAG,YAAY;EACzB,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,YAAY;EAChC,QAAQ,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;EACpC,KAAK,CAAC,CAAC;EACP,CAAC;;ECHD,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,QAAQ,EAAE;EAC9B,IAAIC,IAAI,SAAS,GAAG,KAAK,CAAC;EAC1B,IAAI,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE;EAC9B,QAAQ,IAAI,CAAC,IAAI,WAAE,KAAK,EAAE,OAAO,EAAK;EACtC,YAAY,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE;EACxD,gBAAgB,SAAS,GAAG,IAAI,CAAC;EACjC,aAAa;EACb,SAAS,CAAC,CAAC;EACX,QAAQ,OAAO,SAAS,CAAC;EACzB,KAAK;EACL,IAAI,IAAI,QAAQ,CAAC,QAAQ,CAAC,EAAE;EAC5B,QAAQ,IAAI,CAAC,IAAI,WAAE,CAAC,EAAE,OAAO,EAAK;EAClC,YAAY,IAAI,UAAU,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,OAAO,CAAC,EAAE;EAC1D,gBAAgB,OAAO;EACvB,aAAa;EACb;EACA,YAAYD,IAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,iBAAiB,CAAC;EACzE,YAAY,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE;EACjD,gBAAgB,SAAS,GAAG,IAAI,CAAC;EACjC,aAAa;EACb,SAAS,CAAC,CAAC;EACX,QAAQ,OAAO,SAAS,CAAC;EACzB,KAAK;EACL,IAAIA,IAAM,YAAY,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;EACrC,IAAI,IAAI,CAAC,IAAI,WAAE,CAAC,EAAE,OAAO,EAAK;EAC9B,QAAQ,YAAY,CAAC,IAAI,WAAE,CAAC,EAAE,OAAO,EAAK;EAC1C,YAAY,IAAI,OAAO,KAAK,OAAO,EAAE;EACrC,gBAAgB,SAAS,GAAG,IAAI,CAAC;EACjC,aAAa;EACb,SAAS,CAAC,CAAC;EACX,KAAK,CAAC,CAAC;EACP,IAAI,OAAO,SAAS,CAAC;EACrB,CAAC;;EChCD,CAAC,CAAC,EAAE,CAAC,MAAM,GAAG,UAAU,QAAQ,EAAE;EAClC,IAAI,OAAO,IAAI,CAAC,IAAI,WAAE,CAAC,EAAE,OAAO,EAAK;EACrC,QAAQ,IAAI,OAAO,CAAC,UAAU,KAAK,CAAC,QAAQ,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE;EAC1E,YAAY,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;EACpD,SAAS;EACT,KAAK,CAAC,CAAC;EACP,CAAC;;ECAD,IAAI,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,YAAG,SAAS,EAAE,IAAI,EAAK;EACjD,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,YAAmB;;;AAAC;EACrC,QAAQ,OAAO,IAAI,CAAC,IAAI,WAAE,KAAK,EAAE,OAAO,EAAK;;AAAC;EAC9C,YAAYA,IAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;EAClD,YAAYA,IAAM,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC;EAClD,YAAYA,IAAM,KAAK,GAAG,WAAW;EACrC,kBAAkB,UAAU,CAAC,SAAS,GAAG,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;EAC7D,kBAAkB,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EAChD,YAAY,IAAI,CAAC,WAAW,EAAE;EAC9B,gBAAgB,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;EAC3C,aAAa;EACb,YAAYC,IAAI,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;EAC9C,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;EACnE,kBAAkB,IAAI,CAAC;EACvB;EACA,YAAY,IAAI,KAAK,EAAE;EACvB,gBAAgB,QAAQ,GAAG,QAAQ,CAAC,GAAG,WAAE,OAAO,EAAK;EACrD,oBAAoB,OAAO,QAAQ,CAAC,OAAO,CAAC,GAAG,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,CAAC;EAC5E,iBAAiB,CAAC,CAAC;EACnB,aAAa;EACb,mBAAY,CAAC,CAAC,KAAK,GAAE,SAAS,GAAG,OAAO,GAAG,QAAQ,OAAC,MAAI,QAAQ,CAAC,CAAC;EAClE,YAAY,IAAI,CAAC,WAAW,EAAE;EAC9B,gBAAgB,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;EAC3C,aAAa;EACb,SAAS,CAAC,CAAC;EACX,KAAK,CAAC;EACN,CAAC,CAAC;;EC7BF,IAAI,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC,YAAG,SAAS,EAAE,IAAI,EAAK;EACrD,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,UAAU,MAAM,EAAE;EACnC,QAAQD,IAAM,WAAW,GAAG,EAAE,CAAC;EAC/B,QAAQA,IAAM,OAAO,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,WAAE,CAAC,EAAE,OAAO,EAAK;EACtD,YAAYA,IAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;EAClD,YAAYA,IAAM,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC;EAClD,YAAY,IAAI,WAAW,EAAE;EAC7B,gBAAgB,OAAO,UAAU,CAAC,SAAS,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC;EACnE,aAAa;EACb,YAAYA,IAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EACxD,YAAY,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;EACvC,YAAY,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EACpC,YAAY,OAAO,KAAK,CAAC;EACzB,SAAS,CAAC,CAAC;EACX,QAAQA,IAAM,OAAO,GAAG,IAAI,CAAC,SAAS,GAAG,cAAc,GAAG,aAAa,CAAC,CAAC,OAAO,CAAC,CAAC;EAClF,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC,MAAM,EAAE,CAAC;EAChC,QAAQ,OAAO,OAAO,CAAC;EACvB,KAAK,CAAC;EACN,CAAC,CAAC;;ECpBF,IAAI,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,YAAG,SAAS,EAAE,IAAI,EAAK;EACnD,IAAI,SAAS,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;EACtC;EACA,QAAQ,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;EAChC,YAAY,OAAO;EACnB,SAAS;EACT,QAAQ,QAAQ,SAAS;EACzB;EACA,YAAY,KAAK,CAAC;EAClB,gBAAgB,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE;EACnC,oBAAoB,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;EACjD,iBAAiB;EACjB,qBAAqB;EACrB,oBAAoB,OAAO,CAAC,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;EACrD,iBAAiB;EACjB,gBAAgB,MAAM;EACtB;EACA,YAAY,KAAK,CAAC;EAClB;EACA,gBAAgB,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;EACrC,gBAAgB,MAAM;EACtB;EACA,YAAY;EACZ,gBAAgB,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;EACvC;EACA,gBAAgB,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC;EACpD,4BAAyB,SAAQ,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG;EACpE,sBAAsB,KAAK,CAAC;EAC5B,gBAAgB,MAAM;EACtB,SAAS;EACT,KAAK;EACL,IAAI,SAAS,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE;EAC/B,QAAQ,QAAQ,SAAS;EACzB;EACA,YAAY,KAAK,CAAC;EAClB;EACA,gBAAgBA,IAAM,KAAK,GAAG,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;EACxD,gBAAgB,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,KAAK,CAAC;EACzD;EACA,YAAY,KAAK,CAAC;EAClB;EACA,gBAAgB,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;EACpC;EACA,YAAY;EACZ,gBAAgB,OAAO,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;EAC9C,SAAS;EACT,KAAK;EACL,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,UAAU,GAAG,EAAE,KAAK,EAAE;;AAAC;EACxC,QAAQ,IAAI,YAAY,CAAC,GAAG,CAAC,EAAE;EAC/B,YAAY,IAAI,CAAC,GAAG,YAAG,CAAC,EAAE,CAAC,EAAK;EAChC;EACA,gBAAgBG,MAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACjC,aAAa,CAAC,CAAC;EACf,YAAY,OAAO,IAAI,CAAC;EACxB,SAAS;EACT,QAAQ,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;EACpC,YAAYH,IAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;EACpC,YAAY,OAAO,SAAS,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,SAAS,CAAC;EACtE,SAAS;EACT,QAAQ,OAAO,IAAI,CAAC,IAAI,WAAE,CAAC,EAAE,OAAO,EAAK;EACzC,YAAY,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,UAAU,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;EACrG,SAAS,CAAC,CAAC;EACX,KAAK,CAAC;EACN,CAAC,CAAC;;EC5DF,CAAC,CAAC,EAAE,CAAC,QAAQ,GAAG,UAAU,QAAQ,EAAE;EACpC,IAAIA,IAAM,QAAQ,GAAG,EAAE,CAAC;EACxB,IAAI,IAAI,CAAC,IAAI,WAAE,CAAC,EAAE,OAAO,EAAK;EAC9B,QAAQ,IAAI,CAAC,OAAO,CAAC,UAAU,YAAG,EAAE,EAAE,SAAS,EAAK;EACpD,YAAY,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE;EACvC,gBAAgB,OAAO;EACvB,aAAa;EACb,YAAY,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE;EACxD,gBAAgB,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;EACzC,aAAa;EACb,SAAS,CAAC,CAAC;EACX,KAAK,CAAC,CAAC;EACP,IAAI,OAAO,IAAI,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;EACpC,CAAC;;EClBD,CAAC,CAAC,EAAE,CAAC,KAAK,GAAG,YAAmB;;;AAAC;EACjC,IAAI,OAAO,IAAI,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;EAC9C,CAAC;;ECDD,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,KAAK,EAAE;EAC3B,IAAIA,IAAM,GAAG,GAAG,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;EACjF,IAAI,OAAO,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC;EACvB,CAAC;;ECAc,SAAS,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE;EAC1E,IAAIA,IAAM,GAAG,GAAG,EAAE,CAAC;EACnB,IAAIC,IAAI,MAAM,CAAC;EACf,IAAI,SAAS,CAAC,IAAI,WAAE,CAAC,EAAE,OAAO,EAAK;EACnC,QAAQ,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;EAC/B;EACA,QAAQ,OAAO,MAAM,IAAI,SAAS,CAAC,MAAM,CAAC,EAAE;EAC5C;EACA,YAAY,IAAI,SAAS,KAAK,CAAC,EAAE;EACjC,gBAAgB,IAAI,QAAQ,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE;EACxD,oBAAoB,MAAM;EAC1B,iBAAiB;EACjB,gBAAgB,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE;EACrD,oBAAoB,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;EACrC,iBAAiB;EACjB,aAAa;EACb;EACA,iBAAiB,IAAI,SAAS,KAAK,CAAC,EAAE;EACtC,gBAAgB,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE;EACzD,oBAAoB,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;EACrC,iBAAiB;EACjB,gBAAgB,MAAM;EACtB,aAAa;EACb;EACA,iBAAiB;EACjB,gBAAgB,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE;EACzD,oBAAoB,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;EACrC,iBAAiB;EACjB,aAAa;EACb;EACA,YAAY,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;EAClC,SAAS;EACT,KAAK,CAAC,CAAC;EACP,IAAI,OAAO,IAAI,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;EAC/B;;ECpCA,IAAI,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,QAAQ,CAAC,YAAG,SAAS,EAAE,IAAI,EAAK;EAC/C,IAAI,CAAC,CAAC,EAAE,aAAU,MAAO,GAAG,UAAU,QAAQ,EAAE,MAAM,EAAE;EACxD;EACA,QAAQD,IAAM,MAAM,GAAG,CAAC,SAAS,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC;EACnE,QAAQ,OAAO,GAAG,CAAC,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;EACtE,KAAK,CAAC;EACN,CAAC,CAAC;;ECLF,CAAC,CAAC,EAAE,CAAC,OAAO,GAAG,UAAU,QAAQ,EAAE;EACnC,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE;EAC3B,QAAQ,OAAO,IAAI,CAAC;EACpB,KAAK;EACL,IAAIA,IAAM,OAAO,GAAG,EAAE,CAAC;EACvB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC,IAAI,WAAE,CAAC,EAAE,OAAO,EAAK;EACxC,QAAQ,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE;EACrC,YAAY,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;EAClC,YAAY,OAAO,KAAK,CAAC;EACzB,SAAS;EACT,KAAK,CAAC,CAAC;EACP,IAAI,OAAO,IAAI,EAAE,CAAC,OAAO,CAAC,CAAC;EAC3B,CAAC;;ECbDA,IAAM,MAAM,GAAG,8BAA8B,CAAC;EAC9C;EACA,SAAS,OAAO,CAAC,KAAK,EAAE;EACxB,IAAI,IAAI,KAAK,KAAK,MAAM,EAAE;EAC1B,QAAQ,OAAO,IAAI,CAAC;EACpB,KAAK;EACL,IAAI,IAAI,KAAK,KAAK,OAAO,EAAE;EAC3B,QAAQ,OAAO,KAAK,CAAC;EACrB,KAAK;EACL,IAAI,IAAI,KAAK,KAAK,MAAM,EAAE;EAC1B,QAAQ,OAAO,IAAI,CAAC;EACpB,KAAK;EACL,IAAI,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,EAAE,EAAE;EAC/B,QAAQ,OAAO,CAAC,KAAK,CAAC;EACtB,KAAK;EACL,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;EAC5B,QAAQ,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;EACjC,KAAK;EACL,IAAI,OAAO,KAAK,CAAC;EACjB,CAAC;EACD;EACA,SAAS,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;EACvC,IAAI,IAAI,WAAW,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE;EACtD,QAAQA,IAAM,IAAI,GAAG,OAAO,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;EAChD,QAAQ,KAAK,GAAG,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;EAC3C,QAAQ,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;EAC7B,YAAY,IAAI;EAChB,gBAAgB,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;EACvC,aAAa;EACb,YAAY,OAAO,CAAC,EAAE,GAAG;EACzB,SAAS;EACT,aAAa;EACb,YAAY,KAAK,GAAG,SAAS,CAAC;EAC9B,SAAS;EACT,KAAK;EACL,IAAI,OAAO,KAAK,CAAC;EACjB,CAAC;EACD,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,UAAU,GAAG,EAAE,KAAK,EAAE;EAClC;EACA,IAAI,IAAI,WAAW,CAAC,GAAG,CAAC,EAAE;EAC1B,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;EAC1B,YAAY,OAAO,SAAS,CAAC;EAC7B,SAAS;EACT,QAAQA,IAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;EAChC,QAAQA,IAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;EACzC;EACA,QAAQ,IAAI,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE;EACpC,YAAY,OAAO,UAAU,CAAC;EAC9B,SAAS;EACT;EACA,QAAQA,IAAM,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC;EACzC,QAAQC,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;EAC7B,QAAQ,OAAO,CAAC,EAAE,EAAE;EACpB,YAAY,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;EAC1B,gBAAgBA,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;EACzC,gBAAgB,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;EACjD,oBAAoB,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;EACtD,oBAAoB,UAAU,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,OAAO,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;EACjF,iBAAiB;EACjB,aAAa;EACb,SAAS;EACT,QAAQ,OAAO,UAAU,CAAC;EAC1B,KAAK;EACL;EACA,IAAI,IAAI,YAAY,CAAC,GAAG,CAAC,EAAE;EAC3B,QAAQ,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;EACrC,YAAY,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;EAC5B,SAAS,CAAC,CAAC;EACX,KAAK;EACL;EACA,IAAI,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;EACtD,QAAQ,OAAO,IAAI,CAAC;EACpB,KAAK;EACL;EACA,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;EAC7B,QAAQ,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;EACrC,YAAY,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;EACnC,SAAS,CAAC,CAAC;EACX,KAAK;EACL;EACA,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;EACtB,QAAQ,OAAO,SAAS,CAAC;EACzB,KAAK;EACL,IAAI,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EACtD,CAAC;;ECtFD,CAAC,CAAC,EAAE,CAAC,KAAK,GAAG,YAAY;EACzB,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;EACjC,QAAQ,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;EAC5B,KAAK,CAAC,CAAC;EACP,CAAC;;ECJD,CAAC,CAAC,EAAE,CAAC,MAAM,GAAG,UAAU,GAAG,EAAE;EAC7B,IAAI,IAAI,CAAC,GAAG,YAAG,IAAI,EAAE,KAAK,EAAK;EAC/B;EACA,QAAQ,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;EAC3B,KAAK,CAAC,CAAC;EACP,IAAI,OAAO,IAAI,CAAC;EAChB,CAAC;;ECJD,CAAC,CAAC,EAAE,CAAC,MAAM,GAAG,UAAU,QAAQ,EAAE;EAClC,IAAI,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE;EAC9B,QAAQ,OAAO,IAAI,CAAC,GAAG,WAAE,KAAK,EAAE,OAAO,WAAK,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,GAAG,OAAO,GAAG,YAAS,CAAC,CAAC;EAC1G,KAAK;EACL,IAAI,IAAI,QAAQ,CAAC,QAAQ,CAAC,EAAE;EAC5B,QAAQ,OAAO,IAAI,CAAC,GAAG,WAAE,CAAC,EAAE,OAAO,WAAK,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,OAAO,GAAG,YAAS,CAAC,CAAC;EACvF,KAAK;EACL,IAAID,IAAM,SAAS,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC,IAAI,OAAO,IAAI,CAAC,GAAG,WAAE,CAAC,EAAE,OAAO,WAAK,SAAS,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,GAAG,YAAS,CAAC,CAAC;EACjG,CAAC;;ECXD,CAAC,CAAC,EAAE,CAAC,KAAK,GAAG,YAAY;EACzB,IAAI,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC;;ECAD,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,UAAU,QAAQ,EAAE;EAC/B,IAAIA,IAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;EAC5E,IAAY,6BAAoB;EAChC,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,YAAY;EAChC,QAAQ,KAAKC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;EAC5C,YAAY,IAAI,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;EAC7C,gBAAgB,OAAO,IAAI,CAAC;EAC5B,aAAa;EACb,SAAS;EACT,QAAQ,OAAO;EACf,KAAK,CAAC,CAAC;EACP,CAAC;;ECdD,CAAC,CAAC,EAAE,CAAC,QAAQ,GAAG,UAAU,SAAS,EAAE;EACrC,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;EACjD,CAAC;;ECED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,gBAAgB,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,aAAa,EAAE,QAAQ,EAAE;EACpF;EACA,IAAID,IAAM,kBAAkB,aAAI,KAAK,EAAK;EAC1C,QAAQ,QAAQ,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,KAAK,CAAC;EACjE,YAAY,QAAQ,EAAE;EACtB,KAAK,CAAC;EACN,IAAI,IAAI,SAAS,KAAK,CAAC,IAAI,aAAa,EAAE;EAC1C,QAAQ,KAAK,IAAI,kBAAkB,CAAC,QAAQ,CAAC,CAAC;EAC9C,KAAK;EACL,IAAI,IAAI,WAAW,CAAC,OAAO,CAAC,EAAE;EAC9B;EACA;EACA,QAAQ,IAAI,IAAI,EAAE,IAAI,QAAQ,KAAK,CAAC,EAAE;EACtC,YAAY,KAAK,IAAI,kBAAkB,CAAC,QAAQ,CAAC,CAAC;EAClD,YAAY,KAAK,IAAI,kBAAkB,CAAC,SAAS,CAAC,CAAC;EACnD,SAAS;EACT,QAAQ,IAAI,SAAS,KAAK,CAAC,EAAE;EAC7B,YAAY,KAAK,IAAI,kBAAkB,CAAC,QAAQ,CAAC,CAAC;EAClD,SAAS;EACT,QAAQ,IAAI,SAAS,KAAK,CAAC,EAAE;EAC7B,YAAY,KAAK,IAAI,kBAAkB,CAAC,QAAQ,CAAC,CAAC;EAClD,YAAY,KAAK,IAAI,kBAAkB,CAAC,SAAS,CAAC,CAAC;EACnD,SAAS;EACT,KAAK;EACL,SAAS;EACT,QAAQ,IAAI,SAAS,KAAK,CAAC,EAAE;EAC7B,YAAY,KAAK,IAAI,kBAAkB,CAAC,SAAS,CAAC,CAAC;EACnD,SAAS;EACT,QAAQ,IAAI,SAAS,KAAK,CAAC,EAAE;EAC7B,YAAY,KAAK,IAAI,kBAAkB,CAAC,QAAQ,CAAC,CAAC;EAClD,YAAY,KAAK,IAAI,kBAAkB,CAAC,SAAS,CAAC,CAAC;EACnD,SAAS;EACT,KAAK;EACL,IAAI,OAAO,KAAK,CAAC;EACjB,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,aAAa,EAAE;EACtD,IAAIA,IAAM,UAAU,GAAG,WAAS,IAAM,CAAC;EACvC,IAAIA,IAAM,UAAU,GAAG,WAAS,IAAM,CAAC;EACvC,IAAIA,IAAM,UAAU,GAAG,WAAS,IAAM,CAAC;EACvC,IAAIA,IAAM,SAAS,GAAG,UAAQ,IAAM,CAAC;EACrC;EACA,IAAI,IAAI,QAAQ,CAAC,OAAO,CAAC,EAAE;EAC3B;EACA,QAAQ,OAAO,SAAS,KAAK,CAAC;EAC9B,cAAc,OAAO,CAAC,SAAS,CAAC;EAChC,cAAc,SAAS,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;EAC9C,KAAK;EACL;EACA,IAAI,IAAI,UAAU,CAAC,OAAO,CAAC,EAAE;EAC7B,QAAQA,IAAM,GAAG,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;EACvC,QAAQ,OAAO,IAAI,CAAC,GAAG;EACvB;EACA,QAAQ,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,GAAG,CAAC,UAAU,CAAC;EACjD;EACA,QAAQ,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,GAAG,CAAC,UAAU,CAAC,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;EACpE,KAAK;EACL,IAAIA,IAAM,KAAK,GAAG,UAAU,CAAC,qBAAqB,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC;EACxF,IAAI,OAAO,gBAAgB,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,aAAa,EAAE,CAAC,CAAC,CAAC;EAC/E,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,GAAG,CAAC,OAAO,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE,aAAa,EAAE,KAAK,EAAE;EAC3E,IAAIC,IAAI,aAAa,GAAG,UAAU,CAAC,KAAK,CAAC;EACzC,UAAU,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,YAAY,EAAE,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;EACzF,UAAU,KAAK,CAAC;EAChB,IAAI,IAAI,aAAa,IAAI,IAAI,EAAE;EAC/B,QAAQ,OAAO;EACf,KAAK;EACL,IAAID,IAAM,QAAQ,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC;EAChC,IAAIA,IAAM,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;EACzC;EACA,IAAI,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,EAAE;EAC7D,QAAQ,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;EAC/C,QAAQ,OAAO;EACf,KAAK;EACL;EACA,IAAIA,IAAM,MAAM,GAAG,aAAa,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;EACrE,IAAIA,IAAM,SAAS,GAAG,UAAU,CAAC,aAAa,CAAC,CAAC;EAChD,IAAI,aAAa;EACjB,QAAQ,gBAAgB,CAAC,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,aAAa,EAAE,CAAC,CAAC,CAAC;EAChF,aAAa,MAAM,IAAI,IAAI,CAAC,CAAC;EAC7B,IAAI,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;EAC3C,CAAC;EACD,IAAI,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,YAAG,CAAC,EAAE,IAAI,EAAK;EACvC,IAAI,IAAI,CAAC,YAAS,OAAQ,IAAI,CAAC,WAAW,EAAE,aAAU,MAAO,YAAG,SAAS,EAAE,QAAQ,EAAK;EACxF,QAAQ,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,UAAU,MAAM,EAAE,KAAK,EAAE;EAClD;EACA,YAAYA,IAAM,KAAK,GAAG,SAAS,CAAC,MAAM,KAAK,SAAS,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;EACpF,YAAYA,IAAM,aAAa,GAAG,MAAM,KAAK,IAAI,IAAI,KAAK,KAAK,IAAI,CAAC;EACpE;EACA,YAAY,IAAI,CAAC,KAAK,EAAE;EACxB,gBAAgB,OAAO,IAAI,CAAC,MAAM;EAClC,sBAAsB,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,aAAa,CAAC;EAClE,sBAAsB,SAAS,CAAC;EAChC,aAAa;EACb;EACA,YAAY,OAAO,IAAI,CAAC,IAAI,WAAE,KAAK,EAAE,OAAO,WAAK,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,IAAC,CAAC,CAAC;EAC9G,SAAS,CAAC;EACV,KAAK,CAAC,CAAC;EACP,CAAC,CAAC;;EC7HF,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,YAAY;EACxB,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;EACjC,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;EACpC,KAAK,CAAC,CAAC;EACP,CAAC;;ECAD,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,YAAG,SAAS,EAAE,IAAI,EAAK;EACnD,IAAIA,IAAM,KAAK,GAAG;EAClB,QAAQ,CAAC,EAAE,OAAO;EAClB,QAAQ,CAAC,EAAE,WAAW;EACtB,QAAQ,CAAC,EAAE,aAAa;EACxB,KAAK,CAAC;EACN,IAAIA,IAAM,QAAQ,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC;EACtC,IAAI,SAAS,GAAG,CAAC,SAAS,EAAE;EAC5B;EACA,QAAQ,IAAI,SAAS,KAAK,CAAC,EAAE;EAC7B;EACA,YAAY,OAAO,GAAG,CAAC,SAAS,YAAG,OAAO,WAAK,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,IAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;EACtF,SAAS;EACT;EACA,QAAQ,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;EAC/B,YAAY,OAAO,SAAS,CAAC;EAC7B,SAAS;EACT;EACA,QAAQA,IAAM,YAAY,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;EAC1C;EACA,QAAQ,IAAI,SAAS,KAAK,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,kBAAkB,CAAC,EAAE;EACvE,YAAY,OAAO,GAAG,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,YAAG,OAAO,WAAK,OAAO,CAAC,QAAK,CAAC,CAAC;EAC3F,SAAS;EACT;EACA,QAAQ,OAAO,YAAY,CAAC,QAAQ,CAAC,CAAC;EACtC,KAAK;EACL,IAAI,SAAS,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE;EACjC;EACA;EACA,QAAQ,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;EAChC,YAAY,IAAI,SAAS,KAAK,CAAC,EAAE;EACjC,gBAAgB,OAAO;EACvB,aAAa;EACb,YAAY,KAAK,GAAG,EAAE,CAAC;EACvB,SAAS;EACT,QAAQ,IAAI,SAAS,KAAK,CAAC,IAAI,SAAS,CAAC,KAAK,CAAC,EAAE;EACjD,YAAY,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC;EACpC,SAAS;EACT;EACA,QAAQ,OAAO,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;EAClC,KAAK;EACL,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,UAAU,KAAK,EAAE;EAClC;EACA,QAAQ,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;EAC/B,YAAY,OAAO,GAAG,CAAC,IAAI,CAAC,CAAC;EAC7B,SAAS;EACT;EACA,QAAQ,OAAO,IAAI,CAAC,IAAI,WAAE,CAAC,EAAE,OAAO,EAAK;EACzC,YAAYA,IAAM,aAAa,GAAG,UAAU,CAAC,KAAK,CAAC;EACnD,kBAAkB,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;EACzD,kBAAkB,KAAK,CAAC;EACxB;EACA,YAAY,IAAI,SAAS,KAAK,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;EACjE;EACA,gBAAgB,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,kBAAkB,CAAC,EAAE;EACvD,oBAAoB,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAG,MAAM,YAAM,MAAM,CAAC,QAAQ;EAC/E,wBAAwB,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;EAC3D,4BAA4B,CAAC,CAAC,IAAC,CAAC,CAAC;EACjC,iBAAiB;EACjB;EACA,qBAAqB;EACrB,oBAAoB,OAAO,CAAC,OAAO;EACnC,wBAAwB,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;EAClE,iBAAiB;EACjB,aAAa;EACb,iBAAiB;EACjB,gBAAgB,GAAG,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;EAC5C,aAAa;EACb,SAAS,CAAC,CAAC;EACX,KAAK,CAAC;EACN,CAAC,CAAC;;ECtEF,CAAC,CAAC,EAAE,CAAC,KAAK,GAAG,UAAU,QAAQ,EAAE;EACjC,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;EAC3B,QAAQ,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EACrE,KAAK;EACL,IAAI,IAAI,QAAQ,CAAC,QAAQ,CAAC,EAAE;EAC5B,QAAQ,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EAClD,KAAK;EACL,IAAI,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9C,CAAC;;ECZD,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,YAAY;EACxB,IAAI,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC;;ECDD,IAAI,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,YAAG,SAAS,EAAE,IAAI,EAAK;EAChD,IAAI,CAAC,CAAC,EAAE,WAAQ,MAAO,GAAG,UAAU,QAAQ,EAAE,MAAM,EAAE;EACtD,QAAQ,OAAO,GAAG,CAAC,IAAI,EAAE,SAAS,EAAE,oBAAoB,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;EAC5E,KAAK,CAAC;EACN,CAAC,CAAC;;ECJF,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,UAAU,QAAQ,EAAE;EAC/B,IAAIA,IAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;EAC5C,IAAI,OAAO,IAAI,CAAC,GAAG,WAAE,CAAC,EAAE,OAAO,WAAK,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,GAAG,UAAO,CAAC,CAAC;EACzF,CAAC;;ECHD;EACA;EACA;EACA,CAAC,CAAC,EAAE,CAAC,YAAY,GAAG,YAAY;EAChC,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,YAAY;EAChC,QAAQC,IAAI,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;EAC7C,QAAQ,OAAO,YAAY,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,QAAQ,EAAE;EAC7E,YAAY,YAAY,GAAG,YAAY,CAAC,YAAY,CAAC;EACrD,SAAS;EACT,QAAQ,OAAO,YAAY,IAAI,QAAQ,CAAC,eAAe,CAAC;EACxD,KAAK,CAAC,CAAC;EACP,CAAC;;ECTD,SAAS,UAAU,CAAC,QAAQ,EAAE,IAAI,EAAE;EACpC,IAAI,OAAO,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;EAC1C,CAAC;EACD,CAAC,CAAC,EAAE,CAAC,QAAQ,GAAG,YAAY;EAC5B,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;EACtB,QAAQ,OAAO,SAAS,CAAC;EACzB,KAAK;EACL,IAAID,IAAM,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChC,IAAIC,IAAI,aAAa,CAAC;EACtB,IAAIA,IAAI,YAAY,GAAG;EACvB,QAAQ,IAAI,EAAE,CAAC;EACf,QAAQ,GAAG,EAAE,CAAC;EACd,KAAK,CAAC;EACN,IAAI,IAAI,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,OAAO,EAAE;EAC9C,QAAQ,aAAa,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC;EAC5D,KAAK;EACL,SAAS;EACT,QAAQ,aAAa,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;EAC1C,QAAQD,IAAM,aAAa,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;EACtD,QAAQ,YAAY,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC;EAC9C,QAAQ,YAAY,CAAC,GAAG,IAAI,UAAU,CAAC,aAAa,EAAE,kBAAkB,CAAC,CAAC;EAC1E,QAAQ,YAAY,CAAC,IAAI,IAAI,UAAU,CAAC,aAAa,EAAE,mBAAmB,CAAC,CAAC;EAC5E,KAAK;EACL,IAAI,OAAO;EACX,QAAQ,GAAG,EAAE,aAAa,CAAC,GAAG,GAAG,YAAY,CAAC,GAAG,GAAG,UAAU,CAAC,QAAQ,EAAE,YAAY,CAAC;EACtF,QAAQ,IAAI,EAAE,aAAa,CAAC,IAAI;EAChC,YAAY,YAAY,CAAC,IAAI;EAC7B,YAAY,UAAU,CAAC,QAAQ,EAAE,aAAa,CAAC;EAC/C,KAAK,CAAC;EACN,CAAC;;EC5BD,SAASI,KAAG,CAAC,OAAO,EAAE;EACtB,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC,MAAM,EAAE;EAC1C,QAAQ,OAAO,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;EACnC,KAAK;EACL,IAAIJ,IAAM,IAAI,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;EACjD,IAAIA,IAAM,GAAG,GAAG,OAAO,CAAC,aAAa,CAAC,WAAW,CAAC;EAClD,IAAI,OAAO;EACX,QAAQ,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,WAAW;EACvC,QAAQ,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,WAAW;EACzC,KAAK,CAAC;EACN,CAAC;EACD,SAASK,KAAG,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE;EACpC,IAAIL,IAAM,QAAQ,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC;EAChC,IAAIA,IAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;EAC9C,IAAI,IAAI,QAAQ,KAAK,QAAQ,EAAE;EAC/B,QAAQ,QAAQ,CAAC,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;EAC7C,KAAK;EACL,IAAIA,IAAM,aAAa,GAAGI,KAAG,CAAC,OAAO,CAAC,CAAC;EACvC,IAAIJ,IAAM,gBAAgB,GAAG,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;EACjD,IAAIA,IAAM,iBAAiB,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;EACnD,IAAIC,IAAI,UAAU,CAAC;EACnB,IAAIA,IAAI,WAAW,CAAC;EACpB,IAAID,IAAM,iBAAiB,GAAG,CAAC,QAAQ,KAAK,UAAU,IAAI,QAAQ,KAAK,OAAO;EAC9E,QAAQ,CAAC,gBAAgB,GAAG,iBAAiB,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;EACpE,IAAI,IAAI,iBAAiB,EAAE;EAC3B,QAAQA,IAAM,eAAe,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC;EACpD,QAAQ,UAAU,GAAG,eAAe,CAAC,GAAG,CAAC;EACzC,QAAQ,WAAW,GAAG,eAAe,CAAC,IAAI,CAAC;EAC3C,KAAK;EACL,SAAS;EACT,QAAQ,UAAU,GAAG,UAAU,CAAC,gBAAgB,CAAC,CAAC;EAClD,QAAQ,WAAW,GAAG,UAAU,CAAC,iBAAiB,CAAC,CAAC;EACpD,KAAK;EACL,IAAIA,IAAM,aAAa,GAAG,UAAU,CAAC,KAAK,CAAC;EAC3C,UAAU,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE,EAAE,aAAa,CAAC,CAAC;EAC/D,UAAU,KAAK,CAAC;EAChB,IAAI,QAAQ,CAAC,GAAG,CAAC;EACjB,QAAQ,GAAG,EAAE,aAAa,CAAC,GAAG,IAAI,IAAI;EACtC,cAAc,aAAa,CAAC,GAAG,GAAG,aAAa,CAAC,GAAG,GAAG,UAAU;EAChE,cAAc,SAAS;EACvB,QAAQ,IAAI,EAAE,aAAa,CAAC,IAAI,IAAI,IAAI;EACxC,cAAc,aAAa,CAAC,IAAI,GAAG,aAAa,CAAC,IAAI,GAAG,WAAW;EACnE,cAAc,SAAS;EACvB,KAAK,CAAC,CAAC;EACP,CAAC;EACD,CAAC,CAAC,EAAE,CAAC,MAAM,GAAG,UAAU,KAAK,EAAE;EAC/B;EACA,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;EAC3B,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;EAC1B,YAAY,OAAO,SAAS,CAAC;EAC7B,SAAS;EACT,QAAQ,OAAOI,KAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,KAAK;EACL;EACA,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,KAAK,EAAE;EACtC,QAAQC,KAAG,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;EAChC,KAAK,CAAC,CAAC;EACP,CAAC;;EC7DD,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,UAAU,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE;EACtD;EACA,IAAI,OAAO,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;EAC1D,CAAC;;ECDD,IAAI,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,YAAG,SAAS,EAAE,IAAI,EAAK;EAChD,IAAI,CAAC,CAAC,EAAE,WAAQ,MAAO,GAAG,UAAU,QAAQ,EAAE,MAAM,EAAE;EACtD;EACA,QAAQL,IAAM,MAAM,GAAG,CAAC,SAAS,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC;EACnE,QAAQ,OAAO,GAAG,CAAC,MAAM,EAAE,SAAS,EAAE,wBAAwB,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;EAClF,KAAK,CAAC;EACN,CAAC,CAAC;;ECPF,CAAC,CAAC,EAAE,CAAC,UAAU,GAAG,UAAU,aAAa,EAAE;EAC3C,IAAIA,IAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,WAAE,IAAI,WAAK,OAAI,CAAC,CAAC;EAClE,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;;AAAC;EAClC,QAAQ,IAAI,CAAC,KAAK,YAAG,CAAC,EAAE,IAAI,EAAK;EACjC,YAAYG,MAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;EACvC,SAAS,CAAC,CAAC;EACX,KAAK,CAAC,CAAC;EACP,CAAC;;ECPD,CAAC,CAAC,EAAE,CAAC,UAAU,GAAG,UAAU,IAAI,EAAE;EAClC,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;EACjC,QAAQ,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;EAC/B,KAAK,CAAC,CAAC;EACP,CAAC;;ECLD,CAAC,CAAC,EAAE,CAAC,UAAU,GAAG,UAAU,IAAI,EAAE;EAClC,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;EACjC,QAAQ,IAAI;EACZ;EACA,YAAY,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC;EAC9B,SAAS;EACT,QAAQ,OAAO,CAAC,EAAE,GAAG;EACrB,KAAK,CAAC,CAAC;EACP,CAAC;;ECJD,CAAC,CAAC,EAAE,CAAC,WAAW,GAAG,UAAU,UAAU,EAAE;EACzC,IAAI,IAAI,CAAC,IAAI,WAAE,KAAK,EAAE,OAAO,EAAK;EAClC,QAAQF,IAAI,OAAO,GAAG,UAAU,CAAC;EACjC,QAAQ,IAAI,UAAU,CAAC,OAAO,CAAC,EAAE;EACjC,YAAY,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;EACtE,SAAS;EACT,aAAa,IAAI,KAAK,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;EAC9C,YAAY,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,CAAC;EACzC,SAAS;EACT,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;EACnC,KAAK,CAAC,CAAC;EACP,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;EACzB,CAAC;;ECbD,CAAC,CAAC,EAAE,CAAC,UAAU,GAAG,UAAU,MAAM,EAAE;;AAAC;EACrC,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,WAAE,KAAK,EAAE,OAAO,EAAK;EAC7C,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,WAAW,CAAC,KAAK,GAAGE,MAAI,CAAC,KAAK,EAAE,GAAGA,MAAI,CAAC,CAAC;EAC5D,QAAQ,OAAOA,MAAI,CAAC,GAAG,EAAE,CAAC;EAC1B,KAAK,CAAC,CAAC;EACP,CAAC;;ECPD;EACA;EACA;EACA;EACA,CAAC,CAAC,EAAE,CAAC,cAAc,GAAG,YAAY;EAClC,IAAIH,IAAM,MAAM,GAAG,EAAE,CAAC;EACtB,IAAI,IAAI,CAAC,IAAI,WAAE,CAAC,EAAE,OAAO,EAAK;EAC9B,QAAQA,IAAM,QAAQ,GAAG,OAAO,YAAY,eAAe,GAAG,OAAO,CAAC,QAAQ,GAAG,CAAC,OAAO,CAAC,CAAC;EAC3F,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,WAAE,CAAC,EAAE,OAAO,EAAK;EACzC,YAAYA,IAAM,QAAQ,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC;EACxC,YAAYA,IAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;EACtC,YAAYA,IAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;EAC5D,YAAY,IAAI,QAAQ,KAAK,UAAU;EACvC,gBAAgB,OAAO,CAAC,IAAI;EAC5B,gBAAgB,CAAC,OAAO,CAAC,QAAQ;EACjC,gBAAgB,CAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;EAChF,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EACnF,iBAAiB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EAC3D,oBAAoB,OAAO,CAAC,OAAO,CAAC,EAAE;EACtC,gBAAgBA,IAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC;EAC7C,gBAAgBA,IAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC;EACxE,gBAAgB,QAAQ,CAAC,OAAO,WAAE,KAAK,EAAK;EAC5C,oBAAoB,MAAM,CAAC,IAAI,CAAC;EAChC,wBAAwB,IAAI,EAAE,OAAO,CAAC,IAAI;EAC1C,+BAAwB,KAAK;EAC7B,qBAAqB,CAAC,CAAC;EACvB,iBAAiB,CAAC,CAAC;EACnB,aAAa;EACb,SAAS,CAAC,CAAC;EACX,KAAK,CAAC,CAAC;EACP,IAAI,OAAO,MAAM,CAAC;EAClB,CAAC;;EC/BD,CAAC,CAAC,EAAE,CAAC,SAAS,GAAG,YAAY;EAC7B,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;EACxC,CAAC;;ECFDA,IAAM,cAAc,GAAG,EAAE,CAAC;EAC1B;EACA;EACA;EACA;EACA,SAAS,cAAc,CAAC,QAAQ,EAAE;EAClC,IAAIC,IAAI,OAAO,CAAC;EAChB,IAAIA,IAAI,OAAO,CAAC;EAChB,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;EACnC,QAAQ,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;EACnD,QAAQ,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;EAC3C,QAAQ,OAAO,GAAG,QAAQ,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;EAC/C,QAAQ,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;EAChD,QAAQ,IAAI,OAAO,KAAK,MAAM,EAAE;EAChC,YAAY,OAAO,GAAG,OAAO,CAAC;EAC9B,SAAS;EACT,QAAQ,cAAc,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC;EAC3C,KAAK;EACL,IAAI,OAAO,cAAc,CAAC,QAAQ,CAAC,CAAC;EACpC,CAAC;EACD;EACA;EACA;EACA;EACA,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,YAAY;EACxB,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;EACjC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,KAAK,MAAM,EAAE;EAC3C,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC;EACpC,SAAS;EACT,QAAQ,IAAI,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,KAAK,MAAM,EAAE;EAClD,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAC/D,SAAS;EACT,KAAK,CAAC,CAAC;EACP,CAAC;;EChCD;EACA;EACA;EACA;EACA;EACA,CAAC,CAAC,EAAE,CAAC,QAAQ,GAAG,UAAU,QAAQ,EAAE;EACpC,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;EAC9D,CAAC;;ECND;EACA;EACA;EACA,CAAC,CAAC,EAAE,CAAC,MAAM,GAAG,YAAY;EAC1B,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;EACjC,QAAQ,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,KAAK,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;EAC/E,KAAK,CAAC,CAAC;EACP,CAAC;;ECMD,CAAC,CAAC,EAAE,CAAC,MAAM,GAAG;MACZ,OAAO,IAAI,CAAC,IAAI,CAAC;UACf,OAAO,IAAI,CAAC,UAAU,CAAC;OACxB,CAAC,CAAC;EACL,CAAC;;ECFD,CAAC,CAAC,EAAE,CAAC,UAAU,GAAG,UAAoB,QAAyB;MAC7D,IAAI,QAAQ,CAAC,QAAQ,CAAC,EAAE;UACtB,QAAQ,GAAM,QAAQ,OAAI,CAAC;OAC5B;MAED,OAAO,IAAI,CAAC,IAAI,CAAC;UACf,IAAI,CAAC,KAAK,CAAC,wBAAwB,GAAG,QAAkB,CAAC;UACzD,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,QAAkB,CAAC;OACpD,CAAC,CAAC;EACL,CAAC;;ECPD,CAAC,CAAC,EAAE,CAAC,aAAa,GAAG,UAEnB,QAA+C;;MAG/CD,IAAM,IAAI,GAAG,IAAI,CAAC;MAClBA,IAAM,MAAM,GAAG,CAAC,qBAAqB,EAAE,eAAe,CAAC,CAAC;MAExD,SAAS,YAAY,CAAoC,CAAQ;UAC/D,IAAI,CAAC,CAAC,MAAM,KAAK,IAAI,EAAE;cACrB,OAAO;WACR;;UAGD,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;UAEvB,IAAI,CAAC,MAAM,YAAG,CAAC,EAAE,KAAK;cACpB,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;WAC/B,CAAC,CAAC;OACJ;MAED,IAAI,CAAC,MAAM,YAAG,CAAC,EAAE,KAAK;UACpB,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;OAC9B,CAAC,CAAC;MAEH,OAAO,IAAI,CAAC;EACd,CAAC;;EC9BD,CAAC,CAAC,EAAE,CAAC,eAAe,GAAG,UAAoB,eAAuB;MAChE,OAAO,IAAI,CAAC,IAAI,CAAC;UACf,IAAI,CAAC,KAAK,CAAC,qBAAqB,GAAG,eAAe,CAAC;UACnD,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,eAAe,CAAC;OAC9C,CAAC,CAAC;EACL,CAAC;;ECLD,CAAC,CAAC,EAAE,CAAC,SAAS,GAAG,UAAoB,SAAiB;MACpD,OAAO,IAAI,CAAC,IAAI,CAAC;UACf,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,SAAS,CAAC;UACvC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;OAClC,CAAC,CAAC;EACL,CAAC;;ECdD;;;EAGAA,IAAM,OAAO,GAA+B,EAAE,CAAC;EAE/C;;;;;;;EAOA,SAAS,QAAQ,CACf,QAAgB,EAChB,OAAsB,EACtB,CAAS,EACT,OAAoB;MAEpBC,IAAI,SAAS,GAAG,IAAI,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;MAEhD,IAAI,CAAC,SAAS,EAAE;UACd,SAAS,GAAG,EAAE,CAAC;UACf,IAAI,CAAC,OAAO,EAAE,gBAAgB,EAAE,SAAS,CAAC,CAAC;OAC5C;MAED,IAAI,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;UACtC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;UACzB,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;OACnC;EACH;;ECrBA,CAAC,CAAC,EAAE,CAAC,QAAQ,GAAG;MACd,OAAO,IAAI,CAAC,IAAI,WAAE,CAAC,EAAE,OAAO;UAC1BD,IAAM,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC;UAEzB,IAAI,CAAC,OAAO,YAAG,QAAgB,EAAE,OAAO;cACtC,IAAI,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE;kBACtB,QAAQ,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;eACzC;cAED,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,WAAE,CAAC,EAAE,OAAO;kBACnC,QAAQ,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;eACzC,CAAC,CAAC;WACJ,CAAC,CAAC;OACJ,CAAC,CAAC;EACL,CAAC;;ECJD,CAAC,CAAC,WAAW,GAAG,UAAU,MAAe;MACvCC,IAAI,QAAQ,GAAG,CAAC,CAAC,eAAe,CAAC,CAAC;MAElC,IAAI,QAAQ,CAAC,MAAM,EAAE;UACnB,QAAQ,CAAC,IAAI,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;UAE5C,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE;cACxB,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;WACjC;OACF;WAAM;UACL,IAAI,WAAW,CAAC,MAAM,CAAC,EAAE;cACvB,MAAM,GAAG,IAAI,CAAC;WACf;UAED,QAAQ,GAAG,CAAC,CAAC,4BAA4B,CAAC;eACvC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC;eACvB,MAAM,EAAE;eACR,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;OAC3B;MAEDA,IAAI,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;MAEjD,OAAO,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;EAChF,CAAC;;ECxBD,CAAC,CAAC,WAAW,GAAG,UAAU,KAAa;mCAAR,GAAG;;MAChCD,IAAM,QAAQ,GAAG,CAAC,CAAC,eAAe,CAAC,CAAC;MAEpC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;UACpB,OAAO;OACR;MAEDC,IAAI,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;MAExD,IAAI,KAAK,GAAG,CAAC,EAAE;UACb,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,KAAK,CAAC,CAAC;UACzC,OAAO;OACR;MAED,QAAQ;WACL,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;WACzB,WAAW,CAAC,mBAAmB,CAAC;WAChC,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC;WACjC,aAAa;UACZ,IAAI,QAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE;cACxC,QAAQ,CAAC,MAAM,EAAE,CAAC;WACnB;OACF,CAAC,CAAC;EACP,CAAC;;EC/BD,CAAC,CAAC,UAAU,GAAG;MACbD,IAAM,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;;MAGxBA,IAAM,YAAY,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;MACnCC,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;MAEjD,KAAK;WACF,QAAQ,CAAC,aAAa,CAAC;WACvB,KAAK,CAAC,YAAY,CAAC;WACnB,IAAI,CAAC,mBAAmB,EAAE,EAAE,KAAK,CAAC,CAAC;EACxC,CAAC;;ECJD,CAAC,CAAC,YAAY,GAAG,UAAU,KAAa;mCAAR,GAAG;;MACjCD,IAAM,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;MACxBC,IAAI,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;MAExD,IAAI,KAAK,GAAG,CAAC,EAAE;UACb,KAAK,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,KAAK,CAAC,CAAC;UACzC,OAAO;OACR;MAED,KAAK,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;EAC1E,CAAC;;ECfD,CAAC,CAAC,QAAQ,GAAG,UAAU,EAAc,EAAE,KAAU;mCAAL,GAAG;;MAC7CA,IAAI,KAAK,GAAQ,IAAI,CAAC;MAEtB,OAAO;;;;;UACL,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE;cACjB,KAAK,GAAG,UAAU;kBAChB,EAAE,CAAC,KAAK,CAACE,MAAI,EAAE,IAAI,CAAC,CAAC;kBACrB,KAAK,GAAG,IAAI,CAAC;eACd,EAAE,KAAK,CAAC,CAAC;WACX;OACF,CAAC;EACJ,CAAC;;ECTDH,IAAM,IAAI,GAAwB,EAAE,CAAC;EAErC,CAAC,CAAC,IAAI,GAAG,UAAU,IAAa;MAC9B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;UAClD,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC;OACnB;MAED,SAAS,EAAE;UACT,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,IAAI,OAAO,CAAC;eAC7C,QAAQ,CAAC,EAAE,CAAC;eACZ,SAAS,CAAC,CAAC,CAAC,CAAC;OACjB;MAEDA,IAAM,IAAI,GACR,GAAG;UACH,EAAE,EAAE;UACJ,EAAE,EAAE;UACJ,GAAG;UACH,EAAE,EAAE;UACJ,GAAG;UACH,EAAE,EAAE;UACJ,GAAG;UACH,EAAE,EAAE;UACJ,GAAG;UACH,EAAE,EAAE;UACJ,EAAE,EAAE;UACJ,EAAE,EAAE,CAAC;MAEP,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;UACtB,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;OACnB;MAED,OAAO,IAAI,CAAC;EACd,CAAC;;EC3BD,IAAI,CAAC,QAAQ,GAAG,UAAU,QAAiB,EAAE,OAAuB;MAClE,IAAI,WAAW,CAAC,QAAQ,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC,EAAE;UACjD,CAAC,CAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC;UACvB,OAAO;OACR;MAED,OAAO,CAAC,QAAQ,CAAC,GAAG,OAAQ,CAAC;MAC7B,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,WAAE,CAAC,EAAE,OAAO,WAAK,QAAQ,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,IAAC,CAAC,CAAC;EAC5E,CAAC;;EC/BD;;;;;;;;EAQA,SAAS,cAAc,CACrB,SAAiB,EACjB,aAAqB,EACrB,MAAwC,EACxC,QAAc,EACd,UAAwB;MAExB,IAAI,CAAC,UAAU,EAAE;UACf,UAAU,GAAG,EAAE,CAAC;OACjB;;MAGD,UAAU,CAAC,IAAI,GAAG,QAAQ,CAAC;MAE3BA,IAAM,aAAa,GAAM,SAAS,cAAS,aAAe,CAAC;;;MAI3D,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;;UAEjC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;OACnD;MAEDA,IAAM,OAAO,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;;MAG1B,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;MAS3CA,IAAM,WAAW,GAAgB;UAC/B,OAAO,EAAE,IAAI;UACb,UAAU,EAAE,IAAI;UAChB,MAAM,EAAE,UAAU;OACnB,CAAC;MAEFA,IAAM,WAAW,GAAgB,IAAI,WAAW,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;;MAG7E,WAAW,CAAC,OAAO,GAAG,UAAU,CAAC;MAEjC,OAAO,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;EACxC;;EC1DAA,IAAM,SAAS,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;EAC9BA,IAAM,OAAO,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;EAC1BA,IAAM,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC;;EC2EvBA,IAAM,eAAe,GAAY;MAC/B,SAAS,EAAE,CAAC;MACZ,MAAM,EAAE,CAAC;MACT,YAAY,EAAE,eAAe;MAC7B,WAAW,EAAE,0BAA0B;MACvC,aAAa,EAAE,4BAA4B;GAC5C,CAAC;EAEF,IAAM,QAAQ,GA+BZ,kBACE,QAAyD,EACzD,OAAqB;0CAAF;;;;;MAxBd,YAAO,GAAY,MAAM,CAAC,EAAE,EAAE,eAAe,CAAC,CAAC;;;;MAK9C,UAAK,GAAU,QAAQ,CAAC;;;;MAKxB,aAAQ,GAAG,KAAK,CAAC;;;;MAKjB,gBAAW,GAAG,CAAC,CAAC;;;;MAKhB,UAAK,GAAG,CAAC,CAAC;MAMhB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAC;MAEpC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;;MAG9BA,IAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;MACzC,IAAI,QAAQ,CAAC,SAAS,CAAC,EAAE;UACvB,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG;cACvB,IAAI,EAAE,SAAS;cACf,EAAE,EAAE,SAAS;WACd,CAAC;OACH;MAED,IAAI,CAAC,MAAM,EAAE,CAAC;EAChB,EAAC;EAED;;;qBAGQ;;;MACN,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,qBAAqB;UACvCA,IAAM,cAAc,GAAG,MAAM,CAAC,WAAW,CAAC;UAC1CA,IAAM,SAAS,GAAG,cAAc,GAAGG,MAAI,CAAC,WAAW,GAAG,MAAM,GAAG,IAAI,CAAC;UACpEH,IAAM,SAAS,GAAIG,MAAI,CAAC,OAAO,CAAC,SAAuB,CAAC,SAAS,CAAC,CAAC;UACnEH,IAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,GAAGG,MAAI,CAAC,WAAW,CAAC,CAAC;UAC7DH,IAAM,iBAAiB,GAAG,QAAQ,IAAI,SAAS,CAAC;UAEhD,IACE,cAAc,GAAGG,MAAI,CAAC,WAAW;cACjC,cAAc,IAAIA,MAAI,CAAC,OAAO,CAAC,MAAO;cACtC,iBAAiB,EACjB;cACAA,MAAI,CAAC,KAAK,EAAE,CAAC;WACd;eAAM,IACL,CAAC,cAAc,GAAGA,MAAI,CAAC,WAAW,IAAI,iBAAiB;cACvD,cAAc,IAAIA,MAAI,CAAC,OAAO,CAAC,MAAO,EACtC;cACAA,MAAI,CAAC,GAAG,EAAE,CAAC;WACZ;UAEDA,MAAI,CAAC,WAAW,GAAG,cAAc,CAAC;OACnC,CAAC,CAAC;EACL,EAAC;EAED;;;;qBAIQ,sCAAa,IAAW;MAC9B,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;EACxD,EAAC;EAED;;;qBAGQ;MACN,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;UAC5B,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;UACtB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;OAC7B;MAED,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW,EAAE;UAC9B,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC;UACxB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;OAC/B;EACH,EAAC;EAED;;;qBAGO;;;MACL,IACE,IAAI,CAAC,KAAK,KAAK,SAAS;UACxB,IAAI,CAAC,KAAK,KAAK,QAAQ;UACvB,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,YAAa,CAAC,EACnD;UACA,OAAO;OACR;MAED,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;MACzB,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;MACvB,IAAI,CAAC,QAAQ;WACV,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;WACvC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,WAAY,CAAC;WACnC,aAAa,sBAAOA,MAAI,CAAC,aAAa,KAAE,CAAC,CAAC;EAC/C,EAAC;EAED;;;qBAGO;;;MACL,IACE,IAAI,CAAC,KAAK,KAAK,WAAW;UAC1B,IAAI,CAAC,KAAK,KAAK,UAAU;UACzB,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,YAAa,CAAC,EACnD;UACA,OAAO;OACR;MAED,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;MAC3B,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC;MACzB,IAAI,CAAC,QAAQ;WACV,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;WACrC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,aAAc,CAAC;WACrC,aAAa,sBAAOA,MAAI,CAAC,aAAa,KAAE,CAAC,CAAC;EAC/C,EAAC;EAED;;;qBAGO;;;MACL,IAAI,IAAI,CAAC,QAAQ,EAAE;UACjB,OAAO;OACR;MAED,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;MACrB,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;MACtB,IAAI,CAAC,QAAQ;WACV,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,YAAa,CAAC;WACpC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;WACrC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;MAC3C,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;MAEtC,OAAO,CAAC,EAAE,CAAC,QAAQ,uBAAQA,MAAI,CAAC,QAAQ,KAAE,CAAC,CAAC;EAC9C,EAAC;EAED;;;qBAGO;;;MACL,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;UAClB,OAAO;OACR;MAED,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;MACtB,IAAI,CAAC,QAAQ;WACV,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;WACtC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;WACrC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;MAE3C,OAAO,CAAC,GAAG,CAAC,QAAQ,uBAAQA,MAAI,CAAC,QAAQ,KAAE,CAAC,CAAC;MAC7C,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EAC1C,EAAC;EAED;;;qBAGO;MACL,OAAO,IAAI,CAAC,KAAK,CAAC;EACpB,EACD;EAED,IAAI,CAAC,QAAQ,GAAG,QAAQ;;EC9QxB;;;;;EAKA,SAAS,YAAY,CAAC,OAAoB,EAAE,IAAY;MACtDH,IAAM,IAAI,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;MAEnC,IAAI,CAAC,IAAI,EAAE;UACT,OAAO,EAAE,CAAC;OACX;MAED,OAAO,IAAI,QAAQ,CACjB,EAAE,mBACY,IAAI,iDACnB,EAAE,CAAC;EACN;;ECdAA,IAAM,UAAU,GAAG,eAAe,CAAC;EAEnC,CAAC;MACC,IAAI,CAAC,QAAQ,QAAK,UAAU,SAAK;UAC/B,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,YAAY,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC;OACzD,CAAC,CAAC;EACL,CAAC,CAAC;;ECqBFA,IAAMM,iBAAe,GAAY;MAC/B,SAAS,EAAE,KAAK;GACjB,CAAC;EAEF,IAAe,gBAAgB,GAoC7B,0BACE,QAAyD,EACzD,OAAqB;0CAAF;;;;;MA7Bd,YAAO,GAAY,MAAM,CAAC,EAAE,EAAEA,iBAAe,CAAC,CAAC;;MAgCpDN,IAAM,WAAW,GAAG,WAAQ,IAAI,CAAC,YAAY,GAAE,UAAO,CAAC;MACvD,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC;MAC7B,IAAI,CAAC,aAAa,GAAM,WAAW,UAAO,CAAC;MAC3C,IAAI,CAAC,WAAW,GAAM,WAAW,YAAS,CAAC;MAC3C,IAAI,CAAC,SAAS,GAAM,WAAW,UAAO,CAAC;MAEvC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAC;MAEpC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;MAE9B,IAAI,CAAC,SAAS,EAAE,CAAC;EACnB,EAAC;EAED;;;6BAGQ;;MAENA,IAAM,IAAI,GAAG,IAAI,CAAC;;MAGlB,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,UAAM,IAAI,CAAC,eAAe;UAChDA,IAAM,OAAO,GAAG,CAAC,CAAC,IAAmB,CAAC,CAAC;UACvCA,IAAM,KAAK,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;UAC/BA,IAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;UAE/B,MAAM,CAAC,IAAI,WAAE,CAAC,EAAE,IAAI;cAClB,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;kBAClB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;eACnB;WACF,CAAC,CAAC;OACJ,CAAC,CAAC;;MAGH,IAAI,CAAC,QAAQ,CAAC,EAAE,CACd,OAAO,eACE,IAAI,CAAC,YAAY,GAAE,oBAC5B;UACEA,IAAM,OAAO,GAAG,CAAC,CAAC,IAAmB,CAAC,CAAC;UACvCA,IAAM,KAAK,GAAG,OAAO,CAAC,OAAO,SAAK,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;UAE5D,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;OACnB,CACF,CAAC;EACJ,EAAC;EAED;;;;6BAIQ,0BAAO,KAAS;MACtB,OAAO,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;EAC5C,EAAC;EAED;;;6BAGQ;MACN,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,SAAK,IAAI,CAAC,YAAY,CAAC;EACtD,EAAC;EAED;;;;6BAIQ,4BACN,IAA8D;MAE9D,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE;UAClB,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;OACjC;MAED,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;EACzB,EAAC;EAED;;;;;6BAKQ,sCAAa,IAAW,EAAE,KAAS;MACzC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;EACzD,EAAC;EAED;;;;;6BAKQ,wCAAc,QAAY,EAAE,KAAS;MAC3C,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;UACtB,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;UAE9D,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;OACpC;WAAM;UACL,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;UAEpB,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;OACpC;EACH,EAAC;EAED;;;;6BAIO,sBACL,IAA8D;;;MAE9DA,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;MAEjC,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;UACtB,OAAO;OACR;;MAGD,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;UAC1B,IAAI,CAAC,QAAQ,CAAC,QAAQ,SAAK,IAAI,CAAC,gBAAgB,CAAC,IAAI,WAAE,CAAC,EAAE,OAAO;cAC/DA,IAAM,QAAQ,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC;cAE5B,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE;kBACvBG,MAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;eACtB;WACF,CAAC,CAAC;OACJ;MAEDH,IAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,SAAK,IAAI,CAAC,YAAY,CAAC;MAEtD,QAAQ;WACL,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;WAChC,aAAa,sBAAOG,MAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,KAAK,IAAC,CAAC,CAAC;MAE5D,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;MAEjC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;EACrC,EAAC;EAED;;;;6BAIO,wBACL,IAA8D;;;MAE9DH,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;MAEjC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;UACvB,OAAO;OACR;MAEDA,IAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,SAAK,IAAI,CAAC,YAAY,CAAC;MAEtD,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;MAElC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;MAEtC,QAAQ;WACL,UAAU,CAAC,CAAC,CAAC;WACb,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;WAChC,MAAM,EAAE;WACR,UAAU,CAAC,EAAE,CAAC;WACd,MAAM,CAAC,EAAE,CAAC;WACV,aAAa,sBAAOG,MAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,KAAK,IAAC,CAAC,CAAC;EAC9D,EAAC;EAED;;;;6BAIO,0BACL,IAA8D;MAE9DH,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;MAEjC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EAC5D,EAAC;EAED;;;6BAGO;;;MACL,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAI,WAAE,CAAC,EAAE,OAAO,WAAKG,MAAI,CAAC,IAAI,CAAC,OAAO,IAAC,CAAC,CAAC;EAC3D,EAAC;EAED;;;6BAGO;;;MACL,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAI,WAAE,CAAC,EAAE,OAAO,WAAKA,MAAI,CAAC,KAAK,CAAC,OAAO,IAAC,CAAC,CAAC;EAC5D;;ECjPF,IAAM,QAAS;;;;;;;;;yBACH;UACR,OAAO,UAAU,CAAC;;;;IAFC,mBAItB;EAED,IAAI,CAAC,QAAQ,GAAG,QAAQ;;ECzBxBH,IAAMO,YAAU,GAAG,eAAe,CAAC;EAEnC,CAAC;MACC,IAAI,CAAC,QAAQ,QAAKA,YAAU,SAAK;UAC/B,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,YAAY,CAAC,IAAI,EAAEA,YAAU,CAAC,CAAC,CAAC;OACzD,CAAC,CAAC;EACL,CAAC,CAAC;;ECaF,IAAM,KAAM;;;;;;;;;sBACA;UACR,OAAO,OAAO,CAAC;;;;IAFC,mBAInB;EAED,IAAI,CAAC,KAAK,GAAG,KAAK;;ECzBlBP,IAAMO,YAAU,GAAG,YAAY,CAAC;EAEhC,CAAC;MACC,IAAI,CAAC,QAAQ,QAAKA,YAAU,SAAK;UAC/B,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,YAAY,CAAC,IAAI,EAAEA,YAAU,CAAC,CAAC,CAAC;OACtD,CAAC,CAAC;EACL,CAAC,CAAC;;ECqBF,IAAM,KAAK,GAoCT,eACE,QAAyD;;;;MA5BnD,WAAM,GAAO,CAAC,EAAE,CAAC;;;;MAKjB,YAAO,GAAO,CAAC,EAAE,CAAC;;;;MAKlB,gBAAW,GAAyB,CAAC,EAAE,CAAC;;;;MAKxC,iBAAY,GAAyB,CAAC,EAAE,CAAC;;;;MAKzC,eAAU,GAAG,KAAK,CAAC;;;;MAKnB,gBAAW,GAAG,CAAC,CAAC;MAKtB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAC;MACpC,IAAI,CAAC,IAAI,EAAE,CAAC;EACd,EAAC;EAED;;;kBAGO;MACL,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;MAC7C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;MAC9C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,uBAAuB,CAAC,CAAC;MAElE,IAAI,CAAC,gBAAgB,EAAE,CAAC;MACxB,IAAI,CAAC,gBAAgB,EAAE,CAAC;MACxB,IAAI,CAAC,gBAAgB,EAAE,CAAC;EAC1B,EAAC;EAED;;;;kBAIQ,kDAAmB,GAAW;MACpC,QACE,MAAI,GAAG,yCAAoC;UAC3C,+BAA+B;UAC/B,0BAA0B;UAC1B,oCAAoC;UACpC,UAAU;UACV,OAAK,GAAG,MAAG,EACX;EACJ,EAAC;EAED;;;kBAGQ;MACNP,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;MACrCA,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;MACrCA,IAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;MAEzC,QAAQ,CAAC,OAAO,GAAG,WAAW,KAAK,YAAY,CAAC;MAChD,QAAQ,CAAC,aAAa,GAAG,CAAC,CAAC,WAAW,IAAI,WAAW,KAAK,YAAY,CAAC;EACzE,EAAC;EAED;;;kBAGQ;;;MACNA,IAAM,gBAAgB,GAAG,yBAAyB,CAAC;MAEnD,IAAI,CAAC,OAAO,CAAC,IAAI,WAAE,CAAC,EAAE,GAAG;UACvBA,IAAM,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;;UAGpB,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC,MAAM,EAAE,CAAC;UAEhD,IAAI,CAACG,MAAI,CAAC,UAAU,EAAE;cACpB,OAAO;WACR;;UAGDH,IAAM,SAAS,GAAG,CAAC,CAACG,MAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;eAC/C,SAAS,CAAC,IAAI,CAAC;eACf,IAAI,CAAC,wBAAwB,CAAyB,CAAC;;UAG1D,IAAI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;cACnC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC;cAC5BA,MAAI,CAAC,WAAW,EAAE,CAAC;WACpB;UAEDA,MAAI,CAAC,sBAAsB,EAAE,CAAC;;UAG9B,SAAS,CAAC,EAAE,CAAC,QAAQ;cACnB,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE;kBACxB,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;kBAChCA,MAAI,CAAC,WAAW,EAAE,CAAC;eACpB;mBAAM;kBACL,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;kBACnCA,MAAI,CAAC,WAAW,EAAE,CAAC;eACpB;cAEDA,MAAI,CAAC,sBAAsB,EAAE,CAAC;WAC/B,CAAC,CAAC;UAEHA,MAAI,CAAC,YAAY,GAAGA,MAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;OACtD,CAAC,CAAC;EACL,EAAC;EAED;;;kBAGQ;;;;MAEN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC,MAAM,EAAE,CAAC;MAEvD,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;UACpB,OAAO;OACR;MAED,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;WAChD,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC;WACtB,IAAI,CAAC,wBAAwB,CAAC;WAC9B,EAAE,CAAC,QAAQ;UACVH,IAAM,YAAY,GAAGG,MAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;UACjDA,MAAI,CAAC,WAAW,GAAG,YAAY,GAAGA,MAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;UAE1DA,MAAI,CAAC,YAAY,CAAC,IAAI,WAAE,CAAC,EAAE,QAAQ;cACjC,QAAQ,CAAC,OAAO,GAAG,YAAY,CAAC;WACjC,CAAC,CAAC;UAEHA,MAAI,CAAC,OAAO,CAAC,IAAI,WAAE,CAAC,EAAE,GAAG;cACvB,YAAY;oBACR,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,yBAAyB,CAAC;oBAC1C,CAAC,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,yBAAyB,CAAC,CAAC;WACnD,CAAC,CAAC;OACJ,CAAyB,CAAC;EAC/B,EAAC;EAED;;;kBAGQ;;;MACNH,IAAM,YAAY,GAAG,wBAAwB,CAAC;MAE9C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,WAAE,CAAC,EAAE,EAAE;UAChCA,IAAM,YAAY,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;UAElDG,MAAI,CAAC,OAAO,CAAC,IAAI,WAAE,CAAC,EAAE,GAAG;cACvBH,IAAM,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cAEpC,YAAY;oBACR,GAAG,CAAC,QAAQ,CAAC,YAAY,CAAC;oBAC1B,GAAG,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;WACnC,CAAC,CAAC;OACJ,CAAC,CAAC;EACL,EACD;EAEDA,IAAM,QAAQ,GAAG,aAAa,CAAC;EAE/B,CAAC;MACC,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE;UAC3BA,IAAM,QAAQ,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;UAEzB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;cAC5B,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;WAC9C;OACF,CAAC,CAAC;EACL,CAAC,CAAC,CAAC;EAEH,IAAI,CAAC,YAAY,GAAG,UAClB,QAA0D;MAE1DA,IAAM,SAAS,GAAG,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;MAEzE,SAAS,CAAC,IAAI,WAAE,CAAC,EAAE,OAAO;UACxBA,IAAM,QAAQ,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC;UAC5BA,IAAM,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;UAEzC,IAAI,QAAQ,EAAE;cACZ,QAAQ,CAAC,IAAI,EAAE,CAAC;WACjB;eAAM;cACL,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;WAC9C;OACF,CAAC,CAAC;EACL,CAAC;;EC/OD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAkCAA,IAAM,UAAU,GAAG,sBAAsB,CAAC;EAC1CA,IAAM,SAAS,GAAG,qBAAqB,CAAC;EACxCA,IAAM,QAAQ,GAAG,kBAAkB,CAAC;EACpCA,IAAM,WAAW,GAAG,wBAAwB,CAAC;EAC7CA,IAAM,WAAW,GAAG,gCAAgC,CAAC;EAErDC,IAAI,OAAO,GAAG,CAAC,CAAC;EAEhB;;;;;EAKA,SAAS,OAAO,CAAC,KAAY;MAC3B,OAAO,EACL,OAAO;UACP;cACE,WAAW;cACX,SAAS;cACT,WAAW;cACX,OAAO;cACP,WAAW;cACX,UAAU;cACV,YAAY;cACZ,YAAY,EACb,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAC3B,CAAC;EACJ,CAAC;EAED;;;;EAIA,SAAS,QAAQ,CAAC,KAAY;MAC5B,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE;;UAE/B,OAAO,IAAI,CAAC,CAAC;OACd;WAAM,IACL,CAAC,WAAW,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EACjE;;UAEA,UAAU,CAAC;cACT,IAAI,OAAO,EAAE;kBACX,OAAO,IAAI,CAAC,CAAC;eACd;WACF,EAAE,GAAG,CAAC,CAAC;OACT;EACH;;ECjFA;;;;;;EAwCA;;;;;EAKA,SAAS,IAAI,CAAC,KAAY,EAAE,OAAW;;MAErC,IAAI,KAAK,YAAY,UAAU,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;UACrD,OAAO;OACR;;MAGDD,IAAM,aAAa,GACjB,OAAO,UAAU,KAAK,WAAW;UACjC,KAAK,YAAY,UAAU;UAC3B,KAAK,CAAC,OAAO,CAAC,MAAM;YAChB,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YACf,KAAoB,CAAC;MAE5BA,IAAM,WAAW,GAAG,aAAa,CAAC,KAAK,CAAC;MACxCA,IAAM,WAAW,GAAG,aAAa,CAAC,KAAK,CAAC;;MAGxCA,IAAM,MAAM,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;MAChCA,IAAM,MAAM,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;MACrCA,IAAM,KAAK,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC;MACnCA,IAAM,MAAM,GAAG;UACb,CAAC,EAAE,WAAW,GAAG,MAAM,CAAC,IAAI;UAC5B,CAAC,EAAE,WAAW,GAAG,MAAM,CAAC,GAAG;OAC5B,CAAC;MACFA,IAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CACvB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,EACvD,EAAE,CACH,CAAC;;MAGFA,IAAM,SAAS,GACb,kBAAe,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG,EAAC,QAAK;WACtC,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,GAAG,EAAC,oBAAiB,CAAC;;MAG7C,CAAC,CACC,kCAAgC;UAC9B,mBAAgB,QAAQ,kBAAa,QAAQ,QAAK;UAClD,kBAAe,QAAQ,GAAG,EAAC,yBAAmB,QAAQ,GAAG,EAAC,QAAK;UAC/D,WAAQ,MAAM,CAAC,EAAC,gBAAU,MAAM,CAAC,EAAC,iBAAa,CAClD;WACE,IAAI,CAAC,wBAAwB,EAAE,SAAS,CAAC;WACzC,SAAS,CAAC,OAAO,CAAC;WAClB,MAAM,EAAE;WACR,SAAS,CAAC,SAAS,CAAC,CAAC;EAC1B,CAAC;EAED;;;;EAIA,SAAS,YAAY,CAAC,KAAS;MAC7B,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,IAAI,CAAC,sBAAsB,CAAC,EAAE;UACvD,OAAO;OACR;MAED,KAAK,CAAC,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,CAAC;MAEzCC,IAAI,WAAW,GAAG,UAAU,sBAAO,KAAK,CAAC,MAAM,KAAE,EAAE,GAAG,CAAC,CAAC;MACxDD,IAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;MAEvD,KAAK;WACF,QAAQ,CAAC,uBAAuB,CAAC;WACjC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;WACvD,aAAa;UACZ,YAAY,CAAC,WAAW,CAAC,CAAC;UAE1B,KAAK;eACF,QAAQ,CAAC,sBAAsB,CAAC;eAChC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC,CAAC;UAE3D,WAAW,GAAG,UAAU,sBAAO,KAAK,CAAC,MAAM,KAAE,EAAE,GAAG,CAAC,CAAC;UAEpD,UAAU;cACR,KAAK,CAAC,aAAa;kBACjB,YAAY,CAAC,WAAW,CAAC,CAAC;kBAC1B,KAAK,CAAC,MAAM,EAAE,CAAC;eAChB,CAAC,CAAC;WACJ,EAAE,CAAC,CAAC,CAAC;OACP,CAAC,CAAC;EACP,CAAC;EAED;;;;EAIA,SAAS,IAAI;MACXA,IAAM,OAAO,GAAG,CAAC,CAAC,IAAmB,CAAC,CAAC;MAEvC,OAAO,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC,IAAI,WAAE,CAAC,EAAE,IAAI;UACjD,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;OACvB,CAAC,CAAC;MAEH,OAAO,CAAC,GAAG,EAAI,SAAS,SAAI,QAAQ,SAAI,cAAe,IAAI,CAAC,CAAC;EAC/D,CAAC;EAED;;;;EAIA,SAAS,UAAU,CAAC,KAAY;MAC9B,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;UACnB,OAAO;OACR;MAED,QAAQ,CAAC,KAAK,CAAC,CAAC;;MAGhB,IAAI,KAAK,CAAC,MAAM,KAAK,QAAQ,EAAE;UAC7B,OAAO;OACR;MAEDA,IAAM,OAAO,GAAG,CAAC,CAAC,KAAK,CAAC,MAAqB,CAAC,CAAC;;MAG/CA,IAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC;YAC3C,OAAO;YACP,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,KAAK,EAAE,CAAC;MAE5C,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;UACnB,OAAO;OACR;;MAGD,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE;UACtE,OAAO;OACR;MAED,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE;UAC/BC,IAAI,MAAM,GAAG,KAAK,CAAC;;UAGnBA,IAAI,KAAK,GAAG,UAAU;cACpB,KAAK,GAAG,CAAC,CAAC;cACV,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;WACtB,EAAE,GAAG,CAAC,CAAC;UAERD,IAAM,UAAU;;cAEd,IAAI,KAAK,EAAE;kBACT,YAAY,CAAC,KAAK,CAAC,CAAC;kBACpB,KAAK,GAAG,CAAC,CAAC;kBACV,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;eACtB;cAED,IAAI,CAAC,MAAM,EAAE;kBACX,MAAM,GAAG,IAAI,CAAC;kBACd,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;eACpB;WACF,CAAC;;UAGFA,IAAM,SAAS;cACb,IAAI,KAAK,EAAE;kBACT,YAAY,CAAC,KAAK,CAAC,CAAC;kBACpB,KAAK,GAAG,CAAC,CAAC;eACX;cAED,UAAU,EAAE,CAAC;WACd,CAAC;UAEF,OAAO,CAAC,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,sBAAsB,EAAE,UAAU,CAAC,CAAC;OAC3E;WAAM;UACL,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;UACrB,OAAO,CAAC,EAAE,EAAI,SAAS,SAAI,QAAQ,SAAI,cAAe,IAAI,CAAC,CAAC;OAC7D;EACH,CAAC;EAED,CAAC;MACC,SAAS,CAAC,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;EACjE,CAAC,CAAC;;EC9KFA,IAAM,WAAW,GAAqB;MACpC,MAAM,EAAE,KAAK;MACb,cAAc,EAAE,KAAK;GACtB,CAAC;EAEF;;;;;EAKA,SAAS,UAAU,CAAC,KAAY,EAAE,IAA2B;oCAAF;;MACzD,IAAI,GAAG,MAAM,CAAC,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;MAErCA,IAAM,KAAK,GAAG,KAAK,CAAC,MAA0B,CAAC;MAC/CA,IAAM,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;MACxBA,IAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC;MAC7BA,IAAM,KAAK,GAAG,MAAM,CAAC,GAAG,EAAY,CAAC;;MAGrCA,IAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;MAC5C,IACE,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,OAAO,CACjE,SAAS,CACV,GAAG,CAAC,CAAC,EACN;UACA,OAAO;OACR;MAEDA,IAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;;MAGpD,IAAI,SAAS,KAAK,OAAO,EAAE;UACzB,UAAU,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC;OAC7C;MAED,IAAI,SAAS,KAAK,MAAM,EAAE;UACxB,UAAU,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC;OAChD;;MAGD,IAAI,SAAS,KAAK,MAAM,IAAI,SAAS,KAAK,OAAO,EAAE;UACjD,KAAK;gBACD,UAAU,CAAC,QAAQ,CAAC,0BAA0B,CAAC;gBAC/C,UAAU,CAAC,WAAW,CAAC,0BAA0B,CAAC,CAAC;OACxD;;MAGD,KAAK,CAAC,QAAQ;YACV,UAAU,CAAC,QAAQ,CAAC,yBAAyB,CAAC;YAC9C,UAAU,CAAC,WAAW,CAAC,yBAAyB,CAAC,CAAC;;MAGtD,IACE,CAAC,SAAS,KAAK,OAAO,IAAI,SAAS,KAAK,MAAM;UAC9C,CAAC,IAAI,CAAC,cAAc;UACpB,KAAK,CAAC,QAAQ,EACd;UACA,KAAK,CAAC,QAAQ,CAAC,KAAK;gBAChB,UAAU,CAAC,WAAW,CAAC,8BAA8B,CAAC;gBACtD,UAAU,CAAC,QAAQ,CAAC,8BAA8B,CAAC,CAAC;OACzD;;MAGD,IAAI,MAAM,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE;;;UAGzBA,IAAM,UAAU,GAAG,KAAK,CAAC;UACzBC,IAAI,aAAa,GAAG,KAAK,CAAC;UAE1B,IAAI,UAAU,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,KAAK,EAAE,EAAE;cAC5C,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,CAAC,CAAC;cAC7B,aAAa,GAAG,IAAI,CAAC;WACtB;;UAGD,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;UACvBD,IAAM,MAAM,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;UACpCA,IAAM,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC;UAExC,IAAI,YAAY,GAAG,MAAM,EAAE;cACzB,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;WAClC;;UAGD,IAAI,aAAa,EAAE;cACjB,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;WACxB;OACF;;MAGD,IAAI,IAAI,CAAC,MAAM,EAAE;UACf,UAAU,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,MAAM,EAAE,CAAC;OACrD;MAEDA,IAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;MAC3C,IAAI,SAAS,EAAE;UACb,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,cAAc,EAAE;cACtC,CAAC,CACC,sCAAsC;kBACpC,8DAA0D,SAAW;kBACrE,QAAQ,CACX,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;WACxB;UAED,UAAU;eACP,IAAI,CAAC,iCAAiC,CAAC;eACvC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;OAClC;;MAGD,IACE,UAAU,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,MAAM;UAChD,UAAU,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,MAAM;UAC/C,SAAS,EACT;UACA,UAAU,CAAC,QAAQ,CAAC,2BAA2B,CAAC,CAAC;OAClD;EACH,CAAC;EAED,CAAC;;MAEC,SAAS,CAAC,EAAE,CACV,kBAAkB,EAClB,uBAAuB,EACvB,EAAE,UAAU,EAAE,IAAI,EAAE,EACpB,UAAU,CACX,CAAC;;MAGF,SAAS,CAAC,EAAE,CACV,OAAO,EACP,iDAAiD,EACjD;UACE,CAAC,CAAC,IAAmB,CAAC;eACnB,OAAO,CAAC,iBAAiB,CAAC;eAC1B,QAAQ,CAAC,yBAAyB,CAAC;eACnC,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;eAChC,KAAK,EAAE,CAAC;OACZ,CACF,CAAC;;MAGF,SAAS,CAAC,EAAE,CACV,OAAO,EACP,gDAAgD,EAChD;UACE,CAAC,CAAC,IAAI,CAAC;eACJ,OAAO,CAAC,iBAAiB,CAAC;eAC1B,WAAW,CAAC,yBAAyB,CAAC;eACtC,IAAI,CAAC,uBAAuB,CAAC;eAC7B,GAAG,CAAC,EAAE,CAAC,CAAC;OACZ,CACF,CAAC;;;;MAKF,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE;UAC/B,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE;cACrD,cAAc,EAAE,IAAI;WACrB,CAAC,CAAC;OACJ,CAAC,CAAC;EACL,CAAC,CAAC,CAAC;EAEH,IAAI,CAAC,gBAAgB,GAAG,UACtB,QAA0D;MAE1DA,IAAM,SAAS,GAAG,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;MAE7E,SAAS,CAAC,IAAI,WAAE,CAAC,EAAE,OAAO;UACxB,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE;cACxD,MAAM,EAAE,IAAI;WACb,CAAC,CAAC;OACJ,CAAC,CAAC;EACL,CAAC;;EC5KD;;;;EAIA,SAAS,gBAAgB,CAAC,OAAW;MACnCA,IAAM,IAAI,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;MAE5BA,IAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC;MACnCA,IAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC;MACjCA,IAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC;MACnCA,IAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC;MACnCA,IAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC;MAC7BA,IAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC;MAC7BA,IAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC;MACzCA,IAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC;MACzCA,IAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC;MAC3CA,IAAM,KAAK,GAAG,MAAM,CAAC,GAAG,EAAE,CAAC;MAC3BA,IAAM,OAAO,GAAG,CAAC,CAAC,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,IAAI,GAAG,CAAC;MAEpD,KAAK,CAAC,KAAK,EAAI,OAAO,QAAI,CAAC;MAC3B,MAAM,CAAC,KAAK,GAAI,GAAG,GAAG,gBAAW,CAAC;MAElC,IAAI,UAAU,EAAE;UACd,KAAK,CAAC,GAAG,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;UAClC,MAAM,CAAC,GAAG,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;OACnC;MAED,MAAM,CAAC,GAAG,CAAC,MAAM,GAAK,OAAO,QAAI,CAAC;MAElC,IAAI,UAAU,EAAE;UACd,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;OACxB;MAED,OAAO,KAAK,CAAC;YACT,OAAO,CAAC,QAAQ,CAAC,kBAAkB,CAAC;YACpC,OAAO,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC;EAC9C,CAAC;EAED;;;;EAIA,SAAS,MAAM,CAAC,OAAW;MACzBA,IAAM,MAAM,GAAG,CAAC,CAAC,uCAAuC,CAAC,CAAC;MAC1DA,IAAM,KAAK,GAAG,CAAC,CAAC,sCAAsC,CAAC,CAAC;MACxDA,IAAM,MAAM,GAAG,CAAC,CAAC,uCAAuC,CAAC,CAAC;MAC1DA,IAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAyB,CAAC;MAC3EA,IAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;MACtCA,IAAM,UAAU,GAAG,OAAO,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC;;MAG5D,UAAU;YACN,OAAO,CAAC,QAAQ,CAAC,sBAAsB,CAAC;YACxC,OAAO,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC;;MAGhD,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,MAAM,EAAE,CAAC;MAC5C,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,MAAM,EAAE,CAAC;MAC3C,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,MAAM,EAAE,CAAC;MAC5C,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;;MAGpDC,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;MACrB,IAAI,UAAU,EAAE;UACd,UAAU,GAAG,CAAC,CAAC,eAAe,CAAC,CAAC;UAChC,MAAM,CAAC,KAAK,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;OACnC;MAED,OAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;MACvC,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;MACrC,OAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;MACvC,OAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;MACvC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;MAChD,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;MAChD,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;MAC7C,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;MAC7C,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,UAAU,CAAC,CAAC;;MAG/C,gBAAgB,CAAC,OAAO,CAAC,CAAC;EAC5B,CAAC;EAEDD,IAAM,aAAa,GAAG,kCAAkC,CAAC;EAEzD,CAAC;;MAEC,SAAS,CAAC,EAAE,CAAC,cAAc,EAAE,aAAa,EAAE;UAC1CA,IAAM,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAqB,CAAC;UAEpD,gBAAgB,CAAC,OAAO,CAAC,CAAC;OAC3B,CAAC,CAAC;;MAGH,SAAS,CAAC,EAAE,CAAC,UAAU,EAAE,aAAa,EAAE,UAAU,KAAY;UAC5D,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;cACnB,OAAO;WACR;UAED,QAAQ,CAAC,KAAK,CAAC,CAAC;UAEhB,IAAK,IAAyB,CAAC,QAAQ,EAAE;cACvC,OAAO;WACR;UAEDA,IAAM,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAqB,CAAC;UAEpD,OAAO,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;OACvC,CAAC,CAAC;;MAGH,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE,aAAa,EAAE,UAAU,KAAY;UAC1D,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;cACnB,OAAO;WACR;UAED,IAAK,IAAyB,CAAC,QAAQ,EAAE;cACvC,OAAO;WACR;UAEDA,IAAM,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAqB,CAAC;UAEpD,OAAO,CAAC,WAAW,CAAC,mBAAmB,CAAC,CAAC;OAC1C,CAAC,CAAC;MAEH,SAAS,CAAC,EAAE,CAAC,WAAW,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC;;;;MAKnD,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;UAC5B,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;OACjB,CAAC,CAAC;EACL,CAAC,CAAC,CAAC;EAEH,IAAI,CAAC,aAAa,GAAG,UACnB,QAA0D;MAE1DA,IAAM,SAAS,GAAG,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;MAE1E,SAAS,CAAC,IAAI,WAAE,CAAC,EAAE,OAAO;UACxB,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;OACpB,CAAC,CAAC;EACL,CAAC;;ECrIDA,IAAMM,iBAAe,GAAY;MAC/B,OAAO,EAAE,OAAO;GACjB,CAAC;EAEF,IAAM,GAAG,GA+BP,aACE,QAAyD,EACzD,OAAqB;;0CAAF;;;;;MAxBd,YAAO,GAAY,MAAM,CAAC,EAAE,EAAEA,iBAAe,CAAC,CAAC;;;;MAK9C,UAAK,GAAU,QAAQ,CAAC;MAqB9B,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAC;MAEpC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;MAE9B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;MAC5C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;MAClD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;MAE9C,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,OAAO,EAAE;UACpC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,uBAAuB,uBAAQH,MAAI,CAAC,IAAI,KAAE,CAAC,CAAC;UACzD,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,YAAY,uBAAQA,MAAI,CAAC,KAAK,KAAE,CAAC,CAAC;OACpD;MAED,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,OAAO,EAAE;UACpC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,uBAAQA,MAAI,CAAC,IAAI,KAAE,CAAC,CAAC;OAC7C;;MAGD,SAAS,CAAC,EAAE,CAAC,UAAU,YAAG,KAAK;UAC7B,IAAI,CAAC,CAAC,KAAK,CAAC,MAAqB,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,MAAM,EAAE;cACtE,OAAO;WACR;UAEDA,MAAI,CAAC,KAAK,EAAE,CAAC;OACd,CAAC,CAAC;EACL,EAAC;EAED;;;;gBAIQ,sCAAa,IAAW;MAC9B,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;EACnD,EAAC;EAED;;;gBAGQ;MACN,OAAO,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC;EAC7D,EAAC;EAED;;;gBAGO;;;MACL,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;UACjB,OAAO;OACR;;MAGD,IAAI,CAAC,SAAS,CAAC,IAAI,WAAE,KAAK,EAAE,GAAG;UAC7BH,IAAM,KAAK,IAAM,EAAE,IAAIG,MAAI,CAAC,SAAS,CAAC,MAAM,GAAG,KAAK,SAAK,CAAC;UAE1D,GAAG,CAAC,KAAK,CAAC,eAAe,GAAG,KAAK,CAAC;UAClC,GAAG,CAAC,KAAK,CAAC,qBAAqB,GAAG,KAAK,CAAC;OACzC,CAAC,CAAC;MAEH,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;;MAGhE,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,MAAM,EAAE;UAC7C,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;OACvC;MAED,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;MACvB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;;MAG1B,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,aAAa;UAClC,IAAIA,MAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;cACzCA,MAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;cACtBA,MAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;WAC7B;OACF,CAAC,CAAC;EACL,EAAC;EAED;;;gBAGO;;;MACL,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE;UAClB,OAAO;OACR;;MAGD,IAAI,CAAC,SAAS,CAAC,IAAI,WAAE,KAAK,EAAE,GAAG;UAC7BH,IAAM,KAAK,IAAM,EAAE,GAAG,aAAS,CAAC;UAEhC,GAAG,CAAC,KAAK,CAAC,eAAe,GAAG,KAAK,CAAC;UAClC,GAAG,CAAC,KAAK,CAAC,qBAAqB,GAAG,KAAK,CAAC;OACzC,CAAC,CAAC;MAEH,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC;MAC7C,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC;MACzC,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;MACvB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;;MAG3B,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,aAAa;UACjC,IAAIG,MAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;cACzC,OAAO;WACR;UAEDA,MAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;UACtBA,MAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;UAC5BA,MAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;OAC7B,CAAC,CAAC;EACL,EAAC;EAED;;;gBAGO;MACL,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;EAC7C,EAAC;EAED;;;gBAGO;MACL,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;EAC7C,EAAC;EAED;;;gBAGO;MACL,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;EAC1C,EAAC;EAED;;;gBAGO;MACL,OAAO,IAAI,CAAC,KAAK,CAAC;EACpB,EACD;EAED,IAAI,CAAC,GAAG,GAAG,GAAG;;ECjOdH,IAAMO,YAAU,GAAG,UAAU,CAAC;EAE9B,CAAC;;;MAIC,SAAS,CAAC,EAAE,CACV,gCAAgC,SAC5BA,YAAU,SACd;UACE,IAAI,IAAI,CAAC,GAAG,CACV,IAAmB,EACnB,YAAY,CAAC,IAAmB,EAAEA,YAAU,CAAC,CAC9C,CAAC;OACH,CACF,CAAC;EACJ,CAAC,CAAC;;ECtBF;;;;;;;;;;;;;;;;EAuFAP,IAAMM,iBAAe,GAAY;MAC/B,QAAQ,EAAE,MAAM;MAChB,MAAM,EAAE,EAAE;GACX,CAAC;EAEF,IAAM,MAAM,GA6DV,gBACE,QAAyD,EACzD,OAAqB;;0CAAF;;;;;MAtDd,aAAQ,GAAO,CAAC,EAAE,CAAC;;;;MAKnB,YAAO,GAAY,MAAM,CAAC,EAAE,EAAEA,iBAAe,CAAC,CAAC;;;;MAK9C,SAAI,GAAG,CAAC,CAAC;;;;MAKT,cAAS,GAAO,CAAC,EAAE,CAAC;;;;MAKpB,UAAK,GAAO,CAAC,EAAE,CAAC;;;;MAKhB,WAAM,GAAO,CAAC,EAAE,CAAC;;;;MAKjB,kBAAa,GAAG,CAAC,CAAC;;;;MAKlB,iBAAY,GAAG,EAAE,CAAC;;;;MAKlB,kBAAa,GAAG,EAAE,CAAC;;;;MAUnB,UAAK,GAAU,QAAQ,CAAC;MAM9B,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,EAA2B,CAAC;MAC5D,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;MAEpB,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;;MAG9B,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;;MAGzB,IAAI,CAAC,YAAY,EAAE,CAAC;;MAGpB,SAAS,CAAC,EAAE,CAAC,kBAAkB,YAAG,KAAY;UAC5CN,IAAM,OAAO,GAAG,CAAC,CAAC,KAAK,CAAC,MAAqB,CAAC,CAAC;UAE/C,IACEG,MAAI,CAAC,MAAM,EAAE;cACb,CAAC,OAAO,CAAC,EAAE,CAACA,MAAI,CAAC,QAAQ,CAAC;cAC1B,CAAC,QAAQ,CAACA,MAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EACvC;cACAA,MAAI,CAAC,KAAK,EAAE,CAAC;WACd;OACF,CAAC,CAAC;EACL,EAAC;EAED;;;mBAGQ;MACNH,IAAM,YAAY,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;;MAGtCA,IAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;;MAG7CA,IAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;MACvCA,IAAM,UAAU,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC;MACvCA,IAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;;MAG1DA,IAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,GAAG,IAAI,CAAC;MACpDC,IAAI,UAAU,GAAG,UAAU,GAAG,IAAI,CAAC,IAAI,GAAG,UAAU,GAAG,CAAC,CAAC;;MAGzDD,IAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC,GAAG,CAAC;MAEhEC,IAAI,gBAAwB,CAAC;MAC7BA,IAAI,aAAqB,CAAC;MAE1B,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE;UACtC,aAAa,GAAG,aAAa,CAAC;UAC9B,gBAAgB,GAAG,KAAK,CAAC;OAC1B;WAAM,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,KAAK,EAAE;UAC1C,aAAa,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC;UAChC,gBAAgB,GAAG,MAAM,CAAC;OAC3B;WAAM;;UAELD,IAAM,aAAa,GAAG,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,MAAO,GAAG,CAAC,CAAC;UAC9D,IAAI,UAAU,GAAG,aAAa,EAAE;cAC9B,UAAU,GAAG,aAAa,CAAC;WAC5B;;UAGD,aAAa,GAAG,EACd,UAAU;cACV,IAAI,CAAC,aAAa,GAAG,UAAU;cAC/B,CAAC,UAAU,GAAG,aAAa,IAAI,CAAC,CACjC,CAAC;UAEFA,IAAM,gBAAgB,GAAG,EACvB,UAAU;cACV,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,UAAU;cAC5B,CAAC,UAAU,GAAG,aAAa,IAAI,CAAC,CACjC,CAAC;UACF,IAAI,aAAa,GAAG,gBAAgB,EAAE;cACpC,aAAa,GAAG,gBAAgB,CAAC;WAClC;;UAGDA,IAAM,OAAO,GAAG,UAAU,GAAG,aAAa,CAAC;UAC3C,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAO,EAAE;;cAElC,aAAa,GAAG,EAAE,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,MAAO,CAAC,CAAC;WACtD;eAAM,IAAI,OAAO,GAAG,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,MAAO,GAAG,YAAY,EAAE;;cAErE,aAAa,GAAG,EACd,UAAU;kBACV,UAAU;kBACV,IAAI,CAAC,OAAO,CAAC,MAAO;kBACpB,YAAY,CACb,CAAC;WACH;;UAGD,gBAAgB,IACd,IAAI,CAAC,aAAa,GAAG,UAAU,GAAG,UAAU,GAAG,CAAC,GAAG,kBACjD,CAAC;OACN;;MAGD,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;MACpC,IAAI,CAAC,KAAK;WACP,UAAU,CAAC,SAAS,CAAC;WACrB,MAAM,CAAC,UAAU,CAAC;WAClB,GAAG,CAAC;UACH,YAAY,EAAE,aAAa,GAAG,IAAI;UAClC,kBAAkB,EAAE,SAAS,GAAG,gBAAgB,GAAG,IAAI;OACxD,CAAC,CAAC;EACP,EAAC;EAED;;;mBAGQ;MACN,OAAO,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC;EAC7D,EAAC;EAED;;;mBAGO;;;MACL,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;UACjB,IAAI,CAAC,KAAK,EAAE,CAAC;OACd;MAED,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAY,CAAC;MAUlDA,IAAM,SAAS,GAAoB,EAAE,CAAC;MACtC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;;MAGlB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,WAAE,KAAK,EAAE,MAAM;UAC7CA,IAAM,IAAI,GAAG,MAAM,CAAC,WAAW,IAAI,EAAE,CAAC;UACtCA,IAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;UAC3BA,IAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;UACjCA,IAAM,QAAQ,GAAGG,MAAI,CAAC,aAAa,KAAK,KAAK,CAAC;UAE9C,SAAS,CAAC,IAAI,CAAC;qBACb,KAAK;oBACL,IAAI;wBACJ,QAAQ;wBACR,QAAQ;qBACR,KAAK;WACN,CAAC,CAAC;UAEH,IAAI,QAAQ,EAAE;cACZA,MAAI,CAAC,YAAY,GAAG,IAAI,CAAC;cACzBA,MAAI,CAAC,aAAa,GAAG,KAAK,CAAC;WAC5B;UAEDA,MAAI,CAAC,MAAM,GAAGA,MAAI,CAAC,MAAM,CAAC,GAAG,CAC3B,gDAAgD;eAC7C,QAAQ,GAAG,WAAW,GAAG,EAAE,CAAC;eAC5B,QAAQ,GAAG,WAAW,GAAG,EAAE,CAAC;cAC7B,MAAI,IAAI,WAAQ,CACnB,CAAC;OACH,CAAC,CAAC;MAEH,IAAI,CAAC,SAAS,GAAG,CAAC,6CACsB,IAAI,CAAC,aAAY,cACxD,CAAC;MAEF,IAAI,CAAC,QAAQ,GAAG,CAAC,CACf,oDAAgD,IAAI,CAAC,OAAO,CAAC,SAAQ,QAAI;UACvE,cAAU,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAC,QAAI;UACxC,WAAO,IAAI,CAAC,SAAQ,cAAU,CACjC;WACE,IAAI,EAAE;WACN,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;MAE1B,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,sCAAsC,CAAC;WACnD,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC;WACvB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;MAEvB,CAAC,SAAK,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;MAChC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;;MAGlC,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC;MAEvD,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE;UAClB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;UAE/B,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE;cACjB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;WACf;OACF;;;MAIDH,IAAM,IAAI,GAAG,IAAI,CAAC;MAClB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE;UACtB,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;cAC5B,OAAO;WACR;UAEDA,IAAM,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;UACtBA,IAAM,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;UAC5BA,IAAM,IAAI,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;UAE9B,IAAI,IAAI,CAAC,QAAQ,EAAE;cACjB,OAAO;WACR;UAED,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;UAC/B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;UAC7B,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;UACnC,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;UAC3B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC;UAChC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC;UAChC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC;UAC9B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;UAC/B,IAAI,CAAC,KAAK,EAAE,CAAC;OACd,CAAC,CAAC;;MAGH,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,YAAG,KAAY;UACrCA,IAAM,OAAO,GAAG,CAAC,CAAC,KAAK,CAAC,MAAqB,CAAC,CAAC;;UAG/C,IACE,OAAO,CAAC,EAAE,CAAC,mBAAmB,CAAC;cAC/B,OAAO,CAAC,EAAE,CAAC,wBAAwB,CAAC,EACpC;cACA,OAAO;WACR;UAEDG,MAAI,CAAC,MAAM,EAAE,CAAC;OACf,CAAC,CAAC;EACL,EAAC;EAED;;;mBAGQ;MACN,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,qBAAqB,CAAC,CAAC;MAEjD,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;UAC5B,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;UACtB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;UAC5B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;OACtC;MAED,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;UAC5B,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;UACtB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;;UAG5B,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;UAC7B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;cACb,YAAY,EAAE,EAAE;cAChB,MAAM,EAAE,EAAE;cACV,KAAK,EAAE,EAAE;WACV,CAAC,CAAC;OACJ;EACH,EAAC;EAED;;;;mBAIQ,sCAAa,IAAW;MAC9B,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;EACrD,EAAC;EAED;;;mBAGO;MACL,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;EAC7C,EAAC;EAED;;;mBAGO;;;MACL,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;UACjB,OAAO;OACR;MAED,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;MACvB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;MAC1B,IAAI,CAAC,YAAY,EAAE,CAAC;MACpB,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;MAC3C,IAAI,CAAC,KAAK,CAAC,aAAa,sBAAOA,MAAI,CAAC,aAAa,KAAE,CAAC,CAAC;EACvD,EAAC;EAED;;;mBAGO;;;MACL,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE;UAClB,OAAO;OACR;MAED,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;MACvB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;MAC3B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;MACjC,IAAI,CAAC,QAAQ;WACV,WAAW,CAAC,kBAAkB,CAAC;WAC/B,QAAQ,CAAC,qBAAqB,CAAC,CAAC;MACnC,IAAI,CAAC,KAAK,CAAC,aAAa,sBAAOA,MAAI,CAAC,aAAa,KAAE,CAAC,CAAC;EACvD,EAAC;EAED;;;mBAGO;MACL,OAAO,IAAI,CAAC,KAAK,CAAC;EACpB,EACD;EAED,IAAI,CAAC,MAAM,GAAG,MAAM;;ECvdpBH,IAAMO,YAAU,GAAG,aAAa,CAAC;EAEjC,CAAC;MACC,IAAI,CAAC,QAAQ,QAAKA,YAAU,SAAK;UAC/B,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,YAAY,CAAC,IAAI,EAAEA,YAAU,CAAC,CAAC,CAAC;OACvD,CAAC,CAAC;EACL,CAAC,CAAC;;ECPF,CAAC;;MAEC,IAAI,CAAC,QAAQ,CAAC,0BAA0B,EAAE;UACxC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;OACzB,CAAC,CAAC;;MAGH,IAAI,CAAC,QAAQ,CAAC,kCAAkC,EAAE;UAChD,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;cACtB,WAAW,EAAE,8BAA8B;cAC3C,aAAa,EAAE,gCAAgC;WAChD,CAAC,CAAC;OACJ,CAAC,CAAC;EACL,CAAC,CAAC;;EC4CFP,IAAMM,iBAAe,GAAY;MAC/B,OAAO,EAAE,OAAO;MAChB,IAAI,EAAE,KAAK;GACZ,CAAC;EAEF,IAAM,GAAG,GA0BP,aACE,QAAyD,EACzD,OAAqB;;0CAAF;;;;;MAnBd,YAAO,GAAY,MAAM,CAAC,EAAE,EAAEA,iBAAe,CAAC,CAAC;;;;MAK/C,gBAAW,GAAG,CAAC,CAAC,CAAC;MAgBtB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAC;MAEpC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;MAE9B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;MACzC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,wCAAwC,CAAC,CAAC,QAAQ,CACpE,IAAI,CAAC,QAAQ,CACd,CAAC;;MAGFN,IAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;MAClC,IAAI,IAAI,EAAE;UACR,IAAI,CAAC,KAAK,CAAC,IAAI,WAAE,KAAK,EAAE,GAAG;cACzB,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,EAAE;kBAChCG,MAAI,CAAC,WAAW,GAAG,KAAK,CAAC;kBACzB,OAAO,KAAK,CAAC;eACd;cAED,OAAO,IAAI,CAAC;WACb,CAAC,CAAC;OACJ;;MAGD,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,CAAC,EAAE;UAC3B,IAAI,CAAC,KAAK,CAAC,IAAI,WAAE,KAAK,EAAE,GAAG;cACzB,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;kBACtCA,MAAI,CAAC,WAAW,GAAG,KAAK,CAAC;kBACzB,OAAO,KAAK,CAAC;eACd;cAED,OAAO,IAAI,CAAC;WACb,CAAC,CAAC;OACJ;;MAGD,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,CAAC,EAAE;UAChD,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;OACtB;;MAGD,IAAI,CAAC,SAAS,EAAE,CAAC;;MAGjB,OAAO,CAAC,EAAE,CACR,QAAQ,EACR,CAAC,CAAC,QAAQ,sBAAOA,MAAI,CAAC,oBAAoB,KAAE,EAAE,GAAG,CAAC,CACnD,CAAC;;MAGF,IAAI,CAAC,KAAK,CAAC,IAAI,WAAE,CAAC,EAAE,GAAG;UACrBA,MAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;OACxB,CAAC,CAAC;EACL,EAAC;EAED;;;;gBAIQ,kCAAW,IAAQ;MACzB,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,SAAS,CAAC;EAC7C,EAAC;EAED;;;;gBAIQ,sCAAa,GAAgB;;;MACnCH,IAAM,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;;MAGpBA,IAAM,UAAU;;UAEd,IAAIG,MAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;cACzB,OAAO,KAAK,CAAC;WACd;UAEDA,MAAI,CAAC,WAAW,GAAGA,MAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;UACzCA,MAAI,CAAC,SAAS,EAAE,CAAC;OAClB,CAAC;;MAGF,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;;MAG7B,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,OAAO,EAAE;UACpC,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;OACnC;;MAGD,IAAI,CAAC,EAAE,CAAC,OAAO;UACb,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;cAChD,OAAO,KAAK,CAAC;WACd;OACF,CAAC,CAAC;EACL,EAAC;EAED;;;;;;gBAMQ,sCAAa,IAAW,EAAE,QAAY,EAAE,UAAe;iDAAL,GAAG;;MAC3D,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;EAC1D,EAAC;EAED;;;gBAGQ;;;MACN,IAAI,CAAC,KAAK,CAAC,IAAI,WAAE,KAAK,EAAE,GAAG;UACzBH,IAAM,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;UACpBA,IAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;;UAGzC,IAAI,KAAK,KAAKG,MAAI,CAAC,WAAW,IAAI,CAACA,MAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;cACxD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;kBACrCA,MAAI,CAAC,YAAY,CAAC,QAAQ,EAAEA,MAAI,CAAC,QAAQ,EAAE;sBACzC,KAAK,EAAEA,MAAI,CAAC,WAAW;sBACvB,EAAE,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;mBACvB,CAAC,CAAC;kBACHA,MAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;kBAEhC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;eAClC;cAED,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,EAAE,CAAC;cACnBA,MAAI,CAAC,oBAAoB,EAAE,CAAC;WAC7B;eAAM;cACL,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC;cACpC,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,EAAE,CAAC;WACpB;OACF,CAAC,CAAC;EACL,EAAC;EAED;;;gBAGQ;;MAEN,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,CAAC,EAAE;UAC3B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;cAClB,IAAI,EAAE,CAAC;cACP,KAAK,EAAE,CAAC;WACT,CAAC,CAAC;UAEH,OAAO;OACR;MAEDH,IAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;MAEnD,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;UAC/B,OAAO;OACR;MAEDA,IAAM,eAAe,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC;MAE5C,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;UAClB,IAAI,IACF,eAAe,CAAC,IAAI;cACpB,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU;cAC3B,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC,aACvC;UACJ,KAAK,IAAK,UAAU,CAAC,UAAU,WAAM;OACtC,CAAC,CAAC;EACL,EAAC;EAED;;;gBAGO;MACL,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,CAAC,EAAE;UAC3B,OAAO;OACR;MAED,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE;UAC5C,IAAI,CAAC,WAAW,EAAE,CAAC;OACpB;WAAM,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;UAC5B,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;OACtB;MAED,IAAI,CAAC,SAAS,EAAE,CAAC;EACnB,EAAC;EAED;;;gBAGO;MACL,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,CAAC,EAAE;UAC3B,OAAO;OACR;MAED,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE;UACxB,IAAI,CAAC,WAAW,EAAE,CAAC;OACpB;WAAM,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;UAC5B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;OAC1C;MAED,IAAI,CAAC,SAAS,EAAE,CAAC;EACnB,EAAC;EAED;;;;gBAIO,sBAAK,KAAsB;;;MAChC,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,CAAC,EAAE;UAC3B,OAAO;OACR;MAED,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;UACnB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;OAC1B;WAAM;UACL,IAAI,CAAC,KAAK,CAAC,IAAI,WAAE,CAAC,EAAE,GAAG;cACrB,IAAI,GAAG,CAAC,EAAE,KAAK,KAAK,EAAE;kBACpBG,MAAI,CAAC,WAAW,KAAK,CAAC,CAAC;kBACvB,OAAO,KAAK,CAAC;eACd;WACF,CAAC,CAAC;OACJ;MAED,IAAI,CAAC,SAAS,EAAE,CAAC;EACnB,EAAC;EAED;;;;gBAIO;;;MACLH,IAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;MAC5BA,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;MAC7CA,IAAM,cAAc,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC;MACtCA,IAAM,cAAc,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC;MAEtC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;UACpB,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;UACtB,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;UACtB,IAAI,CAAC,oBAAoB,EAAE,CAAC;UAE5B,OAAO;OACR;;MAGD,QAAQ,CAAC,IAAI,WAAE,KAAK,EAAE,GAAG;;UAEvB,IAAI,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;cACnCG,MAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;cAEvB,IAAIA,MAAI,CAAC,WAAW,KAAK,CAAC,CAAC,EAAE;kBAC3BA,MAAI,CAAC,WAAW,GAAG,CAAC,CAAC;eACtB;mBAAM,IAAI,KAAK,IAAIA,MAAI,CAAC,WAAW,EAAE;kBACpCA,MAAI,CAAC,WAAW,EAAE,CAAC;eACpB;WACF;OACF,CAAC,CAAC;;MAGH,QAAQ,CAAC,IAAI,WAAE,KAAK,EAAE,GAAG;;UAEvB,IAAI,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;cACnC,IAAI,KAAK,GAAGA,MAAI,CAAC,WAAW,EAAE;kBAC5BA,MAAI,CAAC,WAAW,EAAE,CAAC;eACpB;mBAAM,IAAI,KAAK,KAAKA,MAAI,CAAC,WAAW,EAAE;kBACrCA,MAAI,CAAC,WAAW,GAAG,CAAC,CAAC;eACtB;WACF;OACF,CAAC,CAAC;MAEH,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;MAEtB,IAAI,CAAC,SAAS,EAAE,CAAC;EACnB,EACD;EAED,IAAI,CAAC,GAAG,GAAG,GAAG;;EC7WdH,IAAMO,YAAU,GAAG,UAAU,CAAC;EAE9B,CAAC;MACC,IAAI,CAAC,QAAQ,QAAKA,YAAU,SAAK;UAC/B,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,YAAY,CAAC,IAAI,EAAEA,YAAU,CAAC,CAAC,CAAC;OACpD,CAAC,CAAC;EACL,CAAC,CAAC;;ECZF;;;;EAiEAP,IAAMM,iBAAe,GAAY;MAC/B,OAAO,EAAE,KAAK;MACd,KAAK,EAAE,KAAK;GACb,CAAC;EAEF,IAAM,MAAM,GA0BV,gBACE,QAAyD,EACzD,OAAqB;;0CAAF;;;;;MAnBd,YAAO,GAAY,MAAM,CAAC,EAAE,EAAEA,iBAAe,CAAC,CAAC;;;;MAK9C,YAAO,GAAG,KAAK,CAAC;MAgBtB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAC;MAEpC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;MAE9B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,mBAAmB,CAAC;YACvD,OAAO;YACP,MAAM,CAAC;MAEX,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;UAC/C,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;OACvB;WAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;UACrD,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;OACvB;WAAM,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;UAC3B,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;OACvB;WAAM;UACL,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;OACvB;;MAGD,OAAO,CAAC,EAAE,CACR,QAAQ,EACR,CAAC,CAAC,QAAQ;UACR,IAAIH,MAAI,CAAC,SAAS,EAAE,EAAE;;;cAGpB,IAAIA,MAAI,CAAC,OAAO,IAAI,CAACA,MAAI,CAAC,OAAO,CAAC,OAAO,EAAE;kBACzC,CAAC,CAAC,WAAW,EAAE,CAAC;kBAChBA,MAAI,CAAC,OAAO,GAAG,KAAK,CAAC;kBACrB,CAAC,CAAC,YAAY,EAAE,CAAC;eAClB;;cAGD,IAAI,CAACA,MAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;kBAChDA,MAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;eACvB;WACF;eAAM,IAAI,CAACA,MAAI,CAAC,OAAO,IAAIA,MAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;;cAEnD,IAAIA,MAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;kBAC9C,CAAC,CAAC,WAAW,EAAE,CAAC;kBAChBA,MAAI,CAAC,OAAO,GAAG,IAAI,CAAC;kBACpB,CAAC,CAAC,UAAU,EAAE,CAAC;kBAEf,CAAC,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,OAAO,uBAAQA,MAAI,CAAC,KAAK,KAAE,CAAC,CAAC;eACrD;mBAAM;kBACLA,MAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;eACvB;WACF;OACF,EAAE,GAAG,CAAC,CACR,CAAC;;MAGF,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,IAAI,WAAE,CAAC,EAAE,KAAK;UACtD,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,OAAO,uBAAQA,MAAI,CAAC,KAAK,KAAE,CAAC,CAAC;OAC1C,CAAC,CAAC;MAEH,IAAI,CAAC,YAAY,EAAE,CAAC;EACtB,EAAC;EAED;;;mBAGQ;MACN,OAAO,OAAO,CAAC,KAAK,EAAE,IAAI,IAAI,CAAC;EACjC,EAAC;EAED;;;mBAGQ;;MAENH,IAAM,IAAI,GAAG,IAAI,CAAC;;MAGlBC,IAAI,mBAA2C,CAAC;MAChDA,IAAI,WAAmB,CAAC;MACxBA,IAAI,WAAmB,CAAC;MACxBA,IAAI,WAAmB,CAAC;MACxBA,IAAI,OAAO,GAAiC,IAAI,CAAC;MACjDA,IAAI,YAAY,GAAG,KAAK,CAAC;MACzBD,IAAM,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;;MAGxBA,IAAM,cAAc,GAAG,EAAE,CAAC;MAE1B,SAAS,WAAW,CAAC,UAAkB;UACrCA,IAAM,sBAAsB,GAAG,IAAI,CAAC,QAAQ,KAAK,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;UAClEA,IAAM,YAAY,GAAG,gBACnB,CAAC,CAAC,GAAG,sBAAsB,GAAG,WAChC,uBAAoB,CAAC;UACrBA,IAAM,aAAa,GAAG,qBAAqB,CAAC;UAE5C,IAAI,CAAC,QAAQ,CAAC,GAAG,CACf,SAAS,mBACK,YAAY,sBAAiB,aAAa,QACzD,CAAC;OACH;MAED,SAAS,aAAa;UACpB,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC;UACtC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,eAAe,GAAG,EAAE,CAAC;UAC5C,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,GAAG,EAAE,CAAC;UACvC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,gBAAgB,GAAG,EAAE,CAAC;OAC9C;MAED,SAAS,gBAAgB;UACvB,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC;OACnC;MAED,SAAS,aAAa,CAAC,QAAgB;UACrC,OAAO,IAAI,CAAC,GAAG,CACb,IAAI,CAAC,GAAG,CACN,OAAO,KAAK,SAAS;gBACjB,WAAW,GAAG,QAAQ;gBACtB,gBAAgB,EAAE,GAAG,WAAW,GAAG,QAAQ,EAC/C,CAAC,CACF,EACD,gBAAgB,EAAE,CACnB,CAAC;OACH;MAED,SAAS,cAAc,CAAC,KAAa;UACnC,IAAI,OAAO,EAAE;cACXC,IAAI,MAAM,GAAI,KAAoB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;cAC3D,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;kBAC7B,MAAM,GAAG,KAAK,CAAC,KAAK,EAAE,GAAG,MAAM,CAAC;eACjC;cAEDD,IAAM,cAAc,GAAG,aAAa,CAAC,MAAM,CAAC,GAAG,gBAAgB,EAAE,CAAC;cAElE,YAAY,GAAG,KAAK,CAAC;cACrBA,IAAM,YAAY,GAAG,OAAO,CAAC;cAC7B,OAAO,GAAG,IAAI,CAAC;cAEf,IAAI,YAAY,KAAK,SAAS,EAAE;kBAC9B,IAAI,cAAc,GAAG,IAAI,EAAE;sBACzB,aAAa,EAAE,CAAC;sBAChB,IAAI,CAAC,IAAI,EAAE,CAAC;mBACb;uBAAM;sBACL,aAAa,EAAE,CAAC;mBACjB;eACF;mBAAM;kBACL,IAAI,cAAc,GAAG,IAAI,EAAE;sBACzB,aAAa,EAAE,CAAC;sBAChB,IAAI,CAAC,KAAK,EAAE,CAAC;mBACd;uBAAM;sBACL,aAAa,EAAE,CAAC;mBACjB;eACF;cAED,CAAC,CAAC,YAAY,EAAE,CAAC;WAClB;eAAM;cACL,YAAY,GAAG,KAAK,CAAC;WACtB;UAED,KAAK,CAAC,GAAG,CAAC;;cAER,SAAS,EAAE,eAAe;cAC1B,QAAQ,EAAE,cAAc;;cAExB,WAAW,EAAE,eAAe;WAC7B,CAAC,CAAC;OACJ;MAED,SAAS,eAAe,CAAC,KAAY;UACnCC,IAAI,MAAM,GAAI,KAAoB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;UACpD,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;cAC7B,MAAM,GAAG,KAAK,CAAC,KAAK,EAAE,GAAG,MAAM,CAAC;WACjC;UAEDD,IAAM,MAAM,GAAI,KAAoB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;UAEtD,IAAI,OAAO,EAAE;cACX,WAAW,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;WACpC;eAAM,IAAI,YAAY,EAAE;cACvBA,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,WAAW,CAAC,CAAC;cAC7CA,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,WAAW,CAAC,CAAC;cAC7CA,IAAM,SAAS,GAAG,CAAC,CAAC;cAEpB,IAAI,KAAK,GAAG,SAAS,IAAI,KAAK,IAAI,SAAS,EAAE;kBAC3C,WAAW,GAAG,MAAM,CAAC;kBACrB,OAAO,GAAG,IAAI,CAAC,KAAK,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS,CAAC;kBAC1D,CAAC,CAAC,UAAU,EAAE,CAAC;kBACf,WAAW,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;eACpC;mBAAM,IAAI,KAAK,IAAI,SAAS,IAAI,KAAK,GAAG,SAAS,EAAE;kBAClD,cAAc,EAAE,CAAC;eAClB;WACF;OACF;MAED,SAAS,gBAAgB,CAAC,KAAY;UACpC,WAAW,GAAI,KAAoB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;UACrD,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;cAC7B,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,GAAG,WAAW,CAAC;WAC3C;UAED,WAAW,GAAI,KAAoB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;UAErD,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;cAC3B,IACE,WAAW,GAAG,cAAc;kBAC5B,mBAAmB,KAAK,gBAAgB,EACxC;kBACA,OAAO;eACR;WACF;UAED,YAAY,GAAG,IAAI,CAAC;UAEpB,KAAK,CAAC,EAAE,CAAC;cACP,SAAS,EAAE,eAAe;cAC1B,QAAQ,EAAE,cAAc;cACxB,WAAW,EAAE,eAAe;WAC7B,CAAC,CAAC;OACJ;MAED,SAAS,mBAAmB;UAC1B,IAAI,CAAC,mBAAmB,EAAE;cACxB,KAAK,CAAC,EAAE,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC;cACzC,mBAAmB,GAAG,gBAAgB,CAAC;WACxC;OACF;MAED,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;UACtB,mBAAmB,EAAE,CAAC;OACvB;EACH,EAAC;EAED;;;;mBAIQ,sCAAa,IAAW;MAC9B,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;EACtD,EAAC;EAED;;;mBAGQ;MACN,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;UAC9C,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;UACtB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;OAC7B;WAAM;UACL,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;UACtB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;OAC7B;EACH,EAAC;EAED;;;mBAGQ;MACN,OAAO,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC;EAC7D,EAAC;EAED;;;mBAGO;;;MACL,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;UACjB,OAAO;OACR;MAED,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;MACvB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;MAE1B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;UACzB,CAAC,CAAC,MAAM,CAAC,CAAC,QAAQ,yBAAqB,IAAI,CAAC,WAAW,CAAC;OACzD;MAED,IAAI,CAAC,QAAQ;WACV,WAAW,CAAC,mBAAmB,CAAC;WAChC,QAAQ,CAAC,kBAAkB,CAAC;WAC5B,aAAa,sBAAOG,MAAI,CAAC,aAAa,KAAE,CAAC,CAAC;MAE7C,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;UAC7C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;UACpB,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,OAAO,uBAAQA,MAAI,CAAC,KAAK,KAAE,CAAC,CAAC;UACjD,CAAC,CAAC,UAAU,EAAE,CAAC;OAChB;EACH,EAAC;EAED;;;mBAGO;;;MACL,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE;UAClB,OAAO;OACR;MAED,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;MACvB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;MAE3B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;UACzB,CAAC,CAAC,MAAM,CAAC,CAAC,WAAW,yBAAqB,IAAI,CAAC,WAAW,CAAC;OAC5D;MAED,IAAI,CAAC,QAAQ;WACV,QAAQ,CAAC,mBAAmB,CAAC;WAC7B,WAAW,CAAC,kBAAkB,CAAC;WAC/B,aAAa,sBAAOA,MAAI,CAAC,aAAa,KAAE,CAAC,CAAC;MAE7C,IAAI,IAAI,CAAC,OAAO,EAAE;UAChB,CAAC,CAAC,WAAW,EAAE,CAAC;UAChB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;UACrB,CAAC,CAAC,YAAY,EAAE,CAAC;OAClB;EACH,EAAC;EAED;;;mBAGO;MACL,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;EAC7C,EAAC;EAED;;;mBAGO;MACL,OAAO,IAAI,CAAC,KAAK,CAAC;EACpB,EACD;EAED,IAAI,CAAC,MAAM,GAAG,MAAM;;EChapBH,IAAMO,YAAU,GAAG,aAAa,CAAC;EAQjC,CAAC;MACC,IAAI,CAAC,QAAQ,QAAKA,YAAU,SAAK;UAC/BP,IAAM,QAAQ,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;UACzBA,IAAM,OAAO,GAAG,YAAY,CAAC,IAAI,EAAEO,YAAU,CAAY,CAAC;UAC1DP,IAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC;;UAEhC,OAAO,OAAO,CAAC,MAAM,CAAC;UAEtBA,IAAM,OAAO,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAC;UACpCA,IAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;UAEnD,QAAQ,CAAC,EAAE,CAAC,OAAO,uBAAQ,QAAQ,CAAC,MAAM,KAAE,CAAC,CAAC;OAC/C,CAAC,CAAC;EACL,CAAC,CAAC;;ECxBFA,IAAM,SAAS,GAAwB,EAAE,CAAC;EAe1C,SAAS,KAAK,CAAC,IAAY,EAAE,IAAW;MACtC,IAAI,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE;UAChC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;OACtB;MAED,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE;UACrB,OAAO,SAAS,CAAC,IAAI,CAAC,CAAC;OACxB;MAED,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC7B,CAAC;EAED;;;;EAIA,SAAS,OAAO,CAAC,IAAY;MAC3B,IAAI,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE;UAChC,OAAO;OACR;MAED,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE;UAC3B,OAAO;OACR;MAEDA,IAAM,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,KAAK,EAAG,CAAC;MAEtC,IAAI,EAAE,CAAC;EACT;;ECuBAA,IAAMM,iBAAe,GAAY;MAC/B,OAAO,EAAE,IAAI;MACb,OAAO,EAAE,IAAI;MACb,KAAK,EAAE,KAAK;MACZ,UAAU,EAAE,IAAI;MAChB,aAAa,EAAE,IAAI;MACnB,cAAc,EAAE,IAAI;MACpB,eAAe,EAAE,KAAK;GACvB,CAAC;EAEF;;;EAGAL,IAAI,WAAW,GAAkB,IAAI,CAAC;EAEtC;;;EAGAD,IAAM,SAAS,GAAG,cAAc,CAAC;EAEjC;;;EAGAC,IAAI,YAAY,GAAG,KAAK,CAAC;EAEzB;;;EAGAA,IAAI,QAAmB,CAAC;EAExB,IAAM,MAAM,GAqBV,gBACE,QAAyD,EACzD,OAAqB;;0CAAF;;;;;MAdd,YAAO,GAAY,MAAM,CAAC,EAAE,EAAEK,iBAAe,CAAC,CAAC;;;;MAK/C,UAAK,GAAU,QAAQ,CAAC;;;;MAKvB,WAAM,GAAG,KAAK,CAAC;MAMrB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAC;;MAGpC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;UAC9C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;UACnB,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;OACjC;MAED,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;;MAG9B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,IAAI,WAAE,CAAC,EAAE,MAAM;UACxD,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,OAAO;cAClBH,MAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;cAE5B,IAAIA,MAAI,CAAC,OAAO,CAAC,aAAa,EAAE;kBAC9BA,MAAI,CAAC,KAAK,EAAE,CAAC;eACd;WACF,CAAC,CAAC;OACJ,CAAC,CAAC;;MAGH,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,IAAI,WAAE,CAAC,EAAE,OAAO;UAC1D,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,OAAO;cACnBA,MAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;cAE7B,IAAIA,MAAI,CAAC,OAAO,CAAC,cAAc,EAAE;kBAC/BA,MAAI,CAAC,KAAK,EAAE,CAAC;eACd;WACF,CAAC,CAAC;OACJ,CAAC,CAAC;;MAGH,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,IAAI,WAAE,CAAC,EAAE,KAAK;UACtD,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,OAAO,uBAAQA,MAAI,CAAC,KAAK,KAAE,CAAC,CAAC;OAC1C,CAAC,CAAC;EACL,EAAC;EAED;;;;mBAIQ,sCAAa,IAAW;MAC9B,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;EACtD,EAAC;EAED;;;mBAGQ;MACN,IAAI,CAAC,WAAW,EAAE;UAChB,OAAO;OACR;MAEDH,IAAM,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;MACtCA,IAAM,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;MACvDA,IAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC;MAC3DA,IAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC;;MAG3D,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;MACpB,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;MAEpBA,IAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;MACxC,QAAQ,CAAC,GAAG,CAAC;UACX,GAAG,IAAK,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,aAAa,IAAI,UAAK;UAClD,MAAM,GAAK,aAAa,QAAI;OAC7B,CAAC,CAAC;;MAGH,QAAQ,CAAC,WAAW,CAClB,aAAa;WACV,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;WAC1B,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAChC,CAAC;EACJ,EAAC;EAED;;;mBAGQ;MACN,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE;UAChE,WAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;OAC1B;EACH,EAAC;EAED;;;;mBAIQ,sCAAa,KAAY;MAC/B,IACE,CAAC,CAAC,KAAK,CAAC,MAAqB,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC;UACvD,WAAW,EACX;UACA,WAAW,CAAC,KAAK,EAAE,CAAC;OACrB;EACH,EAAC;EAED;;;mBAGQ;MACN,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;UAC9C,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;UACtB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;OAC7B;WAAM;UACL,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;UACtB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;UAC5B,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;;UAGrB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,IAAI,CAAC,WAAW,IAAI,YAAY,EAAE;cAC5D,CAAC,CAAC,YAAY,EAAE,CAAC;cACjB,YAAY,GAAG,KAAK,CAAC;WACtB;UAED,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;UAEtD,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;cAChC,IAAI,CAAC,OAAO,EAAE,CAAC;WAChB;OACF;EACH,EAAC;EAED;;;mBAGQ;;;MACN,WAAW,GAAG,IAAI,CAAC;MAEnB,IAAI,CAAC,YAAY,EAAE;UACjB,CAAC,CAAC,UAAU,EAAE,CAAC;UACf,YAAY,GAAG,IAAI,CAAC;OACrB;MAED,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;MACrB,IAAI,CAAC,QAAQ,EAAE,CAAC;MAEhB,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;;MAGrD,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;MACvB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;MAC1B,IAAI,CAAC,QAAQ;WACV,QAAQ,CAAC,kBAAkB,CAAC;WAC5B,aAAa,sBAAOG,MAAI,CAAC,aAAa,KAAE,CAAC,CAAC;;MAG7C,IAAI,CAAC,QAAQ,EAAE;UACb,QAAQ,GAAG,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;OAChC;;MAGD,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;UACtB,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;OAC1C;WAAM;UACL,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;OACzC;;MAGD,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;MAEvD,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;;;UAGxBF,IAAI,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;UAC7C,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,EAAE;cACpC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,EAAE,CAAC,CAAC;WAC9C;;UAGD,IAAI,IAAI,EAAE;cACR,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,KAAG,QACxB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,IACjC,gBAAa,CAAC;WACf;eAAM;cACL,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC;WACtC;UAED,OAAO,CAAC,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;OAChD;EACH,EAAC;EAED;;;mBAGQ;MACN,OAAO,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC;EAC7D,EAAC;EAED;;;mBAGO;;;MACL,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;UACjB,OAAO;OACR;;MAGD,IACE,CAAC,WAAW;WACT,WAAW,CAAC,KAAK,KAAK,SAAS,IAAI,WAAW,CAAC,KAAK,KAAK,QAAQ,CAAC;UACrE,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,EACvB;UACA,KAAK,CAAC,SAAS,uBAAQE,MAAI,CAAC,MAAM,KAAE,CAAC,CAAC;UAEtC,OAAO;OACR;MAED,IAAI,CAAC,MAAM,EAAE,CAAC;EAChB,EAAC;EAED;;;mBAGO,wBAAM,WAAmB;;mDAAR,GAAG;;;;;;;MAOzB,UAAU;UACR,IAAI,CAACA,MAAI,CAAC,MAAM,EAAE,EAAE;cAClB,OAAO;WACR;UAED,WAAW,GAAG,IAAI,CAAC;UAEnBA,MAAI,CAAC,KAAK,GAAG,SAAS,CAAC;UACvBA,MAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;;UAG3B,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,IAAI,QAAQ,EAAE;cACxC,CAAC,CAAC,WAAW,EAAE,CAAC;cAChB,QAAQ,GAAG,IAAI,CAAC;;cAGhB,CAAC,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;WACzC;UAEDA,MAAI,CAAC,QAAQ;eACV,WAAW,CAAC,kBAAkB,CAAC;eAC/B,aAAa,sBAAOA,MAAI,CAAC,aAAa,KAAE,CAAC,CAAC;UAE7C,IAAIA,MAAI,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE;cACpD,IAAI,CAAC,WAAW,EAAE;kBAChB,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;eACvB;cAED,OAAO,CAAC,GAAG,CAAC,YAAY,EAAEA,MAAI,CAAC,eAAe,CAAC,CAAC;WACjD;;;UAID,UAAU;cACR,OAAO,CAAC,SAAS,CAAC,CAAC;WACpB,EAAE,GAAG,CAAC,CAAC;OACT,CAAC,CAAC;EACL,EAAC;EAED;;;mBAGO;MACL,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;EAC7C,EAAC;EAED;;;mBAGO;MACL,OAAO,IAAI,CAAC,KAAK,CAAC;EACpB,EAAC;EAED;;;mBAGO;MACL,IAAI,IAAI,CAAC,MAAM,EAAE;UACf,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;OACxB;MAED,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,IAAI,CAAC,WAAW,EAAE;UAC5C,IAAI,QAAQ,EAAE;cACZ,CAAC,CAAC,WAAW,EAAE,CAAC;cAChB,QAAQ,GAAG,IAAI,CAAC;WACjB;UAED,IAAI,YAAY,EAAE;cAChB,CAAC,CAAC,YAAY,EAAE,CAAC;cACjB,YAAY,GAAG,KAAK,CAAC;WACtB;OACF;EACH,EAAC;EAED;;;mBAGO;MACL,IAAI,CAAC,QAAQ,EAAE,CAAC;EAClB;;ECjZF;EACA,SAAS,CAAC,EAAE,CAAC,SAAS,YAAG,KAAY;MACnC,IACE,WAAW;UACX,WAAW,CAAC,OAAO,CAAC,UAAU;UAC9B,WAAW,CAAC,KAAK,KAAK,QAAQ;UAC7B,KAAuB,CAAC,OAAO,KAAK,EAAE,EACvC;UACA,WAAW,CAAC,KAAK,EAAE,CAAC;OACrB;EACH,CAAC,CAAC,CAAC;EAEH,IAAI,CAAC,MAAM,GAAG,MAAM;;EC9BpBH,IAAMO,YAAU,GAAG,aAAa,CAAC;EACjCP,IAAMQ,UAAQ,GAAG,cAAc,CAAC;EAahC,CAAC;MACC,SAAS,CAAC,EAAE,CAAC,OAAO,SAAMD,YAAU,SAAK;UACvCP,IAAM,OAAO,GAAG,YAAY,CAAC,IAAmB,EAAEO,YAAU,CAAY,CAAC;UACzEP,IAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC;;UAEhC,OAAO,OAAO,CAAC,MAAM,CAAC;UAEtBA,IAAM,OAAO,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAC;UACpCC,IAAI,QAAQ,GAAG,OAAO,CAAC,IAAI,CAACO,UAAQ,CAAC,CAAC;UAEtC,IAAI,CAAC,QAAQ,EAAE;cACb,QAAQ,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;cAC7C,OAAO,CAAC,IAAI,CAACA,UAAQ,EAAE,QAAQ,CAAC,CAAC;WAClC;UAED,QAAQ,CAAC,IAAI,EAAE,CAAC;OACjB,CAAC,CAAC;EACL,CAAC,CAAC;;EC0EFR,IAAM,cAAc,GAAW;MAC7B,IAAI,EAAE,EAAE;MACR,IAAI,EAAE,KAAK;MACX,KAAK,EAAE,IAAI;;MAEX,OAAO,iBAAU;GAClB,CAAC;EAEFA,IAAMM,iBAAe,GAAY;MAC/B,KAAK,EAAE,EAAE;MACT,OAAO,EAAE,EAAE;MACX,OAAO,EAAE,EAAE;MACX,cAAc,EAAE,KAAK;MACrB,QAAQ,EAAE,EAAE;MACZ,OAAO,EAAE,IAAI;MACb,OAAO,EAAE,IAAI;MACb,KAAK,EAAE,KAAK;MACZ,UAAU,EAAE,IAAI;MAChB,eAAe,EAAE,IAAI;;MAErB,MAAM,iBAAU;;MAEhB,QAAQ,iBAAU;;MAElB,OAAO,iBAAU;;MAEjB,QAAQ,iBAAU;GACnB,CAAC;EAEF,IAAI,CAAC,MAAM,GAAG,UAAU,OAAgB;;;MAEtC,OAAO,GAAG,MAAM,CAAC,EAAE,EAAEA,iBAAe,EAAE,OAAO,CAAC,CAAC;MAE/C,IAAI,CAAC,OAAO,CAAC,OAAQ,YAAG,CAAC,EAAE,MAAM;UAC/B,OAAO,CAAC,OAAQ,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;OAC1D,CAAC,CAAC;;MAGHL,IAAI,WAAW,GAAG,EAAE,CAAC;MACrB,UAAI,OAAO,CAAC,OAAO,0CAAE,MAAM,EAAE;UAC3B,WAAW,GAAG,sCACZ,OAAO,CAAC,cAAc,GAAG,8BAA8B,GAAG,GAC5D,QAAI,CAAC;UAEL,IAAI,CAAC,OAAO,CAAC,OAAO,YAAG,CAAC,EAAE,MAAM;cAC9B,WAAW;kBACT,+BAA+B;sBAC/B,2DACE,MAAM,CAAC,IAAI,GAAG,eAAe,GAAG,GAClC,YAAK,MAAM,CAAC,KAAI,SAAM,CAAC;WAC1B,CAAC,CAAC;UAEH,WAAW,IAAI,QAAQ,CAAC;OACzB;;MAGDD,IAAM,IAAI,GACR,+BAA2B,OAAO,CAAC,SAAQ,QAAI;WAC9C,OAAO,CAAC,KAAK;wDACwB,OAAO,CAAC,MAAK;gBAC/C,EAAE,CAAC;WACN,OAAO,CAAC,OAAO;0DACwB,OAAO,CAAC,QAAO;gBACnD,EAAE,CAAC;UACP,WAAW;UACX,QAAQ,CAAC;;MAGXA,IAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;UACrC,OAAO,EAAE,OAAO,CAAC,OAAO;UACxB,OAAO,EAAE,OAAO,CAAC,OAAO;UACxB,KAAK,EAAE,OAAO,CAAC,KAAK;UACpB,UAAU,EAAE,OAAO,CAAC,UAAU;UAC9B,eAAe,EAAE,OAAO,CAAC,eAAe;OACzC,CAAC,CAAC;;MAGH,UAAI,OAAO,CAAC,OAAO,0CAAE,MAAM,EAAE;UAC3B,QAAQ,CAAC,QAAQ;eACd,IAAI,CAAC,gCAAgC,CAAC;eACtC,IAAI,WAAE,KAAK,EAAE,MAAM;cAClB,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,OAAO;kBAClB,OAAO,CAAC,OAAQ,CAAC,KAAK,CAAC,CAAC,OAAQ,CAAC,QAAQ,CAAC,CAAC;kBAE3C,IAAI,OAAO,CAAC,OAAQ,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE;sBACjC,QAAQ,CAAC,KAAK,EAAE,CAAC;mBAClB;eACF,CAAC,CAAC;WACJ,CAAC,CAAC;OACN;;MAGD,QAAQ,CAAC,QAAQ;WACd,EAAE,CAAC,kBAAkB;UACpB,OAAO,CAAC,MAAO,CAAC,QAAQ,CAAC,CAAC;OAC3B,CAAC;WACD,EAAE,CAAC,oBAAoB;UACtB,OAAO,CAAC,QAAS,CAAC,QAAQ,CAAC,CAAC;OAC7B,CAAC;WACD,EAAE,CAAC,mBAAmB;UACrB,OAAO,CAAC,OAAQ,CAAC,QAAQ,CAAC,CAAC;OAC5B,CAAC;WACD,EAAE,CAAC,oBAAoB;UACtB,OAAO,CAAC,QAAS,CAAC,QAAQ,CAAC,CAAC;OAC7B,CAAC,CAAC;MAEL,QAAQ,CAAC,IAAI,EAAE,CAAC;MAEhB,OAAO,QAAQ,CAAC;EAClB,CAAC;;EChKDA,IAAMM,iBAAe,GAAY;MAC/B,WAAW,EAAE,IAAI;MACjB,OAAO,EAAE,IAAI;MACb,KAAK,EAAE,KAAK;MACZ,UAAU,EAAE,IAAI;MAChB,cAAc,EAAE,IAAI;GACrB,CAAC;EAEF,IAAI,CAAC,KAAK,GAAG,UACX,IAAY,EACZ,KAAW,EACX,SAAe,EACf,OAAa;MAEb,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE;UACrB,OAAO,GAAG,SAAS,CAAC;UACpB,SAAS,GAAG,KAAK,CAAC;UAClB,KAAK,GAAG,EAAE,CAAC;OACZ;MAED,IAAI,WAAW,CAAC,SAAS,CAAC,EAAE;;UAE1B,SAAS,kBAAiB,CAAC;OAC5B;MAED,IAAI,WAAW,CAAC,OAAO,CAAC,EAAE;UACxB,OAAO,GAAG,EAAE,CAAC;OACd;MAED,OAAO,GAAG,MAAM,CAAC,EAAE,EAAEA,iBAAe,EAAE,OAAO,CAAC,CAAC;MAE/C,OAAO,IAAI,CAAC,MAAM,CAAC;UACjB,KAAK,EAAE,KAAK;UACZ,OAAO,EAAE,IAAI;UACb,OAAO,EAAE;cACP;kBACE,IAAI,EAAE,OAAO,CAAC,WAAW;kBACzB,IAAI,EAAE,KAAK;kBACX,KAAK,EAAE,OAAO,CAAC,cAAc;kBAC7B,OAAO,EAAE,SAAS;eACnB,EACF;UACD,QAAQ,EAAE,mBAAmB;UAC7B,OAAO,EAAE,OAAO,CAAC,OAAO;UACxB,KAAK,EAAE,OAAO,CAAC,KAAK;UACpB,UAAU,EAAE,OAAO,CAAC,UAAU;OAC/B,CAAC,CAAC;EACL,CAAC;;ECjCDN,IAAMM,iBAAe,GAAY;MAC/B,WAAW,EAAE,IAAI;MACjB,UAAU,EAAE,QAAQ;MACpB,OAAO,EAAE,IAAI;MACb,KAAK,EAAE,KAAK;MACZ,UAAU,EAAE,IAAI;MAChB,aAAa,EAAE,IAAI;MACnB,cAAc,EAAE,IAAI;GACrB,CAAC;EAEF,IAAI,CAAC,OAAO,GAAG,UACb,IAAY,EACZ,KAAW,EACX,SAAe,EACf,QAAc,EACd,OAAa;MAEb,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE;UACrB,OAAO,GAAG,QAAQ,CAAC;UACnB,QAAQ,GAAG,SAAS,CAAC;UACrB,SAAS,GAAG,KAAK,CAAC;UAClB,KAAK,GAAG,EAAE,CAAC;OACZ;MAED,IAAI,WAAW,CAAC,SAAS,CAAC,EAAE;;UAE1B,SAAS,kBAAiB,CAAC;OAC5B;MAED,IAAI,WAAW,CAAC,QAAQ,CAAC,EAAE;;UAEzB,QAAQ,kBAAiB,CAAC;OAC3B;MAED,IAAI,WAAW,CAAC,OAAO,CAAC,EAAE;UACxB,OAAO,GAAG,EAAE,CAAC;OACd;MAED,OAAO,GAAG,MAAM,CAAC,EAAE,EAAEA,iBAAe,EAAE,OAAO,CAAC,CAAC;MAE/C,OAAO,IAAI,CAAC,MAAM,CAAC;UACjB,KAAK,EAAE,KAAK;UACZ,OAAO,EAAE,IAAI;UACb,OAAO,EAAE;cACP;kBACE,IAAI,EAAE,OAAO,CAAC,UAAU;kBACxB,IAAI,EAAE,KAAK;kBACX,KAAK,EAAE,OAAO,CAAC,aAAa;kBAC5B,OAAO,EAAE,QAAQ;eAClB;cACD;kBACE,IAAI,EAAE,OAAO,CAAC,WAAW;kBACzB,IAAI,EAAE,KAAK;kBACX,KAAK,EAAE,OAAO,CAAC,cAAc;kBAC7B,OAAO,EAAE,SAAS;eACnB,EACF;UACD,QAAQ,EAAE,qBAAqB;UAC/B,OAAO,EAAE,OAAO,CAAC,OAAO;UACxB,KAAK,EAAE,OAAO,CAAC,KAAK;UACpB,UAAU,EAAE,OAAO,CAAC,UAAU;OAC/B,CAAC,CAAC;EACL,CAAC;;ECtCDN,IAAMM,iBAAe,GAAY;MAC/B,WAAW,EAAE,IAAI;MACjB,UAAU,EAAE,QAAQ;MACpB,OAAO,EAAE,IAAI;MACb,KAAK,EAAE,KAAK;MACZ,UAAU,EAAE,IAAI;MAChB,aAAa,EAAE,IAAI;MACnB,cAAc,EAAE,IAAI;MACpB,IAAI,EAAE,MAAM;MACZ,SAAS,EAAE,CAAC;MACZ,YAAY,EAAE,EAAE;MAChB,cAAc,EAAE,KAAK;GACtB,CAAC;EAEF,IAAI,CAAC,MAAM,GAAG,UACZ,KAAa,EACb,KAAW,EACX,SAAe,EACf,QAAc,EACd,OAAa;MAEb,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE;UACrB,OAAO,GAAG,QAAQ,CAAC;UACnB,QAAQ,GAAG,SAAS,CAAC;UACrB,SAAS,GAAG,KAAK,CAAC;UAClB,KAAK,GAAG,EAAE,CAAC;OACZ;MAED,IAAI,WAAW,CAAC,SAAS,CAAC,EAAE;;UAE1B,SAAS,kBAAiB,CAAC;OAC5B;MAED,IAAI,WAAW,CAAC,QAAQ,CAAC,EAAE;;UAEzB,QAAQ,kBAAiB,CAAC;OAC3B;MAED,IAAI,WAAW,CAAC,OAAO,CAAC,EAAE;UACxB,OAAO,GAAG,EAAE,CAAC;OACd;MAED,OAAO,GAAG,MAAM,CAAC,EAAE,EAAEA,iBAAe,EAAE,OAAO,CAAC,CAAC;MAE/CN,IAAM,OAAO,GACX,8BAA8B;WAC7B,KAAK,+CAA0C,KAAK,iBAAa,EAAE,CAAC;WACpE,OAAO,CAAC,IAAI,KAAK,MAAM;mFAElB,OAAO,CAAC,aACV,YACE,OAAO,CAAC,SAAS,GAAG,aAAa,GAAG,OAAO,CAAC,SAAS,GAAG,GAAG,GAAG,GAChE;gBACA,EAAE,CAAC;WACN,OAAO,CAAC,IAAI,KAAK,UAAU;gEAEtB,OAAO,CAAC,SAAS,GAAG,aAAa,GAAG,OAAO,CAAC,SAAS,GAAG,GAAG,GAAG,GAChE,UAAI,OAAO,CAAC,aAAY;gBACxB,EAAE,CAAC;UACP,QAAQ,CAAC;MAEXA,IAAM,aAAa,aAAI,MAAc;UACnCA,IAAM,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,GAAG,EAAE,CAAC;UAClE,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;OACzB,CAAC;MAEFA,IAAM,cAAc,aAAI,MAAc;UACpCA,IAAM,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,GAAG,EAAE,CAAC;UAClE,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;OAC1B,CAAC;MAEF,OAAO,IAAI,CAAC,MAAM,CAAC;iBACjB,KAAK;mBACL,OAAO;UACP,OAAO,EAAE;cACP;kBACE,IAAI,EAAE,OAAO,CAAC,UAAU;kBACxB,IAAI,EAAE,KAAK;kBACX,KAAK,EAAE,OAAO,CAAC,aAAa;kBAC5B,OAAO,EAAE,aAAa;eACvB;cACD;kBACE,IAAI,EAAE,OAAO,CAAC,WAAW;kBACzB,IAAI,EAAE,KAAK;kBACX,KAAK,EAAE,OAAO,CAAC,cAAc;kBAC7B,OAAO,EAAE,cAAc;eACxB,EACF;UACD,QAAQ,EAAE,oBAAoB;UAC9B,OAAO,EAAE,OAAO,CAAC,OAAO;UACxB,KAAK,EAAE,OAAO,CAAC,KAAK;UACpB,UAAU,EAAE,OAAO,CAAC,UAAU;UAC9B,MAAM,YAAG,MAAM;;cAEbA,IAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;cAC7D,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;;cAG9B,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;;cAGlB,IAAI,OAAO,CAAC,IAAI,KAAK,UAAU,IAAI,OAAO,CAAC,cAAc,KAAK,IAAI,EAAE;kBAClE,MAAM,CAAC,EAAE,CAAC,SAAS,YAAG,KAAK;sBACzB,IAAK,KAAuB,CAAC,OAAO,KAAK,EAAE,EAAE;0BAC3CA,IAAM,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,GAAG,EAAE,CAAC;0BAClE,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;0BAEzB,IAAI,OAAO,CAAC,cAAc,EAAE;8BAC1B,MAAM,CAAC,KAAK,EAAE,CAAC;2BAChB;0BAED,OAAO,KAAK,CAAC;uBACd;sBAED,OAAO;mBACR,CAAC,CAAC;eACJ;;cAGD,IAAI,OAAO,CAAC,IAAI,KAAK,UAAU,EAAE;kBAC/B,MAAM,CAAC,EAAE,CAAC,OAAO,uBAAQ,MAAM,CAAC,YAAY,KAAE,CAAC,CAAC;eACjD;;cAGD,IAAI,OAAO,CAAC,SAAS,EAAE;kBACrB,MAAM,CAAC,YAAY,EAAE,CAAC;eACvB;WACF;OACF,CAAC,CAAC;EACL,CAAC;;ECjKDA,IAAMM,iBAAe,GAAY;MAC/B,QAAQ,EAAE,MAAM;MAChB,KAAK,EAAE,CAAC;MACR,OAAO,EAAE,EAAE;GACZ,CAAC;EAEF,IAAM,OAAO,GA0BX,iBACE,QAAyD,EACzD,OAAqB;0CAAF;;;;;MAdd,YAAO,GAAY,MAAM,CAAC,EAAE,EAAEA,iBAAe,CAAC,CAAC;;;;MAK9C,UAAK,GAAU,QAAQ,CAAC;;;;MAKxB,cAAS,GAAQ,IAAI,CAAC;MAM5B,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAC;MAEnC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;;MAG9B,IAAI,CAAC,QAAQ,GAAG,CAAC,yCACkB,CAAC,CAAC,IAAI,GAAE,YACvC,IAAI,CAAC,OAAO,CAAC,QACf,aACD,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;;;MAI1BN,IAAM,IAAI,GAAG,IAAI,CAAC;MAClB,IAAI,CAAC,OAAO;WACT,EAAE,CAAC,uBAAuB,EAAE,UAAU,KAAK;UAC1C,IAAI,IAAI,CAAC,UAAU,CAAC,IAAmB,CAAC,EAAE;cACxC,OAAO;WACR;UAED,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;cACnB,OAAO;WACR;UAED,QAAQ,CAAC,KAAK,CAAC,CAAC;UAEhB,IAAI,CAAC,IAAI,EAAE,CAAC;OACb,CAAC;WACD,EAAE,CAAC,qBAAqB,EAAE,UAAU,KAAK;UACxC,IAAI,IAAI,CAAC,UAAU,CAAC,IAAmB,CAAC,EAAE;cACxC,OAAO;WACR;UAED,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;cACnB,OAAO;WACR;UAED,IAAI,CAAC,KAAK,EAAE,CAAC;OACd,CAAC;WACD,EAAE,CAAC,WAAW,EAAE,UAAU,KAAK;UAC9B,IAAI,IAAI,CAAC,UAAU,CAAC,IAAmB,CAAC,EAAE;cACxC,OAAO;WACR;UAED,QAAQ,CAAC,KAAK,CAAC,CAAC;OACjB,CAAC,CAAC;EACP,EAAC;EAED;;;;oBAIQ,kCAAW,OAAoB;MACrC,QACG,OAA4B,CAAC,QAAQ;UACtC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,SAAS,EACzC;EACJ,EAAC;EAED;;;oBAGQ;MACN,OAAO,OAAO,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC;EAChC,EAAC;EAED;;;oBAGQ;MACNC,IAAI,UAAkB,CAAC;MACvBA,IAAI,SAAiB,CAAC;;MAGtBD,IAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC;;MAG5DA,IAAM,YAAY,GAAG,IAAI,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;;MAGhDA,IAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;MAClDA,IAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;;MAGpDC,IAAI,QAAQ,GAAa,IAAI,CAAC,OAAO,CAAC,QAAS,CAAC;;MAGhD,IAAI,QAAQ,KAAK,MAAM,EAAE;UACvB,IACE,WAAW,CAAC,GAAG;cACb,WAAW,CAAC,MAAM;cAClB,YAAY;cACZ,aAAa;cACb,CAAC;cACH,OAAO,CAAC,MAAM,EAAE,EAChB;cACA,QAAQ,GAAG,QAAQ,CAAC;WACrB;eAAM,IAAI,YAAY,GAAG,aAAa,GAAG,CAAC,GAAG,WAAW,CAAC,GAAG,EAAE;cAC7D,QAAQ,GAAG,KAAK,CAAC;WAClB;eAAM,IAAI,YAAY,GAAG,YAAY,GAAG,CAAC,GAAG,WAAW,CAAC,IAAI,EAAE;cAC7D,QAAQ,GAAG,MAAM,CAAC;WACnB;eAAM,IACL,WAAW,CAAC,KAAK,GAAG,YAAY,GAAG,YAAY,GAAG,CAAC;cACnD,OAAO,CAAC,KAAK,EAAE,GAAG,WAAW,CAAC,IAAI,EAClC;cACA,QAAQ,GAAG,OAAO,CAAC;WACpB;eAAM;cACL,QAAQ,GAAG,QAAQ,CAAC;WACrB;OACF;;MAGD,QAAQ,QAAQ;UACd,KAAK,QAAQ;cACX,UAAU,GAAG,CAAC,CAAC,IAAI,YAAY,GAAG,CAAC,CAAC,CAAC;cACrC,SAAS,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC,GAAG,YAAY,CAAC;cAClD,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;cAC5C,MAAM;UAER,KAAK,KAAK;cACR,UAAU,GAAG,CAAC,CAAC,IAAI,YAAY,GAAG,CAAC,CAAC,CAAC;cACrC,SAAS;kBACP,CAAC,CAAC,IAAI,aAAa,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC;cAC/D,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;cAC/C,MAAM;UAER,KAAK,MAAM;cACT,UAAU,GAAG,CAAC,CAAC,IAAI,YAAY,GAAG,WAAW,CAAC,KAAK,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC;cACxE,SAAS,GAAG,CAAC,CAAC,IAAI,aAAa,GAAG,CAAC,CAAC,CAAC;cACrC,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;cAC9C,MAAM;UAER,KAAK,OAAO;cACV,UAAU,GAAG,WAAW,CAAC,KAAK,GAAG,CAAC,GAAG,YAAY,CAAC;cAClD,SAAS,GAAG,CAAC,CAAC,IAAI,aAAa,GAAG,CAAC,CAAC,CAAC;cACrC,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;cAC7C,MAAM;OACT;MAEDD,IAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;MAE3C,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;UAChB,GAAG,IAAK,YAAY,CAAC,GAAG,GAAG,WAAW,CAAC,MAAM,GAAG,UAAK;UACrD,IAAI,IAAK,YAAY,CAAC,IAAI,GAAG,WAAW,CAAC,KAAK,GAAG,UAAK;UACtD,aAAa,GAAK,UAAU,QAAI;UAChC,YAAY,GAAK,SAAS,QAAI;OAC/B,CAAC,CAAC;EACL,EAAC;EAED;;;;oBAIQ,sCAAa,IAAW;MAC9B,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;EACtD,EAAC;EAED;;;oBAGQ;MACN,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;UAC/C,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;UACtB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;OAC7B;WAAM;UACL,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;UACtB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;OAC7B;EACH,EAAC;EAED;;;oBAGQ;MACN,OAAO,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC;EAC7D,EAAC;EAED;;;oBAGQ;;;MACN,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;MACvB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;MAE1B,IAAI,CAAC,QAAQ;WACV,QAAQ,CAAC,mBAAmB,CAAC;WAC7B,aAAa,sBAAOG,MAAI,CAAC,aAAa,KAAE,CAAC,CAAC;EAC/C,EAAC;EAED;;;;oBAIO,sBAAK,OAAiB;;;MAC3B,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;UACjB,OAAO;OACR;MAEDH,IAAM,UAAU,GAAG,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;MAE5C,IAAI,OAAO,EAAE;UACX,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;OAC/B;;MAGD,IAAI,UAAU,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;UAC/C,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;OAC1C;MAED,IAAI,CAAC,WAAW,EAAE,CAAC;MAEnB,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;UACtB,IAAI,CAAC,SAAS,GAAG,UAAU,sBAAOG,MAAI,CAAC,MAAM,KAAE,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;OACtE;WAAM;UACL,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;UACtB,IAAI,CAAC,MAAM,EAAE,CAAC;OACf;EACH,EAAC;EAED;;;oBAGO;;;MACL,IAAI,IAAI,CAAC,SAAS,EAAE;UAClB,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;UAC7B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;OACvB;MAED,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE;UAClB,OAAO;OACR;MAED,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;MACvB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;MAE3B,IAAI,CAAC,QAAQ;WACV,WAAW,CAAC,mBAAmB,CAAC;WAChC,aAAa,sBAAOA,MAAI,CAAC,aAAa,KAAE,CAAC,CAAC;EAC/C,EAAC;EAED;;;oBAGO;MACL,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;EAC7C,EAAC;EAED;;;oBAGO;MACL,OAAO,IAAI,CAAC,KAAK,CAAC;EACpB,EACD;EAED,IAAI,CAAC,OAAO,GAAG,OAAO;;EChWtBH,IAAMO,YAAU,GAAG,cAAc,CAAC;EAClCP,IAAMQ,UAAQ,GAAG,eAAe,CAAC;EAEjC,CAAC;;MAEC,SAAS,CAAC,EAAE,CAAC,sBAAsB,SAAMD,YAAU,SAAK;UACtDP,IAAM,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;UACxBC,IAAI,QAAQ,GAAG,OAAO,CAAC,IAAI,CAACO,UAAQ,CAAC,CAAC;UAEtC,IAAI,CAAC,QAAQ,EAAE;cACb,QAAQ,GAAG,IAAI,IAAI,CAAC,OAAO,CACzB,IAAmB,EACnB,YAAY,CAAC,IAAmB,EAAED,YAAU,CAAC,CAC9C,CAAC;cACF,OAAO,CAAC,IAAI,CAACC,UAAQ,EAAE,QAAQ,CAAC,CAAC;WAClC;OACF,CAAC,CAAC;EACL,CAAC,CAAC;;EC8FFR,IAAMM,iBAAe,GAAY;MAC/B,OAAO,EAAE,EAAE;MACX,OAAO,EAAE,IAAI;MACb,QAAQ,EAAE,QAAQ;MAClB,UAAU,EAAE,EAAE;MACd,WAAW,EAAE,EAAE;MACf,kBAAkB,EAAE,IAAI;MACxB,mBAAmB,EAAE,IAAI;;MAEzB,OAAO,iBAAU;;MAEjB,aAAa,iBAAU;;MAEvB,MAAM,iBAAU;;MAEhB,QAAQ,iBAAU;;MAElB,OAAO,iBAAU;;MAEjB,QAAQ,iBAAU;GACnB,CAAC;EAEF;;;EAGAL,IAAIQ,aAAW,GAAoB,IAAI,CAAC;EAExC;;;EAGAT,IAAMU,WAAS,GAAG,gBAAgB,CAAC;EAEnC,IAAM,QAAQ,GAoBZ,kBAAmB,OAAgB;;;;MAZ5B,YAAO,GAAY,MAAM,CAAC,EAAE,EAAEJ,iBAAe,CAAC,CAAC;;;;MAK9C,UAAK,GAAU,QAAQ,CAAC;;;;MAKxB,cAAS,GAAQ,IAAI,CAAC;MAG5B,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;;MAG9BL,IAAI,gBAAgB,GAAG,EAAE,CAAC;MAC1BA,IAAI,gBAAgB,GAAG,EAAE,CAAC;MAE1B,IACE,IAAI,CAAC,OAAO,CAAC,WAAY,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC;UAC5C,IAAI,CAAC,OAAO,CAAC,WAAY,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAC9C;UACA,gBAAgB,GAAG,oBAAgB,IAAI,CAAC,OAAO,CAAC,YAAW,OAAG,CAAC;OAChE;WAAM,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK,EAAE,EAAE;UAC1C,gBAAgB,GAAG,sBAAmB,IAAI,CAAC,OAAO,CAAC,YAAa,CAAC;OAClE;;MAGD,IAAI,CAAC,QAAQ,GAAG,CAAC,CACf,6BAA6B;UAC3B,wCAAmC,IAAI,CAAC,OAAO,CAAC,QAAO,WAAQ;WAC9D,IAAI,CAAC,OAAO,CAAC,UAAU;yHAC+E,gBAAgB,WAAK,gBAAgB,UAAI,IAAI,CAAC,OAAO,CAAC,WAAU;gBACnK,EAAE,CAAC;UACP,QAAQ,CACX,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;;MAG1B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;MAE1B,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,QAAQ,sBAAkB,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;EAC5E,EAAC;EAED;;;;qBAIQ,oDAAoB,KAAY;MACtCD,IAAM,OAAO,GAAG,CAAC,CAAC,KAAK,CAAC,MAAqB,CAAC,CAAC;MAE/C,IACE,CAAC,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC;UAClC,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,MAAM,EACzC;UACAS,aAAY,CAAC,KAAK,EAAE,CAAC;OACtB;EACH,EAAC;EAED;;;;qBAIQ,oCAAY,KAAuB;MACzCT,IAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;MACrDA,IAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;MAEvCC,IAAI,UAAU,CAAC;MACfA,IAAI,UAAU,CAAC;;MAGf,IAAI,QAAQ,KAAK,QAAQ,IAAI,QAAQ,KAAK,KAAK,EAAE;UAC/C,UAAU,GAAG,MAAM,CAAC;OACrB;WAAM;UACL,UAAU,GAAG,GAAG,CAAC;OAClB;;MAGD,IAAI,KAAK,KAAK,MAAM,EAAE;UACpB,UAAU,GAAG,GAAG,CAAC;OAClB;WAAM;UACL,IAAI,QAAQ,KAAK,QAAQ,EAAE;cACzB,UAAU,GAAG,cAAc,CAAC;WAC7B;UAED,IAAI,QAAQ,KAAK,KAAK,EAAE;cACtB,UAAU,GAAG,CAAC,cAAc,CAAC;WAC9B;UAED,IAAI,QAAQ,KAAK,UAAU,IAAI,QAAQ,KAAK,WAAW,EAAE;cACvD,UAAU,GAAG,CAAC,cAAc,GAAG,EAAE,CAAC;WACnC;UAED,IAAI,QAAQ,KAAK,aAAa,IAAI,QAAQ,KAAK,cAAc,EAAE;cAC7D,UAAU,GAAG,cAAc,GAAG,EAAE,CAAC;WAClC;OACF;MAED,IAAI,CAAC,QAAQ,CAAC,SAAS,iBAAc,UAAU,SAAI,UAAU,SAAK,CAAC;EACrE,EAAC;EAED;;;qBAGO;;;MACL,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;UACvD,OAAO;OACR;;MAGD,IAAIQ,aAAW,EAAE;UACf,KAAK,CAACC,WAAS,uBAAQP,MAAI,CAAC,IAAI,KAAE,CAAC,CAAC;UACpC,OAAO;OACR;MAEDM,aAAW,GAAG,IAAI,CAAC;;MAGnB,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;MACvB,IAAI,CAAC,OAAO,CAAC,MAAO,CAAC,IAAI,CAAC,CAAC;MAE3B,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;MAEzB,IAAI,CAAC,QAAQ,CAAC,aAAa;UACzB,IAAIN,MAAI,CAAC,KAAK,KAAK,SAAS,EAAE;cAC5B,OAAO;WACR;UAEDA,MAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;UACtBA,MAAI,CAAC,OAAO,CAAC,QAAS,CAACA,MAAI,CAAC,CAAC;;UAG7B,IAAIA,MAAI,CAAC,OAAO,CAAC,UAAU,EAAE;cAC3BA,MAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAAE,CAAC,OAAO;kBACpDA,MAAI,CAAC,OAAO,CAAC,aAAc,CAACA,MAAI,CAAC,CAAC;kBAClC,IAAIA,MAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE;sBACnCA,MAAI,CAAC,KAAK,EAAE,CAAC;mBACd;eACF,CAAC,CAAC;WACJ;;UAGDA,MAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,YAAG,KAAK;cAC9B,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,MAAqB,CAAC,CAAC,QAAQ,CAAC,sBAAsB,CAAC,EAAE;kBACpEA,MAAI,CAAC,OAAO,CAAC,OAAQ,CAACA,MAAI,CAAC,CAAC;eAC7B;WACF,CAAC,CAAC;;UAGH,IAAIA,MAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;cACpC,SAAS,CAAC,EAAE,CAAC,UAAU,EAAEA,MAAI,CAAC,mBAAmB,CAAC,CAAC;WACpD;;UAGD,IAAIA,MAAI,CAAC,OAAO,CAAC,OAAO,EAAE;cACxBA,MAAI,CAAC,SAAS,GAAG,UAAU,sBAAOA,MAAI,CAAC,KAAK,KAAE,EAAEA,MAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;WACvE;OACF,CAAC,CAAC;EACL,EAAC;EAED;;;qBAGO;;;MACL,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;UACvD,OAAO;OACR;MAED,IAAI,IAAI,CAAC,SAAS,EAAE;UAClB,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;OAC9B;MAED,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;UACpC,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;OACrD;MAED,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;MACvB,IAAI,CAAC,OAAO,CAAC,OAAQ,CAAC,IAAI,CAAC,CAAC;MAE5B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;MAE1B,IAAI,CAAC,QAAQ,CAAC,aAAa;UACzB,IAAIA,MAAI,CAAC,KAAK,KAAK,SAAS,EAAE;cAC5B,OAAO;WACR;UAEDM,aAAW,GAAG,IAAI,CAAC;UACnBN,MAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;UACtBA,MAAI,CAAC,OAAO,CAAC,QAAS,CAACA,MAAI,CAAC,CAAC;UAC7BA,MAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;UACvB,OAAO,CAACO,WAAS,CAAC,CAAC;OACpB,CAAC,CAAC;EACL,EACD;EAED,IAAI,CAAC,QAAQ,GAAG,UAAU,OAAY,EAAE,OAAiB;0CAAF;;MACrD,IAAI,QAAQ,CAAC,OAAO,CAAC,EAAE;UACrB,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;OAC3B;WAAM;UACL,OAAO,GAAG,OAAO,CAAC;OACnB;MAEDV,IAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC;MAEvC,QAAQ,CAAC,IAAI,EAAE,CAAC;MAEhB,OAAO,QAAQ,CAAC;EAClB,CAAC;;EChWD,CAAC;;MAEC,SAAS,CAAC,EAAE,CAAC,OAAO,EAAE,oBAAoB,EAAE;UAC1CA,IAAM,KAAK,GAAG,CAAC,CAAC,IAAmB,CAAC,CAAC;UACrCA,IAAM,UAAU,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;UAElC,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,WAAE,KAAK,EAAE,IAAI;cACxCA,IAAM,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;cAE9B,IAAI,MAAM,EAAE;kBACV,cAAc,CAAC,QAAQ,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE;6BAC9D,KAAK;mBACN,CAAC,CAAC;eACJ;cAED,MAAM;oBACF,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,wBAAwB,CAAC;oBAC1C,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,wBAAwB,CAAC,CAAC;WACnD,CAAC,CAAC;OACJ,CAAC,CAAC;;MAGH,IAAI,CAAC,QAAQ,CAAC,8BAA8B,EAAE;UAC5C,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;cACtB,WAAW,EAAE,2BAA2B;cACxC,aAAa,EAAE,6BAA6B;WAC7C,CAAC,CAAC;OACJ,CAAC,CAAC;EACL,CAAC,CAAC;;ECnBF;;;;EAIA,SAAS,SAAS,CAAC,KAA6B;sCAAL;;MACzC,QACE,sCACE,KAAK,4BAAyB,SAAU,GAC1C,QAAI;UACJ,6DAA6D;UAC7D,yCAAyC;UACzC,QAAQ;UACR,sCAAsC;UACtC,yCAAyC;UACzC,QAAQ;UACR,8DAA8D;UAC9D,yCAAyC;UACzC,QAAQ;UACR,QAAQ,EACR;EACJ,CAAC;EAED;;;;EAIA,SAAS,QAAQ,CAAC,OAAoB;MACpCA,IAAM,QAAQ,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC;MAE5BA,IAAM,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAAC,uBAAuB,CAAC;YACpD,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;YACzD,SAAS,EAAE,CAAC;MAEhB,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EACvB,CAAC;EAED,CAAC;;MAEC,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE;UAC7B,QAAQ,CAAC,IAAI,CAAC,CAAC;OAChB,CAAC,CAAC;EACL,CAAC,CAAC,CAAC;EAEH,IAAI,CAAC,cAAc,GAAG,UACpB,QAA0D;MAE1DA,IAAM,SAAS,GAAG,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;MAE3E,SAAS,CAAC,IAAI,CAAC;UACb,QAAQ,CAAC,IAAI,CAAC,CAAC;OAChB,CAAC,CAAC;EACL,CAAC;;ECkCDA,IAAMM,iBAAe,GAAY;MAC/B,QAAQ,EAAE,MAAM;MAChB,KAAK,EAAE,MAAM;MACb,MAAM,EAAE,EAAE;MACV,KAAK,EAAE,KAAK;MACZ,OAAO,EAAE,MAAM;MACf,cAAc,EAAE,OAAO;MACvB,YAAY,EAAE,GAAG;GAClB,CAAC;EAEF,IAAM,IAAI,GA+BR,cACE,cAA+D,EAC/D,YAA6D,EAC7D,OAAqB;;0CAAF;;;;;MApBd,YAAO,GAAY,MAAM,CAAC,EAAE,EAAEA,iBAAe,CAAC,CAAC;;;;MAK9C,UAAK,GAAU,QAAQ,CAAC;MAiB9B,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,cAAc,CAAC,CAAC,KAAK,EAAE,CAAC;MACzC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,YAAY,CAAC,CAAC,KAAK,EAAE,CAAC;;MAGxC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE;UACrD,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;OACrE;MAED,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;;MAG9B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;;MAG7D,IAAI,CAAC,SAAS;UACZ,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,MAAM,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,OAAQ,CAAC;;MAG5E,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,uBAAQH,MAAI,CAAC,MAAM,KAAE,CAAC,CAAC;;MAG9C,SAAS,CAAC,EAAE,CAAC,kBAAkB,YAAG,KAAY;UAC5CH,IAAM,OAAO,GAAG,CAAC,CAAC,KAAK,CAAC,MAAqB,CAAC,CAAC;UAE/C,IACEG,MAAI,CAAC,MAAM,EAAE;cACb,CAAC,OAAO,CAAC,EAAE,CAACA,MAAI,CAAC,QAAQ,CAAC;cAC1B,CAAC,QAAQ,CAACA,MAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;cACvC,CAAC,OAAO,CAAC,EAAE,CAACA,MAAI,CAAC,OAAO,CAAC;cACzB,CAAC,QAAQ,CAACA,MAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EACtC;cACAA,MAAI,CAAC,KAAK,EAAE,CAAC;WACd;OACF,CAAC,CAAC;;;MAIHH,IAAM,IAAI,GAAG,IAAI,CAAC;MAClB,SAAS,CAAC,EAAE,CAAC,OAAO,EAAE,iBAAiB,EAAE;UACvCA,IAAM,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;UAEtB,IACE,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM;cAChC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,SAAS,EACpC;cACA,IAAI,CAAC,KAAK,EAAE,CAAC;WACd;OACF,CAAC,CAAC;;MAGH,IAAI,CAAC,gBAAgB,EAAE,CAAC;;MAGxB,OAAO,CAAC,EAAE,CACR,QAAQ,EACR,CAAC,CAAC,QAAQ,sBAAOG,MAAI,CAAC,QAAQ,KAAE,EAAE,GAAG,CAAC,CACvC,CAAC;EACJ,EAAC;EAED;;;iBAGQ;MACN,OAAO,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC;EAC7D,EAAC;EAED;;;;iBAIQ,sCAAa,IAAW;MAC9B,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;EACpD,EAAC;EAED;;;iBAGQ;MACNF,IAAI,QAAQ,CAAC;MACbA,IAAI,OAAO,CAAC;;MAGZA,IAAI,QAAqC,CAAC;MAC1CA,IAAI,KAAkC,CAAC;;MAGvCD,IAAM,YAAY,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;MACtCA,IAAM,WAAW,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;;MAGpCA,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAO,CAAC;MACpCA,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;MACjCA,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;;MAGnCC,IAAI,gBAAgB,CAAC;MACrBA,IAAI,gBAAgB,CAAC;;MAGrBD,IAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;MACxCA,IAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;;MAG1CA,IAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC;MAC3DA,IAAM,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC;MACjCA,IAAM,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC;MACnCA,IAAM,YAAY,GAAG,UAAU,CAAC,MAAM,CAAC;MACvCA,IAAM,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC;MACrCA,IAAM,YAAY,GAAG,YAAY,GAAG,SAAS,GAAG,YAAY,CAAC;MAC7DA,IAAM,WAAW,GAAG,WAAW,GAAG,UAAU,GAAG,WAAW,CAAC;;MAG3DA,IAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;MAClDA,IAAM,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;;MAGpD,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,MAAM,EAAE;UACpC,IAAI,YAAY,IAAI,SAAS,GAAG,YAAY,GAAG,CAAC,CAAC,GAAG,UAAU,GAAG,MAAM,EAAE;;cAEvE,QAAQ,GAAG,QAAQ,CAAC;WACrB;eAAM,IACL,SAAS,IAAI,SAAS,GAAG,YAAY,GAAG,CAAC,CAAC;cAC1C,UAAU,GAAG,MAAM,EACnB;;cAEA,QAAQ,GAAG,KAAK,CAAC;WAClB;eAAM;;cAEL,QAAQ,GAAG,QAAQ,CAAC;WACrB;OACF;WAAM;UACL,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAS,CAAC;OACnC;;MAGD,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,MAAM,EAAE;UACjC,IAAI,WAAW,GAAG,WAAW,GAAG,SAAS,GAAG,MAAM,EAAE;;cAElD,KAAK,GAAG,MAAM,CAAC;WAChB;eAAM,IAAI,UAAU,GAAG,WAAW,GAAG,SAAS,GAAG,MAAM,EAAE;;cAExD,KAAK,GAAG,OAAO,CAAC;WACjB;eAAM;;cAEL,KAAK,GAAG,QAAQ,CAAC;WAClB;OACF;WAAM;UACL,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAM,CAAC;OAC7B;;MAGD,IAAI,QAAQ,KAAK,QAAQ,EAAE;UACzB,gBAAgB,GAAG,GAAG,CAAC;UACvB,OAAO;cACL,CAAC,SAAS,GAAG,CAAC,GAAG,YAAY;mBAC5B,OAAO,GAAG,SAAS,GAAG,eAAe,CAAC,CAAC;OAC3C;WAAM,IAAI,QAAQ,KAAK,KAAK,EAAE;UAC7B,gBAAgB,GAAG,MAAM,CAAC;UAC1B,OAAO;cACL,CAAC,SAAS,GAAG,YAAY,GAAG,CAAC;mBAC5B,OAAO,GAAG,SAAS,GAAG,UAAU,GAAG,eAAe,GAAG,UAAU,CAAC,CAAC;OACrE;WAAM;UACL,gBAAgB,GAAG,KAAK,CAAC;;;;UAKzBC,IAAI,cAAc,GAAG,UAAU,CAAC;;UAGhC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;cACnB,IAAI,UAAU,GAAG,MAAM,GAAG,CAAC,GAAG,YAAY,EAAE;kBAC1C,cAAc,GAAG,YAAY,GAAG,MAAM,GAAG,CAAC,CAAC;kBAC3C,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;eACtC;WACF;UAED,OAAO;cACL,CAAC,YAAY,GAAG,cAAc,IAAI,CAAC;mBAClC,OAAO,GAAG,CAAC,GAAG,eAAe,GAAG,SAAS,CAAC,CAAC;OAC/C;MAED,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,GAAK,OAAO,SAAK,CAAC;;MAGzC,IAAI,KAAK,KAAK,MAAM,EAAE;UACpB,gBAAgB,GAAG,GAAG,CAAC;UACvB,QAAQ,GAAG,OAAO,GAAG,UAAU,GAAG,gBAAgB,CAAC;OACpD;WAAM,IAAI,KAAK,KAAK,OAAO,EAAE;UAC5B,gBAAgB,GAAG,MAAM,CAAC;UAC1B,QAAQ,GAAG,OAAO;gBACd,UAAU,GAAG,WAAW,GAAG,SAAS;gBACpC,gBAAgB,GAAG,WAAW,GAAG,SAAS,CAAC;OAChD;WAAM;UACL,gBAAgB,GAAG,KAAK,CAAC;;;UAIzBA,IAAI,aAAa,GAAG,SAAS,CAAC;;UAG9B,IAAI,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,WAAW,EAAE;cACxC,aAAa,GAAG,WAAW,GAAG,MAAM,GAAG,CAAC,CAAC;cACzC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;WACpC;UAED,QAAQ;cACN,CAAC,WAAW,GAAG,aAAa,IAAI,CAAC;mBAChC,OAAO,GAAG,CAAC,GAAG,gBAAgB,GAAG,UAAU,CAAC,CAAC;OACjD;MAED,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,GAAK,QAAQ,SAAK,CAAC;;MAG3C,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAI,gBAAgB,SAAI,kBAAmB,CAAC;EAC3E,EAAC;EAED;;;;iBAIQ,4CAAgB,QAAY;MAClCD,IAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;MAEjDC,IAAI,UAAU,CAAC;MACfA,IAAI,WAAW,CAAC;;MAGhBA,IAAI,QAA0B,CAAC;MAC/BA,IAAI,KAAuB,CAAC;;MAG5BD,IAAM,YAAY,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;MACtCA,IAAM,WAAW,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;;MAGpCC,IAAI,gBAAgB,CAAC;MACrBA,IAAI,gBAAgB,CAAC;;MAGrBD,IAAM,YAAY,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC;MACtCA,IAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;;MAGxCA,IAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC;MAClDA,IAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC;MACjCA,IAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC;MACnCA,IAAM,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC;MAC/BA,IAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC;;MAG7B,IAAI,YAAY,GAAG,OAAO,GAAG,aAAa,EAAE;;UAE1C,QAAQ,GAAG,QAAQ,CAAC;OACrB;WAAM,IAAI,OAAO,GAAG,UAAU,GAAG,aAAa,EAAE;;UAE/C,QAAQ,GAAG,KAAK,CAAC;OAClB;WAAM;;UAEL,QAAQ,GAAG,QAAQ,CAAC;OACrB;;MAGD,IAAI,WAAW,GAAG,QAAQ,GAAG,SAAS,GAAG,YAAY,EAAE;;UAErD,KAAK,GAAG,MAAM,CAAC;OAChB;WAAM,IAAI,QAAQ,GAAG,YAAY,EAAE;;UAElC,KAAK,GAAG,OAAO,CAAC;OACjB;WAAM;;UAEL,KAAK,GAAG,MAAM,CAAC;OAChB;;MAGD,IAAI,QAAQ,KAAK,QAAQ,EAAE;UACzB,gBAAgB,GAAG,GAAG,CAAC;UACvB,UAAU,GAAG,GAAG,CAAC;OAClB;WAAM,IAAI,QAAQ,KAAK,KAAK,EAAE;UAC7B,gBAAgB,GAAG,MAAM,CAAC;UAC1B,UAAU,GAAG,CAAC,aAAa,GAAG,UAAU,CAAC;OAC1C;MAED,QAAQ,CAAC,GAAG,CAAC,KAAK,GAAK,UAAU,SAAK,CAAC;;MAGvC,IAAI,KAAK,KAAK,MAAM,EAAE;UACpB,gBAAgB,GAAG,GAAG,CAAC;UACvB,WAAW,GAAG,SAAS,CAAC;OACzB;WAAM,IAAI,KAAK,KAAK,OAAO,EAAE;UAC5B,gBAAgB,GAAG,MAAM,CAAC;UAC1B,WAAW,GAAG,CAAC,YAAY,CAAC;OAC7B;MAED,QAAQ,CAAC,GAAG,CAAC,MAAM,GAAK,WAAW,SAAK,CAAC;;MAGzC,QAAQ,CAAC,eAAe,EAAI,gBAAgB,SAAI,kBAAmB,CAAC;EACtE,EAAC;EAED;;;;iBAIQ,oCAAY,QAAY;MAC9B,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;MAE/B,QAAQ;WACL,QAAQ,CAAC,gBAAgB,CAAC;WAC1B,MAAM,CAAC,iBAAiB,CAAC;WACzB,QAAQ,CAAC,uBAAuB,CAAC,CAAC;EACvC,EAAC;EAED;;;;iBAIQ,sCAAa,QAAY;;MAE/B,QAAQ;WACL,WAAW,CAAC,gBAAgB,CAAC;WAC7B,QAAQ,CAAC,mBAAmB,CAAC;WAC7B,aAAa,sBAAO,QAAQ,CAAC,WAAW,CAAC,mBAAmB,IAAC,CAAC;;WAG9D,MAAM,CAAC,iBAAiB,CAAC;WACzB,WAAW,CAAC,uBAAuB,CAAC,CAAC;;MAGxC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,WAAE,CAAC,EAAE,IAAI;UACvCA,IAAM,WAAW,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;UAE5B,WAAW;eACR,WAAW,CAAC,gBAAgB,CAAC;eAC7B,QAAQ,CAAC,mBAAmB,CAAC;eAC7B,aAAa,sBAAO,WAAW,CAAC,WAAW,CAAC,mBAAmB,IAAC,CAAC;eACjE,MAAM,CAAC,iBAAiB,CAAC;eACzB,WAAW,CAAC,uBAAuB,CAAC,CAAC;OACzC,CAAC,CAAC;EACL,EAAC;EAED;;;;iBAIQ,wCAAc,QAAY;MAChC,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,CAAC;YAC/B,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;YAC3B,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;EACjC,EAAC;EAED;;;iBAGQ;;MAENA,IAAM,IAAI,GAAG,IAAI,CAAC;;MAGlB,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,iBAAiB,EAAE,UAAU,KAAK;UAC1DA,IAAM,KAAK,GAAG,CAAC,CAAC,IAAmB,CAAC,CAAC;UACrCA,IAAM,OAAO,GAAG,CAAC,CAAC,KAAK,CAAC,MAAqB,CAAC,CAAC;;UAG/C,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,SAAS,EAAE;cACxC,OAAO;WACR;;UAGD,IAAI,OAAO,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,OAAO,CAAC,EAAE,CAAC,eAAe,CAAC,EAAE;cAC3D,OAAO;WACR;;UAGD,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE;cACzD,OAAO;WACR;;UAGDA,IAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;;UAG9C,KAAK;eACF,MAAM,CAAC,YAAY,CAAC;eACpB,QAAQ,CAAC,iBAAiB,CAAC;eAC3B,IAAI,WAAE,CAAC,EAAE,IAAI;cACZA,IAAM,WAAW,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;cAEnD,IACE,WAAW,CAAC,MAAM;mBACjB,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,EAC/C;kBACA,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;eAChC;WACF,CAAC,CAAC;;UAGL,IAAI,QAAQ,CAAC,MAAM,EAAE;cACnB,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;WAC9B;OACF,CAAC,CAAC;MAEH,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,KAAK,OAAO,EAAE;;UAE3CC,IAAI,OAAO,GAAQ,IAAI,CAAC;UACxBA,IAAI,WAAW,GAAQ,IAAI,CAAC;UAE5B,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,oBAAoB,EAAE,iBAAiB,EAAE,UACxD,KAAK;cAELD,IAAM,KAAK,GAAG,CAAC,CAAC,IAAmB,CAAC,CAAC;cACrCA,IAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC;cAC7BA,IAAM,cAAc,GAAG,CAAC,CACrB,KAAoB,CAAC,aAA4B,CACnD,CAAC;;cAGF,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,SAAS,EAAE;kBACxC,OAAO;eACR;;cAGD,IAAI,SAAS,KAAK,WAAW,EAAE;kBAC7B,IACE,CAAC,KAAK,CAAC,EAAE,CAAC,cAAc,CAAC;sBACzB,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,EACrC;sBACA,OAAO;mBACR;eACF;;mBAGI,IAAI,SAAS,KAAK,UAAU,EAAE;kBACjC,IACE,KAAK,CAAC,EAAE,CAAC,cAAc,CAAC;sBACxB,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,EACrC;sBACA,OAAO;mBACR;eACF;;cAGDA,IAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;;cAG9C,IAAI,SAAS,KAAK,WAAW,EAAE;kBAC7B,IAAI,QAAQ,CAAC,MAAM,EAAE;;sBAEnBA,IAAM,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;sBACzD,IAAI,QAAQ,EAAE;0BACZ,YAAY,CAAC,QAAQ,CAAC,CAAC;uBACxB;;sBAGD,IAAI,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;0BACvC,OAAO;uBACR;;sBAGD,YAAY,CAAC,WAAW,CAAC,CAAC;;sBAG1B,OAAO,GAAG,WAAW,GAAG,UAAU,sBAC1B,IAAI,CAAC,WAAW,CAAC,QAAQ,IAAC,EAChC,IAAI,CAAC,OAAO,CAAC,YAAY,CAC1B,CAAC;sBAEF,QAAQ,CAAC,IAAI,CAAC,uBAAuB,EAAE,OAAO,CAAC,CAAC;mBACjD;eACF;;mBAGI,IAAI,SAAS,KAAK,UAAU,EAAE;kBACjC,IAAI,QAAQ,CAAC,MAAM,EAAE;;sBAEnBA,IAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;sBACvD,IAAI,OAAO,EAAE;0BACX,YAAY,CAAC,OAAO,CAAC,CAAC;uBACvB;;sBAGD,OAAO,GAAG,UAAU,sBACZ,IAAI,CAAC,YAAY,CAAC,QAAQ,IAAC,EACjC,IAAI,CAAC,OAAO,CAAC,YAAY,CAC1B,CAAC;sBAEF,QAAQ,CAAC,IAAI,CAAC,wBAAwB,EAAE,OAAO,CAAC,CAAC;mBAClD;eACF;WACF,CAAC,CAAC;OACJ;EACH,EAAC;EAED;;;iBAGQ;MACN,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,mBAAmB,CAAC,CAAC;MAE/C,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;UAC5B,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;UACtB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;OAC7B;MAED,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;UAC5B,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;UACtB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;;UAG5B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;cAChB,GAAG,EAAE,EAAE;cACP,IAAI,EAAE,EAAE;cACR,KAAK,EAAE,EAAE;cACT,QAAQ,EAAE,OAAO;WAClB,CAAC,CAAC;OACJ;EACH,EAAC;EAED;;;iBAGO;MACL,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;EAC7C,EAAC;EAED;;;iBAGO;;;MACL,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;UACjB,OAAO;OACR;MAED,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;MACvB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;MAE1B,IAAI,CAAC,QAAQ,EAAE,CAAC;MAEhB,IAAI,CAAC,QAAQ;;WAEV,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,OAAO,GAAG,UAAU,CAAC;WAC1D,QAAQ,CAAC,gBAAgB,CAAC;WAC1B,aAAa,sBAAOG,MAAI,CAAC,aAAa,KAAE,CAAC,CAAC;EAC/C,EAAC;EAED;;;iBAGO;;;MACL,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE;UAClB,OAAO;OACR;MAED,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;MACvB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;;MAG3B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,WAAE,CAAC,EAAE,OAAO;UAC/CA,MAAI,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;OAC/B,CAAC,CAAC;MAEH,IAAI,CAAC,QAAQ;WACV,WAAW,CAAC,gBAAgB,CAAC;WAC7B,QAAQ,CAAC,mBAAmB,CAAC;WAC7B,aAAa,sBAAOA,MAAI,CAAC,aAAa,KAAE,CAAC,CAAC;EAC/C,EACD;EAED,IAAI,CAAC,IAAI,GAAG,IAAI;;EC1sBhBH,IAAMO,YAAU,GAAG,WAAW,CAAC;EAC/BP,IAAMQ,UAAQ,GAAG,YAAY,CAAC;EAa9B,CAAC;MACC,SAAS,CAAC,EAAE,CAAC,OAAO,SAAMD,YAAU,SAAK;UACvCP,IAAM,KAAK,GAAG,CAAC,CAAC,IAAmB,CAAC,CAAC;UACrCC,IAAI,QAAQ,GAAG,KAAK,CAAC,IAAI,CAACO,UAAQ,CAAC,CAAC;UAEpC,IAAI,CAAC,QAAQ,EAAE;cACbR,IAAM,OAAO,GAAG,YAAY,CAAC,IAAmB,EAAEO,YAAU,CAAY,CAAC;cACzEP,IAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC;;cAEpC,OAAO,OAAO,CAAC,MAAM,CAAC;cAEtB,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;cACvD,KAAK,CAAC,IAAI,CAACQ,UAAQ,EAAE,QAAQ,CAAC,CAAC;cAE/B,QAAQ,CAAC,MAAM,EAAE,CAAC;WACnB;OACF,CAAC,CAAC;EACL,CAAC,CAAC;;;;;;;;"}