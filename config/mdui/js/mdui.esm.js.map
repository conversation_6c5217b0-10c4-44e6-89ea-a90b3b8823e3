{"version": 3, "file": "mdui.esm.js", "sources": ["../../node_modules/mdui.jq/es/utils.js", "../../node_modules/mdui.jq/es/functions/each.js", "../../node_modules/mdui.jq/es/JQ.js", "../../node_modules/mdui.jq/es/$.js", "../../src/mdui.ts", "../../node_modules/mdui.jq/es/methods/each.js", "../../node_modules/mdui.jq/es/functions/contains.js", "../../node_modules/mdui.jq/es/functions/merge.js", "../../node_modules/mdui.jq/es/methods/get.js", "../../node_modules/mdui.jq/es/methods/find.js", "../../node_modules/mdui.jq/es/methods/utils/event.js", "../../node_modules/mdui.jq/es/methods/trigger.js", "../../node_modules/mdui.jq/es/functions/extend.js", "../../node_modules/mdui.jq/es/functions/param.js", "../../node_modules/mdui.jq/es/functions/utils/ajax.js", "../../node_modules/mdui.jq/es/functions/ajax.js", "../../node_modules/mdui.jq/es/static/ajax.js", "../../node_modules/mdui.jq/es/functions/ajaxSetup.js", "../../node_modules/mdui.jq/es/static/ajaxSetup.js", "../../node_modules/mdui.jq/es/static/contains.js", "../../node_modules/mdui.jq/es/functions/utils/data.js", "../../node_modules/mdui.jq/es/functions/data.js", "../../node_modules/mdui.jq/es/static/data.js", "../../node_modules/mdui.jq/es/static/each.js", "../../node_modules/mdui.jq/es/static/extend.js", "../../node_modules/mdui.jq/es/functions/map.js", "../../node_modules/mdui.jq/es/static/map.js", "../../node_modules/mdui.jq/es/static/merge.js", "../../node_modules/mdui.jq/es/static/param.js", "../../node_modules/mdui.jq/es/functions/removeData.js", "../../node_modules/mdui.jq/es/static/removeData.js", "../../node_modules/mdui.jq/es/functions/unique.js", "../../node_modules/mdui.jq/es/static/unique.js", "../../node_modules/mdui.jq/es/methods/add.js", "../../node_modules/mdui.jq/es/methods/addClass.js", "../../node_modules/mdui.jq/es/methods/insertBefore.js", "../../node_modules/mdui.jq/es/methods/before.js", "../../node_modules/mdui.jq/es/methods/off.js", "../../node_modules/mdui.jq/es/methods/on.js", "../../node_modules/mdui.jq/es/methods/ajaxStart.js", "../../node_modules/mdui.jq/es/methods/map.js", "../../node_modules/mdui.jq/es/methods/clone.js", "../../node_modules/mdui.jq/es/methods/is.js", "../../node_modules/mdui.jq/es/methods/remove.js", "../../node_modules/mdui.jq/es/methods/append.js", "../../node_modules/mdui.jq/es/methods/appendTo.js", "../../node_modules/mdui.jq/es/methods/attr.js", "../../node_modules/mdui.jq/es/methods/children.js", "../../node_modules/mdui.jq/es/methods/slice.js", "../../node_modules/mdui.jq/es/methods/eq.js", "../../node_modules/mdui.jq/es/methods/utils/dir.js", "../../node_modules/mdui.jq/es/methods/parent.js", "../../node_modules/mdui.jq/es/methods/closest.js", "../../node_modules/mdui.jq/es/methods/data.js", "../../node_modules/mdui.jq/es/methods/empty.js", "../../node_modules/mdui.jq/es/methods/extend.js", "../../node_modules/mdui.jq/es/methods/filter.js", "../../node_modules/mdui.jq/es/methods/first.js", "../../node_modules/mdui.jq/es/methods/has.js", "../../node_modules/mdui.jq/es/methods/hasClass.js", "../../node_modules/mdui.jq/es/methods/width.js", "../../node_modules/mdui.jq/es/methods/hide.js", "../../node_modules/mdui.jq/es/methods/val.js", "../../node_modules/mdui.jq/es/methods/index.js", "../../node_modules/mdui.jq/es/methods/last.js", "../../node_modules/mdui.jq/es/methods/next.js", "../../node_modules/mdui.jq/es/methods/not.js", "../../node_modules/mdui.jq/es/methods/offsetParent.js", "../../node_modules/mdui.jq/es/methods/position.js", "../../node_modules/mdui.jq/es/methods/offset.js", "../../node_modules/mdui.jq/es/methods/one.js", "../../node_modules/mdui.jq/es/methods/prev.js", "../../node_modules/mdui.jq/es/methods/removeAttr.js", "../../node_modules/mdui.jq/es/methods/removeData.js", "../../node_modules/mdui.jq/es/methods/removeProp.js", "../../node_modules/mdui.jq/es/methods/replaceWith.js", "../../node_modules/mdui.jq/es/methods/replaceAll.js", "../../node_modules/mdui.jq/es/methods/serializeArray.js", "../../node_modules/mdui.jq/es/methods/serialize.js", "../../node_modules/mdui.jq/es/methods/show.js", "../../node_modules/mdui.jq/es/methods/siblings.js", "../../node_modules/mdui.jq/es/methods/toggle.js", "../../src/jq_extends/methods/reflow.ts", "../../src/jq_extends/methods/transition.ts", "../../src/jq_extends/methods/transitionEnd.ts", "../../src/jq_extends/methods/transformOrigin.ts", "../../src/jq_extends/methods/transform.ts", "../../src/utils/mutation.ts", "../../src/jq_extends/methods/mutation.ts", "../../src/jq_extends/static/showOverlay.ts", "../../src/jq_extends/static/hideOverlay.ts", "../../src/jq_extends/static/lockScreen.ts", "../../src/jq_extends/static/unlockScreen.ts", "../../src/jq_extends/static/throttle.ts", "../../src/jq_extends/static/guid.ts", "../../src/global/mutation.ts", "../../src/utils/componentEvent.ts", "../../src/utils/dom.ts", "../../src/components/headroom/index.ts", "../../src/utils/parseOptions.ts", "../../src/components/headroom/customAttr.ts", "../../src/components/collapse/collapseAbstract.ts", "../../src/components/collapse/index.ts", "../../src/components/collapse/customAttr.ts", "../../src/components/panel/index.ts", "../../src/components/panel/customAttr.ts", "../../src/components/table/index.ts", "../../src/utils/touchHandler.ts", "../../src/components/ripple/index.ts", "../../src/components/textfield/index.ts", "../../src/components/slider/index.ts", "../../src/components/fab/index.ts", "../../src/components/fab/customAttr.ts", "../../src/components/select/index.ts", "../../src/components/select/customAttr.ts", "../../src/components/appbar/index.ts", "../../src/components/tab/index.ts", "../../src/components/tab/customAttr.ts", "../../src/components/drawer/index.ts", "../../src/components/drawer/customAttr.ts", "../../src/utils/queue.ts", "../../src/components/dialog/class.ts", "../../src/components/dialog/index.ts", "../../src/components/dialog/customAttr.ts", "../../src/components/dialog/dialog.ts", "../../src/components/dialog/alert.ts", "../../src/components/dialog/confirm.ts", "../../src/components/dialog/prompt.ts", "../../src/components/tooltip/index.ts", "../../src/components/tooltip/customAttr.ts", "../../src/components/snackbar/index.ts", "../../src/components/bottom_nav/index.ts", "../../src/components/progress/spinner.ts", "../../src/components/menu/index.ts", "../../src/components/menu/customAttr.ts"], "sourcesContent": ["function isNodeName(element, name) {\n    return element.nodeName.toLowerCase() === name.toLowerCase();\n}\nfunction isFunction(target) {\n    return typeof target === 'function';\n}\nfunction isString(target) {\n    return typeof target === 'string';\n}\nfunction isNumber(target) {\n    return typeof target === 'number';\n}\nfunction isBoolean(target) {\n    return typeof target === 'boolean';\n}\nfunction isUndefined(target) {\n    return typeof target === 'undefined';\n}\nfunction isNull(target) {\n    return target === null;\n}\nfunction isWindow(target) {\n    return target instanceof Window;\n}\nfunction isDocument(target) {\n    return target instanceof Document;\n}\nfunction isElement(target) {\n    return target instanceof Element;\n}\nfunction isNode(target) {\n    return target instanceof Node;\n}\n/**\n * 是否是 IE 浏览器\n */\nfunction isIE() {\n    // @ts-ignore\n    return !!window.document.documentMode;\n}\nfunction isArrayLike(target) {\n    if (isFunction(target) || isWindow(target)) {\n        return false;\n    }\n    return isNumber(target.length);\n}\nfunction isObjectLike(target) {\n    return typeof target === 'object' && target !== null;\n}\nfunction toElement(target) {\n    return isDocument(target) ? target.documentElement : target;\n}\n/**\n * 把用 - 分隔的字符串转为驼峰（如 box-sizing 转换为 boxSizing）\n * @param string\n */\nfunction toCamelCase(string) {\n    return string\n        .replace(/^-ms-/, 'ms-')\n        .replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());\n}\n/**\n * 把驼峰法转为用 - 分隔的字符串（如 boxSizing 转换为 box-sizing）\n * @param string\n */\nfunction toKebabCase(string) {\n    return string.replace(/[A-Z]/g, (replacer) => '-' + replacer.toLowerCase());\n}\n/**\n * 获取元素的样式值\n * @param element\n * @param name\n */\nfunction getComputedStyleValue(element, name) {\n    return window.getComputedStyle(element).getPropertyValue(toKebabCase(name));\n}\n/**\n * 检查元素的 box-sizing 是否是 border-box\n * @param element\n */\nfunction isBorderBox(element) {\n    return getComputedStyleValue(element, 'box-sizing') === 'border-box';\n}\n/**\n * 获取元素的 padding, border, margin 宽度（两侧宽度的和，单位为px）\n * @param element\n * @param direction\n * @param extra\n */\nfunction getExtraWidth(element, direction, extra) {\n    const position = direction === 'width' ? ['Left', 'Right'] : ['Top', 'Bottom'];\n    return [0, 1].reduce((prev, _, index) => {\n        let prop = extra + position[index];\n        if (extra === 'border') {\n            prop += 'Width';\n        }\n        return prev + parseFloat(getComputedStyleValue(element, prop) || '0');\n    }, 0);\n}\n/**\n * 获取元素的样式值，对 width 和 height 进行过处理\n * @param element\n * @param name\n */\nfunction getStyle(element, name) {\n    // width、height 属性使用 getComputedStyle 得到的值不准确，需要使用 getBoundingClientRect 获取\n    if (name === 'width' || name === 'height') {\n        const valueNumber = element.getBoundingClientRect()[name];\n        if (isBorderBox(element)) {\n            return `${valueNumber}px`;\n        }\n        return `${valueNumber -\n            getExtraWidth(element, name, 'border') -\n            getExtraWidth(element, name, 'padding')}px`;\n    }\n    return getComputedStyleValue(element, name);\n}\n/**\n * 获取子节点组成的数组\n * @param target\n * @param parent\n */\nfunction getChildNodesArray(target, parent) {\n    const tempParent = document.createElement(parent);\n    tempParent.innerHTML = target;\n    return [].slice.call(tempParent.childNodes);\n}\n/**\n * 始终返回 false 的函数\n */\nfunction returnFalse() {\n    return false;\n}\n/**\n * 数值单位的 CSS 属性\n */\nconst cssNumber = [\n    'animationIterationCount',\n    'columnCount',\n    'fillOpacity',\n    'flexGrow',\n    'flexShrink',\n    'fontWeight',\n    'gridArea',\n    'gridColumn',\n    'gridColumnEnd',\n    'gridColumnStart',\n    'gridRow',\n    'gridRowEnd',\n    'gridRowStart',\n    'lineHeight',\n    'opacity',\n    'order',\n    'orphans',\n    'widows',\n    'zIndex',\n    'zoom',\n];\nexport { isNodeName, isArrayLike, isObjectLike, isFunction, isString, isNumber, isBoolean, isUndefined, isNull, isWindow, isDocument, isElement, isNode, isIE, toElement, toCamelCase, toKebabCase, getComputedStyleValue, isBorderBox, getExtraWidth, getStyle, getChildNodesArray, returnFalse, cssNumber, };\n", "import { isArrayLike } from '../utils';\nfunction each(target, callback) {\n    if (isArrayLike(target)) {\n        for (let i = 0; i < target.length; i += 1) {\n            if (callback.call(target[i], i, target[i]) === false) {\n                return target;\n            }\n        }\n    }\n    else {\n        const keys = Object.keys(target);\n        for (let i = 0; i < keys.length; i += 1) {\n            if (callback.call(target[keys[i]], keys[i], target[keys[i]]) === false) {\n                return target;\n            }\n        }\n    }\n    return target;\n}\nexport default each;\n", "import each from './functions/each';\n/**\n * 为了使用模块扩充，这里不能使用默认导出\n */\nexport class JQ {\n    constructor(arr) {\n        this.length = 0;\n        if (!arr) {\n            return this;\n        }\n        each(arr, (i, item) => {\n            // @ts-ignore\n            this[i] = item;\n        });\n        this.length = arr.length;\n        return this;\n    }\n}\n", "import each from './functions/each';\nimport { JQ } from './JQ';\nimport { getChildNodesArray, isArrayLike, isFunction, isNode, isString, } from './utils';\nfunction get$() {\n    const $ = function (selector) {\n        if (!selector) {\n            return new JQ();\n        }\n        // JQ\n        if (selector instanceof JQ) {\n            return selector;\n        }\n        // function\n        if (isFunction(selector)) {\n            if (/complete|loaded|interactive/.test(document.readyState) &&\n                document.body) {\n                selector.call(document, $);\n            }\n            else {\n                document.addEventListener('DOMContentLoaded', () => selector.call(document, $), false);\n            }\n            return new JQ([document]);\n        }\n        // String\n        if (isString(selector)) {\n            const html = selector.trim();\n            // 根据 HTML 字符串创建 JQ 对象\n            if (html[0] === '<' && html[html.length - 1] === '>') {\n                let toCreate = 'div';\n                const tags = {\n                    li: 'ul',\n                    tr: 'tbody',\n                    td: 'tr',\n                    th: 'tr',\n                    tbody: 'table',\n                    option: 'select',\n                };\n                each(tags, (childTag, parentTag) => {\n                    if (html.indexOf(`<${childTag}`) === 0) {\n                        toCreate = parentTag;\n                        return false;\n                    }\n                    return;\n                });\n                return new JQ(getChildNodesArray(html, toCreate));\n            }\n            // 根据 CSS 选择器创建 JQ 对象\n            const isIdSelector = selector[0] === '#' && !selector.match(/[ .<>:~]/);\n            if (!isIdSelector) {\n                return new JQ(document.querySelectorAll(selector));\n            }\n            const element = document.getElementById(selector.slice(1));\n            if (element) {\n                return new JQ([element]);\n            }\n            return new JQ();\n        }\n        if (isArrayLike(selector) && !isNode(selector)) {\n            return new JQ(selector);\n        }\n        return new JQ([selector]);\n    };\n    $.fn = JQ.prototype;\n    return $;\n}\nconst $ = get$();\nexport default $;\n", "import { MduiStatic } from './interfaces/MduiStatic';\nimport $ from 'mdui.jq/es/$';\n\n// 避免页面加载完后直接执行css动画\n// https://css-tricks.com/transitions-only-after-page-load/\nsetTimeout(() => $('body').addClass('mdui-loaded'));\n\nconst mdui = {\n  $: $,\n} as MduiStatic;\n\nexport default mdui;\n", "import $ from '../$';\nimport each from '../functions/each';\n$.fn.each = function (callback) {\n    return each(this, callback);\n};\n", "import { toElement } from '../utils';\n/**\n * 检查 container 元素内是否包含 contains 元素\n * @param container 父元素\n * @param contains 子元素\n * @example\n```js\ncontains( document, document.body ); // true\ncontains( document.getElementById('test'), document ); // false\ncontains( $('.container').get(0), $('.contains').get(0) ); // false\n```\n */\nfunction contains(container, contains) {\n    return container !== contains && toElement(container).contains(contains);\n}\nexport default contains;\n", "import each from './each';\n/**\n * 把第二个数组的元素追加到第一个数组中，并返回合并后的数组\n * @param first 第一个数组\n * @param second 该数组的元素将被追加到第一个数组中\n * @example\n```js\nmerge( [ 0, 1, 2 ], [ 2, 3, 4 ] )\n// [ 0, 1, 2, 2, 3, 4 ]\n```\n */\nfunction merge(first, second) {\n    each(second, (_, value) => {\n        first.push(value);\n    });\n    return first;\n}\nexport default merge;\n", "import $ from '../$';\n$.fn.get = function (index) {\n    return index === undefined\n        ? [].slice.call(this)\n        : this[index >= 0 ? index : index + this.length];\n};\n", "import $ from '../$';\nimport merge from '../functions/merge';\nimport { JQ } from '../JQ';\nimport './each';\nimport './get';\n$.fn.find = function (selector) {\n    const foundElements = [];\n    this.each((_, element) => {\n        merge(foundElements, $(element.querySelectorAll(selector)).get());\n    });\n    return new JQ(foundElements);\n};\n", "import $ from '../../$';\nimport contains from '../../functions/contains';\nimport { isObjectLike } from '../../utils';\nimport '../find';\n// 存储事件\nconst handlers = {};\n// 元素ID\nlet mduiElementId = 1;\n/**\n * 为元素赋予一个唯一的ID\n */\nfunction getElementId(element) {\n    const key = '_mduiEventId';\n    // @ts-ignore\n    if (!element[key]) {\n        // @ts-ignore\n        element[key] = ++mduiElementId;\n    }\n    // @ts-ignore\n    return element[key];\n}\n/**\n * 解析事件名中的命名空间\n */\nfunction parse(type) {\n    const parts = type.split('.');\n    return {\n        type: parts[0],\n        ns: parts.slice(1).sort().join(' '),\n    };\n}\n/**\n * 命名空间匹配规则\n */\nfunction matcherFor(ns) {\n    return new RegExp('(?:^| )' + ns.replace(' ', ' .* ?') + '(?: |$)');\n}\n/**\n * 获取匹配的事件\n * @param element\n * @param type\n * @param func\n * @param selector\n */\nfunction getHandlers(element, type, func, selector) {\n    const event = parse(type);\n    return (handlers[getElementId(element)] || []).filter((handler) => handler &&\n        (!event.type || handler.type === event.type) &&\n        (!event.ns || matcherFor(event.ns).test(handler.ns)) &&\n        (!func || getElementId(handler.func) === getElementId(func)) &&\n        (!selector || handler.selector === selector));\n}\n/**\n * 添加事件监听\n * @param element\n * @param types\n * @param func\n * @param data\n * @param selector\n */\nfunction add(element, types, func, data, selector) {\n    const elementId = getElementId(element);\n    if (!handlers[elementId]) {\n        handlers[elementId] = [];\n    }\n    // 传入 data.useCapture 来设置 useCapture: true\n    let useCapture = false;\n    if (isObjectLike(data) && data.useCapture) {\n        useCapture = true;\n    }\n    types.split(' ').forEach((type) => {\n        if (!type) {\n            return;\n        }\n        const event = parse(type);\n        function callFn(e, elem) {\n            // 因为鼠标事件模拟事件的 detail 属性是只读的，因此在 e._detail 中存储参数\n            const result = func.apply(elem, \n            // @ts-ignore\n            e._detail === undefined ? [e] : [e].concat(e._detail));\n            if (result === false) {\n                e.preventDefault();\n                e.stopPropagation();\n            }\n        }\n        function proxyFn(e) {\n            // @ts-ignore\n            if (e._ns && !matcherFor(e._ns).test(event.ns)) {\n                return;\n            }\n            // @ts-ignore\n            e._data = data;\n            if (selector) {\n                // 事件代理\n                $(element)\n                    .find(selector)\n                    .get()\n                    .reverse()\n                    .forEach((elem) => {\n                    if (elem === e.target ||\n                        contains(elem, e.target)) {\n                        callFn(e, elem);\n                    }\n                });\n            }\n            else {\n                // 不使用事件代理\n                callFn(e, element);\n            }\n        }\n        const handler = {\n            type: event.type,\n            ns: event.ns,\n            func,\n            selector,\n            id: handlers[elementId].length,\n            proxy: proxyFn,\n        };\n        handlers[elementId].push(handler);\n        element.addEventListener(handler.type, proxyFn, useCapture);\n    });\n}\n/**\n * 移除事件监听\n * @param element\n * @param types\n * @param func\n * @param selector\n */\nfunction remove(element, types, func, selector) {\n    const handlersInElement = handlers[getElementId(element)] || [];\n    const removeEvent = (handler) => {\n        delete handlersInElement[handler.id];\n        element.removeEventListener(handler.type, handler.proxy, false);\n    };\n    if (!types) {\n        handlersInElement.forEach((handler) => removeEvent(handler));\n    }\n    else {\n        types.split(' ').forEach((type) => {\n            if (type) {\n                getHandlers(element, type, func, selector).forEach((handler) => removeEvent(handler));\n            }\n        });\n    }\n}\nexport { parse, add, remove };\n", "import $ from '../$';\nimport './each';\nimport { parse } from './utils/event';\n$.fn.trigger = function (type, extraParameters) {\n    const event = parse(type);\n    let eventObject;\n    const eventParams = {\n        bubbles: true,\n        cancelable: true,\n    };\n    const isMouseEvent = ['click', 'mousedown', 'mouseup', 'mousemove'].indexOf(event.type) > -1;\n    if (isMouseEvent) {\n        // Note: MouseEvent 无法传入 detail 参数\n        eventObject = new MouseEvent(event.type, eventParams);\n    }\n    else {\n        eventParams.detail = extraParameters;\n        eventObject = new CustomEvent(event.type, eventParams);\n    }\n    // @ts-ignore\n    eventObject._detail = extraParameters;\n    // @ts-ignore\n    eventObject._ns = event.ns;\n    return this.each(function () {\n        this.dispatchEvent(eventObject);\n    });\n};\n", "import each from '../functions/each';\nimport { isUndefined } from '../utils';\nfunction extend(target, object1, ...objectN) {\n    objectN.unshift(object1);\n    each(objectN, (_, object) => {\n        each(object, (prop, value) => {\n            if (!isUndefined(value)) {\n                target[prop] = value;\n            }\n        });\n    });\n    return target;\n}\nexport default extend;\n", "import { isObjectLike } from '../utils';\nimport each from './each';\n/**\n * 将数组或对象序列化，序列化后的字符串可作为 URL 查询字符串使用\n *\n * 若传入数组，则格式必须和 serializeArray 方法的返回值一样\n * @param obj 对象或数组\n * @example\n```js\nparam({ width: 1680, height: 1050 });\n// width=1680&height=1050\n```\n * @example\n```js\nparam({ foo: { one: 1, two: 2 }})\n// foo[one]=1&foo[two]=2\n```\n * @example\n```js\nparam({ids: [1, 2, 3]})\n// ids[]=1&ids[]=2&ids[]=3\n```\n * @example\n```js\nparam([\n  {\"name\":\"name\",\"value\":\"mdui\"},\n  {\"name\":\"password\",\"value\":\"123456\"}\n])\n// name=mdui&password=123456\n```\n */\nfunction param(obj) {\n    if (!isObjectLike(obj) && !Array.isArray(obj)) {\n        return '';\n    }\n    const args = [];\n    function destructure(key, value) {\n        let keyTmp;\n        if (isObjectLike(value)) {\n            each(value, (i, v) => {\n                if (Array.isArray(value) && !isObjectLike(v)) {\n                    keyTmp = '';\n                }\n                else {\n                    keyTmp = i;\n                }\n                destructure(`${key}[${keyTmp}]`, v);\n            });\n        }\n        else {\n            if (value == null || value === '') {\n                keyTmp = '=';\n            }\n            else {\n                keyTmp = `=${encodeURIComponent(value)}`;\n            }\n            args.push(encodeURIComponent(key) + keyTmp);\n        }\n    }\n    if (Array.isArray(obj)) {\n        each(obj, function () {\n            destructure(this.name, this.value);\n        });\n    }\n    else {\n        each(obj, destructure);\n    }\n    return args.join('&');\n}\nexport default param;\n", "// 全局配置参数\nconst globalOptions = {};\n// 全局事件名\nconst ajaxEvents = {\n    ajaxStart: 'start.mdui.ajax',\n    ajaxSuccess: 'success.mdui.ajax',\n    ajaxError: 'error.mdui.ajax',\n    ajaxComplete: 'complete.mdui.ajax',\n};\nexport { globalOptions, ajaxEvents };\n", "import $ from '../$';\nimport '../methods/trigger';\nimport { isString, isUndefined } from '../utils';\nimport each from './each';\nimport extend from './extend';\nimport param from './param';\nimport { ajaxEvents, globalOptions } from './utils/ajax';\n/**\n * 判断此请求方法是否通过查询字符串提交参数\n * @param method 请求方法，大写\n */\nfunction isQueryStringData(method) {\n    return ['GET', 'HEAD'].indexOf(method) >= 0;\n}\n/**\n * 添加参数到 URL 上，且 URL 中不存在 ? 时，自动把第一个 & 替换为 ?\n * @param url\n * @param query\n */\nfunction appendQuery(url, query) {\n    return `${url}&${query}`.replace(/[&?]{1,2}/, '?');\n}\n/**\n * 合并请求参数，参数优先级：options > globalOptions > defaults\n * @param options\n */\nfunction mergeOptions(options) {\n    // 默认参数\n    const defaults = {\n        url: '',\n        method: 'GET',\n        data: '',\n        processData: true,\n        async: true,\n        cache: true,\n        username: '',\n        password: '',\n        headers: {},\n        xhrFields: {},\n        statusCode: {},\n        dataType: 'text',\n        contentType: 'application/x-www-form-urlencoded',\n        timeout: 0,\n        global: true,\n    };\n    // globalOptions 中的回调函数不合并\n    each(globalOptions, (key, value) => {\n        const callbacks = [\n            'beforeSend',\n            'success',\n            'error',\n            'complete',\n            'statusCode',\n        ];\n        // @ts-ignore\n        if (callbacks.indexOf(key) < 0 && !isUndefined(value)) {\n            defaults[key] = value;\n        }\n    });\n    return extend({}, defaults, options);\n}\n/**\n * 发送 ajax 请求\n * @param options\n * @example\n```js\najax({\n  method: \"POST\",\n  url: \"some.php\",\n  data: { name: \"John\", location: \"Boston\" }\n}).then(function( msg ) {\n  alert( \"Data Saved: \" + msg );\n});\n```\n */\nfunction ajax(options) {\n    // 是否已取消请求\n    let isCanceled = false;\n    // 事件参数\n    const eventParams = {};\n    // 参数合并\n    const mergedOptions = mergeOptions(options);\n    let url = mergedOptions.url || window.location.toString();\n    const method = mergedOptions.method.toUpperCase();\n    let data = mergedOptions.data;\n    const processData = mergedOptions.processData;\n    const async = mergedOptions.async;\n    const cache = mergedOptions.cache;\n    const username = mergedOptions.username;\n    const password = mergedOptions.password;\n    const headers = mergedOptions.headers;\n    const xhrFields = mergedOptions.xhrFields;\n    const statusCode = mergedOptions.statusCode;\n    const dataType = mergedOptions.dataType;\n    const contentType = mergedOptions.contentType;\n    const timeout = mergedOptions.timeout;\n    const global = mergedOptions.global;\n    // 需要发送的数据\n    // GET/HEAD 请求和 processData 为 true 时，转换为查询字符串格式，特殊格式不转换\n    if (data &&\n        (isQueryStringData(method) || processData) &&\n        !isString(data) &&\n        !(data instanceof ArrayBuffer) &&\n        !(data instanceof Blob) &&\n        !(data instanceof Document) &&\n        !(data instanceof FormData)) {\n        data = param(data);\n    }\n    // 对于 GET、HEAD 类型的请求，把 data 数据添加到 URL 中\n    if (data && isQueryStringData(method)) {\n        // 查询字符串拼接到 URL 中\n        url = appendQuery(url, data);\n        data = null;\n    }\n    /**\n     * 触发事件和回调函数\n     * @param event\n     * @param params\n     * @param callback\n     * @param args\n     */\n    function trigger(event, params, callback, ...args) {\n        // 触发全局事件\n        if (global) {\n            $(document).trigger(event, params);\n        }\n        // 触发 ajax 回调和事件\n        let result1;\n        let result2;\n        if (callback) {\n            // 全局回调\n            if (callback in globalOptions) {\n                // @ts-ignore\n                result1 = globalOptions[callback](...args);\n            }\n            // 自定义回调\n            if (mergedOptions[callback]) {\n                // @ts-ignore\n                result2 = mergedOptions[callback](...args);\n            }\n            // beforeSend 回调返回 false 时取消 ajax 请求\n            if (callback === 'beforeSend' &&\n                (result1 === false || result2 === false)) {\n                isCanceled = true;\n            }\n        }\n    }\n    // XMLHttpRequest 请求\n    function XHR() {\n        let textStatus;\n        return new Promise((resolve, reject) => {\n            // GET/HEAD 请求的缓存处理\n            if (isQueryStringData(method) && !cache) {\n                url = appendQuery(url, `_=${Date.now()}`);\n            }\n            // 创建 XHR\n            const xhr = new XMLHttpRequest();\n            xhr.open(method, url, async, username, password);\n            if (contentType ||\n                (data && !isQueryStringData(method) && contentType !== false)) {\n                xhr.setRequestHeader('Content-Type', contentType);\n            }\n            // 设置 Accept\n            if (dataType === 'json') {\n                xhr.setRequestHeader('Accept', 'application/json, text/javascript');\n            }\n            // 添加 headers\n            if (headers) {\n                each(headers, (key, value) => {\n                    // undefined 值不发送，string 和 null 需要发送\n                    if (!isUndefined(value)) {\n                        xhr.setRequestHeader(key, value + ''); // 把 null 转换成字符串\n                    }\n                });\n            }\n            // 检查是否是跨域请求，跨域请求时不添加 X-Requested-With\n            const crossDomain = /^([\\w-]+:)?\\/\\/([^/]+)/.test(url) &&\n                RegExp.$2 !== window.location.host;\n            if (!crossDomain) {\n                xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');\n            }\n            if (xhrFields) {\n                each(xhrFields, (key, value) => {\n                    // @ts-ignore\n                    xhr[key] = value;\n                });\n            }\n            eventParams.xhr = xhr;\n            eventParams.options = mergedOptions;\n            let xhrTimeout;\n            xhr.onload = function () {\n                if (xhrTimeout) {\n                    clearTimeout(xhrTimeout);\n                }\n                // AJAX 返回的 HTTP 响应码是否表示成功\n                const isHttpStatusSuccess = (xhr.status >= 200 && xhr.status < 300) ||\n                    xhr.status === 304 ||\n                    xhr.status === 0;\n                let responseData;\n                if (isHttpStatusSuccess) {\n                    if (xhr.status === 204 || method === 'HEAD') {\n                        textStatus = 'nocontent';\n                    }\n                    else if (xhr.status === 304) {\n                        textStatus = 'notmodified';\n                    }\n                    else {\n                        textStatus = 'success';\n                    }\n                    if (dataType === 'json') {\n                        try {\n                            responseData =\n                                method === 'HEAD' ? undefined : JSON.parse(xhr.responseText);\n                            eventParams.data = responseData;\n                        }\n                        catch (err) {\n                            textStatus = 'parsererror';\n                            trigger(ajaxEvents.ajaxError, eventParams, 'error', xhr, textStatus);\n                            reject(new Error(textStatus));\n                        }\n                        if (textStatus !== 'parsererror') {\n                            trigger(ajaxEvents.ajaxSuccess, eventParams, 'success', responseData, textStatus, xhr);\n                            resolve(responseData);\n                        }\n                    }\n                    else {\n                        responseData =\n                            method === 'HEAD'\n                                ? undefined\n                                : xhr.responseType === 'text' || xhr.responseType === ''\n                                    ? xhr.responseText\n                                    : xhr.response;\n                        eventParams.data = responseData;\n                        trigger(ajaxEvents.ajaxSuccess, eventParams, 'success', responseData, textStatus, xhr);\n                        resolve(responseData);\n                    }\n                }\n                else {\n                    textStatus = 'error';\n                    trigger(ajaxEvents.ajaxError, eventParams, 'error', xhr, textStatus);\n                    reject(new Error(textStatus));\n                }\n                // statusCode\n                each([globalOptions.statusCode, statusCode], (_, func) => {\n                    if (func && func[xhr.status]) {\n                        if (isHttpStatusSuccess) {\n                            func[xhr.status](responseData, textStatus, xhr);\n                        }\n                        else {\n                            func[xhr.status](xhr, textStatus);\n                        }\n                    }\n                });\n                trigger(ajaxEvents.ajaxComplete, eventParams, 'complete', xhr, textStatus);\n            };\n            xhr.onerror = function () {\n                if (xhrTimeout) {\n                    clearTimeout(xhrTimeout);\n                }\n                trigger(ajaxEvents.ajaxError, eventParams, 'error', xhr, xhr.statusText);\n                trigger(ajaxEvents.ajaxComplete, eventParams, 'complete', xhr, 'error');\n                reject(new Error(xhr.statusText));\n            };\n            xhr.onabort = function () {\n                let statusText = 'abort';\n                if (xhrTimeout) {\n                    statusText = 'timeout';\n                    clearTimeout(xhrTimeout);\n                }\n                trigger(ajaxEvents.ajaxError, eventParams, 'error', xhr, statusText);\n                trigger(ajaxEvents.ajaxComplete, eventParams, 'complete', xhr, statusText);\n                reject(new Error(statusText));\n            };\n            // ajax start 回调\n            trigger(ajaxEvents.ajaxStart, eventParams, 'beforeSend', xhr);\n            if (isCanceled) {\n                reject(new Error('cancel'));\n                return;\n            }\n            // Timeout\n            if (timeout > 0) {\n                xhrTimeout = setTimeout(() => {\n                    xhr.abort();\n                }, timeout);\n            }\n            // 发送 XHR\n            xhr.send(data);\n        });\n    }\n    return XHR();\n}\nexport default ajax;\n", "import $ from '../$';\nimport ajax from '../functions/ajax';\n$.ajax = ajax;\n", "import extend from '../functions/extend';\nimport { globalOptions } from './utils/ajax';\n/**\n * 为 Ajax 请求设置全局配置参数\n * @param options 键值对参数\n * @example\n```js\najaxSetup({\n  dataType: 'json',\n  method: 'POST',\n});\n```\n */\nfunction ajaxSetup(options) {\n    return extend(globalOptions, options);\n}\nexport default ajaxSetup;\n", "import $ from '../$';\nimport ajaxSetup from '../functions/ajaxSetup';\n$.ajaxSetup = ajaxSetup;\n", "import $ from '../$';\nimport contains from '../functions/contains';\n$.contains = contains;\n", "const dataNS = '_mduiElementDataStorage';\nexport default dataNS;\n", "import { isObjectLike, isUndefined, toCamelCase } from '../utils';\nimport each from './each';\nimport dataNS from './utils/data';\n/**\n * 在元素上设置键值对数据\n * @param element\n * @param object\n */\nfunction setObjectToElement(element, object) {\n    // @ts-ignore\n    if (!element[dataNS]) {\n        // @ts-ignore\n        element[dataNS] = {};\n    }\n    each(object, (key, value) => {\n        // @ts-ignore\n        element[dataNS][toCamelCase(key)] = value;\n    });\n}\nfunction data(element, key, value) {\n    // 根据键值对设置值\n    // data(element, { 'key' : 'value' })\n    if (isObjectLike(key)) {\n        setObjectToElement(element, key);\n        return key;\n    }\n    // 根据 key、value 设置值\n    // data(element, 'key', 'value')\n    if (!isUndefined(value)) {\n        setObjectToElement(element, { [key]: value });\n        return value;\n    }\n    // 获取所有值\n    // data(element)\n    if (isUndefined(key)) {\n        // @ts-ignore\n        return element[dataNS] ? element[dataNS] : {};\n    }\n    // 从 dataNS 中获取指定值\n    // data(element, 'key')\n    key = toCamelCase(key);\n    // @ts-ignore\n    if (element[dataNS] && key in element[dataNS]) {\n        // @ts-ignore\n        return element[dataNS][key];\n    }\n    return undefined;\n}\nexport default data;\n", "import $ from '../$';\nimport data from '../functions/data';\n$.data = data;\n", "import $ from '../$';\nimport each from '../functions/each';\n$.each = each;\n", "import $ from '../$';\nimport each from '../functions/each';\nimport extend from '../functions/extend';\n$.extend = function (...objectN) {\n    if (objectN.length === 1) {\n        each(objectN[0], (prop, value) => {\n            this[prop] = value;\n        });\n        return this;\n    }\n    return extend(objectN.shift(), objectN.shift(), ...objectN);\n};\n", "import each from './each';\nfunction map(elements, callback) {\n    let value;\n    const ret = [];\n    each(elements, (i, element) => {\n        value = callback.call(window, element, i);\n        if (value != null) {\n            ret.push(value);\n        }\n    });\n    return [].concat(...ret);\n}\nexport default map;\n", "import $ from '../$';\nimport map from '../functions/map';\n$.map = map;\n", "import $ from '../$';\nimport merge from '../functions/merge';\n$.merge = merge;\n", "import $ from '../$';\nimport param from '../functions/param';\n$.param = param;\n", "import each from '../functions/each';\nimport { isUndefined, isString, toCamelCase } from '../utils';\nimport dataNS from './utils/data';\n/**\n * 移除指定元素上存放的数据\n * @param element 存放数据的元素\n * @param name\n * 数据键名\n *\n * 若未指定键名，将移除元素上所有数据\n *\n * 多个键名可以用空格分隔，或者用数组表示多个键名\n  @example\n```js\n// 移除元素上键名为 name 的数据\nremoveData(document.body, 'name');\n```\n * @example\n```js\n// 移除元素上键名为 name1 和 name2 的数据\nremoveData(document.body, 'name1 name2');\n```\n * @example\n```js\n// 移除元素上键名为 name1 和 name2 的数据\nremoveData(document.body, ['name1', 'name2']);\n```\n * @example\n```js\n// 移除元素上所有数据\nremoveData(document.body);\n```\n */\nfunction removeData(element, name) {\n    // @ts-ignore\n    if (!element[dataNS]) {\n        return;\n    }\n    const remove = (nameItem) => {\n        nameItem = toCamelCase(nameItem);\n        // @ts-ignore\n        if (element[dataNS][nameItem]) {\n            // @ts-ignore\n            element[dataNS][nameItem] = null;\n            // @ts-ignore\n            delete element[dataNS][nameItem];\n        }\n    };\n    if (isUndefined(name)) {\n        // @ts-ignore\n        element[dataNS] = null;\n        // @ts-ignore\n        delete element[dataNS];\n        // @ts-ignore\n    }\n    else if (isString(name)) {\n        name\n            .split(' ')\n            .filter((nameItem) => nameItem)\n            .forEach((nameItem) => remove(nameItem));\n    }\n    else {\n        each(name, (_, nameItem) => remove(nameItem));\n    }\n}\nexport default removeData;\n", "import $ from '../$';\nimport removeData from '../functions/removeData';\n$.removeData = removeData;\n", "import each from './each';\n/**\n * 过滤掉数组中的重复元素\n * @param arr 数组\n * @example\n```js\nunique([1, 2, 12, 3, 2, 1, 2, 1, 1]);\n// [1, 2, 12, 3]\n```\n */\nfunction unique(arr) {\n    const result = [];\n    each(arr, (_, val) => {\n        if (result.indexOf(val) === -1) {\n            result.push(val);\n        }\n    });\n    return result;\n}\nexport default unique;\n", "import $ from '../$';\nimport unique from '../functions/unique';\n$.unique = unique;\n", "import $ from '../$';\nimport merge from '../functions/merge';\nimport unique from '../functions/unique';\nimport { JQ } from '../JQ';\nimport './get';\n$.fn.add = function (selector) {\n    return new JQ(unique(merge(this.get(), $(selector).get())));\n};\n", "import $ from '../$';\nimport each from '../functions/each';\nimport { isElement, isFunction } from '../utils';\nimport './each';\neach(['add', 'remove', 'toggle'], (_, name) => {\n    $.fn[`${name}Class`] = function (className) {\n        if (name === 'remove' && !arguments.length) {\n            return this.each((_, element) => {\n                element.setAttribute('class', '');\n            });\n        }\n        return this.each((i, element) => {\n            if (!isElement(element)) {\n                return;\n            }\n            const classes = (isFunction(className)\n                ? className.call(element, i, element.getAttribute('class') || '')\n                : className)\n                .split(' ')\n                .filter((name) => name);\n            each(classes, (_, cls) => {\n                element.classList[name](cls);\n            });\n        });\n    };\n});\n", "import $ from '../$';\nimport each from '../functions/each';\nimport './each';\neach(['insertBefore', 'insertAfter'], (nameIndex, name) => {\n    $.fn[name] = function (target) {\n        const $element = nameIndex ? $(this.get().reverse()) : this; // 顺序和 jQuery 保持一致\n        const $target = $(target);\n        const result = [];\n        $target.each((index, target) => {\n            if (!target.parentNode) {\n                return;\n            }\n            $element.each((_, element) => {\n                const newItem = index\n                    ? element.cloneNode(true)\n                    : element;\n                const existingItem = nameIndex ? target.nextSibling : target;\n                result.push(newItem);\n                target.parentNode.insertBefore(newItem, existingItem);\n            });\n        });\n        return $(nameIndex ? result.reverse() : result);\n    };\n});\n", "import $ from '../$';\nimport each from '../functions/each';\nimport { getChildNodesArray, isFunction, isString, isElement } from '../utils';\nimport './each';\nimport './insertAfter';\nimport './insertBefore';\n/**\n * 是否不是 HTML 字符串（包裹在 <> 中）\n * @param target\n */\nfunction isPlainText(target) {\n    return (isString(target) && (target[0] !== '<' || target[target.length - 1] !== '>'));\n}\neach(['before', 'after'], (nameIndex, name) => {\n    $.fn[name] = function (...args) {\n        // after 方法，多个参数需要按参数顺序添加到元素后面，所以需要将参数顺序反向处理\n        if (nameIndex === 1) {\n            args = args.reverse();\n        }\n        return this.each((index, element) => {\n            const targets = isFunction(args[0])\n                ? [args[0].call(element, index, element.innerHTML)]\n                : args;\n            each(targets, (_, target) => {\n                let $target;\n                if (isPlainText(target)) {\n                    $target = $(getChildNodesArray(target, 'div'));\n                }\n                else if (index && isElement(target)) {\n                    $target = $(target.cloneNode(true));\n                }\n                else {\n                    $target = $(target);\n                }\n                $target[nameIndex ? 'insertAfter' : 'insertBefore'](element);\n            });\n        });\n    };\n});\n", "import $ from '../$';\nimport each from '../functions/each';\nimport { isFunction, isObjectLike, returnFalse } from '../utils';\nimport './each';\nimport { remove } from './utils/event';\n$.fn.off = function (types, selector, callback) {\n    // types 是对象\n    if (isObjectLike(types)) {\n        each(types, (type, fn) => {\n            // this.off('click', undefined, function () {})\n            // this.off('click', '.box', function () {})\n            this.off(type, selector, fn);\n        });\n        return this;\n    }\n    // selector 不存在\n    if (selector === false || isFunction(selector)) {\n        callback = selector;\n        selector = undefined;\n        // this.off('click', undefined, function () {})\n    }\n    // callback 传入 `false`，相当于 `return false`\n    if (callback === false) {\n        callback = returnFalse;\n    }\n    return this.each(function () {\n        remove(this, types, callback, selector);\n    });\n};\n", "import $ from '../$';\nimport each from '../functions/each';\nimport { isObjectLike, isString, returnFalse } from '../utils';\nimport './each';\nimport './off';\nimport { add } from './utils/event';\n$.fn.on = function (types, selector, data, callback, one) {\n    // types 可以是 type/func 对象\n    if (isObjectLike(types)) {\n        // (types-Object, selector, data)\n        if (!isString(selector)) {\n            // (types-Object, data)\n            data = data || selector;\n            selector = undefined;\n        }\n        each(types, (type, fn) => {\n            // selector 和 data 都可能是 undefined\n            // @ts-ignore\n            this.on(type, selector, data, fn, one);\n        });\n        return this;\n    }\n    if (data == null && callback == null) {\n        // (types, fn)\n        callback = selector;\n        data = selector = undefined;\n    }\n    else if (callback == null) {\n        if (isString(selector)) {\n            // (types, selector, fn)\n            callback = data;\n            data = undefined;\n        }\n        else {\n            // (types, data, fn)\n            callback = data;\n            data = selector;\n            selector = undefined;\n        }\n    }\n    if (callback === false) {\n        callback = returnFalse;\n    }\n    else if (!callback) {\n        return this;\n    }\n    // $().one()\n    if (one) {\n        // eslint-disable-next-line @typescript-eslint/no-this-alias\n        const _this = this;\n        const origCallback = callback;\n        callback = function (event) {\n            _this.off(event.type, selector, callback);\n            // eslint-disable-next-line prefer-rest-params\n            return origCallback.apply(this, arguments);\n        };\n    }\n    return this.each(function () {\n        add(this, types, callback, data, selector);\n    });\n};\n", "import $ from '../$';\nimport each from '../functions/each';\nimport { ajaxEvents } from '../functions/utils/ajax';\nimport './on';\neach(ajaxEvents, (name, eventName) => {\n    $.fn[name] = function (fn) {\n        return this.on(eventName, (e, params) => {\n            fn(e, params.xhr, params.options, params.data);\n        });\n    };\n});\n", "import $ from '../$';\nimport map from '../functions/map';\nimport { JQ } from '../JQ';\n$.fn.map = function (callback) {\n    return new JQ(map(this, (element, i) => callback.call(element, i, element)));\n};\n", "import $ from '../$';\nimport './map';\n$.fn.clone = function () {\n    return this.map(function () {\n        return this.cloneNode(true);\n    });\n};\n", "import $ from '../$';\nimport { isDocument, isFunction, isString, isWindow } from '../utils';\nimport './each';\n$.fn.is = function (selector) {\n    let isMatched = false;\n    if (isFunction(selector)) {\n        this.each((index, element) => {\n            if (selector.call(element, index, element)) {\n                isMatched = true;\n            }\n        });\n        return isMatched;\n    }\n    if (isString(selector)) {\n        this.each((_, element) => {\n            if (isDocument(element) || isWindow(element)) {\n                return;\n            }\n            // @ts-ignore\n            const matches = element.matches || element.msMatchesSelector;\n            if (matches.call(element, selector)) {\n                isMatched = true;\n            }\n        });\n        return isMatched;\n    }\n    const $compareWith = $(selector);\n    this.each((_, element) => {\n        $compareWith.each((_, compare) => {\n            if (element === compare) {\n                isMatched = true;\n            }\n        });\n    });\n    return isMatched;\n};\n", "import $ from '../$';\nimport './each';\nimport './is';\n$.fn.remove = function (selector) {\n    return this.each((_, element) => {\n        if (element.parentNode && (!selector || $(element).is(selector))) {\n            element.parentNode.removeChild(element);\n        }\n    });\n};\n", "import $ from '../$';\nimport each from '../functions/each';\nimport { isFunction, isString } from '../utils';\nimport './after';\nimport './before';\nimport './clone';\nimport './each';\nimport './map';\nimport './remove';\neach(['prepend', 'append'], (nameIndex, name) => {\n    $.fn[name] = function (...args) {\n        return this.each((index, element) => {\n            const childNodes = element.childNodes;\n            const childLength = childNodes.length;\n            const child = childLength\n                ? childNodes[nameIndex ? childLength - 1 : 0]\n                : document.createElement('div');\n            if (!childLength) {\n                element.appendChild(child);\n            }\n            let contents = isFunction(args[0])\n                ? [args[0].call(element, index, element.innerHTML)]\n                : args;\n            // 如果不是字符串，则仅第一个元素使用原始元素，其他的都克隆自第一个元素\n            if (index) {\n                contents = contents.map((content) => {\n                    return isString(content) ? content : $(content).clone();\n                });\n            }\n            $(child)[nameIndex ? 'after' : 'before'](...contents);\n            if (!childLength) {\n                element.removeChild(child);\n            }\n        });\n    };\n});\n", "import $ from '../$';\nimport each from '../functions/each';\nimport './insertAfter';\nimport './insertBefore';\nimport './map';\nimport './remove';\neach(['appendTo', 'prependTo'], (nameIndex, name) => {\n    $.fn[name] = function (target) {\n        const extraChilds = [];\n        const $target = $(target).map((_, element) => {\n            const childNodes = element.childNodes;\n            const childLength = childNodes.length;\n            if (childLength) {\n                return childNodes[nameIndex ? 0 : childLength - 1];\n            }\n            const child = document.createElement('div');\n            element.appendChild(child);\n            extraChilds.push(child);\n            return child;\n        });\n        const $result = this[nameIndex ? 'insertBefore' : 'insertAfter']($target);\n        $(extraChilds).remove();\n        return $result;\n    };\n});\n", "import $ from '../$';\nimport each from '../functions/each';\nimport { cssNumber, getStyle, isElement, isFunction, isNull, isNumber, isObjectLike, isUndefined, toCamelCase, } from '../utils';\nimport './each';\neach(['attr', 'prop', 'css'], (nameIndex, name) => {\n    function set(element, key, value) {\n        // 值为 undefined 时，不修改\n        if (isUndefined(value)) {\n            return;\n        }\n        switch (nameIndex) {\n            // attr\n            case 0:\n                if (isNull(value)) {\n                    element.removeAttribute(key);\n                }\n                else {\n                    element.setAttribute(key, value);\n                }\n                break;\n            // prop\n            case 1:\n                // @ts-ignore\n                element[key] = value;\n                break;\n            // css\n            default:\n                key = toCamelCase(key);\n                // @ts-ignore\n                element.style[key] = isNumber(value)\n                    ? `${value}${cssNumber.indexOf(key) > -1 ? '' : 'px'}`\n                    : value;\n                break;\n        }\n    }\n    function get(element, key) {\n        switch (nameIndex) {\n            // attr\n            case 0:\n                // 属性不存在时，原生 getAttribute 方法返回 null，而 jquery 返回 undefined。这里和 jquery 保持一致\n                const value = element.getAttribute(key);\n                return isNull(value) ? undefined : value;\n            // prop\n            case 1:\n                // @ts-ignore\n                return element[key];\n            // css\n            default:\n                return getStyle(element, key);\n        }\n    }\n    $.fn[name] = function (key, value) {\n        if (isObjectLike(key)) {\n            each(key, (k, v) => {\n                // @ts-ignore\n                this[name](k, v);\n            });\n            return this;\n        }\n        if (arguments.length === 1) {\n            const element = this[0];\n            return isElement(element) ? get(element, key) : undefined;\n        }\n        return this.each((i, element) => {\n            set(element, key, isFunction(value) ? value.call(element, i, get(element, key)) : value);\n        });\n    };\n});\n", "import $ from '../$';\nimport each from '../functions/each';\nimport unique from '../functions/unique';\nimport { JQ } from '../JQ';\nimport { isElement } from '../utils';\nimport './each';\nimport './is';\n$.fn.children = function (selector) {\n    const children = [];\n    this.each((_, element) => {\n        each(element.childNodes, (__, childNode) => {\n            if (!isElement(childNode)) {\n                return;\n            }\n            if (!selector || $(childNode).is(selector)) {\n                children.push(childNode);\n            }\n        });\n    });\n    return new JQ(unique(children));\n};\n", "import $ from '../$';\nimport { JQ } from '../JQ';\n$.fn.slice = function (...args) {\n    return new JQ([].slice.apply(this, args));\n};\n", "import $ from '../$';\nimport { JQ } from '../JQ';\nimport './slice';\n$.fn.eq = function (index) {\n    const ret = index === -1 ? this.slice(index) : this.slice(index, +index + 1);\n    return new JQ(ret);\n};\n", "import $ from '../../$';\nimport unique from '../../functions/unique';\nimport { JQ } from '../../JQ';\nimport { isElement } from '../../utils';\nimport '../each';\nimport '../is';\nexport default function dir($elements, nameIndex, node, selector, filter) {\n    const ret = [];\n    let target;\n    $elements.each((_, element) => {\n        target = element[node];\n        // 不能包含最顶层的 document 元素\n        while (target && isElement(target)) {\n            // prevUntil, nextUntil, parentsUntil\n            if (nameIndex === 2) {\n                if (selector && $(target).is(selector)) {\n                    break;\n                }\n                if (!filter || $(target).is(filter)) {\n                    ret.push(target);\n                }\n            }\n            // prev, next, parent\n            else if (nameIndex === 0) {\n                if (!selector || $(target).is(selector)) {\n                    ret.push(target);\n                }\n                break;\n            }\n            // prevAll, nextAll, parents\n            else {\n                if (!selector || $(target).is(selector)) {\n                    ret.push(target);\n                }\n            }\n            // @ts-ignore\n            target = target[node];\n        }\n    });\n    return new JQ(unique(ret));\n}\n", "import $ from '../$';\nimport each from '../functions/each';\nimport './get';\nimport dir from './utils/dir';\neach(['', 's', 'sUntil'], (nameIndex, name) => {\n    $.fn[`parent${name}`] = function (selector, filter) {\n        // parents、parentsUntil 需要把元素的顺序反向处理，以便和 jQuery 的结果一致\n        const $nodes = !nameIndex ? this : $(this.get().reverse());\n        return dir($nodes, nameIndex, 'parentNode', selector, filter);\n    };\n});\n", "import $ from '../$';\nimport { JQ } from '../JQ';\nimport './eq';\nimport './is';\nimport './parents';\n$.fn.closest = function (selector) {\n    if (this.is(selector)) {\n        return this;\n    }\n    const matched = [];\n    this.parents().each((_, element) => {\n        if ($(element).is(selector)) {\n            matched.push(element);\n            return false;\n        }\n    });\n    return new JQ(matched);\n};\n", "import $ from '../$';\nimport data from '../functions/data';\nimport { isObjectLike, isString, isUndefined, toCamelCase, toKebabCase, } from '../utils';\nimport './each';\nconst rbrace = /^(?:{[\\w\\W]*\\}|\\[[\\w\\W]*\\])$/;\n// 从 `data-*` 中获取的值，需要经过该函数转换\nfunction getData(value) {\n    if (value === 'true') {\n        return true;\n    }\n    if (value === 'false') {\n        return false;\n    }\n    if (value === 'null') {\n        return null;\n    }\n    if (value === +value + '') {\n        return +value;\n    }\n    if (rbrace.test(value)) {\n        return JSON.parse(value);\n    }\n    return value;\n}\n// 若 value 不存在，则从 `data-*` 中获取值\nfunction dataAttr(element, key, value) {\n    if (isUndefined(value) && element.nodeType === 1) {\n        const name = 'data-' + toKebabCase(key);\n        value = element.getAttribute(name);\n        if (isString(value)) {\n            try {\n                value = getData(value);\n            }\n            catch (e) { }\n        }\n        else {\n            value = undefined;\n        }\n    }\n    return value;\n}\n$.fn.data = function (key, value) {\n    // 获取所有值\n    if (isUndefined(key)) {\n        if (!this.length) {\n            return undefined;\n        }\n        const element = this[0];\n        const resultData = data(element);\n        // window, document 上不存在 `data-*` 属性\n        if (element.nodeType !== 1) {\n            return resultData;\n        }\n        // 从 `data-*` 中获取值\n        const attrs = element.attributes;\n        let i = attrs.length;\n        while (i--) {\n            if (attrs[i]) {\n                let name = attrs[i].name;\n                if (name.indexOf('data-') === 0) {\n                    name = toCamelCase(name.slice(5));\n                    resultData[name] = dataAttr(element, name, resultData[name]);\n                }\n            }\n        }\n        return resultData;\n    }\n    // 同时设置多个值\n    if (isObjectLike(key)) {\n        return this.each(function () {\n            data(this, key);\n        });\n    }\n    // value 传入了 undefined\n    if (arguments.length === 2 && isUndefined(value)) {\n        return this;\n    }\n    // 设置值\n    if (!isUndefined(value)) {\n        return this.each(function () {\n            data(this, key, value);\n        });\n    }\n    // 获取值\n    if (!this.length) {\n        return undefined;\n    }\n    return dataAttr(this[0], key, data(this[0], key));\n};\n", "import $ from '../$';\nimport './each';\n$.fn.empty = function () {\n    return this.each(function () {\n        this.innerHTML = '';\n    });\n};\n", "import $ from '../$';\nimport each from '../functions/each';\n$.fn.extend = function (obj) {\n    each(obj, (prop, value) => {\n        // 在 JQ 对象上扩展方法时，需要自己添加 typescript 的类型定义\n        $.fn[prop] = value;\n    });\n    return this;\n};\n", "import $ from '../$';\nimport { isFunction, isString } from '../utils';\nimport './is';\nimport './map';\n$.fn.filter = function (selector) {\n    if (isFunction(selector)) {\n        return this.map((index, element) => selector.call(element, index, element) ? element : undefined);\n    }\n    if (isString(selector)) {\n        return this.map((_, element) => $(element).is(selector) ? element : undefined);\n    }\n    const $selector = $(selector);\n    return this.map((_, element) => $selector.get().indexOf(element) > -1 ? element : undefined);\n};\n", "import $ from '../$';\nimport './eq';\n$.fn.first = function () {\n    return this.eq(0);\n};\n", "import $ from '../$';\nimport contains from '../functions/contains';\nimport { isString } from '../utils';\nimport './find';\n$.fn.has = function (selector) {\n    const $targets = isString(selector) ? this.find(selector) : $(selector);\n    const { length } = $targets;\n    return this.map(function () {\n        for (let i = 0; i < length; i += 1) {\n            if (contains(this, $targets[i])) {\n                return this;\n            }\n        }\n        return;\n    });\n};\n", "import $ from '../$';\n$.fn.hasClass = function (className) {\n    return this[0].classList.contains(className);\n};\n", "import $ from '../$';\nimport each from '../functions/each';\nimport { isBoolean, isDocument, isFunction, isWindow, toElement, isBorderBox, getExtraWidth, getComputedStyleValue, isIE, } from '../utils';\nimport './css';\nimport './each';\n/**\n * 值上面的 padding、border、margin 处理\n * @param element\n * @param name\n * @param value\n * @param funcIndex\n * @param includeMargin\n * @param multiply\n */\nfunction handleExtraWidth(element, name, value, funcIndex, includeMargin, multiply) {\n    // 获取元素的 padding, border, margin 宽度（两侧宽度的和）\n    const getExtraWidthValue = (extra) => {\n        return (getExtraWidth(element, name.toLowerCase(), extra) *\n            multiply);\n    };\n    if (funcIndex === 2 && includeMargin) {\n        value += getExtraWidthValue('margin');\n    }\n    if (isBorderBox(element)) {\n        // IE 为 box-sizing: border-box 时，得到的值不含 border 和 padding，这里先修复\n        // 仅获取时需要处理，multiply === 1 为 get\n        if (isIE() && multiply === 1) {\n            value += getExtraWidthValue('border');\n            value += getExtraWidthValue('padding');\n        }\n        if (funcIndex === 0) {\n            value -= getExtraWidthValue('border');\n        }\n        if (funcIndex === 1) {\n            value -= getExtraWidthValue('border');\n            value -= getExtraWidthValue('padding');\n        }\n    }\n    else {\n        if (funcIndex === 0) {\n            value += getExtraWidthValue('padding');\n        }\n        if (funcIndex === 2) {\n            value += getExtraWidthValue('border');\n            value += getExtraWidthValue('padding');\n        }\n    }\n    return value;\n}\n/**\n * 获取元素的样式值\n * @param element\n * @param name\n * @param funcIndex 0: innerWidth, innerHeight; 1: width, height; 2: outerWidth, outerHeight\n * @param includeMargin\n */\nfunction get(element, name, funcIndex, includeMargin) {\n    const clientProp = `client${name}`;\n    const scrollProp = `scroll${name}`;\n    const offsetProp = `offset${name}`;\n    const innerProp = `inner${name}`;\n    // $(window).width()\n    if (isWindow(element)) {\n        // outerWidth, outerHeight 需要包含滚动条的宽度\n        return funcIndex === 2\n            ? element[innerProp]\n            : toElement(document)[clientProp];\n    }\n    // $(document).width()\n    if (isDocument(element)) {\n        const doc = toElement(element);\n        return Math.max(\n        // @ts-ignore\n        element.body[scrollProp], doc[scrollProp], \n        // @ts-ignore\n        element.body[offsetProp], doc[offsetProp], doc[clientProp]);\n    }\n    const value = parseFloat(getComputedStyleValue(element, name.toLowerCase()) || '0');\n    return handleExtraWidth(element, name, value, funcIndex, includeMargin, 1);\n}\n/**\n * 设置元素的样式值\n * @param element\n * @param elementIndex\n * @param name\n * @param funcIndex 0: innerWidth, innerHeight; 1: width, height; 2: outerWidth, outerHeight\n * @param includeMargin\n * @param value\n */\nfunction set(element, elementIndex, name, funcIndex, includeMargin, value) {\n    let computedValue = isFunction(value)\n        ? value.call(element, elementIndex, get(element, name, funcIndex, includeMargin))\n        : value;\n    if (computedValue == null) {\n        return;\n    }\n    const $element = $(element);\n    const dimension = name.toLowerCase();\n    // 特殊的值，不需要计算 padding、border、margin\n    if (['auto', 'inherit', ''].indexOf(computedValue) > -1) {\n        $element.css(dimension, computedValue);\n        return;\n    }\n    // 其他值保留原始单位。注意：如果不使用 px 作为单位，则算出的值一般是不准确的\n    const suffix = computedValue.toString().replace(/\\b[0-9.]*/, '');\n    const numerical = parseFloat(computedValue);\n    computedValue =\n        handleExtraWidth(element, name, numerical, funcIndex, includeMargin, -1) +\n            (suffix || 'px');\n    $element.css(dimension, computedValue);\n}\neach(['Width', 'Height'], (_, name) => {\n    each([`inner${name}`, name.toLowerCase(), `outer${name}`], (funcIndex, funcName) => {\n        $.fn[funcName] = function (margin, value) {\n            // 是否是赋值操作\n            const isSet = arguments.length && (funcIndex < 2 || !isBoolean(margin));\n            const includeMargin = margin === true || value === true;\n            // 获取第一个元素的值\n            if (!isSet) {\n                return this.length\n                    ? get(this[0], name, funcIndex, includeMargin)\n                    : undefined;\n            }\n            // 设置每个元素的值\n            return this.each((index, element) => set(element, index, name, funcIndex, includeMargin, margin));\n        };\n    });\n});\n", "import $ from '../$';\nimport './each';\n$.fn.hide = function () {\n    return this.each(function () {\n        this.style.display = 'none';\n    });\n};\n", "import $ from '../$';\nimport each from '../functions/each';\nimport map from '../functions/map';\nimport { isElement, isFunction, isUndefined, toElement } from '../utils';\nimport './each';\nimport './is';\neach(['val', 'html', 'text'], (nameIndex, name) => {\n    const props = {\n        0: 'value',\n        1: 'innerHTML',\n        2: 'textContent',\n    };\n    const propName = props[nameIndex];\n    function get($elements) {\n        // text() 获取所有元素的文本\n        if (nameIndex === 2) {\n            // @ts-ignore\n            return map($elements, (element) => toElement(element)[propName]).join('');\n        }\n        // 空集合时，val() 和 html() 返回 undefined\n        if (!$elements.length) {\n            return undefined;\n        }\n        // val() 和 html() 仅获取第一个元素的内容\n        const firstElement = $elements[0];\n        // select multiple 返回数组\n        if (nameIndex === 0 && $(firstElement).is('select[multiple]')) {\n            return map($(firstElement).find('option:checked'), (element) => element.value);\n        }\n        // @ts-ignore\n        return firstElement[propName];\n    }\n    function set(element, value) {\n        // text() 和 html() 赋值为 undefined，则保持原内容不变\n        // val() 赋值为 undefined 则赋值为空\n        if (isUndefined(value)) {\n            if (nameIndex !== 0) {\n                return;\n            }\n            value = '';\n        }\n        if (nameIndex === 1 && isElement(value)) {\n            value = value.outerHTML;\n        }\n        // @ts-ignore\n        element[propName] = value;\n    }\n    $.fn[name] = function (value) {\n        // 获取值\n        if (!arguments.length) {\n            return get(this);\n        }\n        // 设置值\n        return this.each((i, element) => {\n            const computedValue = isFunction(value)\n                ? value.call(element, i, get($(element)))\n                : value;\n            // value 是数组，则选中数组中的元素，反选不在数组中的元素\n            if (nameIndex === 0 && Array.isArray(computedValue)) {\n                // select[multiple]\n                if ($(element).is('select[multiple]')) {\n                    map($(element).find('option'), (option) => (option.selected =\n                        computedValue.indexOf(option.value) >\n                            -1));\n                }\n                // 其他 checkbox, radio 等元素\n                else {\n                    element.checked =\n                        computedValue.indexOf(element.value) > -1;\n                }\n            }\n            else {\n                set(element, computedValue);\n            }\n        });\n    };\n});\n", "import $ from '../$';\nimport { isString } from '../utils';\nimport './children';\nimport './eq';\nimport './get';\nimport './parent';\n$.fn.index = function (selector) {\n    if (!arguments.length) {\n        return this.eq(0).parent().children().get().indexOf(this[0]);\n    }\n    if (isString(selector)) {\n        return $(selector).get().indexOf(this[0]);\n    }\n    return this.get().indexOf($(selector)[0]);\n};\n", "import $ from '../$';\nimport './eq';\n$.fn.last = function () {\n    return this.eq(-1);\n};\n", "import $ from '../$';\nimport each from '../functions/each';\nimport dir from './utils/dir';\neach(['', 'All', 'Until'], (nameIndex, name) => {\n    $.fn[`next${name}`] = function (selector, filter) {\n        return dir(this, nameIndex, 'nextElementSibling', selector, filter);\n    };\n});\n", "import $ from '../$';\nimport './filter';\nimport './map';\n$.fn.not = function (selector) {\n    const $excludes = this.filter(selector);\n    return this.map((_, element) => $excludes.index(element) > -1 ? undefined : element);\n};\n", "import $ from '../$';\nimport './css';\nimport './map';\n/**\n * 返回最近的用于定位的父元素\n */\n$.fn.offsetParent = function () {\n    return this.map(function () {\n        let offsetParent = this.offsetParent;\n        while (offsetParent && $(offsetParent).css('position') === 'static') {\n            offsetParent = offsetParent.offsetParent;\n        }\n        return offsetParent || document.documentElement;\n    });\n};\n", "import $ from '../$';\nimport './css';\nimport './eq';\nimport './offset';\nimport './offsetParent';\nfunction floatStyle($element, name) {\n    return parseFloat($element.css(name));\n}\n$.fn.position = function () {\n    if (!this.length) {\n        return undefined;\n    }\n    const $element = this.eq(0);\n    let currentOffset;\n    let parentOffset = {\n        left: 0,\n        top: 0,\n    };\n    if ($element.css('position') === 'fixed') {\n        currentOffset = $element[0].getBoundingClientRect();\n    }\n    else {\n        currentOffset = $element.offset();\n        const $offsetParent = $element.offsetParent();\n        parentOffset = $offsetParent.offset();\n        parentOffset.top += floatStyle($offsetParent, 'border-top-width');\n        parentOffset.left += floatStyle($offsetParent, 'border-left-width');\n    }\n    return {\n        top: currentOffset.top - parentOffset.top - floatStyle($element, 'margin-top'),\n        left: currentOffset.left -\n            parentOffset.left -\n            floatStyle($element, 'margin-left'),\n    };\n};\n", "import $ from '../$';\nimport extend from '../functions/extend';\nimport { isFunction } from '../utils';\nimport './css';\nimport './each';\nimport './position';\nfunction get(element) {\n    if (!element.getClientRects().length) {\n        return { top: 0, left: 0 };\n    }\n    const rect = element.getBoundingClientRect();\n    const win = element.ownerDocument.defaultView;\n    return {\n        top: rect.top + win.pageYOffset,\n        left: rect.left + win.pageXOffset,\n    };\n}\nfunction set(element, value, index) {\n    const $element = $(element);\n    const position = $element.css('position');\n    if (position === 'static') {\n        $element.css('position', 'relative');\n    }\n    const currentOffset = get(element);\n    const currentTopString = $element.css('top');\n    const currentLeftString = $element.css('left');\n    let currentTop;\n    let currentLeft;\n    const calculatePosition = (position === 'absolute' || position === 'fixed') &&\n        (currentTopString + currentLeftString).indexOf('auto') > -1;\n    if (calculatePosition) {\n        const currentPosition = $element.position();\n        currentTop = currentPosition.top;\n        currentLeft = currentPosition.left;\n    }\n    else {\n        currentTop = parseFloat(currentTopString);\n        currentLeft = parseFloat(currentLeftString);\n    }\n    const computedValue = isFunction(value)\n        ? value.call(element, index, extend({}, currentOffset))\n        : value;\n    $element.css({\n        top: computedValue.top != null\n            ? computedValue.top - currentOffset.top + currentTop\n            : undefined,\n        left: computedValue.left != null\n            ? computedValue.left - currentOffset.left + currentLeft\n            : undefined,\n    });\n}\n$.fn.offset = function (value) {\n    // 获取坐标\n    if (!arguments.length) {\n        if (!this.length) {\n            return undefined;\n        }\n        return get(this[0]);\n    }\n    // 设置坐标\n    return this.each(function (index) {\n        set(this, value, index);\n    });\n};\n", "import $ from '../$';\nimport './on';\n$.fn.one = function (types, selector, data, callback) {\n    // @ts-ignore\n    return this.on(types, selector, data, callback, true);\n};\n", "import $ from '../$';\nimport each from '../functions/each';\nimport './get';\nimport dir from './utils/dir';\neach(['', 'All', 'Until'], (nameIndex, name) => {\n    $.fn[`prev${name}`] = function (selector, filter) {\n        // prevAll、prevUntil 需要把元素的顺序倒序处理，以便和 jQuery 的结果一致\n        const $nodes = !nameIndex ? this : $(this.get().reverse());\n        return dir($nodes, nameIndex, 'previousElementSibling', selector, filter);\n    };\n});\n", "import $ from '../$';\nimport each from '../functions/each';\nimport './each';\n$.fn.removeAttr = function (attributeName) {\n    const names = attributeName.split(' ').filter((name) => name);\n    return this.each(function () {\n        each(names, (_, name) => {\n            this.removeAttribute(name);\n        });\n    });\n};\n", "import $ from '../$';\nimport removeData from '../functions/removeData';\nimport './each';\n$.fn.removeData = function (name) {\n    return this.each(function () {\n        removeData(this, name);\n    });\n};\n", "import $ from '../$';\nimport './each';\n$.fn.removeProp = function (name) {\n    return this.each(function () {\n        try {\n            // @ts-ignore\n            delete this[name];\n        }\n        catch (e) { }\n    });\n};\n", "import $ from '../$';\nimport './before';\nimport './clone';\nimport './each';\nimport './remove';\nimport { isFunction, isString } from '../utils';\n$.fn.replaceWith = function (newContent) {\n    this.each((index, element) => {\n        let content = newContent;\n        if (isFunction(content)) {\n            content = content.call(element, index, element.innerHTML);\n        }\n        else if (index && !isString(content)) {\n            content = $(content).clone();\n        }\n        $(element).before(content);\n    });\n    return this.remove();\n};\n", "import $ from '../$';\nimport './clone';\nimport './get';\nimport './map';\nimport './replaceWith';\n$.fn.replaceAll = function (target) {\n    return $(target).map((index, element) => {\n        $(element).replaceWith(index ? this.clone() : this);\n        return this.get();\n    });\n};\n", "import $ from '../$';\nimport './each';\nimport './val';\n/**\n * 将表单元素的值组合成键值对数组\n * @returns {Array}\n */\n$.fn.serializeArray = function () {\n    const result = [];\n    this.each((_, element) => {\n        const elements = element instanceof HTMLFormElement ? element.elements : [element];\n        $(elements).each((_, element) => {\n            const $element = $(element);\n            const type = element.type;\n            const nodeName = element.nodeName.toLowerCase();\n            if (nodeName !== 'fieldset' &&\n                element.name &&\n                !element.disabled &&\n                ['input', 'select', 'textarea', 'keygen'].indexOf(nodeName) > -1 &&\n                ['submit', 'button', 'image', 'reset', 'file'].indexOf(type) === -1 &&\n                (['radio', 'checkbox'].indexOf(type) === -1 ||\n                    element.checked)) {\n                const value = $element.val();\n                const valueArr = Array.isArray(value) ? value : [value];\n                valueArr.forEach((value) => {\n                    result.push({\n                        name: element.name,\n                        value,\n                    });\n                });\n            }\n        });\n    });\n    return result;\n};\n", "import $ from '../$';\nimport param from '../functions/param';\nimport './serializeArray';\n$.fn.serialize = function () {\n    return param(this.serializeArray());\n};\n", "import $ from '../$';\nimport { getStyle } from '../utils';\nimport './each';\nconst elementDisplay = {};\n/**\n * 获取元素的初始 display 值，用于 .show() 方法\n * @param nodeName\n */\nfunction defaultDisplay(nodeName) {\n    let element;\n    let display;\n    if (!elementDisplay[nodeName]) {\n        element = document.createElement(nodeName);\n        document.body.appendChild(element);\n        display = getStyle(element, 'display');\n        element.parentNode.removeChild(element);\n        if (display === 'none') {\n            display = 'block';\n        }\n        elementDisplay[nodeName] = display;\n    }\n    return elementDisplay[nodeName];\n}\n/**\n * 显示指定元素\n * @returns {JQ}\n */\n$.fn.show = function () {\n    return this.each(function () {\n        if (this.style.display === 'none') {\n            this.style.display = '';\n        }\n        if (getStyle(this, 'display') === 'none') {\n            this.style.display = defaultDisplay(this.nodeName);\n        }\n    });\n};\n", "import $ from '../$';\nimport './add';\nimport './nextAll';\nimport './prevAll';\n/**\n * 取得同辈元素的集合\n * @param selector {String=}\n * @returns {JQ}\n */\n$.fn.siblings = function (selector) {\n    return this.prevAll(selector).add(this.nextAll(selector));\n};\n", "import $ from '../$';\nimport { getStyle } from '../utils';\nimport './each';\nimport './hide';\nimport './show';\n/**\n * 切换元素的显示状态\n */\n$.fn.toggle = function () {\n    return this.each(function () {\n        getStyle(this, 'display') === 'none' ? $(this).show() : $(this).hide();\n    });\n};\n", "import $ from 'mdui.jq/es/$';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/each';\n\ndeclare module 'mdui.jq/es/JQ' {\n  interface JQ<T = HTMLElement> {\n    /**\n     * 强制重绘当前元素\n     *\n     * @example\n```js\n$('.box').reflow();\n```\n     */\n    reflow(): this;\n  }\n}\n\n$.fn.reflow = function (this: JQ): JQ {\n  return this.each(function () {\n    return this.clientLeft;\n  });\n};\n", "import $ from 'mdui.jq/es/$';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport { isNumber } from 'mdui.jq/es/utils';\nimport 'mdui.jq/es/methods/each';\n\ndeclare module 'mdui.jq/es/JQ' {\n  interface JQ<T = HTMLElement> {\n    /**\n     * 设置当前元素的 transition-duration 属性\n     * @param duration 可以是带单位的值；若不带单位，则自动添加 `ms` 作为单位\n     * @example\n```js\n$('.box').transition('300ms');\n$('.box').transition(300);\n```\n     */\n    transition(duration: string | number): this;\n  }\n}\n\n$.fn.transition = function (this: JQ, duration: string | number): JQ {\n  if (isNumber(duration)) {\n    duration = `${duration}ms`;\n  }\n\n  return this.each(function () {\n    this.style.webkitTransitionDuration = duration as string;\n    this.style.transitionDuration = duration as string;\n  });\n};\n", "import $ from 'mdui.jq/es/$';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport each from 'mdui.jq/es/functions/each';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/off';\n\ndeclare module 'mdui.jq/es/JQ' {\n  interface JQ<T = HTMLElement> {\n    /**\n     * 在当前元素上添加 transitionend 事件回调\n     * @param callback 回调函数的参数为 `transitionend` 事件对象；`this` 指向当前元素\n     * @example\n```js\n$('.box').transitionEnd(function() {\n  alert('.box 元素的 transitionend 事件已触发');\n});\n```\n     */\n    transitionEnd(callback: (this: T, e: Event) => void): this;\n  }\n}\n\n$.fn.transitionEnd = function (\n  this: JQ,\n  callback: (this: HTMLElement, e: Event) => void,\n): JQ {\n  // eslint-disable-next-line @typescript-eslint/no-this-alias\n  const that = this;\n  const events = ['webkitTransitionEnd', 'transitionend'];\n\n  function fireCallback(this: Element | Document | Window, e: Event): void {\n    if (e.target !== this) {\n      return;\n    }\n\n    // @ts-ignore\n    callback.call(this, e);\n\n    each(events, (_, event) => {\n      that.off(event, fireCallback);\n    });\n  }\n\n  each(events, (_, event) => {\n    that.on(event, fireCallback);\n  });\n\n  return this;\n};\n", "import $ from 'mdui.jq/es/$';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/each';\n\ndeclare module 'mdui.jq/es/JQ' {\n  interface JQ<T = HTMLElement> {\n    /**\n     * 设置当前元素的 transform-origin 属性\n     * @param transformOrigin\n     * @example\n```js\n$('.box').transformOrigin('top center');\n```\n     */\n    transformOrigin(transformOrigin: string): this;\n  }\n}\n\n$.fn.transformOrigin = function (this: JQ, transformOrigin: string): JQ {\n  return this.each(function () {\n    this.style.webkitTransformOrigin = transformOrigin;\n    this.style.transformOrigin = transformOrigin;\n  });\n};\n", "import $ from 'mdui.jq/es/$';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/each';\n\ndeclare module 'mdui.jq/es/JQ' {\n  interface JQ<T = HTMLElement> {\n    /**\n     * 设置当前元素的 transform 属性\n     * @param transform\n     * @example\n```js\n$('.box').transform('rotate(90deg)');\n```\n     */\n    transform(transform: string): this;\n  }\n}\n\n$.fn.transform = function (this: JQ, transform: string): JQ {\n  return this.each(function () {\n    this.style.webkitTransform = transform;\n    this.style.transform = transform;\n  });\n};\n", "import PlainObject from 'mdui.jq/es/interfaces/PlainObject';\nimport data from 'mdui.jq/es/functions/data';\n\ntype TYPE_API_INIT = (\n  this: HTMLElement,\n  i: number,\n  element: HTMLElement,\n) => void;\n\n/**\n * CSS 选择器和初始化函数组成的对象\n */\nconst entries: PlainObject<TYPE_API_INIT> = {};\n\n/**\n * 注册并执行初始化函数\n * @param selector CSS 选择器\n * @param apiInit 初始化函数\n * @param i 元素索引\n * @param element 元素\n */\nfunction mutation(\n  selector: string,\n  apiInit: TYPE_API_INIT,\n  i: number,\n  element: HTMLElement,\n): void {\n  let selectors = data(element, '_mdui_mutation');\n\n  if (!selectors) {\n    selectors = [];\n    data(element, '_mdui_mutation', selectors);\n  }\n\n  if (selectors.indexOf(selector) === -1) {\n    selectors.push(selector);\n    apiInit.call(element, i, element);\n  }\n}\n\nexport { TYPE_API_INIT, entries, mutation };\n", "import $ from 'mdui.jq/es/$';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport each from 'mdui.jq/es/functions/each';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/is';\nimport { entries, mutation } from '../../utils/mutation';\n\ndeclare module 'mdui.jq/es/JQ' {\n  interface JQ<T = HTMLElement> {\n    /**\n     * 执行在当前元素及其子元素内注册的初始化函数\n     */\n    mutation(): this;\n  }\n}\n\n$.fn.mutation = function (this: JQ): JQ {\n  return this.each((i, element) => {\n    const $this = $(element);\n\n    each(entries, (selector: string, apiInit) => {\n      if ($this.is(selector)) {\n        mutation(selector, apiInit, i, element);\n      }\n\n      $this.find(selector).each((i, element) => {\n        mutation(selector, apiInit, i, element);\n      });\n    });\n  });\n};\n", "import $ from 'mdui.jq/es/$';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport { isUndefined } from 'mdui.jq/es/utils';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/css';\nimport 'mdui.jq/es/methods/appendTo';\nimport 'mdui.jq/es/methods/addClass';\nimport '../methods/reflow';\n\ndeclare module 'mdui.jq/es/interfaces/JQStatic' {\n  interface JQStatic {\n    /**\n     * 创建并显示遮罩，返回遮罩层的 JQ 对象\n     * @param zIndex 遮罩层的 `z-index` 值，默认为 `2000`\n     * @example\n```js\n$.showOverlay();\n```\n     * @example\n```js\n$.showOverlay(3000);\n```\n     */\n    showOverlay(zIndex?: number): JQ;\n  }\n}\n\n$.showOverlay = function (zIndex?: number): JQ {\n  let $overlay = $('.mdui-overlay');\n\n  if ($overlay.length) {\n    $overlay.data('_overlay_is_deleted', false);\n\n    if (!isUndefined(zIndex)) {\n      $overlay.css('z-index', zIndex);\n    }\n  } else {\n    if (isUndefined(zIndex)) {\n      zIndex = 2000;\n    }\n\n    $overlay = $('<div class=\"mdui-overlay\">')\n      .appendTo(document.body)\n      .reflow()\n      .css('z-index', zIndex);\n  }\n\n  let level = $overlay.data('_overlay_level') || 0;\n\n  return $overlay.data('_overlay_level', ++level).addClass('mdui-overlay-show');\n};\n", "import $ from 'mdui.jq/es/$';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/removeClass';\nimport 'mdui.jq/es/methods/remove';\nimport '../methods/transitionEnd';\n\ndeclare module 'mdui.jq/es/interfaces/JQStatic' {\n  interface JQStatic {\n    /**\n     * 隐藏遮罩层\n     *\n     * 如果调用了多次 $.showOverlay() 来显示遮罩层，则也需要调用相同次数的 $.hideOverlay() 才能隐藏遮罩层。可以通过传入参数 true 来强制隐藏遮罩层。\n     * @param force 是否强制隐藏遮罩\n     * @example\n```js\n$.hideOverlay();\n```\n     * @example\n```js\n$.hideOverlay(true);\n```\n     */\n    hideOverlay(force?: boolean): void;\n  }\n}\n\n$.hideOverlay = function (force = false): void {\n  const $overlay = $('.mdui-overlay');\n\n  if (!$overlay.length) {\n    return;\n  }\n\n  let level = force ? 1 : $overlay.data('_overlay_level');\n\n  if (level > 1) {\n    $overlay.data('_overlay_level', --level);\n    return;\n  }\n\n  $overlay\n    .data('_overlay_level', 0)\n    .removeClass('mdui-overlay-show')\n    .data('_overlay_is_deleted', true)\n    .transitionEnd(() => {\n      if ($overlay.data('_overlay_is_deleted')) {\n        $overlay.remove();\n      }\n    });\n};\n", "import $ from 'mdui.jq/es/$';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/width';\n\ndeclare module 'mdui.jq/es/interfaces/JQStatic' {\n  interface JQStatic {\n    /**\n     * 锁定屏页面，禁止页面滚动\n     * @example\n```js\n$.lockScreen();\n```\n     */\n    lockScreen(): void;\n  }\n}\n\n$.lockScreen = function (): void {\n  const $body = $('body');\n\n  // 不直接把 body 设为 box-sizing: border-box，避免污染全局样式\n  const newBodyWidth = $body.width();\n  let level = $body.data('_lockscreen_level') || 0;\n\n  $body\n    .addClass('mdui-locked')\n    .width(newBodyWidth)\n    .data('_lockscreen_level', ++level);\n};\n", "import $ from 'mdui.jq/es/$';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/removeClass';\nimport 'mdui.jq/es/methods/width';\n\ndeclare module 'mdui.jq/es/interfaces/JQStatic' {\n  interface JQStatic {\n    /**\n     * 解除页面锁定\n     *\n     * 如果调用了多次 $.lockScreen() 来显示遮罩层，则也需要调用相同次数的 $.unlockScreen() 才能隐藏遮罩层。可以通过传入参数 true 来强制隐藏遮罩层。\n     * @param force 是否强制解除锁定\n     * @example\n```js\n$.unlockScreen();\n```\n     * @example\n```js\n$.unlockScreen(true);\n```\n     */\n    unlockScreen(force?: boolean): void;\n  }\n}\n\n$.unlockScreen = function (force = false): void {\n  const $body = $('body');\n  let level = force ? 1 : $body.data('_lockscreen_level');\n\n  if (level > 1) {\n    $body.data('_lockscreen_level', --level);\n    return;\n  }\n\n  $body.data('_lockscreen_level', 0).removeClass('mdui-locked').width('');\n};\n", "import $ from 'mdui.jq/es/$';\nimport { isNull } from 'mdui.jq/es/utils';\n\ndeclare module 'mdui.jq/es/interfaces/JQStatic' {\n  interface JQStatic {\n    /**\n     * 函数节流\n     * @param fn 执行的函数\n     * @param delay 最多多少毫秒执行一次\n     * @example\n```js\n$.throttle(function () {\n  console.log('这个函数最多 100ms 执行一次');\n}, 100)\n```\n     */\n    throttle(fn: () => void, delay: number): () => void;\n  }\n}\n\n$.throttle = function (fn: () => void, delay = 16): () => void {\n  let timer: any = null;\n\n  return function (this: any, ...args: any): void {\n    if (isNull(timer)) {\n      timer = setTimeout(() => {\n        fn.apply(this, args);\n        timer = null;\n      }, delay);\n    }\n  };\n};\n", "import $ from 'mdui.jq/es/$';\nimport { isUndefined } from 'mdui.jq/es/utils';\nimport PlainObject from 'mdui.jq/es/interfaces/PlainObject';\n\ndeclare module 'mdui.jq/es/interfaces/JQStatic' {\n  interface JQStatic {\n    /**\n     * 生成一个全局唯一的 ID\n     * @param name 当该参数值对应的 guid 不存在时，会生成一个新的 guid，并返回；当该参数对应的 guid 已存在，则直接返回已有 guid\n     * @example\n```js\n$.guid();\n```\n     * @example\n```js\n$.guid('test');\n```\n     */\n    guid(name?: string): string;\n  }\n}\n\nconst GUID: PlainObject<string> = {};\n\n$.guid = function (name?: string): string {\n  if (!isUndefined(name) && !isUndefined(GUID[name])) {\n    return GUID[name];\n  }\n\n  function s4(): string {\n    return Math.floor((1 + Math.random()) * 0x10000)\n      .toString(16)\n      .substring(1);\n  }\n\n  const guid =\n    '_' +\n    s4() +\n    s4() +\n    '-' +\n    s4() +\n    '-' +\n    s4() +\n    '-' +\n    s4() +\n    '-' +\n    s4() +\n    s4() +\n    s4();\n\n  if (!isUndefined(name)) {\n    GUID[name] = guid;\n  }\n\n  return guid;\n};\n", "import $ from 'mdui.jq/es/$';\nimport 'mdui.jq/es/methods/each';\nimport mdui from '../mdui';\nimport '../jq_extends/methods/mutation';\nimport { isUndefined } from 'mdui.jq/es/utils';\nimport { TYPE_API_INIT, entries, mutation } from '../utils/mutation';\n\ndeclare module '../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 传入了两个参数时，注册并执行初始化函数\n     *\n     * 没有传入参数时，执行初始化\n     * @param selector CSS 选择器\n     * @param apiInit 初始化函数\n     * @example\n```js\nmdui.mutation();\n```\n     * @example\n```js\nmdui.mutation();\n```\n     */\n    mutation(selector?: string, apiInit?: TYPE_API_INIT): void;\n  }\n}\n\nmdui.mutation = function (selector?: string, apiInit?: TYPE_API_INIT): void {\n  if (isUndefined(selector) || isUndefined(apiInit)) {\n    $(document).mutation();\n    return;\n  }\n\n  entries[selector] = apiInit!;\n  $(selector).each((i, element) => mutation(selector, apiInit, i, element));\n};\n", "import $ from 'mdui.jq/es/$';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport PlainObject from 'mdui.jq/es/interfaces/PlainObject';\nimport 'mdui.jq/es/methods/trigger';\n\n/**\n * 触发组件上的事件\n * @param eventName 事件名\n * @param componentName 组件名\n * @param target 在该元素上触发事件\n * @param instance 组件实例\n * @param parameters 事件参数\n */\nfunction componentEvent(\n  eventName: string,\n  componentName: string,\n  target: HTMLElement | HTMLElement[] | JQ,\n  instance?: any,\n  parameters?: PlainObject,\n): void {\n  if (!parameters) {\n    parameters = {};\n  }\n\n  // @ts-ignore\n  parameters.inst = instance;\n\n  const fullEventName = `${eventName}.mdui.${componentName}`;\n\n  // jQuery 事件\n  // @ts-ignore\n  if (typeof jQuery !== 'undefined') {\n    // @ts-ignore\n    jQuery(target).trigger(fullEventName, parameters);\n  }\n\n  const $target = $(target);\n\n  // mdui.jq 事件\n  $target.trigger(fullEventName, parameters);\n\n  // 原生事件，供使用 addEventListener 监听\n  type EventParams = {\n    detail?: any;\n    bubbles: boolean;\n    cancelable: boolean;\n  };\n\n  const eventParams: EventParams = {\n    bubbles: true,\n    cancelable: true,\n    detail: parameters,\n  };\n\n  const eventObject: CustomEvent = new CustomEvent(fullEventName, eventParams);\n\n  // @ts-ignore\n  eventObject._detail = parameters;\n\n  $target[0].dispatchEvent(eventObject);\n}\n\nexport { componentEvent };\n", "import $ from 'mdui.jq/es/$';\n\nconst $document = $(document);\nconst $window = $(window);\nconst $body = $('body');\n\nexport { $document, $window, $body };\n", "import $ from 'mdui.jq/es/$';\nimport extend from 'mdui.jq/es/functions/extend';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/removeClass';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport { isNumber } from 'mdui.jq/es/utils';\nimport mdui from '../../mdui';\nimport '../../jq_extends/methods/transitionEnd';\nimport { componentEvent } from '../../utils/componentEvent';\nimport { $window } from '../../utils/dom';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * Headroom 插件\n     *\n     * 请通过 `new mdui.Headroom()` 调用\n     */\n    Headroom: {\n      /**\n       * 实例化 Headroom 组件\n       * @param selector CSS 选择器、或 DOM 元素、或 JQ 对象\n       * @param options 配置参数\n       */\n      new (\n        selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n        options?: OPTIONS,\n      ): Headroom;\n    };\n  }\n}\n\ntype TOLERANCE = {\n  /**\n   * 滚动条向下滚动多少距离开始隐藏或显示元素\n   */\n  down: number;\n\n  /**\n   * 滚动条向上滚动多少距离开始隐藏或显示元素\n   */\n  up: number;\n};\n\ntype OPTIONS = {\n  /**\n   * 滚动条滚动多少距离开始隐藏或显示元素\n   */\n  tolerance?: TOLERANCE | number;\n\n  /**\n   * 在页面顶部多少距离内滚动不会隐藏元素\n   */\n  offset?: number;\n\n  /**\n   * 初始化时添加的类\n   */\n  initialClass?: string;\n\n  /**\n   * 元素固定时添加的类\n   */\n  pinnedClass?: string;\n\n  /**\n   * 元素隐藏时添加的类\n   */\n  unpinnedClass?: string;\n};\n\ntype STATE = 'pinning' | 'pinned' | 'unpinning' | 'unpinned';\ntype EVENT = 'pin' | 'pinned' | 'unpin' | 'unpinned';\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  tolerance: 5,\n  offset: 0,\n  initialClass: 'mdui-headroom',\n  pinnedClass: 'mdui-headroom-pinned-top',\n  unpinnedClass: 'mdui-headroom-unpinned-top',\n};\n\nclass Headroom {\n  /**\n   * headroom 元素的 JQ 对象\n   */\n  public $element: JQ;\n\n  /**\n   * 配置参数\n   */\n  public options: OPTIONS = extend({}, DEFAULT_OPTIONS);\n\n  /**\n   * 当前 headroom 的状态\n   */\n  private state: STATE = 'pinned';\n\n  /**\n   * 当前是否启用\n   */\n  private isEnable = false;\n\n  /**\n   * 上次滚动后，垂直方向的距离\n   */\n  private lastScrollY = 0;\n\n  /**\n   * AnimationFrame ID\n   */\n  private rafId = 0;\n\n  public constructor(\n    selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    options: OPTIONS = {},\n  ) {\n    this.$element = $(selector).first();\n\n    extend(this.options, options);\n\n    // tolerance 参数若为数值，转换为对象\n    const tolerance = this.options.tolerance;\n    if (isNumber(tolerance)) {\n      this.options.tolerance = {\n        down: tolerance,\n        up: tolerance,\n      };\n    }\n\n    this.enable();\n  }\n\n  /**\n   * 滚动时的处理\n   */\n  private onScroll(): void {\n    this.rafId = window.requestAnimationFrame(() => {\n      const currentScrollY = window.pageYOffset;\n      const direction = currentScrollY > this.lastScrollY ? 'down' : 'up';\n      const tolerance = (this.options.tolerance as TOLERANCE)[direction];\n      const scrolled = Math.abs(currentScrollY - this.lastScrollY);\n      const toleranceExceeded = scrolled >= tolerance;\n\n      if (\n        currentScrollY > this.lastScrollY &&\n        currentScrollY >= this.options.offset! &&\n        toleranceExceeded\n      ) {\n        this.unpin();\n      } else if (\n        (currentScrollY < this.lastScrollY && toleranceExceeded) ||\n        currentScrollY <= this.options.offset!\n      ) {\n        this.pin();\n      }\n\n      this.lastScrollY = currentScrollY;\n    });\n  }\n\n  /**\n   * 触发组件事件\n   * @param name\n   */\n  private triggerEvent(name: EVENT): void {\n    componentEvent(name, 'headroom', this.$element, this);\n  }\n\n  /**\n   * 动画结束的回调\n   */\n  private transitionEnd(): void {\n    if (this.state === 'pinning') {\n      this.state = 'pinned';\n      this.triggerEvent('pinned');\n    }\n\n    if (this.state === 'unpinning') {\n      this.state = 'unpinned';\n      this.triggerEvent('unpinned');\n    }\n  }\n\n  /**\n   * 使元素固定住\n   */\n  public pin(): void {\n    if (\n      this.state === 'pinning' ||\n      this.state === 'pinned' ||\n      !this.$element.hasClass(this.options.initialClass!)\n    ) {\n      return;\n    }\n\n    this.triggerEvent('pin');\n    this.state = 'pinning';\n    this.$element\n      .removeClass(this.options.unpinnedClass)\n      .addClass(this.options.pinnedClass!)\n      .transitionEnd(() => this.transitionEnd());\n  }\n\n  /**\n   * 使元素隐藏\n   */\n  public unpin(): void {\n    if (\n      this.state === 'unpinning' ||\n      this.state === 'unpinned' ||\n      !this.$element.hasClass(this.options.initialClass!)\n    ) {\n      return;\n    }\n\n    this.triggerEvent('unpin');\n    this.state = 'unpinning';\n    this.$element\n      .removeClass(this.options.pinnedClass)\n      .addClass(this.options.unpinnedClass!)\n      .transitionEnd(() => this.transitionEnd());\n  }\n\n  /**\n   * 启用 headroom 插件\n   */\n  public enable(): void {\n    if (this.isEnable) {\n      return;\n    }\n\n    this.isEnable = true;\n    this.state = 'pinned';\n    this.$element\n      .addClass(this.options.initialClass!)\n      .removeClass(this.options.pinnedClass)\n      .removeClass(this.options.unpinnedClass);\n    this.lastScrollY = window.pageYOffset;\n\n    $window.on('scroll', () => this.onScroll());\n  }\n\n  /**\n   * 禁用 headroom 插件\n   */\n  public disable(): void {\n    if (!this.isEnable) {\n      return;\n    }\n\n    this.isEnable = false;\n    this.$element\n      .removeClass(this.options.initialClass)\n      .removeClass(this.options.pinnedClass)\n      .removeClass(this.options.unpinnedClass);\n\n    $window.off('scroll', () => this.onScroll());\n    window.cancelAnimationFrame(this.rafId);\n  }\n\n  /**\n   * 获取当前状态。共包含四种状态：`pinning`、`pinned`、`unpinning`、`unpinned`\n   */\n  public getState(): STATE {\n    return this.state;\n  }\n}\n\nmdui.Headroom = Headroom;\n", "import $ from 'mdui.jq/es/$';\nimport 'mdui.jq/es/methods/attr';\nimport PlainObject from 'mdui.jq/es/interfaces/PlainObject';\n\n/**\n * 解析 DATA API 参数\n * @param element 元素\n * @param name 属性名\n */\nfunction parseOptions(element: HTMLElement, name: string): PlainObject {\n  const attr = $(element).attr(name);\n\n  if (!attr) {\n    return {};\n  }\n\n  return new Function(\n    '',\n    `var json = ${attr}; return JSON.parse(JSON.stringify(json));`,\n  )();\n}\n\nexport { parseOptions };\n", "import $ from 'mdui.jq/es/$';\nimport mdui from '../../mdui';\nimport '../../global/mutation';\nimport { parseOptions } from '../../utils/parseOptions';\nimport './index';\n\nconst customAttr = 'mdui-headroom';\n\n$(() => {\n  mdui.mutation(`[${customAttr}]`, function () {\n    new mdui.Headroom(this, parseOptions(this, customAttr));\n  });\n});\n", "import $ from 'mdui.jq/es/$';\nimport extend from 'mdui.jq/es/functions/extend';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/children';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/eq';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/height';\nimport 'mdui.jq/es/methods/is';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/parent';\nimport 'mdui.jq/es/methods/parents';\nimport 'mdui.jq/es/methods/removeClass';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport { isNumber } from 'mdui.jq/es/utils';\nimport '../../jq_extends/methods/reflow';\nimport '../../jq_extends/methods/transition';\nimport '../../jq_extends/methods/transitionEnd';\nimport { componentEvent } from '../../utils/componentEvent';\n\ntype OPTIONS = {\n  /**\n   * 是否启用手风琴效果\n   * 为 `true` 时，最多只能有一个面板项处于打开状态，打开一个面板项时会关闭其他面板项\n   * 为 `false` 时，可同时打开多个面板项\n   */\n  accordion?: boolean;\n};\n\ntype EVENT = 'open' | 'opened' | 'close' | 'closed';\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  accordion: false,\n};\n\nabstract class CollapseAbstract {\n  /**\n   * collapse 元素的 JQ 对象\n   */\n  public $element: JQ;\n\n  /**\n   * 配置参数\n   */\n  public options: OPTIONS = extend({}, DEFAULT_OPTIONS);\n\n  /**\n   * item 的 class 名\n   */\n  private classItem: string;\n\n  /**\n   * 打开状态的 item 的 class 名\n   */\n  private classItemOpen: string;\n\n  /**\n   * item-header 的 class 名\n   */\n  private classHeader: string;\n\n  /**\n   * item-body 的 class 名\n   */\n  private classBody: string;\n\n  /**\n   * 获取继承的组件名称\n   */\n  protected abstract getNamespace(): string;\n\n  public constructor(\n    selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    options: OPTIONS = {},\n  ) {\n    // CSS 类名\n    const classPrefix = `mdui-${this.getNamespace()}-item`;\n    this.classItem = classPrefix;\n    this.classItemOpen = `${classPrefix}-open`;\n    this.classHeader = `${classPrefix}-header`;\n    this.classBody = `${classPrefix}-body`;\n\n    this.$element = $(selector).first();\n\n    extend(this.options, options);\n\n    this.bindEvent();\n  }\n\n  /**\n   * 绑定事件\n   */\n  private bindEvent(): void {\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const that = this;\n\n    // 点击 header 时，打开/关闭 item\n    this.$element.on('click', `.${this.classHeader}`, function () {\n      const $header = $(this as HTMLElement);\n      const $item = $header.parent();\n      const $items = that.getItems();\n\n      $items.each((_, item) => {\n        if ($item.is(item)) {\n          that.toggle(item);\n        }\n      });\n    });\n\n    // 点击关闭按钮时，关闭 item\n    this.$element.on(\n      'click',\n      `[mdui-${this.getNamespace()}-item-close]`,\n      function () {\n        const $target = $(this as HTMLElement);\n        const $item = $target.parents(`.${that.classItem}`).first();\n\n        that.close($item);\n      },\n    );\n  }\n\n  /**\n   * 指定 item 是否处于打开状态\n   * @param $item\n   */\n  private isOpen($item: JQ): boolean {\n    return $item.hasClass(this.classItemOpen);\n  }\n\n  /**\n   * 获取所有 item\n   */\n  private getItems(): JQ {\n    return this.$element.children(`.${this.classItem}`);\n  }\n\n  /**\n   * 获取指定 item\n   * @param item\n   */\n  private getItem(\n    item: number | Selector | HTMLElement | ArrayLike<HTMLElement>,\n  ): JQ {\n    if (isNumber(item)) {\n      return this.getItems().eq(item);\n    }\n\n    return $(item).first();\n  }\n\n  /**\n   * 触发组件事件\n   * @param name 事件名\n   * @param $item 事件触发的目标 item\n   */\n  private triggerEvent(name: EVENT, $item: JQ): void {\n    componentEvent(name, this.getNamespace(), $item, this);\n  }\n\n  /**\n   * 动画结束回调\n   * @param $content body 元素\n   * @param $item item 元素\n   */\n  private transitionEnd($content: JQ, $item: JQ): void {\n    if (this.isOpen($item)) {\n      $content.transition(0).height('auto').reflow().transition('');\n\n      this.triggerEvent('opened', $item);\n    } else {\n      $content.height('');\n\n      this.triggerEvent('closed', $item);\n    }\n  }\n\n  /**\n   * 打开指定面板项\n   * @param item 面板项的索引号、或 CSS 选择器、或 DOM 元素、或 JQ 对象\n   */\n  public open(\n    item: number | Selector | HTMLElement | ArrayLike<HTMLElement>,\n  ): void {\n    const $item = this.getItem(item);\n\n    if (this.isOpen($item)) {\n      return;\n    }\n\n    // 关闭其他项\n    if (this.options.accordion) {\n      this.$element.children(`.${this.classItemOpen}`).each((_, element) => {\n        const $element = $(element);\n\n        if (!$element.is($item)) {\n          this.close($element);\n        }\n      });\n    }\n\n    const $content = $item.children(`.${this.classBody}`);\n\n    $content\n      .height($content[0].scrollHeight)\n      .transitionEnd(() => this.transitionEnd($content, $item));\n\n    this.triggerEvent('open', $item);\n\n    $item.addClass(this.classItemOpen);\n  }\n\n  /**\n   * 关闭指定面板项\n   * @param item 面板项的索引号、或 CSS 选择器、或 DOM 元素、或 JQ 对象\n   */\n  public close(\n    item: number | Selector | HTMLElement | ArrayLike<HTMLElement>,\n  ): void {\n    const $item = this.getItem(item);\n\n    if (!this.isOpen($item)) {\n      return;\n    }\n\n    const $content = $item.children(`.${this.classBody}`);\n\n    this.triggerEvent('close', $item);\n\n    $item.removeClass(this.classItemOpen);\n\n    $content\n      .transition(0)\n      .height($content[0].scrollHeight)\n      .reflow()\n      .transition('')\n      .height('')\n      .transitionEnd(() => this.transitionEnd($content, $item));\n  }\n\n  /**\n   * 切换指定面板项的打开状态\n   * @param item 面板项的索引号、或 CSS 选择器、或 DOM 元素、或 JQ 对象\n   */\n  public toggle(\n    item: number | Selector | HTMLElement | ArrayLike<HTMLElement>,\n  ): void {\n    const $item = this.getItem(item);\n\n    this.isOpen($item) ? this.close($item) : this.open($item);\n  }\n\n  /**\n   * 打开所有面板项\n   */\n  public openAll(): void {\n    this.getItems().each((_, element) => this.open(element));\n  }\n\n  /**\n   * 关闭所有面板项\n   */\n  public closeAll(): void {\n    this.getItems().each((_, element) => this.close(element));\n  }\n}\n\nexport { OPTIONS, CollapseAbstract };\n", "import Selector from 'mdui.jq/es/types/Selector';\nimport mdui from '../../mdui';\nimport { CollapseAbstract, OPTIONS } from './collapseAbstract';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 折叠内容块组件\n     *\n     * 请通过 `new mdui.Collapse()` 调用\n     */\n    Collapse: {\n      /**\n       * 实例化 Collapse 组件\n       * @param selector CSS 选择器或 DOM 元素\n       * @param options 配置参数\n       */\n      new (\n        selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n        options?: OPTIONS,\n      ): Collapse;\n    };\n  }\n}\n\nclass Collapse extends CollapseAbstract {\n  protected getNamespace(): string {\n    return 'collapse';\n  }\n}\n\nmdui.Collapse = Collapse;\n", "import $ from 'mdui.jq/es/$';\nimport mdui from '../../mdui';\nimport '../../global/mutation';\nimport { parseOptions } from '../../utils/parseOptions';\nimport './index';\n\nconst customAttr = 'mdui-collapse';\n\n$(() => {\n  mdui.mutation(`[${customAttr}]`, function () {\n    new mdui.Collapse(this, parseOptions(this, customAttr));\n  });\n});\n", "import Selector from 'mdui.jq/es/types/Selector';\nimport mdui from '../../mdui';\nimport { CollapseAbstract, OPTIONS } from '../collapse/collapseAbstract';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 可扩展面板组件\n     *\n     * 请通过 `new mdui.Panel()` 调用\n     */\n    Panel: {\n      /**\n       * 实例化 Panel 组件\n       * @param selector CSS 选择器或 DOM 元素\n       * @param options 配置参数\n       */\n      new (\n        selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n        options?: OPTIONS,\n      ): Panel;\n    };\n  }\n}\n\nclass Panel extends CollapseAbstract {\n  protected getNamespace(): string {\n    return 'panel';\n  }\n}\n\nmdui.Panel = Panel;\n", "import $ from 'mdui.jq/es/$';\nimport mdui from '../../mdui';\nimport '../../global/mutation';\nimport { parseOptions } from '../../utils/parseOptions';\nimport './index';\n\nconst customAttr = 'mdui-panel';\n\n$(() => {\n  mdui.mutation(`[${customAttr}]`, function () {\n    new mdui.Panel(this, parseOptions(this, customAttr));\n  });\n});\n", "import $ from 'mdui.jq/es/$';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/add';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/eq';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/prependTo';\nimport 'mdui.jq/es/methods/remove';\nimport 'mdui.jq/es/methods/removeClass';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport { isUndefined } from 'mdui.jq/es/utils';\nimport mdui from '../../mdui';\nimport '../../global/mutation';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 动态修改了表格后，需要调用该方法重新初始化表格。\n     *\n     * 若传入了参数，则只初始化该参数对应的表格。若没有传入参数，则重新初始化所有表格。\n     * @param selector CSS 选择器、或 DOM 元素、或 DOM 元素组成的数组、或 JQ 对象\n     */\n    updateTables(\n      selector?: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    ): void;\n  }\n}\n\nclass Table {\n  /**\n   * table 元素的 JQ 对象\n   */\n  public $element: JQ;\n\n  /**\n   * 表头 tr 元素\n   */\n  private $thRow: JQ = $();\n\n  /**\n   * 表格 body 中的 tr 元素\n   */\n  private $tdRows: JQ = $();\n\n  /**\n   * 表头的 checkbox 元素\n   */\n  private $thCheckbox: JQ<HTMLInputElement> = $();\n\n  /**\n   * 表格 body 中的 checkbox 元素\n   */\n  private $tdCheckboxs: JQ<HTMLInputElement> = $();\n\n  /**\n   * 表格行是否可选择\n   */\n  private selectable = false;\n\n  /**\n   * 已选中的行数\n   */\n  private selectedRow = 0;\n\n  public constructor(\n    selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n  ) {\n    this.$element = $(selector).first();\n    this.init();\n  }\n\n  /**\n   * 初始化表格\n   */\n  public init(): void {\n    this.$thRow = this.$element.find('thead tr');\n    this.$tdRows = this.$element.find('tbody tr');\n    this.selectable = this.$element.hasClass('mdui-table-selectable');\n\n    this.updateThCheckbox();\n    this.updateTdCheckbox();\n    this.updateNumericCol();\n  }\n\n  /**\n   * 生成 checkbox 的 HTML 结构\n   * @param tag 标签名\n   */\n  private createCheckboxHTML(tag: string): string {\n    return (\n      `<${tag} class=\"mdui-table-cell-checkbox\">` +\n      '<label class=\"mdui-checkbox\">' +\n      '<input type=\"checkbox\"/>' +\n      '<i class=\"mdui-checkbox-icon\"></i>' +\n      '</label>' +\n      `</${tag}>`\n    );\n  }\n\n  /**\n   * 更新表头 checkbox 的状态\n   */\n  private updateThCheckboxStatus(): void {\n    const checkbox = this.$thCheckbox[0];\n    const selectedRow = this.selectedRow;\n    const tdRowsLength = this.$tdRows.length;\n\n    checkbox.checked = selectedRow === tdRowsLength;\n    checkbox.indeterminate = !!selectedRow && selectedRow !== tdRowsLength;\n  }\n\n  /**\n   * 更新表格行的 checkbox\n   */\n  private updateTdCheckbox(): void {\n    const rowSelectedClass = 'mdui-table-row-selected';\n\n    this.$tdRows.each((_, row) => {\n      const $row = $(row);\n\n      // 移除旧的 checkbox\n      $row.find('.mdui-table-cell-checkbox').remove();\n\n      if (!this.selectable) {\n        return;\n      }\n\n      // 创建 DOM\n      const $checkbox = $(this.createCheckboxHTML('td'))\n        .prependTo($row)\n        .find('input[type=\"checkbox\"]') as JQ<HTMLInputElement>;\n\n      // 默认选中的行\n      if ($row.hasClass(rowSelectedClass)) {\n        $checkbox[0].checked = true;\n        this.selectedRow++;\n      }\n\n      this.updateThCheckboxStatus();\n\n      // 绑定事件\n      $checkbox.on('change', () => {\n        if ($checkbox[0].checked) {\n          $row.addClass(rowSelectedClass);\n          this.selectedRow++;\n        } else {\n          $row.removeClass(rowSelectedClass);\n          this.selectedRow--;\n        }\n\n        this.updateThCheckboxStatus();\n      });\n\n      this.$tdCheckboxs = this.$tdCheckboxs.add($checkbox);\n    });\n  }\n\n  /**\n   * 更新表头的 checkbox\n   */\n  private updateThCheckbox(): void {\n    // 移除旧的 checkbox\n    this.$thRow.find('.mdui-table-cell-checkbox').remove();\n\n    if (!this.selectable) {\n      return;\n    }\n\n    this.$thCheckbox = $(this.createCheckboxHTML('th'))\n      .prependTo(this.$thRow)\n      .find('input[type=\"checkbox\"]')\n      .on('change', () => {\n        const isCheckedAll = this.$thCheckbox[0].checked;\n        this.selectedRow = isCheckedAll ? this.$tdRows.length : 0;\n\n        this.$tdCheckboxs.each((_, checkbox) => {\n          checkbox.checked = isCheckedAll;\n        });\n\n        this.$tdRows.each((_, row) => {\n          isCheckedAll\n            ? $(row).addClass('mdui-table-row-selected')\n            : $(row).removeClass('mdui-table-row-selected');\n        });\n      }) as JQ<HTMLInputElement>;\n  }\n\n  /**\n   * 更新数值列\n   */\n  private updateNumericCol(): void {\n    const numericClass = 'mdui-table-col-numeric';\n\n    this.$thRow.find('th').each((i, th) => {\n      const isNumericCol = $(th).hasClass(numericClass);\n\n      this.$tdRows.each((_, row) => {\n        const $td = $(row).find('td').eq(i);\n\n        isNumericCol\n          ? $td.addClass(numericClass)\n          : $td.removeClass(numericClass);\n      });\n    });\n  }\n}\n\nconst dataName = '_mdui_table';\n\n$(() => {\n  mdui.mutation('.mdui-table', function () {\n    const $element = $(this);\n\n    if (!$element.data(dataName)) {\n      $element.data(dataName, new Table($element));\n    }\n  });\n});\n\nmdui.updateTables = function (\n  selector?: Selector | HTMLElement | ArrayLike<HTMLElement>,\n): void {\n  const $elements = isUndefined(selector) ? $('.mdui-table') : $(selector);\n\n  $elements.each((_, element) => {\n    const $element = $(element);\n    const instance = $element.data(dataName);\n\n    if (instance) {\n      instance.init();\n    } else {\n      $element.data(dataName, new Table($element));\n    }\n  });\n};\n", "/**\n * touch 事件后的 500ms 内禁用 mousedown 事件\n *\n * 不支持触控的屏幕上事件顺序为 mousedown -> mouseup -> click\n * 支持触控的屏幕上事件顺序为 touchstart -> touchend -> mousedown -> mouseup -> click\n *\n * 在每一个事件中都使用 TouchHandler.isAllow(event) 判断事件是否可执行\n * 在 touchstart 和 touchmove、touchend、touchcancel\n *\n * (function () {\n *   $document\n *     .on(start, function (e) {\n *       if (!isAllow(e)) {\n *         return;\n *       }\n *       register(e);\n *       console.log(e.type);\n *     })\n *     .on(move, function (e) {\n *       if (!isAllow(e)) {\n *         return;\n *       }\n *       console.log(e.type);\n *     })\n *     .on(end, function (e) {\n *       if (!isAllow(e)) {\n *         return;\n *       }\n *       console.log(e.type);\n *     })\n *     .on(unlock, register);\n * })();\n */\n\nconst startEvent = 'touchstart mousedown';\nconst moveEvent = 'touchmove mousemove';\nconst endEvent = 'touchend mouseup';\nconst cancelEvent = 'touchcancel mouseleave';\nconst unlockEvent = 'touchend touchmove touchcancel';\n\nlet touches = 0;\n\n/**\n * 该事件是否被允许，在执行事件前调用该方法判断事件是否可以执行\n * 若已触发 touch 事件，则阻止之后的鼠标事件\n * @param event\n */\nfunction isAllow(event: Event): boolean {\n  return !(\n    touches &&\n    [\n      'mousedown',\n      'mouseup',\n      'mousemove',\n      'click',\n      'mouseover',\n      'mouseout',\n      'mouseenter',\n      'mouseleave',\n    ].indexOf(event.type) > -1\n  );\n}\n\n/**\n * 在 touchstart 和 touchmove、touchend、touchcancel 事件中调用该方法注册事件\n * @param event\n */\nfunction register(event: Event): void {\n  if (event.type === 'touchstart') {\n    // 触发了 touch 事件\n    touches += 1;\n  } else if (\n    ['touchmove', 'touchend', 'touchcancel'].indexOf(event.type) > -1\n  ) {\n    // touch 事件结束 500ms 后解除对鼠标事件的阻止\n    setTimeout(function () {\n      if (touches) {\n        touches -= 1;\n      }\n    }, 500);\n  }\n}\n\nexport {\n  startEvent,\n  moveEvent,\n  endEvent,\n  cancelEvent,\n  unlockEvent,\n  isAllow,\n  register,\n};\n", "/**\n * Inspired by https://github.com/nolimits4web/Framework7/blob/master/src/js/fast-clicks.js\n * https://github.com/nolimits4web/Framework7/blob/master/LICENSE\n *\n * Inspired by https://github.com/fians/Waves\n */\n\nimport $ from 'mdui.jq/es/$';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/attr';\nimport 'mdui.jq/es/methods/children';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/innerHeight';\nimport 'mdui.jq/es/methods/innerWidth';\nimport 'mdui.jq/es/methods/off';\nimport 'mdui.jq/es/methods/offset';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/parents';\nimport 'mdui.jq/es/methods/prependTo';\nimport 'mdui.jq/es/methods/prop';\nimport 'mdui.jq/es/methods/remove';\nimport { isUndefined } from 'mdui.jq/es/utils';\nimport '../../jq_extends/methods/reflow';\nimport '../../jq_extends/methods/transform';\nimport '../../jq_extends/methods/transitionEnd';\nimport { $document } from '../../utils/dom';\nimport {\n  cancelEvent,\n  endEvent,\n  isAllow,\n  moveEvent,\n  register,\n  startEvent,\n  unlockEvent,\n} from '../../utils/touchHandler';\n\n/**\n * 显示涟漪动画\n * @param event\n * @param $ripple\n */\nfunction show(event: Event, $ripple: JQ): void {\n  // 鼠标右键不产生涟漪\n  if (event instanceof MouseEvent && event.button === 2) {\n    return;\n  }\n\n  // 点击位置坐标\n  const touchPosition =\n    typeof TouchEvent !== 'undefined' &&\n    event instanceof TouchEvent &&\n    event.touches.length\n      ? event.touches[0]\n      : (event as MouseEvent);\n\n  const touchStartX = touchPosition.pageX;\n  const touchStartY = touchPosition.pageY;\n\n  // 涟漪位置\n  const offset = $ripple.offset();\n  const height = $ripple.innerHeight();\n  const width = $ripple.innerWidth();\n  const center = {\n    x: touchStartX - offset.left,\n    y: touchStartY - offset.top,\n  };\n  const diameter = Math.max(\n    Math.pow(Math.pow(height, 2) + Math.pow(width, 2), 0.5),\n    48,\n  );\n\n  // 涟漪扩散动画\n  const translate =\n    `translate3d(${-center.x + width / 2}px,` +\n    `${-center.y + height / 2}px, 0) scale(1)`;\n\n  // 涟漪的 DOM 结构，并缓存动画效果\n  $(\n    `<div class=\"mdui-ripple-wave\" ` +\n      `style=\"width:${diameter}px;height:${diameter}px;` +\n      `margin-top:-${diameter / 2}px;margin-left:-${diameter / 2}px;` +\n      `left:${center.x}px;top:${center.y}px;\"></div>`,\n  )\n    .data('_ripple_wave_translate', translate)\n    .prependTo($ripple)\n    .reflow()\n    .transform(translate);\n}\n\n/**\n * 隐藏并移除涟漪\n * @param $wave\n */\nfunction removeRipple($wave: JQ): void {\n  if (!$wave.length || $wave.data('_ripple_wave_removed')) {\n    return;\n  }\n\n  $wave.data('_ripple_wave_removed', true);\n\n  let removeTimer = setTimeout(() => $wave.remove(), 400);\n  const translate = $wave.data('_ripple_wave_translate');\n\n  $wave\n    .addClass('mdui-ripple-wave-fill')\n    .transform(translate.replace('scale(1)', 'scale(1.01)'))\n    .transitionEnd(() => {\n      clearTimeout(removeTimer);\n\n      $wave\n        .addClass('mdui-ripple-wave-out')\n        .transform(translate.replace('scale(1)', 'scale(1.01)'));\n\n      removeTimer = setTimeout(() => $wave.remove(), 700);\n\n      setTimeout(() => {\n        $wave.transitionEnd(() => {\n          clearTimeout(removeTimer);\n          $wave.remove();\n        });\n      }, 0);\n    });\n}\n\n/**\n * 隐藏涟漪动画\n * @param this\n */\nfunction hide(this: any): void {\n  const $ripple = $(this as HTMLElement);\n\n  $ripple.children('.mdui-ripple-wave').each((_, wave) => {\n    removeRipple($(wave));\n  });\n\n  $ripple.off(`${moveEvent} ${endEvent} ${cancelEvent}`, hide);\n}\n\n/**\n * 显示涟漪，并绑定 touchend 等事件\n * @param event\n */\nfunction showRipple(event: Event): void {\n  if (!isAllow(event)) {\n    return;\n  }\n\n  register(event);\n\n  // Chrome 59 点击滚动条时，会在 document 上触发事件\n  if (event.target === document) {\n    return;\n  }\n\n  const $target = $(event.target as HTMLElement);\n\n  // 获取含 .mdui-ripple 类的元素\n  const $ripple = $target.hasClass('mdui-ripple')\n    ? $target\n    : $target.parents('.mdui-ripple').first();\n\n  if (!$ripple.length) {\n    return;\n  }\n\n  // 禁用状态的元素上不产生涟漪效果\n  if ($ripple.prop('disabled') || !isUndefined($ripple.attr('disabled'))) {\n    return;\n  }\n\n  if (event.type === 'touchstart') {\n    let hidden = false;\n\n    // touchstart 触发指定时间后开始涟漪动画，避免手指滑动时也触发涟漪\n    let timer = setTimeout(() => {\n      timer = 0;\n      show(event, $ripple);\n    }, 200);\n\n    const hideRipple = (): void => {\n      // 如果手指没有移动，且涟漪动画还没有开始，则开始涟漪动画\n      if (timer) {\n        clearTimeout(timer);\n        timer = 0;\n        show(event, $ripple);\n      }\n\n      if (!hidden) {\n        hidden = true;\n        hide.call($ripple);\n      }\n    };\n\n    // 手指移动后，移除涟漪动画\n    const touchMove = (): void => {\n      if (timer) {\n        clearTimeout(timer);\n        timer = 0;\n      }\n\n      hideRipple();\n    };\n\n    $ripple.on('touchmove', touchMove).on('touchend touchcancel', hideRipple);\n  } else {\n    show(event, $ripple);\n    $ripple.on(`${moveEvent} ${endEvent} ${cancelEvent}`, hide);\n  }\n}\n\n$(() => {\n  $document.on(startEvent, showRipple).on(unlockEvent, register);\n});\n", "import $ from 'mdui.jq/es/$';\nimport extend from 'mdui.jq/es/functions/extend';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/appendTo';\nimport 'mdui.jq/es/methods/attr';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/is';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/outerHeight';\nimport 'mdui.jq/es/methods/parent';\nimport 'mdui.jq/es/methods/parents';\nimport 'mdui.jq/es/methods/remove';\nimport 'mdui.jq/es/methods/removeClass';\nimport 'mdui.jq/es/methods/text';\nimport 'mdui.jq/es/methods/trigger';\nimport 'mdui.jq/es/methods/val';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport { isUndefined } from 'mdui.jq/es/utils';\nimport mdui from '../../mdui';\nimport '../../global/mutation';\nimport { $document } from '../../utils/dom';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 动态修改了文本框后，需要调用该方法重新初始化文本框。\n     *\n     * 若传入了参数，则只初始化该参数对应的文本框。若没有传入参数，则重新初始化所有文本框。\n     * @param selector CSS 选择器、或 DOM 元素、或 DOM 元素组成的数组、或 JQ 对象\n     */\n    updateTextFields(\n      selector?: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    ): void;\n  }\n}\n\ntype INPUT_EVENT_DATA = {\n  reInit?: boolean;\n  domLoadedEvent?: boolean;\n};\n\nconst defaultData: INPUT_EVENT_DATA = {\n  reInit: false,\n  domLoadedEvent: false,\n};\n\n/**\n * 输入框事件\n * @param event\n * @param data\n */\nfunction inputEvent(event: Event, data: INPUT_EVENT_DATA = {}): void {\n  data = extend({}, defaultData, data);\n\n  const input = event.target as HTMLInputElement;\n  const $input = $(input);\n  const eventType = event.type;\n  const value = $input.val() as string;\n\n  // 文本框类型\n  const inputType = $input.attr('type') || '';\n  if (\n    ['checkbox', 'button', 'submit', 'range', 'radio', 'image'].indexOf(\n      inputType,\n    ) > -1\n  ) {\n    return;\n  }\n\n  const $textfield = $input.parent('.mdui-textfield');\n\n  // 输入框是否聚焦\n  if (eventType === 'focus') {\n    $textfield.addClass('mdui-textfield-focus');\n  }\n\n  if (eventType === 'blur') {\n    $textfield.removeClass('mdui-textfield-focus');\n  }\n\n  // 输入框是否为空\n  if (eventType === 'blur' || eventType === 'input') {\n    value\n      ? $textfield.addClass('mdui-textfield-not-empty')\n      : $textfield.removeClass('mdui-textfield-not-empty');\n  }\n\n  // 输入框是否禁用\n  input.disabled\n    ? $textfield.addClass('mdui-textfield-disabled')\n    : $textfield.removeClass('mdui-textfield-disabled');\n\n  // 表单验证\n  if (\n    (eventType === 'input' || eventType === 'blur') &&\n    !data.domLoadedEvent &&\n    input.validity\n  ) {\n    input.validity.valid\n      ? $textfield.removeClass('mdui-textfield-invalid-html5')\n      : $textfield.addClass('mdui-textfield-invalid-html5');\n  }\n\n  // textarea 高度自动调整\n  if ($input.is('textarea')) {\n    // IE bug：textarea 的值仅为多个换行，不含其他内容时，textarea 的高度不准确\n    //         此时，在计算高度前，在值的开头加入一个空格，计算完后，移除空格\n    const inputValue = value;\n    let hasExtraSpace = false;\n\n    if (inputValue.replace(/[\\r\\n]/g, '') === '') {\n      $input.val(' ' + inputValue);\n      hasExtraSpace = true;\n    }\n\n    // 设置 textarea 高度\n    $input.outerHeight('');\n    const height = $input.outerHeight();\n    const scrollHeight = input.scrollHeight;\n\n    if (scrollHeight > height) {\n      $input.outerHeight(scrollHeight);\n    }\n\n    // 计算完，还原 textarea 的值\n    if (hasExtraSpace) {\n      $input.val(inputValue);\n    }\n  }\n\n  // 实时字数统计\n  if (data.reInit) {\n    $textfield.find('.mdui-textfield-counter').remove();\n  }\n\n  const maxLength = $input.attr('maxlength');\n  if (maxLength) {\n    if (data.reInit || data.domLoadedEvent) {\n      $(\n        '<div class=\"mdui-textfield-counter\">' +\n          `<span class=\"mdui-textfield-counter-inputed\"></span> / ${maxLength}` +\n          '</div>',\n      ).appendTo($textfield);\n    }\n\n    $textfield\n      .find('.mdui-textfield-counter-inputed')\n      .text(value.length.toString());\n  }\n\n  // 含 帮助文本、错误提示、字数统计 时，增加文本框底部内边距\n  if (\n    $textfield.find('.mdui-textfield-helper').length ||\n    $textfield.find('.mdui-textfield-error').length ||\n    maxLength\n  ) {\n    $textfield.addClass('mdui-textfield-has-bottom');\n  }\n}\n\n$(() => {\n  // 绑定事件\n  $document.on(\n    'input focus blur',\n    '.mdui-textfield-input',\n    { useCapture: true },\n    inputEvent,\n  );\n\n  // 可展开文本框展开\n  $document.on(\n    'click',\n    '.mdui-textfield-expandable .mdui-textfield-icon',\n    function () {\n      $(this as HTMLElement)\n        .parents('.mdui-textfield')\n        .addClass('mdui-textfield-expanded')\n        .find('.mdui-textfield-input')[0]\n        .focus();\n    },\n  );\n\n  // 可展开文本框关闭\n  $document.on(\n    'click',\n    '.mdui-textfield-expanded .mdui-textfield-close',\n    function () {\n      $(this)\n        .parents('.mdui-textfield')\n        .removeClass('mdui-textfield-expanded')\n        .find('.mdui-textfield-input')\n        .val('');\n    },\n  );\n\n  /**\n   * 初始化文本框\n   */\n  mdui.mutation('.mdui-textfield', function () {\n    $(this).find('.mdui-textfield-input').trigger('input', {\n      domLoadedEvent: true,\n    });\n  });\n});\n\nmdui.updateTextFields = function (\n  selector?: Selector | HTMLElement | ArrayLike<HTMLElement>,\n): void {\n  const $elements = isUndefined(selector) ? $('.mdui-textfield') : $(selector);\n\n  $elements.each((_, element) => {\n    $(element).find('.mdui-textfield-input').trigger('input', {\n      reInit: true,\n    });\n  });\n};\n", "import $ from 'mdui.jq/es/$';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/append';\nimport 'mdui.jq/es/methods/attr';\nimport 'mdui.jq/es/methods/css';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/empty';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/parent';\nimport 'mdui.jq/es/methods/remove';\nimport 'mdui.jq/es/methods/removeClass';\nimport 'mdui.jq/es/methods/text';\nimport 'mdui.jq/es/methods/val';\nimport 'mdui.jq/es/methods/width';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport { isUndefined } from 'mdui.jq/es/utils';\nimport mdui from '../../mdui';\nimport { $document } from '../../utils/dom';\nimport {\n  endEvent,\n  isAllow,\n  register,\n  startEvent,\n  unlockEvent,\n} from '../../utils/touchHandler';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 动态修改了滑块后，需要调用该方法重新初始化滑块\n     *\n     * 若传入了参数，则只初始化该参数对应的滑块。若没有传入参数，则重新初始化所有滑块。\n     * @param selector CSS 选择器、或 DOM 元素、或 DOM 元素组成的数组、或 JQ 对象\n     */\n    updateSliders(\n      selector?: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    ): void;\n  }\n}\n\n/**\n * 滑块的值改变后修改滑块样式\n * @param $slider\n */\nfunction updateValueStyle($slider: JQ): void {\n  const data = $slider.data();\n\n  const $track = data._slider_$track;\n  const $fill = data._slider_$fill;\n  const $thumb = data._slider_$thumb;\n  const $input = data._slider_$input;\n  const min = data._slider_min;\n  const max = data._slider_max;\n  const isDisabled = data._slider_disabled;\n  const isDiscrete = data._slider_discrete;\n  const $thumbText = data._slider_$thumbText;\n  const value = $input.val();\n  const percent = ((value - min) / (max - min)) * 100;\n\n  $fill.width(`${percent}%`);\n  $track.width(`${100 - percent}%`);\n\n  if (isDisabled) {\n    $fill.css('padding-right', '6px');\n    $track.css('padding-left', '6px');\n  }\n\n  $thumb.css('left', `${percent}%`);\n\n  if (isDiscrete) {\n    $thumbText.text(value);\n  }\n\n  percent === 0\n    ? $slider.addClass('mdui-slider-zero')\n    : $slider.removeClass('mdui-slider-zero');\n}\n\n/**\n * 重新初始化滑块\n * @param $slider\n */\nfunction reInit($slider: JQ): void {\n  const $track = $('<div class=\"mdui-slider-track\"></div>');\n  const $fill = $('<div class=\"mdui-slider-fill\"></div>');\n  const $thumb = $('<div class=\"mdui-slider-thumb\"></div>');\n  const $input = $slider.find('input[type=\"range\"]') as JQ<HTMLInputElement>;\n  const isDisabled = $input[0].disabled;\n  const isDiscrete = $slider.hasClass('mdui-slider-discrete');\n\n  // 禁用状态\n  isDisabled\n    ? $slider.addClass('mdui-slider-disabled')\n    : $slider.removeClass('mdui-slider-disabled');\n\n  // 重新填充 HTML\n  $slider.find('.mdui-slider-track').remove();\n  $slider.find('.mdui-slider-fill').remove();\n  $slider.find('.mdui-slider-thumb').remove();\n  $slider.append($track).append($fill).append($thumb);\n\n  // 间续型滑块\n  let $thumbText = $();\n  if (isDiscrete) {\n    $thumbText = $('<span></span>');\n    $thumb.empty().append($thumbText);\n  }\n\n  $slider.data('_slider_$track', $track);\n  $slider.data('_slider_$fill', $fill);\n  $slider.data('_slider_$thumb', $thumb);\n  $slider.data('_slider_$input', $input);\n  $slider.data('_slider_min', $input.attr('min'));\n  $slider.data('_slider_max', $input.attr('max'));\n  $slider.data('_slider_disabled', isDisabled);\n  $slider.data('_slider_discrete', isDiscrete);\n  $slider.data('_slider_$thumbText', $thumbText);\n\n  // 设置默认值\n  updateValueStyle($slider);\n}\n\nconst rangeSelector = '.mdui-slider input[type=\"range\"]';\n\n$(() => {\n  // 滑块滑动事件\n  $document.on('input change', rangeSelector, function () {\n    const $slider = $(this).parent() as JQ<HTMLElement>;\n\n    updateValueStyle($slider);\n  });\n\n  // 开始触摸滑块事件\n  $document.on(startEvent, rangeSelector, function (event: Event) {\n    if (!isAllow(event)) {\n      return;\n    }\n\n    register(event);\n\n    if ((this as HTMLInputElement).disabled) {\n      return;\n    }\n\n    const $slider = $(this).parent() as JQ<HTMLElement>;\n\n    $slider.addClass('mdui-slider-focus');\n  });\n\n  // 结束触摸滑块事件\n  $document.on(endEvent, rangeSelector, function (event: Event) {\n    if (!isAllow(event)) {\n      return;\n    }\n\n    if ((this as HTMLInputElement).disabled) {\n      return;\n    }\n\n    const $slider = $(this).parent() as JQ<HTMLElement>;\n\n    $slider.removeClass('mdui-slider-focus');\n  });\n\n  $document.on(unlockEvent, rangeSelector, register);\n\n  /**\n   * 初始化滑块\n   */\n  mdui.mutation('.mdui-slider', function () {\n    reInit($(this));\n  });\n});\n\nmdui.updateSliders = function (\n  selector?: Selector | HTMLElement | ArrayLike<HTMLElement>,\n): void {\n  const $elements = isUndefined(selector) ? $('.mdui-slider') : $(selector);\n\n  $elements.each((_, element) => {\n    reInit($(element));\n  });\n};\n", "import $ from 'mdui.jq/es/$';\nimport extend from 'mdui.jq/es/functions/extend';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/css';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/last';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/parents';\nimport 'mdui.jq/es/methods/removeClass';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport mdui from '../../mdui';\nimport '../../jq_extends/methods/transitionEnd';\nimport { componentEvent } from '../../utils/componentEvent';\nimport { $document } from '../../utils/dom';\nimport { startEvent } from '../../utils/touchHandler';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 浮动操作按钮组件\n     *\n     * 请通过 `new mdui.Fab()` 调用\n     */\n    Fab: {\n      /**\n       * 实例化 Fab 组件\n       * @param selector CSS 选择器、或 DOM 元素、或 JQ 对象\n       * @param options 配置参数\n       */\n      new (\n        selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n        options?: OPTIONS,\n      ): Fab;\n    };\n  }\n}\n\ntype OPTIONS = {\n  /**\n   * 触发方式。`hover`: 鼠标悬浮触发；`click`: 点击触发\n   *\n   * 默认为 `hover`\n   */\n  trigger?: 'click' | 'hover';\n};\n\ntype STATE = 'opening' | 'opened' | 'closing' | 'closed';\ntype EVENT = 'open' | 'opened' | 'close' | 'closed';\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  trigger: 'hover',\n};\n\nclass Fab {\n  /**\n   * Fab 元素的 JQ 对象\n   */\n  public $element: JQ;\n\n  /**\n   * 配置参数\n   */\n  public options: OPTIONS = extend({}, DEFAULT_OPTIONS);\n\n  /**\n   * 当前 fab 的状态\n   */\n  private state: STATE = 'closed';\n\n  /**\n   * 按钮元素\n   */\n  private $btn: JQ;\n\n  /**\n   * 拨号菜单元素\n   */\n  private $dial: JQ;\n\n  /**\n   * 拨号菜单内的按钮\n   */\n  private $dialBtns: JQ;\n\n  public constructor(\n    selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    options: OPTIONS = {},\n  ) {\n    this.$element = $(selector).first();\n\n    extend(this.options, options);\n\n    this.$btn = this.$element.find('.mdui-fab');\n    this.$dial = this.$element.find('.mdui-fab-dial');\n    this.$dialBtns = this.$dial.find('.mdui-fab');\n\n    if (this.options.trigger === 'hover') {\n      this.$btn.on('touchstart mouseenter', () => this.open());\n      this.$element.on('mouseleave', () => this.close());\n    }\n\n    if (this.options.trigger === 'click') {\n      this.$btn.on(startEvent, () => this.open());\n    }\n\n    // 触摸屏幕其他地方关闭快速拨号\n    $document.on(startEvent, (event) => {\n      if ($(event.target as HTMLElement).parents('.mdui-fab-wrapper').length) {\n        return;\n      }\n\n      this.close();\n    });\n  }\n\n  /**\n   * 触发组件事件\n   * @param name\n   */\n  private triggerEvent(name: EVENT): void {\n    componentEvent(name, 'fab', this.$element, this);\n  }\n\n  /**\n   * 当前是否为打开状态\n   */\n  private isOpen(): boolean {\n    return this.state === 'opening' || this.state === 'opened';\n  }\n\n  /**\n   * 打开快速拨号菜单\n   */\n  public open(): void {\n    if (this.isOpen()) {\n      return;\n    }\n\n    // 为菜单中的按钮添加不同的 transition-delay\n    this.$dialBtns.each((index, btn) => {\n      const delay = `${15 * (this.$dialBtns.length - index)}ms`;\n\n      btn.style.transitionDelay = delay;\n      btn.style.webkitTransitionDelay = delay;\n    });\n\n    this.$dial.css('height', 'auto').addClass('mdui-fab-dial-show');\n\n    // 如果按钮中存在 .mdui-fab-opened 的图标，则进行图标切换\n    if (this.$btn.find('.mdui-fab-opened').length) {\n      this.$btn.addClass('mdui-fab-opened');\n    }\n\n    this.state = 'opening';\n    this.triggerEvent('open');\n\n    // 打开顺序为从下到上逐个打开，最上面的打开后才表示动画完成\n    this.$dialBtns.first().transitionEnd(() => {\n      if (this.$btn.hasClass('mdui-fab-opened')) {\n        this.state = 'opened';\n        this.triggerEvent('opened');\n      }\n    });\n  }\n\n  /**\n   * 关闭快速拨号菜单\n   */\n  public close(): void {\n    if (!this.isOpen()) {\n      return;\n    }\n\n    // 为菜单中的按钮添加不同的 transition-delay\n    this.$dialBtns.each((index, btn) => {\n      const delay = `${15 * index}ms`;\n\n      btn.style.transitionDelay = delay;\n      btn.style.webkitTransitionDelay = delay;\n    });\n\n    this.$dial.removeClass('mdui-fab-dial-show');\n    this.$btn.removeClass('mdui-fab-opened');\n    this.state = 'closing';\n    this.triggerEvent('close');\n\n    // 从上往下依次关闭，最后一个关闭后才表示动画完成\n    this.$dialBtns.last().transitionEnd(() => {\n      if (this.$btn.hasClass('mdui-fab-opened')) {\n        return;\n      }\n\n      this.state = 'closed';\n      this.triggerEvent('closed');\n      this.$dial.css('height', 0);\n    });\n  }\n\n  /**\n   * 切换快速拨号菜单的打开状态\n   */\n  public toggle(): void {\n    this.isOpen() ? this.close() : this.open();\n  }\n\n  /**\n   * 以动画的形式显示整个浮动操作按钮\n   */\n  public show(): void {\n    this.$element.removeClass('mdui-fab-hide');\n  }\n\n  /**\n   * 以动画的形式隐藏整个浮动操作按钮\n   */\n  public hide(): void {\n    this.$element.addClass('mdui-fab-hide');\n  }\n\n  /**\n   * 返回当前快速拨号菜单的打开状态。共包含四种状态：`opening`、`opened`、`closing`、`closed`\n   */\n  public getState(): STATE {\n    return this.state;\n  }\n}\n\nmdui.Fab = Fab;\n", "import $ from 'mdui.jq/es/$';\nimport mdui from '../../mdui';\nimport { $document } from '../../utils/dom';\nimport { parseOptions } from '../../utils/parseOptions';\nimport './index';\n\nconst customAttr = 'mdui-fab';\n\n$(() => {\n  // mouseenter 不冒泡，无法进行事件委托，这里用 mouseover 代替。\n  // 不管是 click 、 mouseover 还是 touchstart ，都先初始化。\n\n  $document.on(\n    'touchstart mousedown mouseover',\n    `[${customAttr}]`,\n    function () {\n      new mdui.Fab(\n        this as HTMLElement,\n        parseOptions(this as HTMLElement, customAttr),\n      );\n    },\n  );\n});\n", "/**\n * 最终生成的元素结构为：\n *  <select class=\"mdui-select\" mdui-select=\"{position: 'top'}\" style=\"display: none;\"> // $native\n *    <option value=\"1\">State 1</option>\n *    <option value=\"2\">State 2</option>\n *    <option value=\"3\" disabled=\"\">State 3</option>\n *  </select>\n *  <div class=\"mdui-select mdui-select-position-top\" style=\"\" id=\"88dec0e4-d4a2-c6d0-0e7f-1ba4501e0553\"> // $element\n *    <span class=\"mdui-select-selected\">State 1</span> // $selected\n *    <div class=\"mdui-select-menu\" style=\"transform-origin: center 100% 0px;\"> // $menu\n *      <div class=\"mdui-select-menu-item mdui-ripple\" selected=\"\">State 1</div> // $items\n *      <div class=\"mdui-select-menu-item mdui-ripple\">State 2</div>\n *      <div class=\"mdui-select-menu-item mdui-ripple\" disabled=\"\">State 3</div>\n *    </div>\n *  </div>\n */\n\nimport $ from 'mdui.jq/es/$';\nimport contains from 'mdui.jq/es/functions/contains';\nimport extend from 'mdui.jq/es/functions/extend';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/add';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/after';\nimport 'mdui.jq/es/methods/append';\nimport 'mdui.jq/es/methods/appendTo';\nimport 'mdui.jq/es/methods/attr';\nimport 'mdui.jq/es/methods/css';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/height';\nimport 'mdui.jq/es/methods/hide';\nimport 'mdui.jq/es/methods/index';\nimport 'mdui.jq/es/methods/innerWidth';\nimport 'mdui.jq/es/methods/is';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/remove';\nimport 'mdui.jq/es/methods/removeAttr';\nimport 'mdui.jq/es/methods/removeClass';\nimport 'mdui.jq/es/methods/show';\nimport 'mdui.jq/es/methods/text';\nimport 'mdui.jq/es/methods/trigger';\nimport 'mdui.jq/es/methods/val';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport mdui from '../../mdui';\nimport '../../jq_extends/methods/transitionEnd';\nimport '../../jq_extends/static/guid';\nimport { componentEvent } from '../../utils/componentEvent';\nimport { $document, $window } from '../../utils/dom';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 下拉选择组件\n     *\n     * 请通过 `new mdui.Select()` 调用\n     */\n    Select: {\n      /**\n       * 实例化 Select 组件\n       * @param selector CSS 选择器、或 DOM 元素、或 JQ 对象\n       * @param options 配置参数\n       */\n      new (\n        selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n        options?: OPTIONS,\n      ): Select;\n    };\n  }\n}\n\ntype OPTIONS = {\n  /**\n   * 下拉框位置：`auto`、`top`、`bottom`\n   */\n  position?: 'auto' | 'top' | 'bottom';\n\n  /**\n   * 菜单与窗口上下边框至少保持多少间距\n   */\n  gutter?: number;\n};\n\ntype STATE = 'closing' | 'closed' | 'opening' | 'opened';\ntype EVENT = 'open' | 'opened' | 'close' | 'closed';\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  position: 'auto',\n  gutter: 16,\n};\n\nclass Select {\n  /**\n   * 原生 `<select>` 元素的 JQ 对象\n   */\n  public $native: JQ<HTMLSelectElement>;\n\n  /**\n   * 生成的 `<div class=\"mdui-select\">` 元素的 JQ 对象\n   */\n  public $element: JQ = $();\n\n  /**\n   * 配置参数\n   */\n  public options: OPTIONS = extend({}, DEFAULT_OPTIONS);\n\n  /**\n   * select 的 size 属性的值，根据该值设置 select 的高度\n   */\n  private size = 0;\n\n  /**\n   * 占位元素，显示已选中菜单项的文本\n   */\n  private $selected: JQ = $();\n\n  /**\n   * 菜单项的外层元素的 JQ 对象\n   */\n  private $menu: JQ = $();\n\n  /**\n   * 菜单项数组的 JQ 对象\n   */\n  private $items: JQ = $();\n\n  /**\n   * 当前选中的菜单项的索引号\n   */\n  private selectedIndex = 0;\n\n  /**\n   * 当前选中菜单项的文本\n   */\n  private selectedText = '';\n\n  /**\n   * 当前选中菜单项的值\n   */\n  private selectedValue = '';\n\n  /**\n   * 唯一 ID\n   */\n  private uniqueID: string;\n\n  /**\n   * 当前 select 的状态\n   */\n  private state: STATE = 'closed';\n\n  public constructor(\n    selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    options: OPTIONS = {},\n  ) {\n    this.$native = $(selector).first() as JQ<HTMLSelectElement>;\n    this.$native.hide();\n\n    extend(this.options, options);\n\n    // 为当前 select 生成唯一 ID\n    this.uniqueID = $.guid();\n\n    // 生成 select\n    this.handleUpdate();\n\n    // 点击 select 外面区域关闭\n    $document.on('click touchstart', (event: Event) => {\n      const $target = $(event.target as HTMLElement);\n\n      if (\n        this.isOpen() &&\n        !$target.is(this.$element) &&\n        !contains(this.$element[0], $target[0])\n      ) {\n        this.close();\n      }\n    });\n  }\n\n  /**\n   * 调整菜单位置\n   */\n  private readjustMenu(): void {\n    const windowHeight = $window.height();\n\n    // mdui-select 高度\n    const elementHeight = this.$element.height();\n\n    // 菜单项高度\n    const $itemFirst = this.$items.first();\n    const itemHeight = $itemFirst.height();\n    const itemMargin = parseInt($itemFirst.css('margin-top'));\n\n    // 菜单高度\n    const menuWidth = this.$element.innerWidth() + 0.01; // 必须比真实宽度多一点，不然会出现省略号\n    let menuHeight = itemHeight * this.size + itemMargin * 2;\n\n    // mdui-select 在窗口中的位置\n    const elementTop = this.$element[0].getBoundingClientRect().top;\n\n    let transformOriginY: string;\n    let menuMarginTop: number;\n\n    if (this.options.position === 'bottom') {\n      menuMarginTop = elementHeight;\n      transformOriginY = '0px';\n    } else if (this.options.position === 'top') {\n      menuMarginTop = -menuHeight - 1;\n      transformOriginY = '100%';\n    } else {\n      // 菜单高度不能超过窗口高度\n      const menuMaxHeight = windowHeight - this.options.gutter! * 2;\n      if (menuHeight > menuMaxHeight) {\n        menuHeight = menuMaxHeight;\n      }\n\n      // 菜单的 margin-top\n      menuMarginTop = -(\n        itemMargin +\n        this.selectedIndex * itemHeight +\n        (itemHeight - elementHeight) / 2\n      );\n\n      const menuMaxMarginTop = -(\n        itemMargin +\n        (this.size - 1) * itemHeight +\n        (itemHeight - elementHeight) / 2\n      );\n      if (menuMarginTop < menuMaxMarginTop) {\n        menuMarginTop = menuMaxMarginTop;\n      }\n\n      // 菜单不能超出窗口\n      const menuTop = elementTop + menuMarginTop;\n      if (menuTop < this.options.gutter!) {\n        // 不能超出窗口上方\n        menuMarginTop = -(elementTop - this.options.gutter!);\n      } else if (menuTop + menuHeight + this.options.gutter! > windowHeight) {\n        // 不能超出窗口下方\n        menuMarginTop = -(\n          elementTop +\n          menuHeight +\n          this.options.gutter! -\n          windowHeight\n        );\n      }\n\n      // transform 的 Y 轴坐标\n      transformOriginY = `${\n        this.selectedIndex * itemHeight + itemHeight / 2 + itemMargin\n      }px`;\n    }\n\n    // 设置样式\n    this.$element.innerWidth(menuWidth);\n    this.$menu\n      .innerWidth(menuWidth)\n      .height(menuHeight)\n      .css({\n        'margin-top': menuMarginTop + 'px',\n        'transform-origin': 'center ' + transformOriginY + ' 0',\n      });\n  }\n\n  /**\n   * select 是否为打开状态\n   */\n  private isOpen(): boolean {\n    return this.state === 'opening' || this.state === 'opened';\n  }\n\n  /**\n   * 对原生 select 组件进行了修改后，需要调用该方法\n   */\n  public handleUpdate(): void {\n    if (this.isOpen()) {\n      this.close();\n    }\n\n    this.selectedValue = this.$native.val() as string;\n\n    // 保存菜单项数据的数组\n    type typeItemsData = {\n      value: string;\n      text: string;\n      disabled: boolean;\n      selected: boolean;\n      index: number;\n    };\n    const itemsData: typeItemsData[] = [];\n    this.$items = $();\n\n    // 生成 HTML\n    this.$native.find('option').each((index, option) => {\n      const text = option.textContent || '';\n      const value = option.value;\n      const disabled = option.disabled;\n      const selected = this.selectedValue === value;\n\n      itemsData.push({\n        value,\n        text,\n        disabled,\n        selected,\n        index,\n      });\n\n      if (selected) {\n        this.selectedText = text;\n        this.selectedIndex = index;\n      }\n\n      this.$items = this.$items.add(\n        '<div class=\"mdui-select-menu-item mdui-ripple\"' +\n          (disabled ? ' disabled' : '') +\n          (selected ? ' selected' : '') +\n          `>${text}</div>`,\n      );\n    });\n\n    this.$selected = $(\n      `<span class=\"mdui-select-selected\">${this.selectedText}</span>`,\n    );\n\n    this.$element = $(\n      `<div class=\"mdui-select mdui-select-position-${this.options.position}\" ` +\n        `style=\"${this.$native.attr('style')}\" ` +\n        `id=\"${this.uniqueID}\"></div>`,\n    )\n      .show()\n      .append(this.$selected);\n\n    this.$menu = $('<div class=\"mdui-select-menu\"></div>')\n      .appendTo(this.$element)\n      .append(this.$items);\n\n    $(`#${this.uniqueID}`).remove();\n    this.$native.after(this.$element);\n\n    // 根据 select 的 size 属性设置高度\n    this.size = parseInt(this.$native.attr('size') || '0');\n\n    if (this.size <= 0) {\n      this.size = this.$items.length;\n\n      if (this.size > 8) {\n        this.size = 8;\n      }\n    }\n\n    // 点击选项时关闭下拉菜单\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const that = this;\n    this.$items.on('click', function () {\n      if (that.state === 'closing') {\n        return;\n      }\n\n      const $item = $(this);\n      const index = $item.index();\n      const data = itemsData[index];\n\n      if (data.disabled) {\n        return;\n      }\n\n      that.$selected.text(data.text);\n      that.$native.val(data.value);\n      that.$items.removeAttr('selected');\n      $item.attr('selected', '');\n      that.selectedIndex = data.index;\n      that.selectedValue = data.value;\n      that.selectedText = data.text;\n      that.$native.trigger('change');\n      that.close();\n    });\n\n    // 点击 $element 时打开下拉菜单\n    this.$element.on('click', (event: Event) => {\n      const $target = $(event.target as HTMLElement);\n\n      // 在菜单上点击时不打开\n      if (\n        $target.is('.mdui-select-menu') ||\n        $target.is('.mdui-select-menu-item')\n      ) {\n        return;\n      }\n\n      this.toggle();\n    });\n  }\n\n  /**\n   * 动画结束的回调\n   */\n  private transitionEnd(): void {\n    this.$element.removeClass('mdui-select-closing');\n\n    if (this.state === 'opening') {\n      this.state = 'opened';\n      this.triggerEvent('opened');\n      this.$menu.css('overflow-y', 'auto');\n    }\n\n    if (this.state === 'closing') {\n      this.state = 'closed';\n      this.triggerEvent('closed');\n\n      // 恢复样式\n      this.$element.innerWidth('');\n      this.$menu.css({\n        'margin-top': '',\n        height: '',\n        width: '',\n      });\n    }\n  }\n\n  /**\n   * 触发组件事件\n   * @param name\n   */\n  private triggerEvent(name: EVENT): void {\n    componentEvent(name, 'select', this.$native, this);\n  }\n\n  /**\n   * 切换下拉菜单的打开状态\n   */\n  public toggle(): void {\n    this.isOpen() ? this.close() : this.open();\n  }\n\n  /**\n   * 打开下拉菜单\n   */\n  public open(): void {\n    if (this.isOpen()) {\n      return;\n    }\n\n    this.state = 'opening';\n    this.triggerEvent('open');\n    this.readjustMenu();\n    this.$element.addClass('mdui-select-open');\n    this.$menu.transitionEnd(() => this.transitionEnd());\n  }\n\n  /**\n   * 关闭下拉菜单\n   */\n  public close(): void {\n    if (!this.isOpen()) {\n      return;\n    }\n\n    this.state = 'closing';\n    this.triggerEvent('close');\n    this.$menu.css('overflow-y', '');\n    this.$element\n      .removeClass('mdui-select-open')\n      .addClass('mdui-select-closing');\n    this.$menu.transitionEnd(() => this.transitionEnd());\n  }\n\n  /**\n   * 获取当前菜单的状态。共包含四种状态：`opening`、`opened`、`closing`、`closed`\n   */\n  public getState(): STATE {\n    return this.state;\n  }\n}\n\nmdui.Select = Select;\n", "import $ from 'mdui.jq/es/$';\nimport mdui from '../../mdui';\nimport '../../global/mutation';\nimport { parseOptions } from '../../utils/parseOptions';\nimport './index';\n\nconst customAttr = 'mdui-select';\n\n$(() => {\n  mdui.mutation(`[${customAttr}]`, function () {\n    new mdui.Select(this, parseOptions(this, customAttr));\n  });\n});\n", "import $ from 'mdui.jq/es/$';\nimport mdui from '../../mdui';\nimport '../../global/mutation';\nimport '../headroom';\n\n$(() => {\n  // 滚动时隐藏应用栏\n  mdui.mutation('.mdui-appbar-scroll-hide', function () {\n    new mdui.Headroom(this);\n  });\n\n  // 滚动时只隐藏应用栏中的工具栏\n  mdui.mutation('.mdui-appbar-scroll-toolbar-hide', function () {\n    new mdui.Headroom(this, {\n      pinnedClass: 'mdui-headroom-pinned-toolbar',\n      unpinnedClass: 'mdui-headroom-unpinned-toolbar',\n    });\n  });\n});\n", "import $ from 'mdui.jq/es/$';\nimport extend from 'mdui.jq/es/functions/extend';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/appendTo';\nimport 'mdui.jq/es/methods/attr';\nimport 'mdui.jq/es/methods/children';\nimport 'mdui.jq/es/methods/css';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/eq';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/get';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/hide';\nimport 'mdui.jq/es/methods/index';\nimport 'mdui.jq/es/methods/innerWidth';\nimport 'mdui.jq/es/methods/offset';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/removeClass';\nimport 'mdui.jq/es/methods/show';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport { isNumber } from 'mdui.jq/es/utils';\nimport mdui from '../../mdui';\nimport '../../jq_extends/static/throttle';\nimport { componentEvent } from '../../utils/componentEvent';\nimport { $window } from '../../utils/dom';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * Tab 选项卡组件\n     *\n     * 请通过 `new mdui.Tab()` 调用\n     */\n    Tab: {\n      /**\n       * 实例化 Tab 组件\n       * @param selector CSS 选择器、或 DOM 元素、或 JQ 对象\n       * @param options 配置参数\n       */\n      new (\n        selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n        options?: OPTIONS,\n      ): Tab;\n    };\n  }\n}\n\ntype OPTIONS = {\n  /**\n   * 切换选项卡的触发方式。`click`: 点击切换；`hover`: 鼠标悬浮切换\n   */\n  trigger?: 'click' | 'hover';\n\n  /**\n   * 是否启用循环切换，若为 `true`，则最后一个选项激活时调用 `next` 方法将回到第一个选项，第一个选项激活时调用 `prev` 方法将回到最后一个选项。\n   */\n  loop?: boolean;\n};\n\ntype EVENT = 'change' | 'show';\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  trigger: 'click',\n  loop: false,\n};\n\nclass Tab {\n  /**\n   * tab 元素的 JQ 对象\n   */\n  public $element: JQ;\n\n  /**\n   * 配置参数\n   */\n  public options: OPTIONS = extend({}, DEFAULT_OPTIONS);\n\n  /**\n   * 当前激活的 tab 的索引号。为 -1 时表示没有激活的选项卡，或不存在选项卡\n   */\n  public activeIndex = -1;\n\n  /**\n   * 选项数组 JQ 对象\n   */\n  private $tabs: JQ;\n\n  /**\n   * 激活状态的 tab 底部的指示符\n   */\n  private $indicator: JQ;\n\n  public constructor(\n    selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    options: OPTIONS = {},\n  ) {\n    this.$element = $(selector).first();\n\n    extend(this.options, options);\n\n    this.$tabs = this.$element.children('a');\n    this.$indicator = $('<div class=\"mdui-tab-indicator\"></div>').appendTo(\n      this.$element,\n    );\n\n    // 根据 url hash 获取默认激活的选项卡\n    const hash = window.location.hash;\n    if (hash) {\n      this.$tabs.each((index, tab) => {\n        if ($(tab).attr('href') === hash) {\n          this.activeIndex = index;\n          return false;\n        }\n\n        return true;\n      });\n    }\n\n    // 含 .mdui-tab-active 的元素默认激活\n    if (this.activeIndex === -1) {\n      this.$tabs.each((index, tab) => {\n        if ($(tab).hasClass('mdui-tab-active')) {\n          this.activeIndex = index;\n          return false;\n        }\n\n        return true;\n      });\n    }\n\n    // 存在选项卡时，默认激活第一个选项卡\n    if (this.$tabs.length && this.activeIndex === -1) {\n      this.activeIndex = 0;\n    }\n\n    // 设置激活状态选项卡\n    this.setActive();\n\n    // 监听窗口大小变化事件，调整指示器位置\n    $window.on(\n      'resize',\n      $.throttle(() => this.setIndicatorPosition(), 100),\n    );\n\n    // 监听点击选项卡事件\n    this.$tabs.each((_, tab) => {\n      this.bindTabEvent(tab);\n    });\n  }\n\n  /**\n   * 指定选项卡是否已禁用\n   * @param $tab\n   */\n  private isDisabled($tab: JQ): boolean {\n    return $tab.attr('disabled') !== undefined;\n  }\n\n  /**\n   * 绑定在 Tab 上点击或悬浮的事件\n   * @param tab\n   */\n  private bindTabEvent(tab: HTMLElement): void {\n    const $tab = $(tab);\n\n    // 点击或鼠标移入触发的事件\n    const clickEvent = (): void | false => {\n      // 禁用状态的选项卡无法选中\n      if (this.isDisabled($tab)) {\n        return false;\n      }\n\n      this.activeIndex = this.$tabs.index(tab);\n      this.setActive();\n    };\n\n    // 无论 trigger 是 click 还是 hover，都会响应 click 事件\n    $tab.on('click', clickEvent);\n\n    // trigger 为 hover 时，额外响应 mouseenter 事件\n    if (this.options.trigger === 'hover') {\n      $tab.on('mouseenter', clickEvent);\n    }\n\n    // 阻止链接的默认点击动作\n    $tab.on('click', (): void | false => {\n      if (($tab.attr('href') || '').indexOf('#') === 0) {\n        return false;\n      }\n    });\n  }\n\n  /**\n   * 触发组件事件\n   * @param name\n   * @param $element\n   * @param parameters\n   */\n  private triggerEvent(name: EVENT, $element: JQ, parameters = {}): void {\n    componentEvent(name, 'tab', $element, this, parameters);\n  }\n\n  /**\n   * 设置激活状态的选项卡\n   */\n  private setActive(): void {\n    this.$tabs.each((index, tab) => {\n      const $tab = $(tab);\n      const targetId = $tab.attr('href') || '';\n\n      // 设置选项卡激活状态\n      if (index === this.activeIndex && !this.isDisabled($tab)) {\n        if (!$tab.hasClass('mdui-tab-active')) {\n          this.triggerEvent('change', this.$element, {\n            index: this.activeIndex,\n            id: targetId.substr(1),\n          });\n          this.triggerEvent('show', $tab);\n\n          $tab.addClass('mdui-tab-active');\n        }\n\n        $(targetId).show();\n        this.setIndicatorPosition();\n      } else {\n        $tab.removeClass('mdui-tab-active');\n        $(targetId).hide();\n      }\n    });\n  }\n\n  /**\n   * 设置选项卡指示器的位置\n   */\n  private setIndicatorPosition(): void {\n    // 选项卡数量为 0 时，不显示指示器\n    if (this.activeIndex === -1) {\n      this.$indicator.css({\n        left: 0,\n        width: 0,\n      });\n\n      return;\n    }\n\n    const $activeTab = this.$tabs.eq(this.activeIndex);\n\n    if (this.isDisabled($activeTab)) {\n      return;\n    }\n\n    const activeTabOffset = $activeTab.offset();\n\n    this.$indicator.css({\n      left: `${\n        activeTabOffset.left +\n        this.$element[0].scrollLeft -\n        this.$element[0].getBoundingClientRect().left\n      }px`,\n      width: `${$activeTab.innerWidth()}px`,\n    });\n  }\n\n  /**\n   * 切换到下一个选项卡\n   */\n  public next(): void {\n    if (this.activeIndex === -1) {\n      return;\n    }\n\n    if (this.$tabs.length > this.activeIndex + 1) {\n      this.activeIndex++;\n    } else if (this.options.loop) {\n      this.activeIndex = 0;\n    }\n\n    this.setActive();\n  }\n\n  /**\n   * 切换到上一个选项卡\n   */\n  public prev(): void {\n    if (this.activeIndex === -1) {\n      return;\n    }\n\n    if (this.activeIndex > 0) {\n      this.activeIndex--;\n    } else if (this.options.loop) {\n      this.activeIndex = this.$tabs.length - 1;\n    }\n\n    this.setActive();\n  }\n\n  /**\n   * 显示指定索引号、或指定id的选项卡\n   * @param index 索引号、或id\n   */\n  public show(index: number | string): void {\n    if (this.activeIndex === -1) {\n      return;\n    }\n\n    if (isNumber(index)) {\n      this.activeIndex = index;\n    } else {\n      this.$tabs.each((i, tab): void | false => {\n        if (tab.id === index) {\n          this.activeIndex === i;\n          return false;\n        }\n      });\n    }\n\n    this.setActive();\n  }\n\n  /**\n   * 在父元素的宽度变化时，需要调用该方法重新调整指示器位置\n   * 在添加或删除选项卡时，需要调用该方法\n   */\n  public handleUpdate(): void {\n    const $oldTabs = this.$tabs; // 旧的 tabs JQ对象\n    const $newTabs = this.$element.children('a'); // 新的 tabs JQ对象\n    const oldTabsElement = $oldTabs.get(); // 旧的 tabs 元素数组\n    const newTabsElement = $newTabs.get(); // 新的 tabs 元素数组\n\n    if (!$newTabs.length) {\n      this.activeIndex = -1;\n      this.$tabs = $newTabs;\n      this.setIndicatorPosition();\n\n      return;\n    }\n\n    // 重新遍历选项卡，找出新增的选项卡\n    $newTabs.each((index, tab) => {\n      // 有新增的选项卡\n      if (oldTabsElement.indexOf(tab) < 0) {\n        this.bindTabEvent(tab);\n\n        if (this.activeIndex === -1) {\n          this.activeIndex = 0;\n        } else if (index <= this.activeIndex) {\n          this.activeIndex++;\n        }\n      }\n    });\n\n    // 找出被移除的选项卡\n    $oldTabs.each((index, tab) => {\n      // 有被移除的选项卡\n      if (newTabsElement.indexOf(tab) < 0) {\n        if (index < this.activeIndex) {\n          this.activeIndex--;\n        } else if (index === this.activeIndex) {\n          this.activeIndex = 0;\n        }\n      }\n    });\n\n    this.$tabs = $newTabs;\n\n    this.setActive();\n  }\n}\n\nmdui.Tab = Tab;\n", "import $ from 'mdui.jq/es/$';\nimport mdui from '../../mdui';\nimport '../../global/mutation';\nimport { parseOptions } from '../../utils/parseOptions';\nimport './index';\n\nconst customAttr = 'mdui-tab';\n\n$(() => {\n  mdui.mutation(`[${customAttr}]`, function () {\n    new mdui.Tab(this, parseOptions(this, customAttr));\n  });\n});\n", "/**\n * 在桌面设备上默认显示抽屉栏，不显示遮罩层\n * 在手机和平板设备上默认不显示抽屉栏，始终显示遮罩层，且覆盖导航栏\n */\n\nimport $ from 'mdui.jq/es/$';\nimport extend from 'mdui.jq/es/functions/extend';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/off';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/one';\nimport 'mdui.jq/es/methods/removeClass';\nimport 'mdui.jq/es/methods/width';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport mdui from '../../mdui';\nimport '../../jq_extends/methods/transitionEnd';\nimport '../../jq_extends/static/hideOverlay';\nimport '../../jq_extends/static/lockScreen';\nimport '../../jq_extends/static/showOverlay';\nimport '../../jq_extends/static/throttle';\nimport '../../jq_extends/static/unlockScreen';\nimport { componentEvent } from '../../utils/componentEvent';\nimport { $window } from '../../utils/dom';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * Drawer 组件\n     *\n     * 请通过 `new mdui.Drawer()` 调用\n     */\n    Drawer: {\n      /**\n       * 实例化 Drawer 组件\n       * @param selector CSS 选择器、或 DOM 元素、或 JQ 对象\n       * @param options 配置参数\n       */\n      new (\n        selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n        options?: OPTIONS,\n      ): Drawer;\n    };\n  }\n}\n\ntype OPTIONS = {\n  /**\n   * 打开抽屉栏时是否显示遮罩层。该参数只对中等屏幕及以上的设备有效，在超小屏和小屏设备上始终会显示遮罩层。\n   */\n  overlay?: boolean;\n\n  /**\n   * 是否启用滑动手势。\n   */\n  swipe?: boolean;\n};\n\ntype STATE = 'opening' | 'opened' | 'closing' | 'closed';\ntype EVENT = 'open' | 'opened' | 'close' | 'closed';\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  overlay: false,\n  swipe: false,\n};\n\nclass Drawer {\n  /**\n   * drawer 元素的 JQ 对象\n   */\n  public $element: JQ;\n\n  /**\n   * 配置参数\n   */\n  public options: OPTIONS = extend({}, DEFAULT_OPTIONS);\n\n  /**\n   * 当前是否显示着遮罩层\n   */\n  private overlay = false;\n\n  /**\n   * 抽屉栏的位置\n   */\n  private position: 'left' | 'right';\n\n  /**\n   * 当前抽屉栏状态\n   */\n  private state: STATE;\n\n  public constructor(\n    selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    options: OPTIONS = {},\n  ) {\n    this.$element = $(selector).first();\n\n    extend(this.options, options);\n\n    this.position = this.$element.hasClass('mdui-drawer-right')\n      ? 'right'\n      : 'left';\n\n    if (this.$element.hasClass('mdui-drawer-close')) {\n      this.state = 'closed';\n    } else if (this.$element.hasClass('mdui-drawer-open')) {\n      this.state = 'opened';\n    } else if (this.isDesktop()) {\n      this.state = 'opened';\n    } else {\n      this.state = 'closed';\n    }\n\n    // 浏览器窗口大小调整时\n    $window.on(\n      'resize',\n      $.throttle(() => {\n        if (this.isDesktop()) {\n          // 由手机平板切换到桌面时\n          // 如果显示着遮罩，则隐藏遮罩\n          if (this.overlay && !this.options.overlay) {\n            $.hideOverlay();\n            this.overlay = false;\n            $.unlockScreen();\n          }\n\n          // 没有强制关闭，则状态为打开状态\n          if (!this.$element.hasClass('mdui-drawer-close')) {\n            this.state = 'opened';\n          }\n        } else if (!this.overlay && this.state === 'opened') {\n          // 由桌面切换到手机平板时。如果抽屉栏是打开着的且没有遮罩层，则关闭抽屉栏\n          if (this.$element.hasClass('mdui-drawer-open')) {\n            $.showOverlay();\n            this.overlay = true;\n            $.lockScreen();\n\n            $('.mdui-overlay').one('click', () => this.close());\n          } else {\n            this.state = 'closed';\n          }\n        }\n      }, 100),\n    );\n\n    // 绑定关闭按钮事件\n    this.$element.find('[mdui-drawer-close]').each((_, close) => {\n      $(close).on('click', () => this.close());\n    });\n\n    this.swipeSupport();\n  }\n\n  /**\n   * 是否是桌面设备\n   */\n  private isDesktop(): boolean {\n    return $window.width() >= 1024;\n  }\n\n  /**\n   * 滑动手势支持\n   */\n  private swipeSupport(): void {\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const that = this;\n\n    // 抽屉栏滑动手势控制\n    let openNavEventHandler: (event: Event) => void;\n    let touchStartX: number;\n    let touchStartY: number;\n    let swipeStartX: number;\n    let swiping: null | 'opening' | 'closing' = null;\n    let maybeSwiping = false;\n    const $body = $('body');\n\n    // 手势触发的范围\n    const swipeAreaWidth = 24;\n\n    function setPosition(translateX: number): void {\n      const rtlTranslateMultiplier = that.position === 'right' ? -1 : 1;\n      const transformCSS = `translate(${\n        -1 * rtlTranslateMultiplier * translateX\n      }px, 0) !important;`;\n      const transitionCSS = 'initial !important;';\n\n      that.$element.css(\n        'cssText',\n        `transform: ${transformCSS}; transition: ${transitionCSS};`,\n      );\n    }\n\n    function cleanPosition(): void {\n      that.$element[0].style.transform = '';\n      that.$element[0].style.webkitTransform = '';\n      that.$element[0].style.transition = '';\n      that.$element[0].style.webkitTransition = '';\n    }\n\n    function getMaxTranslateX(): number {\n      return that.$element.width() + 10;\n    }\n\n    function getTranslateX(currentX: number): number {\n      return Math.min(\n        Math.max(\n          swiping === 'closing'\n            ? swipeStartX - currentX\n            : getMaxTranslateX() + swipeStartX - currentX,\n          0,\n        ),\n        getMaxTranslateX(),\n      );\n    }\n\n    function onBodyTouchEnd(event?: Event): void {\n      if (swiping) {\n        let touchX = (event as TouchEvent).changedTouches[0].pageX;\n        if (that.position === 'right') {\n          touchX = $body.width() - touchX;\n        }\n\n        const translateRatio = getTranslateX(touchX) / getMaxTranslateX();\n\n        maybeSwiping = false;\n        const swipingState = swiping;\n        swiping = null;\n\n        if (swipingState === 'opening') {\n          if (translateRatio < 0.92) {\n            cleanPosition();\n            that.open();\n          } else {\n            cleanPosition();\n          }\n        } else {\n          if (translateRatio > 0.08) {\n            cleanPosition();\n            that.close();\n          } else {\n            cleanPosition();\n          }\n        }\n\n        $.unlockScreen();\n      } else {\n        maybeSwiping = false;\n      }\n\n      $body.off({\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        touchmove: onBodyTouchMove,\n        touchend: onBodyTouchEnd,\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        touchcancel: onBodyTouchMove,\n      });\n    }\n\n    function onBodyTouchMove(event: Event): void {\n      let touchX = (event as TouchEvent).touches[0].pageX;\n      if (that.position === 'right') {\n        touchX = $body.width() - touchX;\n      }\n\n      const touchY = (event as TouchEvent).touches[0].pageY;\n\n      if (swiping) {\n        setPosition(getTranslateX(touchX));\n      } else if (maybeSwiping) {\n        const dXAbs = Math.abs(touchX - touchStartX);\n        const dYAbs = Math.abs(touchY - touchStartY);\n        const threshold = 8;\n\n        if (dXAbs > threshold && dYAbs <= threshold) {\n          swipeStartX = touchX;\n          swiping = that.state === 'opened' ? 'closing' : 'opening';\n          $.lockScreen();\n          setPosition(getTranslateX(touchX));\n        } else if (dXAbs <= threshold && dYAbs > threshold) {\n          onBodyTouchEnd();\n        }\n      }\n    }\n\n    function onBodyTouchStart(event: Event): void {\n      touchStartX = (event as TouchEvent).touches[0].pageX;\n      if (that.position === 'right') {\n        touchStartX = $body.width() - touchStartX;\n      }\n\n      touchStartY = (event as TouchEvent).touches[0].pageY;\n\n      if (that.state !== 'opened') {\n        if (\n          touchStartX > swipeAreaWidth ||\n          openNavEventHandler !== onBodyTouchStart\n        ) {\n          return;\n        }\n      }\n\n      maybeSwiping = true;\n\n      $body.on({\n        touchmove: onBodyTouchMove,\n        touchend: onBodyTouchEnd,\n        touchcancel: onBodyTouchMove,\n      });\n    }\n\n    function enableSwipeHandling(): void {\n      if (!openNavEventHandler) {\n        $body.on('touchstart', onBodyTouchStart);\n        openNavEventHandler = onBodyTouchStart;\n      }\n    }\n\n    if (this.options.swipe) {\n      enableSwipeHandling();\n    }\n  }\n\n  /**\n   * 触发组件事件\n   * @param name\n   */\n  private triggerEvent(name: EVENT): void {\n    componentEvent(name, 'drawer', this.$element, this);\n  }\n\n  /**\n   * 动画结束回调\n   */\n  private transitionEnd(): void {\n    if (this.$element.hasClass('mdui-drawer-open')) {\n      this.state = 'opened';\n      this.triggerEvent('opened');\n    } else {\n      this.state = 'closed';\n      this.triggerEvent('closed');\n    }\n  }\n\n  /**\n   * 是否处于打开状态\n   */\n  private isOpen(): boolean {\n    return this.state === 'opening' || this.state === 'opened';\n  }\n\n  /**\n   * 打开抽屉栏\n   */\n  public open(): void {\n    if (this.isOpen()) {\n      return;\n    }\n\n    this.state = 'opening';\n    this.triggerEvent('open');\n\n    if (!this.options.overlay) {\n      $('body').addClass(`mdui-drawer-body-${this.position}`);\n    }\n\n    this.$element\n      .removeClass('mdui-drawer-close')\n      .addClass('mdui-drawer-open')\n      .transitionEnd(() => this.transitionEnd());\n\n    if (!this.isDesktop() || this.options.overlay) {\n      this.overlay = true;\n      $.showOverlay().one('click', () => this.close());\n      $.lockScreen();\n    }\n  }\n\n  /**\n   * 关闭抽屉栏\n   */\n  public close(): void {\n    if (!this.isOpen()) {\n      return;\n    }\n\n    this.state = 'closing';\n    this.triggerEvent('close');\n\n    if (!this.options.overlay) {\n      $('body').removeClass(`mdui-drawer-body-${this.position}`);\n    }\n\n    this.$element\n      .addClass('mdui-drawer-close')\n      .removeClass('mdui-drawer-open')\n      .transitionEnd(() => this.transitionEnd());\n\n    if (this.overlay) {\n      $.hideOverlay();\n      this.overlay = false;\n      $.unlockScreen();\n    }\n  }\n\n  /**\n   * 切换抽屉栏打开/关闭状态\n   */\n  public toggle(): void {\n    this.isOpen() ? this.close() : this.open();\n  }\n\n  /**\n   * 返回当前抽屉栏的状态。共包含四种状态：`opening`、`opened`、`closing`、`closed`\n   */\n  public getState(): STATE {\n    return this.state;\n  }\n}\n\nmdui.Drawer = Drawer;\n", "import $ from 'mdui.jq/es/$';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/on';\nimport mdui from '../../mdui';\nimport '../../global/mutation';\nimport { parseOptions } from '../../utils/parseOptions';\nimport './index';\n\nconst customAttr = 'mdui-drawer';\n\ntype OPTIONS = {\n  target: string;\n  overlay?: boolean;\n  swipe?: boolean;\n};\n\n$(() => {\n  mdui.mutation(`[${customAttr}]`, function () {\n    const $element = $(this);\n    const options = parseOptions(this, customAttr) as OPTIONS;\n    const selector = options.target;\n    // @ts-ignore\n    delete options.target;\n\n    const $drawer = $(selector).first();\n    const instance = new mdui.Drawer($drawer, options);\n\n    $element.on('click', () => instance.toggle());\n  });\n});\n", "import { isUndefined } from 'mdui.jq/es/utils';\nimport PlainObject from 'mdui.jq/es/interfaces/PlainObject';\n\ntype Func = () => any;\n\nconst container: PlainObject<Func[]> = {};\n\n/**\n * 根据队列名，获取队列中所有函数\n * @param name 队列名\n */\nfunction queue(name: string): Func[];\n\n/**\n * 写入队列\n * @param name 队列名\n * @param func 函数\n */\nfunction queue(name: string, func: Func): void;\n\nfunction queue(name: string, func?: Func): void | Func[] {\n  if (isUndefined(container[name])) {\n    container[name] = [];\n  }\n\n  if (isUndefined(func)) {\n    return container[name];\n  }\n\n  container[name].push(func);\n}\n\n/**\n * 从队列中移除第一个函数，并执行该函数\n * @param name 队列满\n */\nfunction dequeue(name: string): void {\n  if (isUndefined(container[name])) {\n    return;\n  }\n\n  if (!container[name].length) {\n    return;\n  }\n\n  const func = container[name].shift()!;\n\n  func();\n}\n\nexport { queue, dequeue };\n", "import $ from 'mdui.jq/es/$';\nimport contains from 'mdui.jq/es/functions/contains';\nimport extend from 'mdui.jq/es/functions/extend';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/append';\nimport 'mdui.jq/es/methods/children';\nimport 'mdui.jq/es/methods/css';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/height';\nimport 'mdui.jq/es/methods/hide';\nimport 'mdui.jq/es/methods/innerHeight';\nimport 'mdui.jq/es/methods/off';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/remove';\nimport 'mdui.jq/es/methods/removeClass';\nimport 'mdui.jq/es/methods/show';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport '../../jq_extends/methods/transitionEnd';\nimport '../../jq_extends/static/hideOverlay';\nimport '../../jq_extends/static/lockScreen';\nimport '../../jq_extends/static/showOverlay';\nimport '../../jq_extends/static/throttle';\nimport '../../jq_extends/static/unlockScreen';\nimport { componentEvent } from '../../utils/componentEvent';\nimport { $window } from '../../utils/dom';\nimport { dequeue, queue } from '../../utils/queue';\n\ntype OPTIONS = {\n  /**\n   * 打开对话框时是否添加 url hash，若为 `true`，则打开对话框后可用过浏览器的后退按钮或 Android 的返回键关闭对话框。\n   */\n  history?: boolean;\n\n  /**\n   * 打开对话框时是否显示遮罩。\n   */\n  overlay?: boolean;\n\n  /**\n   * 是否模态化对话框。为 `false` 时点击对话框外面的区域时关闭对话框，否则不关闭。\n   */\n  modal?: boolean;\n\n  /**\n   * 按下 Esc 键时是否关闭对话框。\n   */\n  closeOnEsc?: boolean;\n\n  /**\n   * 按下取消按钮时是否关闭对话框。\n   */\n  closeOnCancel?: boolean;\n\n  /**\n   * 按下确认按钮时是否关闭对话框。\n   */\n  closeOnConfirm?: boolean;\n\n  /**\n   * 关闭对话框后是否自动销毁对话框。\n   */\n  destroyOnClosed?: boolean;\n};\n\ntype STATE = 'opening' | 'opened' | 'closing' | 'closed';\ntype EVENT = 'open' | 'opened' | 'close' | 'closed' | 'cancel' | 'confirm';\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  history: true,\n  overlay: true,\n  modal: false,\n  closeOnEsc: true,\n  closeOnCancel: true,\n  closeOnConfirm: true,\n  destroyOnClosed: false,\n};\n\n/**\n * 当前显示的对话框实例\n */\nlet currentInst: null | Dialog = null;\n\n/**\n * 队列名\n */\nconst queueName = '_mdui_dialog';\n\n/**\n * 窗口是否已锁定\n */\nlet isLockScreen = false;\n\n/**\n * 遮罩层元素\n */\nlet $overlay: null | JQ;\n\nclass Dialog {\n  /**\n   * dialog 元素的 JQ 对象\n   */\n  public $element: JQ;\n\n  /**\n   * 配置参数\n   */\n  public options: OPTIONS = extend({}, DEFAULT_OPTIONS);\n\n  /**\n   * 当前 dialog 的状态\n   */\n  public state: STATE = 'closed';\n\n  /**\n   * dialog 元素是否是动态添加的\n   */\n  private append = false;\n\n  public constructor(\n    selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    options: OPTIONS = {},\n  ) {\n    this.$element = $(selector).first();\n\n    // 如果对话框元素没有在当前文档中，则需要添加\n    if (!contains(document.body, this.$element[0])) {\n      this.append = true;\n      $('body').append(this.$element);\n    }\n\n    extend(this.options, options);\n\n    // 绑定取消按钮事件\n    this.$element.find('[mdui-dialog-cancel]').each((_, cancel) => {\n      $(cancel).on('click', () => {\n        this.triggerEvent('cancel');\n\n        if (this.options.closeOnCancel) {\n          this.close();\n        }\n      });\n    });\n\n    // 绑定确认按钮事件\n    this.$element.find('[mdui-dialog-confirm]').each((_, confirm) => {\n      $(confirm).on('click', () => {\n        this.triggerEvent('confirm');\n\n        if (this.options.closeOnConfirm) {\n          this.close();\n        }\n      });\n    });\n\n    // 绑定关闭按钮事件\n    this.$element.find('[mdui-dialog-close]').each((_, close) => {\n      $(close).on('click', () => this.close());\n    });\n  }\n\n  /**\n   * 触发组件事件\n   * @param name\n   */\n  private triggerEvent(name: EVENT): void {\n    componentEvent(name, 'dialog', this.$element, this);\n  }\n\n  /**\n   * 窗口宽度变化，或对话框内容变化时，调整对话框位置和对话框内的滚动条\n   */\n  private readjust(): void {\n    if (!currentInst) {\n      return;\n    }\n\n    const $element = currentInst.$element;\n    const $title = $element.children('.mdui-dialog-title');\n    const $content = $element.children('.mdui-dialog-content');\n    const $actions = $element.children('.mdui-dialog-actions');\n\n    // 调整 dialog 的 top 和 height 值\n    $element.height('');\n    $content.height('');\n\n    const elementHeight = $element.height();\n    $element.css({\n      top: `${($window.height() - elementHeight) / 2}px`,\n      height: `${elementHeight}px`,\n    });\n\n    // 调整 mdui-dialog-content 的高度\n    $content.innerHeight(\n      elementHeight -\n        ($title.innerHeight() || 0) -\n        ($actions.innerHeight() || 0),\n    );\n  }\n\n  /**\n   * hashchange 事件触发时关闭对话框\n   */\n  private hashchangeEvent(): void {\n    if (window.location.hash.substring(1).indexOf('mdui-dialog') < 0) {\n      currentInst!.close(true);\n    }\n  }\n\n  /**\n   * 点击遮罩层关闭对话框\n   * @param event\n   */\n  private overlayClick(event: Event): void {\n    if (\n      $(event.target as HTMLElement).hasClass('mdui-overlay') &&\n      currentInst\n    ) {\n      currentInst.close();\n    }\n  }\n\n  /**\n   * 动画结束回调\n   */\n  private transitionEnd(): void {\n    if (this.$element.hasClass('mdui-dialog-open')) {\n      this.state = 'opened';\n      this.triggerEvent('opened');\n    } else {\n      this.state = 'closed';\n      this.triggerEvent('closed');\n      this.$element.hide();\n\n      // 所有对话框都关闭，且当前没有打开的对话框时，解锁屏幕\n      if (!queue(queueName).length && !currentInst && isLockScreen) {\n        $.unlockScreen();\n        isLockScreen = false;\n      }\n\n      $window.off('resize', $.throttle(this.readjust, 100));\n\n      if (this.options.destroyOnClosed) {\n        this.destroy();\n      }\n    }\n  }\n\n  /**\n   * 打开指定对话框\n   */\n  private doOpen(): void {\n    currentInst = this;\n\n    if (!isLockScreen) {\n      $.lockScreen();\n      isLockScreen = true;\n    }\n\n    this.$element.show();\n    this.readjust();\n\n    $window.on('resize', $.throttle(this.readjust, 100));\n\n    // 打开消息框\n    this.state = 'opening';\n    this.triggerEvent('open');\n    this.$element\n      .addClass('mdui-dialog-open')\n      .transitionEnd(() => this.transitionEnd());\n\n    // 不存在遮罩层元素时，添加遮罩层\n    if (!$overlay) {\n      $overlay = $.showOverlay(5100);\n    }\n\n    // 点击遮罩层时是否关闭对话框\n    if (this.options.modal) {\n      $overlay.off('click', this.overlayClick);\n    } else {\n      $overlay.on('click', this.overlayClick);\n    }\n\n    // 是否显示遮罩层，不显示时，把遮罩层背景透明\n    $overlay.css('opacity', this.options.overlay ? '' : 0);\n\n    if (this.options.history) {\n      // 如果 hash 中原来就有 mdui-dialog，先删除，避免后退历史纪录后仍然有 mdui-dialog 导致无法关闭\n      // 包括 mdui-dialog 和 &mdui-dialog 和 ?mdui-dialog\n      let hash = window.location.hash.substring(1);\n      if (hash.indexOf('mdui-dialog') > -1) {\n        hash = hash.replace(/[&?]?mdui-dialog/g, '');\n      }\n\n      // 后退按钮关闭对话框\n      if (hash) {\n        window.location.hash = `${hash}${\n          hash.indexOf('?') > -1 ? '&' : '?'\n        }mdui-dialog`;\n      } else {\n        window.location.hash = 'mdui-dialog';\n      }\n\n      $window.on('hashchange', this.hashchangeEvent);\n    }\n  }\n\n  /**\n   * 当前对话框是否为打开状态\n   */\n  private isOpen(): boolean {\n    return this.state === 'opening' || this.state === 'opened';\n  }\n\n  /**\n   * 打开对话框\n   */\n  public open(): void {\n    if (this.isOpen()) {\n      return;\n    }\n\n    // 如果当前有正在打开或已经打开的对话框,或队列不为空，则先加入队列，等旧对话框开始关闭时再打开\n    if (\n      (currentInst &&\n        (currentInst.state === 'opening' || currentInst.state === 'opened')) ||\n      queue(queueName).length\n    ) {\n      queue(queueName, () => this.doOpen());\n\n      return;\n    }\n\n    this.doOpen();\n  }\n\n  /**\n   * 关闭对话框\n   */\n  public close(historyBack = false): void {\n    // historyBack 是否需要后退历史纪录，默认为 `false`。该参数仅内部使用\n    // 为 `false` 时是通过 js 关闭，需要后退一个历史记录\n    // 为 `true` 时是通过后退按钮关闭，不需要后退历史记录\n\n    // setTimeout 的作用是：\n    // 当同时关闭一个对话框，并打开另一个对话框时，使打开对话框的操作先执行，以使需要打开的对话框先加入队列\n    setTimeout(() => {\n      if (!this.isOpen()) {\n        return;\n      }\n\n      currentInst = null;\n\n      this.state = 'closing';\n      this.triggerEvent('close');\n\n      // 所有对话框都关闭，且当前没有打开的对话框时，隐藏遮罩\n      if (!queue(queueName).length && $overlay) {\n        $.hideOverlay();\n        $overlay = null;\n\n        // 若仍存在遮罩，恢复遮罩的 z-index\n        $('.mdui-overlay').css('z-index', 2000);\n      }\n\n      this.$element\n        .removeClass('mdui-dialog-open')\n        .transitionEnd(() => this.transitionEnd());\n\n      if (this.options.history && !queue(queueName).length) {\n        if (!historyBack) {\n          window.history.back();\n        }\n\n        $window.off('hashchange', this.hashchangeEvent);\n      }\n\n      // 关闭旧对话框，打开新对话框。\n      // 加一点延迟，仅仅为了视觉效果更好。不加延时也不影响功能\n      setTimeout(() => {\n        dequeue(queueName);\n      }, 100);\n    });\n  }\n\n  /**\n   * 切换对话框打开/关闭状态\n   */\n  public toggle(): void {\n    this.isOpen() ? this.close() : this.open();\n  }\n\n  /**\n   * 获取对话框状态。共包含四种状态：`opening`、`opened`、`closing`、`closed`\n   */\n  public getState(): STATE {\n    return this.state;\n  }\n\n  /**\n   * 销毁对话框\n   */\n  public destroy(): void {\n    if (this.append) {\n      this.$element.remove();\n    }\n\n    if (!queue(queueName).length && !currentInst) {\n      if ($overlay) {\n        $.hideOverlay();\n        $overlay = null;\n      }\n\n      if (isLockScreen) {\n        $.unlockScreen();\n        isLockScreen = false;\n      }\n    }\n  }\n\n  /**\n   * 对话框内容变化时，需要调用该方法来调整对话框位置和滚动条高度\n   */\n  public handleUpdate(): void {\n    this.readjust();\n  }\n}\n\nexport { currentInst, OPTIONS, Dialog };\n", "import Selector from 'mdui.jq/es/types/Selector';\nimport mdui from '../../mdui';\nimport 'mdui.jq/es/methods/on';\nimport { $document } from '../../utils/dom';\nimport { currentInst, OPTIONS, Dialog } from './class';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * Dialog 组件\n     *\n     * 请通过 `new mdui.Dialog()` 调用\n     */\n    Dialog: {\n      /**\n       * 实例化 Dialog 组件\n       * @param selector CSS 选择器、或 DOM 元素、或 JQ 对象\n       * @param options 配置参数\n       */\n      new (\n        selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n        options?: OPTIONS,\n      ): Dialog;\n    };\n  }\n}\n\n// esc 按下时关闭对话框\n$document.on('keydown', (event: Event) => {\n  if (\n    currentInst &&\n    currentInst.options.closeOnEsc &&\n    currentInst.state === 'opened' &&\n    (event as KeyboardEvent).keyCode === 27\n  ) {\n    currentInst.close();\n  }\n});\n\nmdui.Dialog = Dialog;\n", "import $ from 'mdui.jq/es/$';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/on';\nimport mdui from '../../mdui';\nimport { $document } from '../../utils/dom';\nimport { parseOptions } from '../../utils/parseOptions';\nimport './index';\n\nconst customAttr = 'mdui-dialog';\nconst dataName = '_mdui_dialog';\n\ntype OPTIONS = {\n  target: string;\n  history?: boolean;\n  overlay?: boolean;\n  modal?: boolean;\n  closeOnEsc?: boolean;\n  closeOnCancel?: boolean;\n  closeOnConfirm?: boolean;\n  destroyOnClosed?: boolean;\n};\n\n$(() => {\n  $document.on('click', `[${customAttr}]`, function () {\n    const options = parseOptions(this as HTMLElement, customAttr) as OPTIONS;\n    const selector = options.target;\n    // @ts-ignore\n    delete options.target;\n\n    const $dialog = $(selector).first();\n    let instance = $dialog.data(dataName);\n\n    if (!instance) {\n      instance = new mdui.Dialog($dialog, options);\n      $dialog.data(dataName, instance);\n    }\n\n    instance.open();\n  });\n});\n", "import $ from 'mdui.jq/es/$';\nimport each from 'mdui.jq/es/functions/each';\nimport extend from 'mdui.jq/es/functions/extend';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/on';\nimport mdui from '../../mdui';\nimport { Dialog } from './class';\nimport './index';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 打开一个对话框，标题、内容、按钮等都可以自定义\n     * @param options 配置参数\n     */\n    dialog(options: OPTIONS): Dialog;\n  }\n}\n\ntype BUTTON = {\n  /**\n   * 按钮文本\n   */\n  text?: string;\n\n  /**\n   * 按钮文本是否加粗，默认为 `false`\n   */\n  bold?: boolean;\n\n  /**\n   * 点击按钮后是否关闭对话框，默认为 `true`\n   */\n  close?: boolean;\n\n  /**\n   * 点击按钮的回调函数，参数为对话框的实例\n   */\n  onClick?: (dialog: Dialog) => void;\n};\n\ntype OPTIONS = {\n  /**\n   * 对话框的标题\n   */\n  title?: string;\n\n  /**\n   * 对话框的内容\n   */\n  content?: string;\n\n  /**\n   * 按钮数组，每个按钮都是一个带按钮参数的对象\n   */\n  buttons?: BUTTON[];\n\n  /**\n   * 按钮是否垂直排列，默认为 `false`\n   */\n  stackedButtons?: boolean;\n\n  /**\n   * 添加到 `.mdui-dialog` 上的 CSS 类\n   */\n  cssClass?: string;\n\n  /**\n   * 是否监听 `hashchange` 事件，为 `true` 时可以通过 Android 的返回键或浏览器后退按钮关闭对话框，默认为 `true`\n   */\n  history?: boolean;\n\n  /**\n   * 打开对话框后是否显示遮罩层，默认为 `true`\n   */\n  overlay?: boolean;\n\n  /**\n   * 是否模态化对话框。为 `false` 时点击对话框外面的区域时关闭对话框，否则不关闭\n   */\n  modal?: boolean;\n\n  /**\n   * 按下 Esc 键时是否关闭对话框，默认为 `true`\n   */\n  closeOnEsc?: boolean;\n\n  /**\n   * 关闭对话框后是否自动销毁对话框，默认为 `true`\n   */\n  destroyOnClosed?: boolean;\n\n  /**\n   * 打开动画开始时的回调，参数为对话框实例\n   */\n  onOpen?: (dialog: Dialog) => void;\n\n  /**\n   * 打开动画结束时的回调，参数为对话框实例\n   */\n  onOpened?: (dialog: Dialog) => void;\n\n  /**\n   * 关闭动画开始时的回调，参数为对话框实例\n   */\n  onClose?: (dialog: Dialog) => void;\n\n  /**\n   * 关闭动画结束时的回调，参数为对话框实例\n   */\n  onClosed?: (dialog: Dialog) => void;\n};\n\nconst DEFAULT_BUTTON: BUTTON = {\n  text: '',\n  bold: false,\n  close: true,\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onClick: () => {},\n};\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  title: '',\n  content: '',\n  buttons: [],\n  stackedButtons: false,\n  cssClass: '',\n  history: true,\n  overlay: true,\n  modal: false,\n  closeOnEsc: true,\n  destroyOnClosed: true,\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onOpen: () => {},\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onOpened: () => {},\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onClose: () => {},\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onClosed: () => {},\n};\n\nmdui.dialog = function (options: OPTIONS): Dialog {\n  // 合并配置参数\n  options = extend({}, DEFAULT_OPTIONS, options);\n\n  each(options.buttons!, (i, button) => {\n    options.buttons![i] = extend({}, DEFAULT_BUTTON, button);\n  });\n\n  // 按钮的 HTML\n  let buttonsHTML = '';\n  if (options.buttons?.length) {\n    buttonsHTML = `<div class=\"mdui-dialog-actions${\n      options.stackedButtons ? ' mdui-dialog-actions-stacked' : ''\n    }\">`;\n\n    each(options.buttons, (_, button) => {\n      buttonsHTML +=\n        '<a href=\"javascript:void(0)\" ' +\n        `class=\"mdui-btn mdui-ripple mdui-text-color-primary ${\n          button.bold ? 'mdui-btn-bold' : ''\n        }\">${button.text}</a>`;\n    });\n\n    buttonsHTML += '</div>';\n  }\n\n  // Dialog 的 HTML\n  const HTML =\n    `<div class=\"mdui-dialog ${options.cssClass}\">` +\n    (options.title\n      ? `<div class=\"mdui-dialog-title\">${options.title}</div>`\n      : '') +\n    (options.content\n      ? `<div class=\"mdui-dialog-content\">${options.content}</div>`\n      : '') +\n    buttonsHTML +\n    '</div>';\n\n  // 实例化 Dialog\n  const instance = new mdui.Dialog(HTML, {\n    history: options.history,\n    overlay: options.overlay,\n    modal: options.modal,\n    closeOnEsc: options.closeOnEsc,\n    destroyOnClosed: options.destroyOnClosed,\n  });\n\n  // 绑定按钮事件\n  if (options.buttons?.length) {\n    instance.$element\n      .find('.mdui-dialog-actions .mdui-btn')\n      .each((index, button) => {\n        $(button).on('click', () => {\n          options.buttons![index].onClick!(instance);\n\n          if (options.buttons![index].close) {\n            instance.close();\n          }\n        });\n      });\n  }\n\n  // 绑定打开关闭事件\n  instance.$element\n    .on('open.mdui.dialog', () => {\n      options.onOpen!(instance);\n    })\n    .on('opened.mdui.dialog', () => {\n      options.onOpened!(instance);\n    })\n    .on('close.mdui.dialog', () => {\n      options.onClose!(instance);\n    })\n    .on('closed.mdui.dialog', () => {\n      options.onClosed!(instance);\n    });\n\n  instance.open();\n\n  return instance;\n};\n", "import extend from 'mdui.jq/es/functions/extend';\nimport { isFunction, isUndefined } from 'mdui.jq/es/utils';\nimport mdui from '../../mdui';\nimport { Dialog } from './class';\nimport './dialog';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 打开一个警告框，可以包含标题、内容和一个确认按钮\n     * @param text 警告框内容\n     * @param title 警告框标题\n     * @param onConfirm 点击确认按钮的回调函数，参数为对话框实例\n     * @param options 配置参数\n     */\n    alert(\n      text: string,\n      title: string,\n      onConfirm?: (dialog: Dialog) => void,\n      options?: OPTIONS,\n    ): Dialog;\n\n    /**\n     * 打开一个警告框，可以包含内容，和一个确认按钮\n     * @param text 警告框内容\n     * @param onConfirm 点击确认按钮的回调函数，参数为对话框实例\n     * @param options 配置参数\n     */\n    alert(\n      text: string,\n      onConfirm?: (dialog: Dialog) => void,\n      options?: OPTIONS,\n    ): Dialog;\n  }\n}\n\ntype OPTIONS = {\n  /**\n   * 确认按钮的文本\n   */\n  confirmText?: string;\n\n  /**\n   * 是否监听 hashchange 事件，为 `true` 时可以通过 Android 的返回键或浏览器后退按钮关闭对话框，默认为 `true`\n   */\n  history?: boolean;\n\n  /**\n   * 是否模态化对话框。为 `false` 时点击对话框外面的区域时关闭对话框，否则不关闭，默认为 `false`\n   */\n  modal?: boolean;\n\n  /**\n   * 按下 Esc 键时是否关闭对话框，默认为 `true`\n   */\n  closeOnEsc?: boolean;\n\n  /**\n   * 是否在按下确认按钮时是否关闭对话框\n   */\n  closeOnConfirm?: boolean;\n};\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  confirmText: 'ok',\n  history: true,\n  modal: false,\n  closeOnEsc: true,\n  closeOnConfirm: true,\n};\n\nmdui.alert = function (\n  text: string,\n  title?: any,\n  onConfirm?: any,\n  options?: any,\n): Dialog {\n  if (isFunction(title)) {\n    options = onConfirm;\n    onConfirm = title;\n    title = '';\n  }\n\n  if (isUndefined(onConfirm)) {\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    onConfirm = (): void => {};\n  }\n\n  if (isUndefined(options)) {\n    options = {};\n  }\n\n  options = extend({}, DEFAULT_OPTIONS, options);\n\n  return mdui.dialog({\n    title: title,\n    content: text,\n    buttons: [\n      {\n        text: options.confirmText,\n        bold: false,\n        close: options.closeOnConfirm,\n        onClick: onConfirm,\n      },\n    ],\n    cssClass: 'mdui-dialog-alert',\n    history: options.history,\n    modal: options.modal,\n    closeOnEsc: options.closeOnEsc,\n  });\n};\n", "import extend from 'mdui.jq/es/functions/extend';\nimport { isFunction, isUndefined } from 'mdui.jq/es/utils';\nimport mdui from '../../mdui';\nimport { Dialog } from './class';\nimport './dialog';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 打开一个确认框，可以包含标题、内容、一个确认按钮和一个取消按钮\n     * @param text 确认框内容\n     * @param title 确认框标题\n     * @param onConfirm 点击确认按钮的回调函数，参数为对话框实例\n     * @param onCancel 点击取消按钮的回调函数，参数为对话框实例\n     * @param options 配置参数\n     */\n    confirm(\n      text: string,\n      title: string,\n      onConfirm?: (dialog: Dialog) => void,\n      onCancel?: (dialog: Dialog) => void,\n      options?: OPTIONS,\n    ): Dialog;\n\n    /**\n     * 打开一个确认框，可以包含内容、一个确认按钮和一个取消按钮\n     * @param text 确认框内容\n     * @param onConfirm 点击确认按钮的回调函数，参数为对话框实例\n     * @param onCancel 点击取消按钮的回调函数，参数为对话框实例\n     * @param options 配置参数\n     */\n    confirm(\n      text: string,\n      onConfirm?: (dialog: Dialog) => void,\n      onCancel?: (dialog: Dialog) => void,\n      options?: OPTIONS,\n    ): Dialog;\n  }\n}\n\ntype OPTIONS = {\n  /**\n   * 确认按钮的文本\n   */\n  confirmText?: string;\n\n  /**\n   * 取消按钮的文本\n   */\n  cancelText?: string;\n\n  /**\n   * 是否监听 hashchange 事件，为 `true` 时可以通过 Android 的返回键或浏览器后退按钮关闭对话框，默认为 `true`\n   */\n  history?: boolean;\n\n  /**\n   * 是否模态化对话框。为 `false` 时点击对话框外面的区域时关闭对话框，否则不关闭，默认为 `false`\n   */\n  modal?: boolean;\n\n  /**\n   * 按下 Esc 键时是否关闭对话框，默认为 `true`\n   */\n  closeOnEsc?: boolean;\n\n  /**\n   * 是否在按下取消按钮时是否关闭对话框\n   */\n  closeOnCancel?: boolean;\n\n  /**\n   * 是否在按下确认按钮时是否关闭对话框\n   */\n  closeOnConfirm?: boolean;\n};\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  confirmText: 'ok',\n  cancelText: 'cancel',\n  history: true,\n  modal: false,\n  closeOnEsc: true,\n  closeOnCancel: true,\n  closeOnConfirm: true,\n};\n\nmdui.confirm = function (\n  text: string,\n  title?: any,\n  onConfirm?: any,\n  onCancel?: any,\n  options?: any,\n): Dialog {\n  if (isFunction(title)) {\n    options = onCancel;\n    onCancel = onConfirm;\n    onConfirm = title;\n    title = '';\n  }\n\n  if (isUndefined(onConfirm)) {\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    onConfirm = (): void => {};\n  }\n\n  if (isUndefined(onCancel)) {\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    onCancel = (): void => {};\n  }\n\n  if (isUndefined(options)) {\n    options = {};\n  }\n\n  options = extend({}, DEFAULT_OPTIONS, options);\n\n  return mdui.dialog({\n    title: title,\n    content: text,\n    buttons: [\n      {\n        text: options.cancelText,\n        bold: false,\n        close: options.closeOnCancel,\n        onClick: onCancel,\n      },\n      {\n        text: options.confirmText,\n        bold: false,\n        close: options.closeOnConfirm,\n        onClick: onConfirm,\n      },\n    ],\n    cssClass: 'mdui-dialog-confirm',\n    history: options.history,\n    modal: options.modal,\n    closeOnEsc: options.closeOnEsc,\n  });\n};\n", "import extend from 'mdui.jq/es/functions/extend';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/val';\nimport { isFunction, isUndefined } from 'mdui.jq/es/utils';\nimport mdui from '../../mdui';\nimport '../textfield';\nimport { Dialog } from './class';\nimport './dialog';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 打开一个提示用户输入的对话框，可以包含标题、文本框标签、文本框、一个确认按钮和一个取消按钮\n     * @param label 文本框浮动标签的文本\n     * @param title 标题\n     * @param onConfirm 点击确认按钮的回调。含两个参数，分别为文本框的值和对话框实例\n     * @param onCancel 点击取消按钮的回调。含两个参数，分别为文本框的值和对话框实例\n     * @param options 配置参数\n     */\n    prompt(\n      label: string,\n      title: string,\n      onConfirm?: (value: string, dialog: Dialog) => void,\n      onCancel?: (value: string, dialog: Dialog) => void,\n      options?: OPTIONS,\n    ): Dialog;\n\n    /**\n     * 打开一个提示用户输入的对话框，可以包含文本框标签、文本框、一个确认按钮和一个取消按钮\n     * @param label 文本框浮动标签的文本\n     * @param onConfirm 点击确认按钮的回调。含两个参数，分别为文本框的值和对话框实例\n     * @param onCancel 点击取消按钮的回调，含两个参数，分别为文本框的值和对话框实例\n     * @param options 配置参数\n     */\n    prompt(\n      label: string,\n      onConfirm?: (value: string, dialog: Dialog) => void,\n      onCancel?: (value: string, dialog: Dialog) => void,\n      options?: OPTIONS,\n    ): Dialog;\n  }\n}\n\ntype OPTIONS = {\n  /**\n   * 确认按钮的文本\n   */\n  confirmText?: string;\n\n  /**\n   * 取消按钮的文本\n   */\n  cancelText?: string;\n\n  /**\n   * 是否监听 hashchange 事件，为 `true` 时可以通过 Android 的返回键或浏览器后退按钮关闭对话框，默认为 `true`\n   */\n  history?: boolean;\n\n  /**\n   * 是否模态化对话框。为 `false` 时点击对话框外面的区域时关闭对话框，否则不关闭，默认为 `false`\n   */\n  modal?: boolean;\n\n  /**\n   * 是否在按下 Esc 键时是否关闭对话框，默认为 `true`\n   */\n  closeOnEsc?: boolean;\n\n  /**\n   * 是否在按下取消按钮时是否关闭对话框\n   */\n  closeOnCancel?: boolean;\n\n  /**\n   * 是否在按下确认按钮时是否关闭对话框\n   */\n  closeOnConfirm?: boolean;\n\n  /**\n   * 是否在按下 Enter 键时触发 `onConfirm` 回调函数，默认为 `false`\n   */\n  confirmOnEnter?: boolean;\n\n  /**\n   * 文本框的类型。`text`: 单行文本框； `textarea`: 多行文本框\n   */\n  type?: 'text' | 'textarea';\n\n  /**\n   * 最大输入字符数量，为 0 时表示不限制\n   */\n  maxlength?: number;\n\n  /**\n   * 文本框的默认值\n   */\n  defaultValue?: string;\n};\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  confirmText: 'ok',\n  cancelText: 'cancel',\n  history: true,\n  modal: false,\n  closeOnEsc: true,\n  closeOnCancel: true,\n  closeOnConfirm: true,\n  type: 'text',\n  maxlength: 0,\n  defaultValue: '',\n  confirmOnEnter: false,\n};\n\nmdui.prompt = function (\n  label: string,\n  title?: any,\n  onConfirm?: any,\n  onCancel?: any,\n  options?: any,\n): Dialog {\n  if (isFunction(title)) {\n    options = onCancel;\n    onCancel = onConfirm;\n    onConfirm = title;\n    title = '';\n  }\n\n  if (isUndefined(onConfirm)) {\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    onConfirm = (): void => {};\n  }\n\n  if (isUndefined(onCancel)) {\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    onCancel = (): void => {};\n  }\n\n  if (isUndefined(options)) {\n    options = {};\n  }\n\n  options = extend({}, DEFAULT_OPTIONS, options);\n\n  const content =\n    '<div class=\"mdui-textfield\">' +\n    (label ? `<label class=\"mdui-textfield-label\">${label}</label>` : '') +\n    (options.type === 'text'\n      ? `<input class=\"mdui-textfield-input\" type=\"text\" value=\"${\n          options.defaultValue\n        }\" ${\n          options.maxlength ? 'maxlength=\"' + options.maxlength + '\"' : ''\n        }/>`\n      : '') +\n    (options.type === 'textarea'\n      ? `<textarea class=\"mdui-textfield-input\" ${\n          options.maxlength ? 'maxlength=\"' + options.maxlength + '\"' : ''\n        }>${options.defaultValue}</textarea>`\n      : '') +\n    '</div>';\n\n  const onCancelClick = (dialog: Dialog): void => {\n    const value = dialog.$element.find('.mdui-textfield-input').val();\n    onCancel(value, dialog);\n  };\n\n  const onConfirmClick = (dialog: Dialog): void => {\n    const value = dialog.$element.find('.mdui-textfield-input').val();\n    onConfirm(value, dialog);\n  };\n\n  return mdui.dialog({\n    title,\n    content,\n    buttons: [\n      {\n        text: options.cancelText,\n        bold: false,\n        close: options.closeOnCancel,\n        onClick: onCancelClick,\n      },\n      {\n        text: options.confirmText,\n        bold: false,\n        close: options.closeOnConfirm,\n        onClick: onConfirmClick,\n      },\n    ],\n    cssClass: 'mdui-dialog-prompt',\n    history: options.history,\n    modal: options.modal,\n    closeOnEsc: options.closeOnEsc,\n    onOpen: (dialog) => {\n      // 初始化输入框\n      const $input = dialog.$element.find('.mdui-textfield-input');\n      mdui.updateTextFields($input);\n\n      // 聚焦到输入框\n      $input[0].focus();\n\n      // 捕捉文本框回车键，在单行文本框的情况下触发回调\n      if (options.type !== 'textarea' && options.confirmOnEnter === true) {\n        $input.on('keydown', (event) => {\n          if ((event as KeyboardEvent).keyCode === 13) {\n            const value = dialog.$element.find('.mdui-textfield-input').val();\n            onConfirm(value, dialog);\n\n            if (options.closeOnConfirm) {\n              dialog.close();\n            }\n\n            return false;\n          }\n\n          return;\n        });\n      }\n\n      // 如果是多行输入框，监听输入框的 input 事件，更新对话框高度\n      if (options.type === 'textarea') {\n        $input.on('input', () => dialog.handleUpdate());\n      }\n\n      // 有字符数限制时，加载完文本框后 DOM 会变化，需要更新对话框高度\n      if (options.maxlength) {\n        dialog.handleUpdate();\n      }\n    },\n  });\n};\n", "import $ from 'mdui.jq/es/$';\nimport extend from 'mdui.jq/es/functions/extend';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/appendTo';\nimport 'mdui.jq/es/methods/attr';\nimport 'mdui.jq/es/methods/css';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/height';\nimport 'mdui.jq/es/methods/html';\nimport 'mdui.jq/es/methods/offset';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/removeClass';\nimport 'mdui.jq/es/methods/width';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport mdui from '../../mdui';\nimport '../../jq_extends/methods/transformOrigin';\nimport '../../jq_extends/methods/transitionEnd';\nimport '../../jq_extends/static/guid';\nimport { componentEvent } from '../../utils/componentEvent';\nimport { $window } from '../../utils/dom';\nimport { isAllow, register, unlockEvent } from '../../utils/touchHandler';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * Tooltip 组件\n     *\n     * 请通过 `new mdui.Tooltip()` 调用\n     */\n    Tooltip: {\n      /**\n       * 实例化 Tooltip 组件\n       * @param selector CSS 选择器、或 DOM 元素、或 JQ 对象\n       * @param options 配置参数\n       */\n      new (\n        selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n        options?: OPTIONS,\n      ): Tooltip;\n    };\n  }\n}\n\ntype POSITION = 'auto' | 'bottom' | 'top' | 'left' | 'right';\n\ntype OPTIONS = {\n  /**\n   * Tooltip 的位置。取值范围包括 `auto`、`bottom`、`top`、`left`、`right`。\n   * 为 `auto` 时，会自动判断位置。默认在下方。优先级为 `bottom` > `top` > `left` > `right`。\n   * 默认为 `auto`\n   */\n  position?: POSITION;\n\n  /**\n   * 延时触发，单位毫秒。默认为 `0`，即没有延时。\n   */\n  delay?: number;\n\n  /**\n   * Tooltip 的内容\n   */\n  content?: string;\n};\n\ntype STATE = 'opening' | 'opened' | 'closing' | 'closed';\ntype EVENT = 'open' | 'opened' | 'close' | 'closed';\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  position: 'auto',\n  delay: 0,\n  content: '',\n};\n\nclass Tooltip {\n  /**\n   * 触发 tooltip 元素的 JQ 对象\n   */\n  public $target: JQ;\n\n  /**\n   * tooltip 元素的 JQ 对象\n   */\n  public $element: JQ;\n\n  /**\n   * 配置参数\n   */\n  public options: OPTIONS = extend({}, DEFAULT_OPTIONS);\n\n  /**\n   * 当前 tooltip 的状态\n   */\n  private state: STATE = 'closed';\n\n  /**\n   * setTimeout 的返回值\n   */\n  private timeoutId: any = null;\n\n  public constructor(\n    selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    options: OPTIONS = {},\n  ) {\n    this.$target = $(selector).first();\n\n    extend(this.options, options);\n\n    // 创建 Tooltip HTML\n    this.$element = $(\n      `<div class=\"mdui-tooltip\" id=\"${$.guid()}\">${\n        this.options.content\n      }</div>`,\n    ).appendTo(document.body);\n\n    // 绑定事件。元素处于 disabled 状态时无法触发鼠标事件，为了统一，把 touch 事件也禁用\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const that = this;\n    this.$target\n      .on('touchstart mouseenter', function (event) {\n        if (that.isDisabled(this as HTMLElement)) {\n          return;\n        }\n\n        if (!isAllow(event)) {\n          return;\n        }\n\n        register(event);\n\n        that.open();\n      })\n      .on('touchend mouseleave', function (event) {\n        if (that.isDisabled(this as HTMLElement)) {\n          return;\n        }\n\n        if (!isAllow(event)) {\n          return;\n        }\n\n        that.close();\n      })\n      .on(unlockEvent, function (event) {\n        if (that.isDisabled(this as HTMLElement)) {\n          return;\n        }\n\n        register(event);\n      });\n  }\n\n  /**\n   * 元素是否已禁用\n   * @param element\n   */\n  private isDisabled(element: HTMLElement): boolean {\n    return (\n      (element as HTMLInputElement).disabled ||\n      $(element).attr('disabled') !== undefined\n    );\n  }\n\n  /**\n   * 是否是桌面设备\n   */\n  private isDesktop(): boolean {\n    return $window.width() > 1024;\n  }\n\n  /**\n   * 设置 Tooltip 的位置\n   */\n  private setPosition(): void {\n    let marginLeft: number;\n    let marginTop: number;\n\n    // 触发的元素\n    const targetProps = this.$target[0].getBoundingClientRect();\n\n    // 触发的元素和 Tooltip 之间的距离\n    const targetMargin = this.isDesktop() ? 14 : 24;\n\n    // Tooltip 的宽度和高度\n    const tooltipWidth = this.$element[0].offsetWidth;\n    const tooltipHeight = this.$element[0].offsetHeight;\n\n    // Tooltip 的方向\n    let position: POSITION = this.options.position!;\n\n    // 自动判断位置，加 2px，使 Tooltip 距离窗口边框至少有 2px 的间距\n    if (position === 'auto') {\n      if (\n        targetProps.top +\n          targetProps.height +\n          targetMargin +\n          tooltipHeight +\n          2 <\n        $window.height()\n      ) {\n        position = 'bottom';\n      } else if (targetMargin + tooltipHeight + 2 < targetProps.top) {\n        position = 'top';\n      } else if (targetMargin + tooltipWidth + 2 < targetProps.left) {\n        position = 'left';\n      } else if (\n        targetProps.width + targetMargin + tooltipWidth + 2 <\n        $window.width() - targetProps.left\n      ) {\n        position = 'right';\n      } else {\n        position = 'bottom';\n      }\n    }\n\n    // 设置位置\n    switch (position) {\n      case 'bottom':\n        marginLeft = -1 * (tooltipWidth / 2);\n        marginTop = targetProps.height / 2 + targetMargin;\n        this.$element.transformOrigin('top center');\n        break;\n\n      case 'top':\n        marginLeft = -1 * (tooltipWidth / 2);\n        marginTop =\n          -1 * (tooltipHeight + targetProps.height / 2 + targetMargin);\n        this.$element.transformOrigin('bottom center');\n        break;\n\n      case 'left':\n        marginLeft = -1 * (tooltipWidth + targetProps.width / 2 + targetMargin);\n        marginTop = -1 * (tooltipHeight / 2);\n        this.$element.transformOrigin('center right');\n        break;\n\n      case 'right':\n        marginLeft = targetProps.width / 2 + targetMargin;\n        marginTop = -1 * (tooltipHeight / 2);\n        this.$element.transformOrigin('center left');\n        break;\n    }\n\n    const targetOffset = this.$target.offset();\n\n    this.$element.css({\n      top: `${targetOffset.top + targetProps.height / 2}px`,\n      left: `${targetOffset.left + targetProps.width / 2}px`,\n      'margin-left': `${marginLeft}px`,\n      'margin-top': `${marginTop}px`,\n    });\n  }\n\n  /**\n   * 触发组件事件\n   * @param name\n   */\n  private triggerEvent(name: EVENT): void {\n    componentEvent(name, 'tooltip', this.$target, this);\n  }\n\n  /**\n   * 动画结束回调\n   */\n  private transitionEnd(): void {\n    if (this.$element.hasClass('mdui-tooltip-open')) {\n      this.state = 'opened';\n      this.triggerEvent('opened');\n    } else {\n      this.state = 'closed';\n      this.triggerEvent('closed');\n    }\n  }\n\n  /**\n   * 当前 tooltip 是否为打开状态\n   */\n  private isOpen(): boolean {\n    return this.state === 'opening' || this.state === 'opened';\n  }\n\n  /**\n   * 执行打开 tooltip\n   */\n  private doOpen(): void {\n    this.state = 'opening';\n    this.triggerEvent('open');\n\n    this.$element\n      .addClass('mdui-tooltip-open')\n      .transitionEnd(() => this.transitionEnd());\n  }\n\n  /**\n   * 打开 Tooltip\n   * @param options 允许每次打开时设置不同的参数\n   */\n  public open(options?: OPTIONS): void {\n    if (this.isOpen()) {\n      return;\n    }\n\n    const oldOptions = extend({}, this.options);\n\n    if (options) {\n      extend(this.options, options);\n    }\n\n    // tooltip 的内容有更新\n    if (oldOptions.content !== this.options.content) {\n      this.$element.html(this.options.content);\n    }\n\n    this.setPosition();\n\n    if (this.options.delay) {\n      this.timeoutId = setTimeout(() => this.doOpen(), this.options.delay);\n    } else {\n      this.timeoutId = null;\n      this.doOpen();\n    }\n  }\n\n  /**\n   * 关闭 Tooltip\n   */\n  public close(): void {\n    if (this.timeoutId) {\n      clearTimeout(this.timeoutId);\n      this.timeoutId = null;\n    }\n\n    if (!this.isOpen()) {\n      return;\n    }\n\n    this.state = 'closing';\n    this.triggerEvent('close');\n\n    this.$element\n      .removeClass('mdui-tooltip-open')\n      .transitionEnd(() => this.transitionEnd());\n  }\n\n  /**\n   * 切换 Tooltip 的打开状态\n   */\n  public toggle(): void {\n    this.isOpen() ? this.close() : this.open();\n  }\n\n  /**\n   * 获取 Tooltip 状态。共包含四种状态：`opening`、`opened`、`closing`、`closed`\n   */\n  public getState(): STATE {\n    return this.state;\n  }\n}\n\nmdui.Tooltip = Tooltip;\n", "import $ from 'mdui.jq/es/$';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/on';\nimport mdui from '../../mdui';\nimport { $document } from '../../utils/dom';\nimport { parseOptions } from '../../utils/parseOptions';\nimport './index';\n\nconst customAttr = 'mdui-tooltip';\nconst dataName = '_mdui_tooltip';\n\n$(() => {\n  // mouseenter 不能冒泡，所以这里用 mouseover 代替\n  $document.on('touchstart mouseover', `[${customAttr}]`, function () {\n    const $target = $(this);\n    let instance = $target.data(dataName);\n\n    if (!instance) {\n      instance = new mdui.Tooltip(\n        this as HTMLElement,\n        parseOptions(this as HTMLElement, customAttr),\n      );\n      $target.data(dataName, instance);\n    }\n  });\n});\n", "import $ from 'mdui.jq/es/$';\nimport extend from 'mdui.jq/es/functions/extend';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/appendTo';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/off';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/parents';\nimport 'mdui.jq/es/methods/remove';\nimport { isString } from 'mdui.jq/es/utils';\nimport mdui from '../../mdui';\nimport '../../jq_extends/methods/reflow';\nimport '../../jq_extends/methods/transform';\nimport '../../jq_extends/methods/transitionEnd';\nimport { $document } from '../../utils/dom';\nimport { dequeue, queue } from '../../utils/queue';\nimport { startEvent } from '../../utils/touchHandler';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 打开一个 Snackbar\n     * @param message Snackbar 的文本\n     * @param options 配置参数\n     */\n    snackbar(message: string, options?: OPTIONS): Snackbar;\n\n    /**\n     * 打开一个 Snackbar\n     * @param options 配置参数\n     */\n    snackbar(options: OPTIONS): Snackbar;\n  }\n}\n\ntype OPTIONS = {\n  /**\n   * Snackbar 的文本。通过 `mdui.snackbar(options)` 调用时，该参数不能为空\n   */\n  message?: string;\n\n  /**\n   * 在用户没有操作时多长时间自动隐藏，单位（毫秒）。为 `0` 时表示不自动关闭，默认为 `4000`\n   */\n  timeout?: number;\n\n  /**\n   * Snackbar 的位置，默认为 `bottom`。\n   * 取值范围包括：\n   *   `bottom`: 下方\n   *   `top`: 上方\n   *   `left-top`: 左上角\n   *   `left-bottom`: 左下角\n   *   `right-top`: 右上角\n   *   `right-bottom`: 右下角\n   */\n  position?:\n    | 'bottom'\n    | 'top'\n    | 'left-top'\n    | 'left-bottom'\n    | 'right-top'\n    | 'right-bottom';\n\n  /**\n   * 按钮的文本\n   */\n  buttonText?: string;\n\n  /**\n   * 按钮的文本颜色，可以是颜色名或颜色值，如 `red`、`#ffffff`、`rgba(255, 255, 255, 0.3)` 等。默认为 `#90CAF9`\n   */\n  buttonColor?: string;\n\n  /**\n   * 点击按钮时是否关闭 Snackbar，默认为 `true`\n   */\n  closeOnButtonClick?: boolean;\n\n  /**\n   * 点击或触摸 Snackbar 以外的区域时是否关闭 Snackbar，默认为 `true`\n   */\n  closeOnOutsideClick?: boolean;\n\n  /**\n   * 在 Snackbar 上点击的回调函数，参数为 Snackbar 的实例\n   */\n  onClick?: (snackbar: Snackbar) => void;\n\n  /**\n   * 点击 Snackbar 上的按钮时的回调函数，参数为 Snackbar 的实例\n   */\n  onButtonClick?: (snackbar: Snackbar) => void;\n\n  /**\n   * Snackbar 开始打开时的回调函数，参数为 Snackbar 的实例\n   */\n  onOpen?: (snackbar: Snackbar) => void;\n\n  /**\n   * Snackbar 打开后的回调函数，参数为 Snackbar 的实例\n   */\n  onOpened?: (snackbar: Snackbar) => void;\n\n  /**\n   * Snackbar 开始关闭时的回调函数，参数为 Snackbar 的实例\n   */\n  onClose?: (snackbar: Snackbar) => void;\n\n  /**\n   * Snackbar 关闭后的回调函数，参数为 Snackbar 的实例\n   */\n  onClosed?: (snackbar: Snackbar) => void;\n};\n\ntype STATE = 'opening' | 'opened' | 'closing' | 'closed';\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  message: '',\n  timeout: 4000,\n  position: 'bottom',\n  buttonText: '',\n  buttonColor: '',\n  closeOnButtonClick: true,\n  closeOnOutsideClick: true,\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onClick: () => {},\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onButtonClick: () => {},\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onOpen: () => {},\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onOpened: () => {},\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onClose: () => {},\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onClosed: () => {},\n};\n\n/**\n * 当前打开着的 Snackbar\n */\nlet currentInst: null | Snackbar = null;\n\n/**\n * 队列名\n */\nconst queueName = '_mdui_snackbar';\n\nclass Snackbar {\n  /**\n   * Snackbar 元素\n   */\n  public $element: JQ;\n  /**\n   * 配置参数\n   */\n  public options: OPTIONS = extend({}, DEFAULT_OPTIONS);\n\n  /**\n   * 当前 Snackbar 的状态\n   */\n  private state: STATE = 'closed';\n\n  /**\n   * setTimeout 的 ID\n   */\n  private timeoutId: any = null;\n\n  public constructor(options: OPTIONS) {\n    extend(this.options, options);\n\n    // 按钮颜色\n    let buttonColorStyle = '';\n    let buttonColorClass = '';\n\n    if (\n      this.options.buttonColor!.indexOf('#') === 0 ||\n      this.options.buttonColor!.indexOf('rgb') === 0\n    ) {\n      buttonColorStyle = `style=\"color:${this.options.buttonColor}\"`;\n    } else if (this.options.buttonColor !== '') {\n      buttonColorClass = `mdui-text-color-${this.options.buttonColor}`;\n    }\n\n    // 添加 HTML\n    this.$element = $(\n      '<div class=\"mdui-snackbar\">' +\n        `<div class=\"mdui-snackbar-text\">${this.options.message}</div>` +\n        (this.options.buttonText\n          ? `<a href=\"javascript:void(0)\" class=\"mdui-snackbar-action mdui-btn mdui-ripple mdui-ripple-white ${buttonColorClass}\" ${buttonColorStyle}>${this.options.buttonText}</a>`\n          : '') +\n        '</div>',\n    ).appendTo(document.body);\n\n    // 设置位置\n    this.setPosition('close');\n\n    this.$element.reflow().addClass(`mdui-snackbar-${this.options.position}`);\n  }\n\n  /**\n   * 点击 Snackbar 外面的区域关闭\n   * @param event\n   */\n  private closeOnOutsideClick(event: Event): void {\n    const $target = $(event.target as HTMLElement);\n\n    if (\n      !$target.hasClass('mdui-snackbar') &&\n      !$target.parents('.mdui-snackbar').length\n    ) {\n      currentInst!.close();\n    }\n  }\n\n  /**\n   * 设置 Snackbar 的位置\n   * @param state\n   */\n  private setPosition(state: 'open' | 'close'): void {\n    const snackbarHeight = this.$element[0].clientHeight;\n    const position = this.options.position;\n\n    let translateX;\n    let translateY;\n\n    // translateX\n    if (position === 'bottom' || position === 'top') {\n      translateX = '-50%';\n    } else {\n      translateX = '0';\n    }\n\n    // translateY\n    if (state === 'open') {\n      translateY = '0';\n    } else {\n      if (position === 'bottom') {\n        translateY = snackbarHeight;\n      }\n\n      if (position === 'top') {\n        translateY = -snackbarHeight;\n      }\n\n      if (position === 'left-top' || position === 'right-top') {\n        translateY = -snackbarHeight - 24;\n      }\n\n      if (position === 'left-bottom' || position === 'right-bottom') {\n        translateY = snackbarHeight + 24;\n      }\n    }\n\n    this.$element.transform(`translate(${translateX},${translateY}px`);\n  }\n\n  /**\n   * 打开 Snackbar\n   */\n  public open(): void {\n    if (this.state === 'opening' || this.state === 'opened') {\n      return;\n    }\n\n    // 如果当前有正在显示的 Snackbar，则先加入队列，等旧 Snackbar 关闭后再打开\n    if (currentInst) {\n      queue(queueName, () => this.open());\n      return;\n    }\n\n    currentInst = this;\n\n    // 开始打开\n    this.state = 'opening';\n    this.options.onOpen!(this);\n\n    this.setPosition('open');\n\n    this.$element.transitionEnd(() => {\n      if (this.state !== 'opening') {\n        return;\n      }\n\n      this.state = 'opened';\n      this.options.onOpened!(this);\n\n      // 有按钮时绑定事件\n      if (this.options.buttonText) {\n        this.$element.find('.mdui-snackbar-action').on('click', () => {\n          this.options.onButtonClick!(this);\n          if (this.options.closeOnButtonClick) {\n            this.close();\n          }\n        });\n      }\n\n      // 点击 snackbar 的事件\n      this.$element.on('click', (event) => {\n        if (!$(event.target as HTMLElement).hasClass('mdui-snackbar-action')) {\n          this.options.onClick!(this);\n        }\n      });\n\n      // 点击 Snackbar 外面的区域关闭\n      if (this.options.closeOnOutsideClick) {\n        $document.on(startEvent, this.closeOnOutsideClick);\n      }\n\n      // 超时后自动关闭\n      if (this.options.timeout) {\n        this.timeoutId = setTimeout(() => this.close(), this.options.timeout);\n      }\n    });\n  }\n\n  /**\n   * 关闭 Snackbar\n   */\n  public close(): void {\n    if (this.state === 'closing' || this.state === 'closed') {\n      return;\n    }\n\n    if (this.timeoutId) {\n      clearTimeout(this.timeoutId);\n    }\n\n    if (this.options.closeOnOutsideClick) {\n      $document.off(startEvent, this.closeOnOutsideClick);\n    }\n\n    this.state = 'closing';\n    this.options.onClose!(this);\n\n    this.setPosition('close');\n\n    this.$element.transitionEnd(() => {\n      if (this.state !== 'closing') {\n        return;\n      }\n\n      currentInst = null;\n      this.state = 'closed';\n      this.options.onClosed!(this);\n      this.$element.remove();\n      dequeue(queueName);\n    });\n  }\n}\n\nmdui.snackbar = function (message: any, options: any = {}): Snackbar {\n  if (isString(message)) {\n    options.message = message;\n  } else {\n    options = message;\n  }\n\n  const instance = new Snackbar(options);\n\n  instance.open();\n\n  return instance;\n};\n", "import $ from 'mdui.jq/es/$';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/children';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/is';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/parent';\nimport 'mdui.jq/es/methods/removeClass';\nimport mdui from '../../mdui';\nimport '../../global/mutation';\nimport { componentEvent } from '../../utils/componentEvent';\nimport { $document } from '../../utils/dom';\nimport '../headroom';\n\n$(() => {\n  // 切换导航项\n  $document.on('click', '.mdui-bottom-nav>a', function () {\n    const $item = $(this as HTMLElement);\n    const $bottomNav = $item.parent();\n\n    $bottomNav.children('a').each((index, item) => {\n      const isThis = $item.is(item);\n\n      if (isThis) {\n        componentEvent('change', 'bottomNav', $bottomNav[0], undefined, {\n          index,\n        });\n      }\n\n      isThis\n        ? $(item).addClass('mdui-bottom-nav-active')\n        : $(item).removeClass('mdui-bottom-nav-active');\n    });\n  });\n\n  // 滚动时隐藏 mdui-bottom-nav-scroll-hide\n  mdui.mutation('.mdui-bottom-nav-scroll-hide', function () {\n    new mdui.Headroom(this, {\n      pinnedClass: 'mdui-headroom-pinned-down',\n      unpinnedClass: 'mdui-headroom-unpinned-down',\n    });\n  });\n});\n", "import $ from 'mdui.jq/es/$';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/html';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport { isUndefined } from 'mdui.jq/es/utils';\nimport mdui from '../../mdui';\nimport '../../global/mutation';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 如果需要修改已有的圆形进度条组件，需要调用该方法来重新初始化组件。\n     *\n     * 若传入了参数，则只重新初始化该参数对应的圆形进度条。若没有传入参数，则重新初始化所有圆形进度条。\n     * @param selector CSS 选择器、或 DOM 元素、或 DOM 元素组成的数组、或 JQ 对象\n     */\n    updateSpinners(\n      selector?: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    ): void;\n  }\n}\n\n/**\n * layer 的 HTML 结构\n * @param index\n */\nfunction layerHTML(index: number | false = false): string {\n  return (\n    `<div class=\"mdui-spinner-layer ${\n      index ? `mdui-spinner-layer-${index}` : ''\n    }\">` +\n    '<div class=\"mdui-spinner-circle-clipper mdui-spinner-left\">' +\n    '<div class=\"mdui-spinner-circle\"></div>' +\n    '</div>' +\n    '<div class=\"mdui-spinner-gap-patch\">' +\n    '<div class=\"mdui-spinner-circle\"></div>' +\n    '</div>' +\n    '<div class=\"mdui-spinner-circle-clipper mdui-spinner-right\">' +\n    '<div class=\"mdui-spinner-circle\"></div>' +\n    '</div>' +\n    '</div>'\n  );\n}\n\n/**\n * 填充 HTML\n * @param spinner\n */\nfunction fillHTML(spinner: HTMLElement): void {\n  const $spinner = $(spinner);\n\n  const layer = $spinner.hasClass('mdui-spinner-colorful')\n    ? layerHTML(1) + layerHTML(2) + layerHTML(3) + layerHTML(4)\n    : layerHTML();\n\n  $spinner.html(layer);\n}\n\n$(() => {\n  // 页面加载完后自动填充 HTML 结构\n  mdui.mutation('.mdui-spinner', function () {\n    fillHTML(this);\n  });\n});\n\nmdui.updateSpinners = function (\n  selector?: Selector | HTMLElement | ArrayLike<HTMLElement>,\n): void {\n  const $elements = isUndefined(selector) ? $('.mdui-spinner') : $(selector);\n\n  $elements.each(function () {\n    fillHTML(this);\n  });\n};\n", "import $ from 'mdui.jq/es/$';\nimport contains from 'mdui.jq/es/functions/contains';\nimport extend from 'mdui.jq/es/functions/extend';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/attr';\nimport 'mdui.jq/es/methods/children';\nimport 'mdui.jq/es/methods/css';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/height';\nimport 'mdui.jq/es/methods/is';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/parent';\nimport 'mdui.jq/es/methods/parents';\nimport 'mdui.jq/es/methods/removeClass';\nimport 'mdui.jq/es/methods/width';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport mdui from '../../mdui';\nimport '../../jq_extends/methods/transformOrigin';\nimport '../../jq_extends/methods/transitionEnd';\nimport '../../jq_extends/static/throttle';\nimport { componentEvent } from '../../utils/componentEvent';\nimport { $document, $window } from '../../utils/dom';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * Menu 组件\n     *\n     * 请通过 `new mdui.Menu()` 调用\n     */\n    Menu: {\n      /**\n       * 实例化 Menu 组件\n       * @param anchorSelector 触发菜单的元素的 CSS 选择器、或 DOM 元素、或 JQ 对象\n       * @param menuSelector 菜单的 CSS 选择器、或 DOM 元素、或 JQ 对象\n       * @param options 配置参数\n       */\n      new (\n        anchorSelector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n        menuSelector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n        options?: OPTIONS,\n      ): Menu;\n    };\n  }\n}\n\ntype OPTIONS = {\n  /**\n   * 菜单相对于触发它的元素的位置，默认为 `auto`。\n   * 取值范围包括：\n   *   `top`: 菜单在触发它的元素的上方\n   *   `bottom`: 菜单在触发它的元素的下方\n   *   `center`: 菜单在窗口中垂直居中\n   *   `auto`: 自动判断位置。优先级为：`bottom` > `top` > `center`\n   */\n  position?: 'auto' | 'top' | 'bottom' | 'center';\n\n  /**\n   * 菜单与触发它的元素的对其方式，默认为 `auto`。\n   * 取值范围包括：\n   *   `left`: 菜单与触发它的元素左对齐\n   *   `right`: 菜单与触发它的元素右对齐\n   *   `center`: 菜单在窗口中水平居中\n   *   `auto`: 自动判断位置：优先级为：`left` > `right` > `center`\n   */\n  align?: 'auto' | 'left' | 'right' | 'center';\n\n  /**\n   * 菜单与窗口边框至少保持多少间距，单位为 px，默认为 `16`\n   */\n  gutter?: number;\n\n  /**\n   * 菜单的定位方式，默认为 `false`。\n   * 为 `true` 时，菜单使用 fixed 定位。在页面滚动时，菜单将保持在窗口固定位置，不随滚动条滚动。\n   * 为 `false` 时，菜单使用 absolute 定位。在页面滚动时，菜单将随着页面一起滚动。\n   */\n  fixed?: boolean;\n\n  /**\n   * 菜单是否覆盖在触发它的元素的上面，默认为 `auto`\n   * 为 `true` 时，使菜单覆盖在触发它的元素的上面\n   * 为 `false` 时，使菜单不覆盖触发它的元素\n   * 为 `auto` 时，简单菜单覆盖触发它的元素。级联菜单不覆盖触发它的元素\n   */\n  covered?: boolean | 'auto';\n\n  /**\n   * 子菜单的触发方式，默认为 `hover`\n   * 为 `click` 时，点击时触发子菜单\n   * 为 `hover` 时，鼠标悬浮时触发子菜单\n   */\n  subMenuTrigger?: 'click' | 'hover';\n\n  /**\n   * 子菜单的触发延迟时间（单位：毫秒），只有在 `subMenuTrigger: hover` 时，这个参数才有效，默认为 `200`\n   */\n  subMenuDelay?: number;\n};\n\ntype EVENT = 'open' | 'opened' | 'close' | 'closed';\ntype STATE = 'opening' | 'opened' | 'closing' | 'closed';\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  position: 'auto',\n  align: 'auto',\n  gutter: 16,\n  fixed: false,\n  covered: 'auto',\n  subMenuTrigger: 'hover',\n  subMenuDelay: 200,\n};\n\nclass Menu {\n  /**\n   * 触发菜单的元素的 JQ 对象\n   */\n  public $anchor: JQ;\n\n  /**\n   * 菜单元素的 JQ 对象\n   */\n  public $element: JQ;\n\n  /**\n   * 配置参数\n   */\n  public options: OPTIONS = extend({}, DEFAULT_OPTIONS);\n\n  /**\n   * 当前菜单状态\n   */\n  private state: STATE = 'closed';\n\n  /**\n   * 是否是级联菜单\n   */\n  private isCascade: boolean;\n\n  /**\n   * 菜单是否覆盖在触发它的元素的上面\n   */\n  private isCovered: boolean;\n\n  public constructor(\n    anchorSelector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    menuSelector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    options: OPTIONS = {},\n  ) {\n    this.$anchor = $(anchorSelector).first();\n    this.$element = $(menuSelector).first();\n\n    // 触发菜单的元素 和 菜单必须是同级的元素，否则菜单可能不能定位\n    if (!this.$anchor.parent().is(this.$element.parent())) {\n      throw new Error('anchorSelector and menuSelector must be siblings');\n    }\n\n    extend(this.options, options);\n\n    // 是否是级联菜单\n    this.isCascade = this.$element.hasClass('mdui-menu-cascade');\n\n    // covered 参数处理\n    this.isCovered =\n      this.options.covered === 'auto' ? !this.isCascade : this.options.covered!;\n\n    // 点击触发菜单切换\n    this.$anchor.on('click', () => this.toggle());\n\n    // 点击菜单外面区域关闭菜单\n    $document.on('click touchstart', (event: Event) => {\n      const $target = $(event.target as HTMLElement);\n\n      if (\n        this.isOpen() &&\n        !$target.is(this.$element) &&\n        !contains(this.$element[0], $target[0]) &&\n        !$target.is(this.$anchor) &&\n        !contains(this.$anchor[0], $target[0])\n      ) {\n        this.close();\n      }\n    });\n\n    // 点击不含子菜单的菜单条目关闭菜单\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const that = this;\n    $document.on('click', '.mdui-menu-item', function () {\n      const $item = $(this);\n\n      if (\n        !$item.find('.mdui-menu').length &&\n        $item.attr('disabled') === undefined\n      ) {\n        that.close();\n      }\n    });\n\n    // 绑定点击或鼠标移入含子菜单的条目的事件\n    this.bindSubMenuEvent();\n\n    // 窗口大小变化时，重新调整菜单位置\n    $window.on(\n      'resize',\n      $.throttle(() => this.readjust(), 100),\n    );\n  }\n\n  /**\n   * 是否为打开状态\n   */\n  private isOpen(): boolean {\n    return this.state === 'opening' || this.state === 'opened';\n  }\n\n  /**\n   * 触发组件事件\n   * @param name\n   */\n  private triggerEvent(name: EVENT): void {\n    componentEvent(name, 'menu', this.$element, this);\n  }\n\n  /**\n   * 调整主菜单位置\n   */\n  private readjust(): void {\n    let menuLeft;\n    let menuTop;\n\n    // 菜单位置和方向\n    let position: 'bottom' | 'top' | 'center';\n    let align: 'left' | 'right' | 'center';\n\n    // window 窗口的宽度和高度\n    const windowHeight = $window.height();\n    const windowWidth = $window.width();\n\n    // 配置参数\n    const gutter = this.options.gutter!;\n    const isCovered = this.isCovered;\n    const isFixed = this.options.fixed;\n\n    // 动画方向参数\n    let transformOriginX;\n    let transformOriginY;\n\n    // 菜单的原始宽度和高度\n    const menuWidth = this.$element.width();\n    const menuHeight = this.$element.height();\n\n    // 触发菜单的元素在窗口中的位置\n    const anchorRect = this.$anchor[0].getBoundingClientRect();\n    const anchorTop = anchorRect.top;\n    const anchorLeft = anchorRect.left;\n    const anchorHeight = anchorRect.height;\n    const anchorWidth = anchorRect.width;\n    const anchorBottom = windowHeight - anchorTop - anchorHeight;\n    const anchorRight = windowWidth - anchorLeft - anchorWidth;\n\n    // 触发元素相对其拥有定位属性的父元素的位置\n    const anchorOffsetTop = this.$anchor[0].offsetTop;\n    const anchorOffsetLeft = this.$anchor[0].offsetLeft;\n\n    // 自动判断菜单位置\n    if (this.options.position === 'auto') {\n      if (anchorBottom + (isCovered ? anchorHeight : 0) > menuHeight + gutter) {\n        // 判断下方是否放得下菜单\n        position = 'bottom';\n      } else if (\n        anchorTop + (isCovered ? anchorHeight : 0) >\n        menuHeight + gutter\n      ) {\n        // 判断上方是否放得下菜单\n        position = 'top';\n      } else {\n        // 上下都放不下，居中显示\n        position = 'center';\n      }\n    } else {\n      position = this.options.position!;\n    }\n\n    // 自动判断菜单对齐方式\n    if (this.options.align === 'auto') {\n      if (anchorRight + anchorWidth > menuWidth + gutter) {\n        // 判断右侧是否放得下菜单\n        align = 'left';\n      } else if (anchorLeft + anchorWidth > menuWidth + gutter) {\n        // 判断左侧是否放得下菜单\n        align = 'right';\n      } else {\n        // 左右都放不下，居中显示\n        align = 'center';\n      }\n    } else {\n      align = this.options.align!;\n    }\n\n    // 设置菜单位置\n    if (position === 'bottom') {\n      transformOriginY = '0';\n      menuTop =\n        (isCovered ? 0 : anchorHeight) +\n        (isFixed ? anchorTop : anchorOffsetTop);\n    } else if (position === 'top') {\n      transformOriginY = '100%';\n      menuTop =\n        (isCovered ? anchorHeight : 0) +\n        (isFixed ? anchorTop - menuHeight : anchorOffsetTop - menuHeight);\n    } else {\n      transformOriginY = '50%';\n\n      // =====================在窗口中居中\n      // 显示的菜单的高度，简单菜单高度不超过窗口高度，若超过了则在菜单内部显示滚动条\n      // 级联菜单内部不允许出现滚动条\n      let menuHeightTemp = menuHeight;\n\n      // 简单菜单比窗口高时，限制菜单高度\n      if (!this.isCascade) {\n        if (menuHeight + gutter * 2 > windowHeight) {\n          menuHeightTemp = windowHeight - gutter * 2;\n          this.$element.height(menuHeightTemp);\n        }\n      }\n\n      menuTop =\n        (windowHeight - menuHeightTemp) / 2 +\n        (isFixed ? 0 : anchorOffsetTop - anchorTop);\n    }\n\n    this.$element.css('top', `${menuTop}px`);\n\n    // 设置菜单对齐方式\n    if (align === 'left') {\n      transformOriginX = '0';\n      menuLeft = isFixed ? anchorLeft : anchorOffsetLeft;\n    } else if (align === 'right') {\n      transformOriginX = '100%';\n      menuLeft = isFixed\n        ? anchorLeft + anchorWidth - menuWidth\n        : anchorOffsetLeft + anchorWidth - menuWidth;\n    } else {\n      transformOriginX = '50%';\n\n      //=======================在窗口中居中\n      // 显示的菜单的宽度，菜单宽度不能超过窗口宽度\n      let menuWidthTemp = menuWidth;\n\n      // 菜单比窗口宽，限制菜单宽度\n      if (menuWidth + gutter * 2 > windowWidth) {\n        menuWidthTemp = windowWidth - gutter * 2;\n        this.$element.width(menuWidthTemp);\n      }\n\n      menuLeft =\n        (windowWidth - menuWidthTemp) / 2 +\n        (isFixed ? 0 : anchorOffsetLeft - anchorLeft);\n    }\n\n    this.$element.css('left', `${menuLeft}px`);\n\n    // 设置菜单动画方向\n    this.$element.transformOrigin(`${transformOriginX} ${transformOriginY}`);\n  }\n\n  /**\n   * 调整子菜单的位置\n   * @param $submenu\n   */\n  private readjustSubmenu($submenu: JQ): void {\n    const $item = $submenu.parent('.mdui-menu-item');\n\n    let submenuTop;\n    let submenuLeft;\n\n    // 子菜单位置和方向\n    let position: 'top' | 'bottom';\n    let align: 'left' | 'right';\n\n    // window 窗口的宽度和高度\n    const windowHeight = $window.height();\n    const windowWidth = $window.width();\n\n    // 动画方向参数\n    let transformOriginX;\n    let transformOriginY;\n\n    // 子菜单的原始宽度和高度\n    const submenuWidth = $submenu.width();\n    const submenuHeight = $submenu.height();\n\n    // 触发子菜单的菜单项的宽度高度\n    const itemRect = $item[0].getBoundingClientRect();\n    const itemWidth = itemRect.width;\n    const itemHeight = itemRect.height;\n    const itemLeft = itemRect.left;\n    const itemTop = itemRect.top;\n\n    // 判断菜单上下位置\n    if (windowHeight - itemTop > submenuHeight) {\n      // 判断下方是否放得下菜单\n      position = 'bottom';\n    } else if (itemTop + itemHeight > submenuHeight) {\n      // 判断上方是否放得下菜单\n      position = 'top';\n    } else {\n      // 默认放在下方\n      position = 'bottom';\n    }\n\n    // 判断菜单左右位置\n    if (windowWidth - itemLeft - itemWidth > submenuWidth) {\n      // 判断右侧是否放得下菜单\n      align = 'left';\n    } else if (itemLeft > submenuWidth) {\n      // 判断左侧是否放得下菜单\n      align = 'right';\n    } else {\n      // 默认放在右侧\n      align = 'left';\n    }\n\n    // 设置菜单位置\n    if (position === 'bottom') {\n      transformOriginY = '0';\n      submenuTop = '0';\n    } else if (position === 'top') {\n      transformOriginY = '100%';\n      submenuTop = -submenuHeight + itemHeight;\n    }\n\n    $submenu.css('top', `${submenuTop}px`);\n\n    // 设置菜单对齐方式\n    if (align === 'left') {\n      transformOriginX = '0';\n      submenuLeft = itemWidth;\n    } else if (align === 'right') {\n      transformOriginX = '100%';\n      submenuLeft = -submenuWidth;\n    }\n\n    $submenu.css('left', `${submenuLeft}px`);\n\n    // 设置菜单动画方向\n    $submenu.transformOrigin(`${transformOriginX} ${transformOriginY}`);\n  }\n\n  /**\n   * 打开子菜单\n   * @param $submenu\n   */\n  private openSubMenu($submenu: JQ): void {\n    this.readjustSubmenu($submenu);\n\n    $submenu\n      .addClass('mdui-menu-open')\n      .parent('.mdui-menu-item')\n      .addClass('mdui-menu-item-active');\n  }\n\n  /**\n   * 关闭子菜单，及其嵌套的子菜单\n   * @param $submenu\n   */\n  private closeSubMenu($submenu: JQ): void {\n    // 关闭子菜单\n    $submenu\n      .removeClass('mdui-menu-open')\n      .addClass('mdui-menu-closing')\n      .transitionEnd(() => $submenu.removeClass('mdui-menu-closing'))\n\n      // 移除激活状态的样式\n      .parent('.mdui-menu-item')\n      .removeClass('mdui-menu-item-active');\n\n    // 循环关闭嵌套的子菜单\n    $submenu.find('.mdui-menu').each((_, menu) => {\n      const $subSubmenu = $(menu);\n\n      $subSubmenu\n        .removeClass('mdui-menu-open')\n        .addClass('mdui-menu-closing')\n        .transitionEnd(() => $subSubmenu.removeClass('mdui-menu-closing'))\n        .parent('.mdui-menu-item')\n        .removeClass('mdui-menu-item-active');\n    });\n  }\n\n  /**\n   * 切换子菜单状态\n   * @param $submenu\n   */\n  private toggleSubMenu($submenu: JQ): void {\n    $submenu.hasClass('mdui-menu-open')\n      ? this.closeSubMenu($submenu)\n      : this.openSubMenu($submenu);\n  }\n\n  /**\n   * 绑定子菜单事件\n   */\n  private bindSubMenuEvent(): void {\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const that = this;\n\n    // 点击打开子菜单\n    this.$element.on('click', '.mdui-menu-item', function (event) {\n      const $item = $(this as HTMLElement);\n      const $target = $(event.target as HTMLElement);\n\n      // 禁用状态菜单不操作\n      if ($item.attr('disabled') !== undefined) {\n        return;\n      }\n\n      // 没有点击在子菜单的菜单项上时，不操作（点在了子菜单的空白区域、或分隔线上）\n      if ($target.is('.mdui-menu') || $target.is('.mdui-divider')) {\n        return;\n      }\n\n      // 阻止冒泡，点击菜单项时只在最后一级的 mdui-menu-item 上生效，不向上冒泡\n      if (!$target.parents('.mdui-menu-item').first().is($item)) {\n        return;\n      }\n\n      // 当前菜单的子菜单\n      const $submenu = $item.children('.mdui-menu');\n\n      // 先关闭除当前子菜单外的所有同级子菜单\n      $item\n        .parent('.mdui-menu')\n        .children('.mdui-menu-item')\n        .each((_, item) => {\n          const $tmpSubmenu = $(item).children('.mdui-menu');\n\n          if (\n            $tmpSubmenu.length &&\n            (!$submenu.length || !$tmpSubmenu.is($submenu))\n          ) {\n            that.closeSubMenu($tmpSubmenu);\n          }\n        });\n\n      // 切换当前子菜单\n      if ($submenu.length) {\n        that.toggleSubMenu($submenu);\n      }\n    });\n\n    if (this.options.subMenuTrigger === 'hover') {\n      // 临时存储 setTimeout 对象\n      let timeout: any = null;\n      let timeoutOpen: any = null;\n\n      this.$element.on('mouseover mouseout', '.mdui-menu-item', function (\n        event,\n      ) {\n        const $item = $(this as HTMLElement);\n        const eventType = event.type;\n        const $relatedTarget = $(\n          (event as MouseEvent).relatedTarget as HTMLElement,\n        );\n\n        // 禁用状态的菜单不操作\n        if ($item.attr('disabled') !== undefined) {\n          return;\n        }\n\n        // 用 mouseover 模拟 mouseenter\n        if (eventType === 'mouseover') {\n          if (\n            !$item.is($relatedTarget) &&\n            contains($item[0], $relatedTarget[0])\n          ) {\n            return;\n          }\n        }\n\n        // 用 mouseout 模拟 mouseleave\n        else if (eventType === 'mouseout') {\n          if (\n            $item.is($relatedTarget) ||\n            contains($item[0], $relatedTarget[0])\n          ) {\n            return;\n          }\n        }\n\n        // 当前菜单项下的子菜单，未必存在\n        const $submenu = $item.children('.mdui-menu');\n\n        // 鼠标移入菜单项时，显示菜单项下的子菜单\n        if (eventType === 'mouseover') {\n          if ($submenu.length) {\n            // 当前子菜单准备打开时，如果当前子菜单正准备着关闭，不用再关闭了\n            const tmpClose = $submenu.data('timeoutClose.mdui.menu');\n            if (tmpClose) {\n              clearTimeout(tmpClose);\n            }\n\n            // 如果当前子菜单已经打开，不操作\n            if ($submenu.hasClass('mdui-menu-open')) {\n              return;\n            }\n\n            // 当前子菜单准备打开时，其他准备打开的子菜单不用再打开了\n            clearTimeout(timeoutOpen);\n\n            // 准备打开当前子菜单\n            timeout = timeoutOpen = setTimeout(\n              () => that.openSubMenu($submenu),\n              that.options.subMenuDelay,\n            );\n\n            $submenu.data('timeoutOpen.mdui.menu', timeout);\n          }\n        }\n\n        // 鼠标移出菜单项时，关闭菜单项下的子菜单\n        else if (eventType === 'mouseout') {\n          if ($submenu.length) {\n            // 鼠标移出菜单项时，如果当前菜单项下的子菜单正准备打开，不用再打开了\n            const tmpOpen = $submenu.data('timeoutOpen.mdui.menu');\n            if (tmpOpen) {\n              clearTimeout(tmpOpen);\n            }\n\n            // 准备关闭当前子菜单\n            timeout = setTimeout(\n              () => that.closeSubMenu($submenu),\n              that.options.subMenuDelay,\n            );\n\n            $submenu.data('timeoutClose.mdui.menu', timeout);\n          }\n        }\n      });\n    }\n  }\n\n  /**\n   * 动画结束回调\n   */\n  private transitionEnd(): void {\n    this.$element.removeClass('mdui-menu-closing');\n\n    if (this.state === 'opening') {\n      this.state = 'opened';\n      this.triggerEvent('opened');\n    }\n\n    if (this.state === 'closing') {\n      this.state = 'closed';\n      this.triggerEvent('closed');\n\n      // 关闭后，恢复菜单样式到默认状态，并恢复 fixed 定位\n      this.$element.css({\n        top: '',\n        left: '',\n        width: '',\n        position: 'fixed',\n      });\n    }\n  }\n\n  /**\n   * 切换菜单状态\n   */\n  public toggle(): void {\n    this.isOpen() ? this.close() : this.open();\n  }\n\n  /**\n   * 打开菜单\n   */\n  public open(): void {\n    if (this.isOpen()) {\n      return;\n    }\n\n    this.state = 'opening';\n    this.triggerEvent('open');\n\n    this.readjust();\n\n    this.$element\n      // 菜单隐藏状态使用使用 fixed 定位。\n      .css('position', this.options.fixed ? 'fixed' : 'absolute')\n      .addClass('mdui-menu-open')\n      .transitionEnd(() => this.transitionEnd());\n  }\n\n  /**\n   * 关闭菜单\n   */\n  public close(): void {\n    if (!this.isOpen()) {\n      return;\n    }\n\n    this.state = 'closing';\n    this.triggerEvent('close');\n\n    // 菜单开始关闭时，关闭所有子菜单\n    this.$element.find('.mdui-menu').each((_, submenu) => {\n      this.closeSubMenu($(submenu));\n    });\n\n    this.$element\n      .removeClass('mdui-menu-open')\n      .addClass('mdui-menu-closing')\n      .transitionEnd(() => this.transitionEnd());\n  }\n}\n\nmdui.Menu = Menu;\n", "import $ from 'mdui.jq/es/$';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/on';\nimport mdui from '../../mdui';\nimport { $document } from '../../utils/dom';\nimport { parseOptions } from '../../utils/parseOptions';\nimport './index';\n\nconst customAttr = 'mdui-menu';\nconst dataName = '_mdui_menu';\n\ntype OPTIONS = {\n  target: string;\n  position?: 'auto' | 'top' | 'bottom' | 'center';\n  align?: 'auto' | 'left' | 'right' | 'center';\n  gutter?: number;\n  fixed?: boolean;\n  covered?: boolean | 'auto';\n  subMenuTrigger?: 'click' | 'hover';\n  subMenuDelay?: number;\n};\n\n$(() => {\n  $document.on('click', `[${customAttr}]`, function () {\n    const $this = $(this as HTMLElement);\n    let instance = $this.data(dataName);\n\n    if (!instance) {\n      const options = parseOptions(this as HTMLElement, customAttr) as OPTIONS;\n      const menuSelector = options.target;\n      // @ts-ignore\n      delete options.target;\n\n      instance = new mdui.Menu($this, menuSelector, options);\n      $this.data(dataName, instance);\n\n      instance.toggle();\n    }\n  });\n});\n"], "names": ["get", "set", "DEFAULT_OPTIONS", "customAttr", "dataName", "currentInst", "queueName"], "mappings": ";;;;;AAGA,SAAS,UAAU,CAAC,MAAM,EAAE;AAC5B,IAAI,OAAO,OAAO,MAAM,KAAK,UAAU,CAAC;AACxC,CAAC;AACD,SAAS,QAAQ,CAAC,MAAM,EAAE;AAC1B,IAAI,OAAO,OAAO,MAAM,KAAK,QAAQ,CAAC;AACtC,CAAC;AACD,SAAS,QAAQ,CAAC,MAAM,EAAE;AAC1B,IAAI,OAAO,OAAO,MAAM,KAAK,QAAQ,CAAC;AACtC,CAAC;AACD,SAAS,SAAS,CAAC,MAAM,EAAE;AAC3B,IAAI,OAAO,OAAO,MAAM,KAAK,SAAS,CAAC;AACvC,CAAC;AACD,SAAS,WAAW,CAAC,MAAM,EAAE;AAC7B,IAAI,OAAO,OAAO,MAAM,KAAK,WAAW,CAAC;AACzC,CAAC;AACD,SAAS,MAAM,CAAC,MAAM,EAAE;AACxB,IAAI,OAAO,MAAM,KAAK,IAAI,CAAC;AAC3B,CAAC;AACD,SAAS,QAAQ,CAAC,MAAM,EAAE;AAC1B,IAAI,OAAO,MAAM,YAAY,MAAM,CAAC;AACpC,CAAC;AACD,SAAS,UAAU,CAAC,MAAM,EAAE;AAC5B,IAAI,OAAO,MAAM,YAAY,QAAQ,CAAC;AACtC,CAAC;AACD,SAAS,SAAS,CAAC,MAAM,EAAE;AAC3B,IAAI,OAAO,MAAM,YAAY,OAAO,CAAC;AACrC,CAAC;AACD,SAAS,MAAM,CAAC,MAAM,EAAE;AACxB,IAAI,OAAO,MAAM,YAAY,IAAI,CAAC;AAClC,CAAC;AACD;AACA;AACA;AACA,SAAS,IAAI,GAAG;AAChB;AACA,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC;AAC1C,CAAC;AACD,SAAS,WAAW,CAAC,MAAM,EAAE;AAC7B,IAAI,IAAI,UAAU,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,MAAM,CAAC,EAAE;AAChD,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,IAAI,OAAO,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACnC,CAAC;AACD,SAAS,YAAY,CAAC,MAAM,EAAE;AAC9B,IAAI,OAAO,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,KAAK,IAAI,CAAC;AACzD,CAAC;AACD,SAAS,SAAS,CAAC,MAAM,EAAE;AAC3B,IAAI,OAAO,UAAU,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,eAAe,GAAG,MAAM,CAAC;AAChE,CAAC;AACD;AACA;AACA;AACA;AACA,SAAS,WAAW,CAAC,MAAM,EAAE;AAC7B,IAAI,OAAO,MAAM;AACjB,SAAS,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC;AAChC,SAAS,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,MAAM,KAAK,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC;AACnE,CAAC;AACD;AACA;AACA;AACA;AACA,SAAS,WAAW,CAAC,MAAM,EAAE;AAC7B,IAAI,OAAO,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,QAAQ,KAAK,GAAG,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC;AAChF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,SAAS,qBAAqB,CAAC,OAAO,EAAE,IAAI,EAAE;AAC9C,IAAI,OAAO,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,gBAAgB,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;AAChF,CAAC;AACD;AACA;AACA;AACA;AACA,SAAS,WAAW,CAAC,OAAO,EAAE;AAC9B,IAAI,OAAO,qBAAqB,CAAC,OAAO,EAAE,YAAY,CAAC,KAAK,YAAY,CAAC;AACzE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,aAAa,CAAC,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE;AAClD,IAAI,MAAM,QAAQ,GAAG,SAAS,KAAK,OAAO,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AACnF,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,KAAK;AAC7C,QAAQ,IAAI,IAAI,GAAG,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC3C,QAAQ,IAAI,KAAK,KAAK,QAAQ,EAAE;AAChC,YAAY,IAAI,IAAI,OAAO,CAAC;AAC5B,SAAS;AACT,QAAQ,OAAO,IAAI,GAAG,UAAU,CAAC,qBAAqB,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;AAC9E,KAAK,EAAE,CAAC,CAAC,CAAC;AACV,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,SAAS,QAAQ,CAAC,OAAO,EAAE,IAAI,EAAE;AACjC;AACA,IAAI,IAAI,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,QAAQ,EAAE;AAC/C,QAAQ,MAAM,WAAW,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC,IAAI,CAAC,CAAC;AAClE,QAAQ,IAAI,WAAW,CAAC,OAAO,CAAC,EAAE;AAClC,YAAY,OAAO,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC;AACtC,SAAS;AACT,QAAQ,OAAO,CAAC,EAAE,WAAW;AAC7B,YAAY,aAAa,CAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,CAAC;AAClD,YAAY,aAAa,CAAC,OAAO,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;AACxD,KAAK;AACL,IAAI,OAAO,qBAAqB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AAChD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,SAAS,kBAAkB,CAAC,MAAM,EAAE,MAAM,EAAE;AAC5C,IAAI,MAAM,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;AACtD,IAAI,UAAU,CAAC,SAAS,GAAG,MAAM,CAAC;AAClC,IAAI,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AAChD,CAAC;AACD;AACA;AACA;AACA,SAAS,WAAW,GAAG;AACvB,IAAI,OAAO,KAAK,CAAC;AACjB,CAAC;AACD;AACA;AACA;AACA,MAAM,SAAS,GAAG;AAClB,IAAI,yBAAyB;AAC7B,IAAI,aAAa;AACjB,IAAI,aAAa;AACjB,IAAI,UAAU;AACd,IAAI,YAAY;AAChB,IAAI,YAAY;AAChB,IAAI,UAAU;AACd,IAAI,YAAY;AAChB,IAAI,eAAe;AACnB,IAAI,iBAAiB;AACrB,IAAI,SAAS;AACb,IAAI,YAAY;AAChB,IAAI,cAAc;AAClB,IAAI,YAAY;AAChB,IAAI,SAAS;AACb,IAAI,OAAO;AACX,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI,QAAQ;AACZ,IAAI,MAAM;AACV,CAAC;;AC5JD,SAAS,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE;AAChC,IAAI,IAAI,WAAW,CAAC,MAAM,CAAC,EAAE;AAC7B,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;AACnD,YAAY,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;AAClE,gBAAgB,OAAO,MAAM,CAAC;AAC9B,aAAa;AACb,SAAS;AACT,KAAK;AACL,SAAS;AACT,QAAQ,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACzC,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;AACjD,YAAY,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;AACpF,gBAAgB,OAAO,MAAM,CAAC;AAC9B,aAAa;AACb,SAAS;AACT,KAAK;AACL,IAAI,OAAO,MAAM,CAAC;AAClB;;ACjBA;AACA;AACA;AACO,MAAM,EAAE,CAAC;AAChB,IAAI,WAAW,CAAC,GAAG,EAAE;AACrB,QAAQ,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AACxB,QAAQ,IAAI,CAAC,GAAG,EAAE;AAClB,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS;AACT,QAAQ,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,KAAK;AAC/B;AACA,YAAY,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AAC3B,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;AACjC,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL;;ACdA,SAAS,IAAI,GAAG;AAChB,IAAI,MAAM,CAAC,GAAG,UAAU,QAAQ,EAAE;AAClC,QAAQ,IAAI,CAAC,QAAQ,EAAE;AACvB,YAAY,OAAO,IAAI,EAAE,EAAE,CAAC;AAC5B,SAAS;AACT;AACA,QAAQ,IAAI,QAAQ,YAAY,EAAE,EAAE;AACpC,YAAY,OAAO,QAAQ,CAAC;AAC5B,SAAS;AACT;AACA,QAAQ,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE;AAClC,YAAY,IAAI,6BAA6B,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;AACvE,gBAAgB,QAAQ,CAAC,IAAI,EAAE;AAC/B,gBAAgB,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;AAC3C,aAAa;AACb,iBAAiB;AACjB,gBAAgB,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,MAAM,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;AACvG,aAAa;AACb,YAAY,OAAO,IAAI,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;AACtC,SAAS;AACT;AACA,QAAQ,IAAI,QAAQ,CAAC,QAAQ,CAAC,EAAE;AAChC,YAAY,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;AACzC;AACA,YAAY,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;AAClE,gBAAgB,IAAI,QAAQ,GAAG,KAAK,CAAC;AACrC,gBAAgB,MAAM,IAAI,GAAG;AAC7B,oBAAoB,EAAE,EAAE,IAAI;AAC5B,oBAAoB,EAAE,EAAE,OAAO;AAC/B,oBAAoB,EAAE,EAAE,IAAI;AAC5B,oBAAoB,EAAE,EAAE,IAAI;AAC5B,oBAAoB,KAAK,EAAE,OAAO;AAClC,oBAAoB,MAAM,EAAE,QAAQ;AACpC,iBAAiB,CAAC;AAClB,gBAAgB,IAAI,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,KAAK;AACpD,oBAAoB,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;AAC5D,wBAAwB,QAAQ,GAAG,SAAS,CAAC;AAC7C,wBAAwB,OAAO,KAAK,CAAC;AACrC,qBAAqB;AACrB,oBAAoB,OAAO;AAC3B,iBAAiB,CAAC,CAAC;AACnB,gBAAgB,OAAO,IAAI,EAAE,CAAC,kBAAkB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;AAClE,aAAa;AACb;AACA,YAAY,MAAM,YAAY,GAAG,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;AACpF,YAAY,IAAI,CAAC,YAAY,EAAE;AAC/B,gBAAgB,OAAO,IAAI,EAAE,CAAC,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC;AACnE,aAAa;AACb,YAAY,MAAM,OAAO,GAAG,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACvE,YAAY,IAAI,OAAO,EAAE;AACzB,gBAAgB,OAAO,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;AACzC,aAAa;AACb,YAAY,OAAO,IAAI,EAAE,EAAE,CAAC;AAC5B,SAAS;AACT,QAAQ,IAAI,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;AACxD,YAAY,OAAO,IAAI,EAAE,CAAC,QAAQ,CAAC,CAAC;AACpC,SAAS;AACT,QAAQ,OAAO,IAAI,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;AAClC,KAAK,CAAC;AACN,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,SAAS,CAAC;AACxB,IAAI,OAAO,CAAC,CAAC;AACb,CAAC;AACD,MAAM,CAAC,GAAG,IAAI,EAAE;;AC9DhB;AACA;AACA,UAAU,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC;MAE9C,IAAI,GAAG;IACX,CAAC,EAAE,CAAC;;;ACNN,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,UAAU,QAAQ,EAAE;AAChC,IAAI,OAAO,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAChC,CAAC;;ACHD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,QAAQ,CAAC,SAAS,EAAE,QAAQ,EAAE;AACvC,IAAI,OAAO,SAAS,KAAK,QAAQ,IAAI,SAAS,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC7E;;ACbA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,KAAK,CAAC,KAAK,EAAE,MAAM,EAAE;AAC9B,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,KAAK,KAAK;AAC/B,QAAQ,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC1B,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,KAAK,CAAC;AACjB;;ACfA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,UAAU,KAAK,EAAE;AAC5B,IAAI,OAAO,KAAK,KAAK,SAAS;AAC9B,UAAU,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;AAC7B,UAAU,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;AACzD,CAAC;;ACAD,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,UAAU,QAAQ,EAAE;AAChC,IAAI,MAAM,aAAa,GAAG,EAAE,CAAC;AAC7B,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;AAC9B,QAAQ,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AAC1E,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,IAAI,EAAE,CAAC,aAAa,CAAC,CAAC;AACjC,CAAC;;ACPD;AACA,MAAM,QAAQ,GAAG,EAAE,CAAC;AACpB;AACA,IAAI,aAAa,GAAG,CAAC,CAAC;AACtB;AACA;AACA;AACA,SAAS,YAAY,CAAC,OAAO,EAAE;AAC/B,IAAI,MAAM,GAAG,GAAG,cAAc,CAAC;AAC/B;AACA,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;AACvB;AACA,QAAQ,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,aAAa,CAAC;AACvC,KAAK;AACL;AACA,IAAI,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;AACxB,CAAC;AACD;AACA;AACA;AACA,SAAS,KAAK,CAAC,IAAI,EAAE;AACrB,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAClC,IAAI,OAAO;AACX,QAAQ,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;AACtB,QAAQ,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC;AAC3C,KAAK,CAAC;AACN,CAAC;AACD;AACA;AACA;AACA,SAAS,UAAU,CAAC,EAAE,EAAE;AACxB,IAAI,OAAO,IAAI,MAAM,CAAC,SAAS,GAAG,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,SAAS,CAAC,CAAC;AACxE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,WAAW,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE;AACpD,IAAI,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;AAC9B,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC,OAAO,KAAK,OAAO;AAC9E,SAAS,CAAC,KAAK,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,CAAC;AACpD,SAAS,CAAC,KAAK,CAAC,EAAE,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AAC5D,SAAS,CAAC,IAAI,IAAI,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,YAAY,CAAC,IAAI,CAAC,CAAC;AACpE,SAAS,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC;AACtD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE;AACnD,IAAI,MAAM,SAAS,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;AAC5C,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;AAC9B,QAAQ,QAAQ,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;AACjC,KAAK;AACL;AACA,IAAI,IAAI,UAAU,GAAG,KAAK,CAAC;AAC3B,IAAI,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE;AAC/C,QAAQ,UAAU,GAAG,IAAI,CAAC;AAC1B,KAAK;AACL,IAAI,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AACvC,QAAQ,IAAI,CAAC,IAAI,EAAE;AACnB,YAAY,OAAO;AACnB,SAAS;AACT,QAAQ,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;AAClC,QAAQ,SAAS,MAAM,CAAC,CAAC,EAAE,IAAI,EAAE;AACjC;AACA,YAAY,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI;AAC1C;AACA,YAAY,CAAC,CAAC,OAAO,KAAK,SAAS,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;AACnE,YAAY,IAAI,MAAM,KAAK,KAAK,EAAE;AAClC,gBAAgB,CAAC,CAAC,cAAc,EAAE,CAAC;AACnC,gBAAgB,CAAC,CAAC,eAAe,EAAE,CAAC;AACpC,aAAa;AACb,SAAS;AACT,QAAQ,SAAS,OAAO,CAAC,CAAC,EAAE;AAC5B;AACA,YAAY,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE;AAC5D,gBAAgB,OAAO;AACvB,aAAa;AACb;AACA,YAAY,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC;AAC3B,YAAY,IAAI,QAAQ,EAAE;AAC1B;AACA,gBAAgB,CAAC,CAAC,OAAO,CAAC;AAC1B,qBAAqB,IAAI,CAAC,QAAQ,CAAC;AACnC,qBAAqB,GAAG,EAAE;AAC1B,qBAAqB,OAAO,EAAE;AAC9B,qBAAqB,OAAO,CAAC,CAAC,IAAI,KAAK;AACvC,oBAAoB,IAAI,IAAI,KAAK,CAAC,CAAC,MAAM;AACzC,wBAAwB,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE;AAClD,wBAAwB,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AACxC,qBAAqB;AACrB,iBAAiB,CAAC,CAAC;AACnB,aAAa;AACb,iBAAiB;AACjB;AACA,gBAAgB,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;AACnC,aAAa;AACb,SAAS;AACT,QAAQ,MAAM,OAAO,GAAG;AACxB,YAAY,IAAI,EAAE,KAAK,CAAC,IAAI;AAC5B,YAAY,EAAE,EAAE,KAAK,CAAC,EAAE;AACxB,YAAY,IAAI;AAChB,YAAY,QAAQ;AACpB,YAAY,EAAE,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC,MAAM;AAC1C,YAAY,KAAK,EAAE,OAAO;AAC1B,SAAS,CAAC;AACV,QAAQ,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC1C,QAAQ,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;AACpE,KAAK,CAAC,CAAC;AACP,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE;AAChD,IAAI,MAAM,iBAAiB,GAAG,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC;AACpE,IAAI,MAAM,WAAW,GAAG,CAAC,OAAO,KAAK;AACrC,QAAQ,OAAO,iBAAiB,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AAC7C,QAAQ,OAAO,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACxE,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,QAAQ,iBAAiB,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;AACrE,KAAK;AACL,SAAS;AACT,QAAQ,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AAC3C,YAAY,IAAI,IAAI,EAAE;AACtB,gBAAgB,WAAW,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;AACtG,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK;AACL;;AC9IA,CAAC,CAAC,EAAE,CAAC,OAAO,GAAG,UAAU,IAAI,EAAE,eAAe,EAAE;AAChD,IAAI,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;AAC9B,IAAI,IAAI,WAAW,CAAC;AACpB,IAAI,MAAM,WAAW,GAAG;AACxB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,UAAU,EAAE,IAAI;AACxB,KAAK,CAAC;AACN,IAAI,MAAM,YAAY,GAAG,CAAC,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACjG,IAAI,IAAI,YAAY,EAAE;AACtB;AACA,QAAQ,WAAW,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;AAC9D,KAAK;AACL,SAAS;AACT,QAAQ,WAAW,CAAC,MAAM,GAAG,eAAe,CAAC;AAC7C,QAAQ,WAAW,GAAG,IAAI,WAAW,CAAC,KAAK,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;AAC/D,KAAK;AACL;AACA,IAAI,WAAW,CAAC,OAAO,GAAG,eAAe,CAAC;AAC1C;AACA,IAAI,WAAW,CAAC,GAAG,GAAG,KAAK,CAAC,EAAE,CAAC;AAC/B,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;AACjC,QAAQ,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;AACxC,KAAK,CAAC,CAAC;AACP,CAAC;;ACxBD,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE;AAC7C,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAC7B,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,MAAM,KAAK;AACjC,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,KAAK,KAAK;AACtC,YAAY,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;AACrC,gBAAgB,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;AACrC,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,MAAM,CAAC;AAClB;;ACVA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,KAAK,CAAC,GAAG,EAAE;AACpB,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;AACnD,QAAQ,OAAO,EAAE,CAAC;AAClB,KAAK;AACL,IAAI,MAAM,IAAI,GAAG,EAAE,CAAC;AACpB,IAAI,SAAS,WAAW,CAAC,GAAG,EAAE,KAAK,EAAE;AACrC,QAAQ,IAAI,MAAM,CAAC;AACnB,QAAQ,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE;AACjC,YAAY,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK;AAClC,gBAAgB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;AAC9D,oBAAoB,MAAM,GAAG,EAAE,CAAC;AAChC,iBAAiB;AACjB,qBAAqB;AACrB,oBAAoB,MAAM,GAAG,CAAC,CAAC;AAC/B,iBAAiB;AACjB,gBAAgB,WAAW,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACpD,aAAa,CAAC,CAAC;AACf,SAAS;AACT,aAAa;AACb,YAAY,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,EAAE,EAAE;AAC/C,gBAAgB,MAAM,GAAG,GAAG,CAAC;AAC7B,aAAa;AACb,iBAAiB;AACjB,gBAAgB,MAAM,GAAG,CAAC,CAAC,EAAE,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACzD,aAAa;AACb,YAAY,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC;AACxD,SAAS;AACT,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;AAC5B,QAAQ,IAAI,CAAC,GAAG,EAAE,YAAY;AAC9B,YAAY,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AAC/C,SAAS,CAAC,CAAC;AACX,KAAK;AACL,SAAS;AACT,QAAQ,IAAI,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;AAC/B,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC1B;;ACpEA;AACA,MAAM,aAAa,GAAG,EAAE,CAAC;AACzB;AACA,MAAM,UAAU,GAAG;AACnB,IAAI,SAAS,EAAE,iBAAiB;AAChC,IAAI,WAAW,EAAE,mBAAmB;AACpC,IAAI,SAAS,EAAE,iBAAiB;AAChC,IAAI,YAAY,EAAE,oBAAoB;AACtC,CAAC;;ACDD;AACA;AACA;AACA;AACA,SAAS,iBAAiB,CAAC,MAAM,EAAE;AACnC,IAAI,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAChD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,SAAS,WAAW,CAAC,GAAG,EAAE,KAAK,EAAE;AACjC,IAAI,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;AACvD,CAAC;AACD;AACA;AACA;AACA;AACA,SAAS,YAAY,CAAC,OAAO,EAAE;AAC/B;AACA,IAAI,MAAM,QAAQ,GAAG;AACrB,QAAQ,GAAG,EAAE,EAAE;AACf,QAAQ,MAAM,EAAE,KAAK;AACrB,QAAQ,IAAI,EAAE,EAAE;AAChB,QAAQ,WAAW,EAAE,IAAI;AACzB,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,QAAQ,EAAE,EAAE;AACpB,QAAQ,QAAQ,EAAE,EAAE;AACpB,QAAQ,OAAO,EAAE,EAAE;AACnB,QAAQ,SAAS,EAAE,EAAE;AACrB,QAAQ,UAAU,EAAE,EAAE;AACtB,QAAQ,QAAQ,EAAE,MAAM;AACxB,QAAQ,WAAW,EAAE,mCAAmC;AACxD,QAAQ,OAAO,EAAE,CAAC;AAClB,QAAQ,MAAM,EAAE,IAAI;AACpB,KAAK,CAAC;AACN;AACA,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC,GAAG,EAAE,KAAK,KAAK;AACxC,QAAQ,MAAM,SAAS,GAAG;AAC1B,YAAY,YAAY;AACxB,YAAY,SAAS;AACrB,YAAY,OAAO;AACnB,YAAY,UAAU;AACtB,YAAY,YAAY;AACxB,SAAS,CAAC;AACV;AACA,QAAQ,IAAI,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;AAC/D,YAAY,QAAQ,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAClC,SAAS;AACT,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,MAAM,CAAC,EAAE,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;AACzC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,IAAI,CAAC,OAAO,EAAE;AACvB;AACA,IAAI,IAAI,UAAU,GAAG,KAAK,CAAC;AAC3B;AACA,IAAI,MAAM,WAAW,GAAG,EAAE,CAAC;AAC3B;AACA,IAAI,MAAM,aAAa,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;AAChD,IAAI,IAAI,GAAG,GAAG,aAAa,CAAC,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;AAC9D,IAAI,MAAM,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;AACtD,IAAI,IAAI,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC;AAClC,IAAI,MAAM,WAAW,GAAG,aAAa,CAAC,WAAW,CAAC;AAClD,IAAI,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;AACtC,IAAI,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;AACtC,IAAI,MAAM,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;AAC5C,IAAI,MAAM,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;AAC5C,IAAI,MAAM,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC;AAC1C,IAAI,MAAM,SAAS,GAAG,aAAa,CAAC,SAAS,CAAC;AAC9C,IAAI,MAAM,UAAU,GAAG,aAAa,CAAC,UAAU,CAAC;AAChD,IAAI,MAAM,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;AAC5C,IAAI,MAAM,WAAW,GAAG,aAAa,CAAC,WAAW,CAAC;AAClD,IAAI,MAAM,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC;AAC1C,IAAI,MAAM,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC;AACxC;AACA;AACA,IAAI,IAAI,IAAI;AACZ,SAAS,iBAAiB,CAAC,MAAM,CAAC,IAAI,WAAW,CAAC;AAClD,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC;AACvB,QAAQ,EAAE,IAAI,YAAY,WAAW,CAAC;AACtC,QAAQ,EAAE,IAAI,YAAY,IAAI,CAAC;AAC/B,QAAQ,EAAE,IAAI,YAAY,QAAQ,CAAC;AACnC,QAAQ,EAAE,IAAI,YAAY,QAAQ,CAAC,EAAE;AACrC,QAAQ,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;AAC3B,KAAK;AACL;AACA,IAAI,IAAI,IAAI,IAAI,iBAAiB,CAAC,MAAM,CAAC,EAAE;AAC3C;AACA,QAAQ,GAAG,GAAG,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AACrC,QAAQ,IAAI,GAAG,IAAI,CAAC;AACpB,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,SAAS,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,EAAE;AACvD;AACA,QAAQ,IAAI,MAAM,EAAE;AACpB,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAC/C,SAAS;AACT;AACA,QAAQ,IAAI,OAAO,CAAC;AACpB,QAAQ,IAAI,OAAO,CAAC;AACpB,QAAQ,IAAI,QAAQ,EAAE;AACtB;AACA,YAAY,IAAI,QAAQ,IAAI,aAAa,EAAE;AAC3C;AACA,gBAAgB,OAAO,GAAG,aAAa,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AAC3D,aAAa;AACb;AACA,YAAY,IAAI,aAAa,CAAC,QAAQ,CAAC,EAAE;AACzC;AACA,gBAAgB,OAAO,GAAG,aAAa,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AAC3D,aAAa;AACb;AACA,YAAY,IAAI,QAAQ,KAAK,YAAY;AACzC,iBAAiB,OAAO,KAAK,KAAK,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE;AAC1D,gBAAgB,UAAU,GAAG,IAAI,CAAC;AAClC,aAAa;AACb,SAAS;AACT,KAAK;AACL;AACA,IAAI,SAAS,GAAG,GAAG;AACnB,QAAQ,IAAI,UAAU,CAAC;AACvB,QAAQ,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAK;AAChD;AACA,YAAY,IAAI,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE;AACrD,gBAAgB,GAAG,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AAC1D,aAAa;AACb;AACA,YAAY,MAAM,GAAG,GAAG,IAAI,cAAc,EAAE,CAAC;AAC7C,YAAY,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAC7D,YAAY,IAAI,WAAW;AAC3B,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,WAAW,KAAK,KAAK,CAAC,EAAE;AAC/E,gBAAgB,GAAG,CAAC,gBAAgB,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;AAClE,aAAa;AACb;AACA,YAAY,IAAI,QAAQ,KAAK,MAAM,EAAE;AACrC,gBAAgB,GAAG,CAAC,gBAAgB,CAAC,QAAQ,EAAE,mCAAmC,CAAC,CAAC;AACpF,aAAa;AACb;AACA,YAAY,IAAI,OAAO,EAAE;AACzB,gBAAgB,IAAI,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,KAAK,KAAK;AAC9C;AACA,oBAAoB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;AAC7C,wBAAwB,GAAG,CAAC,gBAAgB,CAAC,GAAG,EAAE,KAAK,GAAG,EAAE,CAAC,CAAC;AAC9D,qBAAqB;AACrB,iBAAiB,CAAC,CAAC;AACnB,aAAa;AACb;AACA,YAAY,MAAM,WAAW,GAAG,wBAAwB,CAAC,IAAI,CAAC,GAAG,CAAC;AAClE,gBAAgB,MAAM,CAAC,EAAE,KAAK,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;AACnD,YAAY,IAAI,CAAC,WAAW,EAAE;AAC9B,gBAAgB,GAAG,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;AAC3E,aAAa;AACb,YAAY,IAAI,SAAS,EAAE;AAC3B,gBAAgB,IAAI,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,KAAK,KAAK;AAChD;AACA,oBAAoB,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AACrC,iBAAiB,CAAC,CAAC;AACnB,aAAa;AACb,YAAY,WAAW,CAAC,GAAG,GAAG,GAAG,CAAC;AAClC,YAAY,WAAW,CAAC,OAAO,GAAG,aAAa,CAAC;AAChD,YAAY,IAAI,UAAU,CAAC;AAC3B,YAAY,GAAG,CAAC,MAAM,GAAG,YAAY;AACrC,gBAAgB,IAAI,UAAU,EAAE;AAChC,oBAAoB,YAAY,CAAC,UAAU,CAAC,CAAC;AAC7C,iBAAiB;AACjB;AACA,gBAAgB,MAAM,mBAAmB,GAAG,CAAC,GAAG,CAAC,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,MAAM,GAAG,GAAG;AAClF,oBAAoB,GAAG,CAAC,MAAM,KAAK,GAAG;AACtC,oBAAoB,GAAG,CAAC,MAAM,KAAK,CAAC,CAAC;AACrC,gBAAgB,IAAI,YAAY,CAAC;AACjC,gBAAgB,IAAI,mBAAmB,EAAE;AACzC,oBAAoB,IAAI,GAAG,CAAC,MAAM,KAAK,GAAG,IAAI,MAAM,KAAK,MAAM,EAAE;AACjE,wBAAwB,UAAU,GAAG,WAAW,CAAC;AACjD,qBAAqB;AACrB,yBAAyB,IAAI,GAAG,CAAC,MAAM,KAAK,GAAG,EAAE;AACjD,wBAAwB,UAAU,GAAG,aAAa,CAAC;AACnD,qBAAqB;AACrB,yBAAyB;AACzB,wBAAwB,UAAU,GAAG,SAAS,CAAC;AAC/C,qBAAqB;AACrB,oBAAoB,IAAI,QAAQ,KAAK,MAAM,EAAE;AAC7C,wBAAwB,IAAI;AAC5B,4BAA4B,YAAY;AACxC,gCAAgC,MAAM,KAAK,MAAM,GAAG,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;AAC7F,4BAA4B,WAAW,CAAC,IAAI,GAAG,YAAY,CAAC;AAC5D,yBAAyB;AACzB,wBAAwB,OAAO,GAAG,EAAE;AACpC,4BAA4B,UAAU,GAAG,aAAa,CAAC;AACvD,4BAA4B,OAAO,CAAC,UAAU,CAAC,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;AACjG,4BAA4B,MAAM,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;AAC1D,yBAAyB;AACzB,wBAAwB,IAAI,UAAU,KAAK,aAAa,EAAE;AAC1D,4BAA4B,OAAO,CAAC,UAAU,CAAC,WAAW,EAAE,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;AACnH,4BAA4B,OAAO,CAAC,YAAY,CAAC,CAAC;AAClD,yBAAyB;AACzB,qBAAqB;AACrB,yBAAyB;AACzB,wBAAwB,YAAY;AACpC,4BAA4B,MAAM,KAAK,MAAM;AAC7C,kCAAkC,SAAS;AAC3C,kCAAkC,GAAG,CAAC,YAAY,KAAK,MAAM,IAAI,GAAG,CAAC,YAAY,KAAK,EAAE;AACxF,sCAAsC,GAAG,CAAC,YAAY;AACtD,sCAAsC,GAAG,CAAC,QAAQ,CAAC;AACnD,wBAAwB,WAAW,CAAC,IAAI,GAAG,YAAY,CAAC;AACxD,wBAAwB,OAAO,CAAC,UAAU,CAAC,WAAW,EAAE,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;AAC/G,wBAAwB,OAAO,CAAC,YAAY,CAAC,CAAC;AAC9C,qBAAqB;AACrB,iBAAiB;AACjB,qBAAqB;AACrB,oBAAoB,UAAU,GAAG,OAAO,CAAC;AACzC,oBAAoB,OAAO,CAAC,UAAU,CAAC,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;AACzF,oBAAoB,MAAM,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;AAClD,iBAAiB;AACjB;AACA,gBAAgB,IAAI,CAAC,CAAC,aAAa,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,KAAK;AAC1E,oBAAoB,IAAI,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;AAClD,wBAAwB,IAAI,mBAAmB,EAAE;AACjD,4BAA4B,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;AAC5E,yBAAyB;AACzB,6BAA6B;AAC7B,4BAA4B,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;AAC9D,yBAAyB;AACzB,qBAAqB;AACrB,iBAAiB,CAAC,CAAC;AACnB,gBAAgB,OAAO,CAAC,UAAU,CAAC,YAAY,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;AAC3F,aAAa,CAAC;AACd,YAAY,GAAG,CAAC,OAAO,GAAG,YAAY;AACtC,gBAAgB,IAAI,UAAU,EAAE;AAChC,oBAAoB,YAAY,CAAC,UAAU,CAAC,CAAC;AAC7C,iBAAiB;AACjB,gBAAgB,OAAO,CAAC,UAAU,CAAC,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;AACzF,gBAAgB,OAAO,CAAC,UAAU,CAAC,YAAY,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;AACxF,gBAAgB,MAAM,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;AAClD,aAAa,CAAC;AACd,YAAY,GAAG,CAAC,OAAO,GAAG,YAAY;AACtC,gBAAgB,IAAI,UAAU,GAAG,OAAO,CAAC;AACzC,gBAAgB,IAAI,UAAU,EAAE;AAChC,oBAAoB,UAAU,GAAG,SAAS,CAAC;AAC3C,oBAAoB,YAAY,CAAC,UAAU,CAAC,CAAC;AAC7C,iBAAiB;AACjB,gBAAgB,OAAO,CAAC,UAAU,CAAC,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;AACrF,gBAAgB,OAAO,CAAC,UAAU,CAAC,YAAY,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;AAC3F,gBAAgB,MAAM,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;AAC9C,aAAa,CAAC;AACd;AACA,YAAY,OAAO,CAAC,UAAU,CAAC,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,CAAC,CAAC;AAC1E,YAAY,IAAI,UAAU,EAAE;AAC5B,gBAAgB,MAAM,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;AAC5C,gBAAgB,OAAO;AACvB,aAAa;AACb;AACA,YAAY,IAAI,OAAO,GAAG,CAAC,EAAE;AAC7B,gBAAgB,UAAU,GAAG,UAAU,CAAC,MAAM;AAC9C,oBAAoB,GAAG,CAAC,KAAK,EAAE,CAAC;AAChC,iBAAiB,EAAE,OAAO,CAAC,CAAC;AAC5B,aAAa;AACb;AACA,YAAY,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3B,SAAS,CAAC,CAAC;AACX,KAAK;AACL,IAAI,OAAO,GAAG,EAAE,CAAC;AACjB;;AChSA,CAAC,CAAC,IAAI,GAAG,IAAI;;ACAb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,SAAS,CAAC,OAAO,EAAE;AAC5B,IAAI,OAAO,MAAM,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;AAC1C;;ACbA,CAAC,CAAC,SAAS,GAAG,SAAS;;ACAvB,CAAC,CAAC,QAAQ,GAAG,QAAQ;;ACFrB,MAAM,MAAM,GAAG,yBAAyB;;ACGxC;AACA;AACA;AACA;AACA;AACA,SAAS,kBAAkB,CAAC,OAAO,EAAE,MAAM,EAAE;AAC7C;AACA,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;AAC1B;AACA,QAAQ,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;AAC7B,KAAK;AACL,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,KAAK,KAAK;AACjC;AACA,QAAQ,OAAO,CAAC,MAAM,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;AAClD,KAAK,CAAC,CAAC;AACP,CAAC;AACD,SAAS,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;AACnC;AACA;AACA,IAAI,IAAI,YAAY,CAAC,GAAG,CAAC,EAAE;AAC3B,QAAQ,kBAAkB,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AACzC,QAAQ,OAAO,GAAG,CAAC;AACnB,KAAK;AACL;AACA;AACA,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;AAC7B,QAAQ,kBAAkB,CAAC,OAAO,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC;AACtD,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL;AACA;AACA,IAAI,IAAI,WAAW,CAAC,GAAG,CAAC,EAAE;AAC1B;AACA,QAAQ,OAAO,OAAO,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;AACtD,KAAK;AACL;AACA;AACA,IAAI,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;AAC3B;AACA,IAAI,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,OAAO,CAAC,MAAM,CAAC,EAAE;AACnD;AACA,QAAQ,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;AACpC,KAAK;AACL,IAAI,OAAO,SAAS,CAAC;AACrB;;AC7CA,CAAC,CAAC,IAAI,GAAG,IAAI;;ACAb,CAAC,CAAC,IAAI,GAAG,IAAI;;ACCb,CAAC,CAAC,MAAM,GAAG,UAAU,GAAG,OAAO,EAAE;AACjC,IAAI,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;AAC9B,QAAQ,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,KAAK;AAC1C,YAAY,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;AAC/B,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL,IAAI,OAAO,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,OAAO,CAAC,KAAK,EAAE,EAAE,GAAG,OAAO,CAAC,CAAC;AAChE,CAAC;;ACVD,SAAS,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE;AACjC,IAAI,IAAI,KAAK,CAAC;AACd,IAAI,MAAM,GAAG,GAAG,EAAE,CAAC;AACnB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,OAAO,KAAK;AACnC,QAAQ,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;AAClD,QAAQ,IAAI,KAAK,IAAI,IAAI,EAAE;AAC3B,YAAY,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5B,SAAS;AACT,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,EAAE,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC;AAC7B;;ACTA,CAAC,CAAC,GAAG,GAAG,GAAG;;ACAX,CAAC,CAAC,KAAK,GAAG,KAAK;;ACAf,CAAC,CAAC,KAAK,GAAG,KAAK;;ACCf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,UAAU,CAAC,OAAO,EAAE,IAAI,EAAE;AACnC;AACA,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;AAC1B,QAAQ,OAAO;AACf,KAAK;AACL,IAAI,MAAM,MAAM,GAAG,CAAC,QAAQ,KAAK;AACjC,QAAQ,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;AACzC;AACA,QAAQ,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,EAAE;AACvC;AACA,YAAY,OAAO,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;AAC7C;AACA,YAAY,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC;AAC7C,SAAS;AACT,KAAK,CAAC;AACN,IAAI,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE;AAC3B;AACA,QAAQ,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AAC/B;AACA,QAAQ,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC;AAC/B;AACA,KAAK;AACL,SAAS,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE;AAC7B,QAAQ,IAAI;AACZ,aAAa,KAAK,CAAC,GAAG,CAAC;AACvB,aAAa,MAAM,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC;AAC3C,aAAa,OAAO,CAAC,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;AACrD,KAAK;AACL,SAAS;AACT,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,QAAQ,KAAK,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;AACtD,KAAK;AACL;;AC9DA,CAAC,CAAC,UAAU,GAAG,UAAU;;ACDzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,MAAM,CAAC,GAAG,EAAE;AACrB,IAAI,MAAM,MAAM,GAAG,EAAE,CAAC;AACtB,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,GAAG,KAAK;AAC1B,QAAQ,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;AACxC,YAAY,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC7B,SAAS;AACT,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,MAAM,CAAC;AAClB;;AChBA,CAAC,CAAC,MAAM,GAAG,MAAM;;ACGjB,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,UAAU,QAAQ,EAAE;AAC/B,IAAI,OAAO,IAAI,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AAChE,CAAC;;ACHD,IAAI,CAAC,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,KAAK;AAC/C,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,UAAU,SAAS,EAAE;AAChD,QAAQ,IAAI,IAAI,KAAK,QAAQ,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;AACpD,YAAY,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;AAC7C,gBAAgB,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;AAClD,aAAa,CAAC,CAAC;AACf,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;AACzC,YAAY,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;AACrC,gBAAgB,OAAO;AACvB,aAAa;AACb,YAAY,MAAM,OAAO,GAAG,CAAC,UAAU,CAAC,SAAS,CAAC;AAClD,kBAAkB,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;AACjF,kBAAkB,SAAS;AAC3B,iBAAiB,KAAK,CAAC,GAAG,CAAC;AAC3B,iBAAiB,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;AACxC,YAAY,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,GAAG,KAAK;AACtC,gBAAgB,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AAC7C,aAAa,CAAC,CAAC;AACf,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,CAAC,CAAC;;ACtBF,IAAI,CAAC,CAAC,cAAc,EAAE,aAAa,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,KAAK;AAC3D,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,UAAU,MAAM,EAAE;AACnC,QAAQ,MAAM,QAAQ,GAAG,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC;AACpE,QAAQ,MAAM,OAAO,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;AAClC,QAAQ,MAAM,MAAM,GAAG,EAAE,CAAC;AAC1B,QAAQ,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,KAAK;AACxC,YAAY,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;AACpC,gBAAgB,OAAO;AACvB,aAAa;AACb,YAAY,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;AAC1C,gBAAgB,MAAM,OAAO,GAAG,KAAK;AACrC,sBAAsB,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC;AAC7C,sBAAsB,OAAO,CAAC;AAC9B,gBAAgB,MAAM,YAAY,GAAG,SAAS,GAAG,MAAM,CAAC,WAAW,GAAG,MAAM,CAAC;AAC7E,gBAAgB,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACrC,gBAAgB,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;AACtE,aAAa,CAAC,CAAC;AACf,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO,CAAC,CAAC,SAAS,GAAG,MAAM,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC,CAAC;AACxD,KAAK,CAAC;AACN,CAAC,CAAC;;ACjBF;AACA;AACA;AACA;AACA,SAAS,WAAW,CAAC,MAAM,EAAE;AAC7B,IAAI,QAAQ,QAAQ,CAAC,MAAM,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,EAAE;AAC1F,CAAC;AACD,IAAI,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,KAAK;AAC/C,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,UAAU,GAAG,IAAI,EAAE;AACpC;AACA,QAAQ,IAAI,SAAS,KAAK,CAAC,EAAE;AAC7B,YAAY,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;AAClC,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK;AAC7C,YAAY,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC/C,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;AACnE,kBAAkB,IAAI,CAAC;AACvB,YAAY,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,MAAM,KAAK;AACzC,gBAAgB,IAAI,OAAO,CAAC;AAC5B,gBAAgB,IAAI,WAAW,CAAC,MAAM,CAAC,EAAE;AACzC,oBAAoB,OAAO,GAAG,CAAC,CAAC,kBAAkB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;AACnE,iBAAiB;AACjB,qBAAqB,IAAI,KAAK,IAAI,SAAS,CAAC,MAAM,CAAC,EAAE;AACrD,oBAAoB,OAAO,GAAG,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;AACxD,iBAAiB;AACjB,qBAAqB;AACrB,oBAAoB,OAAO,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;AACxC,iBAAiB;AACjB,gBAAgB,OAAO,CAAC,SAAS,GAAG,aAAa,GAAG,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC;AAC7E,aAAa,CAAC,CAAC;AACf,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,CAAC,CAAC;;ACjCF,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,UAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE;AAChD;AACA,IAAI,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE;AAC7B,QAAQ,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,EAAE,KAAK;AAClC;AACA;AACA,YAAY,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAC;AACzC,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL;AACA,IAAI,IAAI,QAAQ,KAAK,KAAK,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE;AACpD,QAAQ,QAAQ,GAAG,QAAQ,CAAC;AAC5B,QAAQ,QAAQ,GAAG,SAAS,CAAC;AAC7B;AACA,KAAK;AACL;AACA,IAAI,IAAI,QAAQ,KAAK,KAAK,EAAE;AAC5B,QAAQ,QAAQ,GAAG,WAAW,CAAC;AAC/B,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;AACjC,QAAQ,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAChD,KAAK,CAAC,CAAC;AACP,CAAC;;ACtBD,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,EAAE;AAC1D;AACA,IAAI,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE;AAC7B;AACA,QAAQ,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;AACjC;AACA,YAAY,IAAI,GAAG,IAAI,IAAI,QAAQ,CAAC;AACpC,YAAY,QAAQ,GAAG,SAAS,CAAC;AACjC,SAAS;AACT,QAAQ,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,EAAE,KAAK;AAClC;AACA;AACA,YAAY,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;AACnD,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,QAAQ,IAAI,IAAI,EAAE;AAC1C;AACA,QAAQ,QAAQ,GAAG,QAAQ,CAAC;AAC5B,QAAQ,IAAI,GAAG,QAAQ,GAAG,SAAS,CAAC;AACpC,KAAK;AACL,SAAS,IAAI,QAAQ,IAAI,IAAI,EAAE;AAC/B,QAAQ,IAAI,QAAQ,CAAC,QAAQ,CAAC,EAAE;AAChC;AACA,YAAY,QAAQ,GAAG,IAAI,CAAC;AAC5B,YAAY,IAAI,GAAG,SAAS,CAAC;AAC7B,SAAS;AACT,aAAa;AACb;AACA,YAAY,QAAQ,GAAG,IAAI,CAAC;AAC5B,YAAY,IAAI,GAAG,QAAQ,CAAC;AAC5B,YAAY,QAAQ,GAAG,SAAS,CAAC;AACjC,SAAS;AACT,KAAK;AACL,IAAI,IAAI,QAAQ,KAAK,KAAK,EAAE;AAC5B,QAAQ,QAAQ,GAAG,WAAW,CAAC;AAC/B,KAAK;AACL,SAAS,IAAI,CAAC,QAAQ,EAAE;AACxB,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL;AACA,IAAI,IAAI,GAAG,EAAE;AACb;AACA,QAAQ,MAAM,KAAK,GAAG,IAAI,CAAC;AAC3B,QAAQ,MAAM,YAAY,GAAG,QAAQ,CAAC;AACtC,QAAQ,QAAQ,GAAG,UAAU,KAAK,EAAE;AACpC,YAAY,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;AACtD;AACA,YAAY,OAAO,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AACvD,SAAS,CAAC;AACV,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;AACjC,QAAQ,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;AACnD,KAAK,CAAC,CAAC;AACP,CAAC;;ACxDD,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,EAAE,SAAS,KAAK;AACtC,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,UAAU,EAAE,EAAE;AAC/B,QAAQ,OAAO,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,MAAM,KAAK;AACjD,YAAY,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;AAC3D,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,CAAC,CAAC;;ACPF,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,UAAU,QAAQ,EAAE;AAC/B,IAAI,OAAO,IAAI,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;AACjF,CAAC;;ACHD,CAAC,CAAC,EAAE,CAAC,KAAK,GAAG,YAAY;AACzB,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,YAAY;AAChC,QAAQ,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AACpC,KAAK,CAAC,CAAC;AACP,CAAC;;ACHD,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,QAAQ,EAAE;AAC9B,IAAI,IAAI,SAAS,GAAG,KAAK,CAAC;AAC1B,IAAI,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE;AAC9B,QAAQ,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK;AACtC,YAAY,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE;AACxD,gBAAgB,SAAS,GAAG,IAAI,CAAC;AACjC,aAAa;AACb,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO,SAAS,CAAC;AACzB,KAAK;AACL,IAAI,IAAI,QAAQ,CAAC,QAAQ,CAAC,EAAE;AAC5B,QAAQ,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;AAClC,YAAY,IAAI,UAAU,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,OAAO,CAAC,EAAE;AAC1D,gBAAgB,OAAO;AACvB,aAAa;AACb;AACA,YAAY,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,iBAAiB,CAAC;AACzE,YAAY,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE;AACjD,gBAAgB,SAAS,GAAG,IAAI,CAAC;AACjC,aAAa;AACb,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO,SAAS,CAAC;AACzB,KAAK;AACL,IAAI,MAAM,YAAY,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;AACrC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;AAC9B,QAAQ,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;AAC1C,YAAY,IAAI,OAAO,KAAK,OAAO,EAAE;AACrC,gBAAgB,SAAS,GAAG,IAAI,CAAC;AACjC,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,SAAS,CAAC;AACrB,CAAC;;AChCD,CAAC,CAAC,EAAE,CAAC,MAAM,GAAG,UAAU,QAAQ,EAAE;AAClC,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;AACrC,QAAQ,IAAI,OAAO,CAAC,UAAU,KAAK,CAAC,QAAQ,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE;AAC1E,YAAY,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AACpD,SAAS;AACT,KAAK,CAAC,CAAC;AACP,CAAC;;ACAD,IAAI,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,KAAK;AACjD,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,UAAU,GAAG,IAAI,EAAE;AACpC,QAAQ,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK;AAC7C,YAAY,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;AAClD,YAAY,MAAM,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC;AAClD,YAAY,MAAM,KAAK,GAAG,WAAW;AACrC,kBAAkB,UAAU,CAAC,SAAS,GAAG,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;AAC7D,kBAAkB,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAChD,YAAY,IAAI,CAAC,WAAW,EAAE;AAC9B,gBAAgB,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC3C,aAAa;AACb,YAAY,IAAI,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC9C,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;AACnE,kBAAkB,IAAI,CAAC;AACvB;AACA,YAAY,IAAI,KAAK,EAAE;AACvB,gBAAgB,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK;AACrD,oBAAoB,OAAO,QAAQ,CAAC,OAAO,CAAC,GAAG,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,CAAC;AAC5E,iBAAiB,CAAC,CAAC;AACnB,aAAa;AACb,YAAY,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,GAAG,OAAO,GAAG,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC;AAClE,YAAY,IAAI,CAAC,WAAW,EAAE;AAC9B,gBAAgB,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC3C,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,CAAC,CAAC;;AC7BF,IAAI,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,KAAK;AACrD,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,UAAU,MAAM,EAAE;AACnC,QAAQ,MAAM,WAAW,GAAG,EAAE,CAAC;AAC/B,QAAQ,MAAM,OAAO,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;AACtD,YAAY,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;AAClD,YAAY,MAAM,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC;AAClD,YAAY,IAAI,WAAW,EAAE;AAC7B,gBAAgB,OAAO,UAAU,CAAC,SAAS,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC;AACnE,aAAa;AACb,YAAY,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACxD,YAAY,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AACvC,YAAY,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACpC,YAAY,OAAO,KAAK,CAAC;AACzB,SAAS,CAAC,CAAC;AACX,QAAQ,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,GAAG,cAAc,GAAG,aAAa,CAAC,CAAC,OAAO,CAAC,CAAC;AAClF,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC,MAAM,EAAE,CAAC;AAChC,QAAQ,OAAO,OAAO,CAAC;AACvB,KAAK,CAAC;AACN,CAAC,CAAC;;ACpBF,IAAI,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,KAAK;AACnD,IAAI,SAAS,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;AACtC;AACA,QAAQ,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;AAChC,YAAY,OAAO;AACnB,SAAS;AACT,QAAQ,QAAQ,SAAS;AACzB;AACA,YAAY,KAAK,CAAC;AAClB,gBAAgB,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE;AACnC,oBAAoB,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;AACjD,iBAAiB;AACjB,qBAAqB;AACrB,oBAAoB,OAAO,CAAC,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACrD,iBAAiB;AACjB,gBAAgB,MAAM;AACtB;AACA,YAAY,KAAK,CAAC;AAClB;AACA,gBAAgB,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AACrC,gBAAgB,MAAM;AACtB;AACA,YAAY;AACZ,gBAAgB,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;AACvC;AACA,gBAAgB,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC;AACpD,sBAAsB,CAAC,EAAE,KAAK,CAAC,EAAE,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;AAC1E,sBAAsB,KAAK,CAAC;AAC5B,gBAAgB,MAAM;AACtB,SAAS;AACT,KAAK;AACL,IAAI,SAAS,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE;AAC/B,QAAQ,QAAQ,SAAS;AACzB;AACA,YAAY,KAAK,CAAC;AAClB;AACA,gBAAgB,MAAM,KAAK,GAAG,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AACxD,gBAAgB,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,KAAK,CAAC;AACzD;AACA,YAAY,KAAK,CAAC;AAClB;AACA,gBAAgB,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;AACpC;AACA,YAAY;AACZ,gBAAgB,OAAO,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AAC9C,SAAS;AACT,KAAK;AACL,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,UAAU,GAAG,EAAE,KAAK,EAAE;AACvC,QAAQ,IAAI,YAAY,CAAC,GAAG,CAAC,EAAE;AAC/B,YAAY,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK;AAChC;AACA,gBAAgB,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACjC,aAAa,CAAC,CAAC;AACf,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS;AACT,QAAQ,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;AACpC,YAAY,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACpC,YAAY,OAAO,SAAS,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,SAAS,CAAC;AACtE,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;AACzC,YAAY,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,UAAU,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;AACrG,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,CAAC,CAAC;;AC5DF,CAAC,CAAC,EAAE,CAAC,QAAQ,GAAG,UAAU,QAAQ,EAAE;AACpC,IAAI,MAAM,QAAQ,GAAG,EAAE,CAAC;AACxB,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;AAC9B,QAAQ,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE,SAAS,KAAK;AACpD,YAAY,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE;AACvC,gBAAgB,OAAO;AACvB,aAAa;AACb,YAAY,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE;AACxD,gBAAgB,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACzC,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,IAAI,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;AACpC,CAAC;;AClBD,CAAC,CAAC,EAAE,CAAC,KAAK,GAAG,UAAU,GAAG,IAAI,EAAE;AAChC,IAAI,OAAO,IAAI,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AAC9C,CAAC;;ACDD,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,KAAK,EAAE;AAC3B,IAAI,MAAM,GAAG,GAAG,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AACjF,IAAI,OAAO,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC;AACvB,CAAC;;ACAc,SAAS,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE;AAC1E,IAAI,MAAM,GAAG,GAAG,EAAE,CAAC;AACnB,IAAI,IAAI,MAAM,CAAC;AACf,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;AACnC,QAAQ,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;AAC/B;AACA,QAAQ,OAAO,MAAM,IAAI,SAAS,CAAC,MAAM,CAAC,EAAE;AAC5C;AACA,YAAY,IAAI,SAAS,KAAK,CAAC,EAAE;AACjC,gBAAgB,IAAI,QAAQ,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE;AACxD,oBAAoB,MAAM;AAC1B,iBAAiB;AACjB,gBAAgB,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE;AACrD,oBAAoB,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACrC,iBAAiB;AACjB,aAAa;AACb;AACA,iBAAiB,IAAI,SAAS,KAAK,CAAC,EAAE;AACtC,gBAAgB,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE;AACzD,oBAAoB,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACrC,iBAAiB;AACjB,gBAAgB,MAAM;AACtB,aAAa;AACb;AACA,iBAAiB;AACjB,gBAAgB,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE;AACzD,oBAAoB,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACrC,iBAAiB;AACjB,aAAa;AACb;AACA,YAAY,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AAClC,SAAS;AACT,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,IAAI,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AAC/B;;ACpCA,IAAI,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,QAAQ,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,KAAK;AAC/C,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,UAAU,QAAQ,EAAE,MAAM,EAAE;AACxD;AACA,QAAQ,MAAM,MAAM,GAAG,CAAC,SAAS,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC;AACnE,QAAQ,OAAO,GAAG,CAAC,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;AACtE,KAAK,CAAC;AACN,CAAC,CAAC;;ACLF,CAAC,CAAC,EAAE,CAAC,OAAO,GAAG,UAAU,QAAQ,EAAE;AACnC,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE;AAC3B,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL,IAAI,MAAM,OAAO,GAAG,EAAE,CAAC;AACvB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;AACxC,QAAQ,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE;AACrC,YAAY,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAClC,YAAY,OAAO,KAAK,CAAC;AACzB,SAAS;AACT,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,IAAI,EAAE,CAAC,OAAO,CAAC,CAAC;AAC3B,CAAC;;ACbD,MAAM,MAAM,GAAG,8BAA8B,CAAC;AAC9C;AACA,SAAS,OAAO,CAAC,KAAK,EAAE;AACxB,IAAI,IAAI,KAAK,KAAK,MAAM,EAAE;AAC1B,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL,IAAI,IAAI,KAAK,KAAK,OAAO,EAAE;AAC3B,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,IAAI,IAAI,KAAK,KAAK,MAAM,EAAE;AAC1B,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL,IAAI,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,EAAE,EAAE;AAC/B,QAAQ,OAAO,CAAC,KAAK,CAAC;AACtB,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AAC5B,QAAQ,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACjC,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,CAAC;AACD;AACA,SAAS,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;AACvC,IAAI,IAAI,WAAW,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE;AACtD,QAAQ,MAAM,IAAI,GAAG,OAAO,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;AAChD,QAAQ,KAAK,GAAG,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;AAC3C,QAAQ,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;AAC7B,YAAY,IAAI;AAChB,gBAAgB,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;AACvC,aAAa;AACb,YAAY,OAAO,CAAC,EAAE,GAAG;AACzB,SAAS;AACT,aAAa;AACb,YAAY,KAAK,GAAG,SAAS,CAAC;AAC9B,SAAS;AACT,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,CAAC;AACD,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,UAAU,GAAG,EAAE,KAAK,EAAE;AAClC;AACA,IAAI,IAAI,WAAW,CAAC,GAAG,CAAC,EAAE;AAC1B,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AAC1B,YAAY,OAAO,SAAS,CAAC;AAC7B,SAAS;AACT,QAAQ,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAChC,QAAQ,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;AACzC;AACA,QAAQ,IAAI,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE;AACpC,YAAY,OAAO,UAAU,CAAC;AAC9B,SAAS;AACT;AACA,QAAQ,MAAM,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC;AACzC,QAAQ,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;AAC7B,QAAQ,OAAO,CAAC,EAAE,EAAE;AACpB,YAAY,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;AAC1B,gBAAgB,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AACzC,gBAAgB,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AACjD,oBAAoB,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACtD,oBAAoB,UAAU,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,OAAO,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;AACjF,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,UAAU,CAAC;AAC1B,KAAK;AACL;AACA,IAAI,IAAI,YAAY,CAAC,GAAG,CAAC,EAAE;AAC3B,QAAQ,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;AACrC,YAAY,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AAC5B,SAAS,CAAC,CAAC;AACX,KAAK;AACL;AACA,IAAI,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;AACtD,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL;AACA,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;AAC7B,QAAQ,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;AACrC,YAAY,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;AACnC,SAAS,CAAC,CAAC;AACX,KAAK;AACL;AACA,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AACtB,QAAQ,OAAO,SAAS,CAAC;AACzB,KAAK;AACL,IAAI,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AACtD,CAAC;;ACtFD,CAAC,CAAC,EAAE,CAAC,KAAK,GAAG,YAAY;AACzB,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;AACjC,QAAQ,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;AAC5B,KAAK,CAAC,CAAC;AACP,CAAC;;ACJD,CAAC,CAAC,EAAE,CAAC,MAAM,GAAG,UAAU,GAAG,EAAE;AAC7B,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,KAAK,KAAK;AAC/B;AACA,QAAQ,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;AAC3B,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,IAAI,CAAC;AAChB,CAAC;;ACJD,CAAC,CAAC,EAAE,CAAC,MAAM,GAAG,UAAU,QAAQ,EAAE;AAClC,IAAI,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE;AAC9B,QAAQ,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,GAAG,OAAO,GAAG,SAAS,CAAC,CAAC;AAC1G,KAAK;AACL,IAAI,IAAI,QAAQ,CAAC,QAAQ,CAAC,EAAE;AAC5B,QAAQ,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,OAAO,GAAG,SAAS,CAAC,CAAC;AACvF,KAAK;AACL,IAAI,MAAM,SAAS,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;AAClC,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK,SAAS,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,GAAG,SAAS,CAAC,CAAC;AACjG,CAAC;;ACXD,CAAC,CAAC,EAAE,CAAC,KAAK,GAAG,YAAY;AACzB,IAAI,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACtB,CAAC;;ACAD,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,UAAU,QAAQ,EAAE;AAC/B,IAAI,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;AAC5E,IAAI,MAAM,EAAE,MAAM,EAAE,GAAG,QAAQ,CAAC;AAChC,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,YAAY;AAChC,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;AAC5C,YAAY,IAAI,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;AAC7C,gBAAgB,OAAO,IAAI,CAAC;AAC5B,aAAa;AACb,SAAS;AACT,QAAQ,OAAO;AACf,KAAK,CAAC,CAAC;AACP,CAAC;;ACdD,CAAC,CAAC,EAAE,CAAC,QAAQ,GAAG,UAAU,SAAS,EAAE;AACrC,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;AACjD,CAAC;;ACED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,gBAAgB,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,aAAa,EAAE,QAAQ,EAAE;AACpF;AACA,IAAI,MAAM,kBAAkB,GAAG,CAAC,KAAK,KAAK;AAC1C,QAAQ,QAAQ,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,KAAK,CAAC;AACjE,YAAY,QAAQ,EAAE;AACtB,KAAK,CAAC;AACN,IAAI,IAAI,SAAS,KAAK,CAAC,IAAI,aAAa,EAAE;AAC1C,QAAQ,KAAK,IAAI,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AAC9C,KAAK;AACL,IAAI,IAAI,WAAW,CAAC,OAAO,CAAC,EAAE;AAC9B;AACA;AACA,QAAQ,IAAI,IAAI,EAAE,IAAI,QAAQ,KAAK,CAAC,EAAE;AACtC,YAAY,KAAK,IAAI,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AAClD,YAAY,KAAK,IAAI,kBAAkB,CAAC,SAAS,CAAC,CAAC;AACnD,SAAS;AACT,QAAQ,IAAI,SAAS,KAAK,CAAC,EAAE;AAC7B,YAAY,KAAK,IAAI,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AAClD,SAAS;AACT,QAAQ,IAAI,SAAS,KAAK,CAAC,EAAE;AAC7B,YAAY,KAAK,IAAI,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AAClD,YAAY,KAAK,IAAI,kBAAkB,CAAC,SAAS,CAAC,CAAC;AACnD,SAAS;AACT,KAAK;AACL,SAAS;AACT,QAAQ,IAAI,SAAS,KAAK,CAAC,EAAE;AAC7B,YAAY,KAAK,IAAI,kBAAkB,CAAC,SAAS,CAAC,CAAC;AACnD,SAAS;AACT,QAAQ,IAAI,SAAS,KAAK,CAAC,EAAE;AAC7B,YAAY,KAAK,IAAI,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AAClD,YAAY,KAAK,IAAI,kBAAkB,CAAC,SAAS,CAAC,CAAC;AACnD,SAAS;AACT,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,aAAa,EAAE;AACtD,IAAI,MAAM,UAAU,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;AACvC,IAAI,MAAM,UAAU,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;AACvC,IAAI,MAAM,UAAU,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;AACvC,IAAI,MAAM,SAAS,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;AACrC;AACA,IAAI,IAAI,QAAQ,CAAC,OAAO,CAAC,EAAE;AAC3B;AACA,QAAQ,OAAO,SAAS,KAAK,CAAC;AAC9B,cAAc,OAAO,CAAC,SAAS,CAAC;AAChC,cAAc,SAAS,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;AAC9C,KAAK;AACL;AACA,IAAI,IAAI,UAAU,CAAC,OAAO,CAAC,EAAE;AAC7B,QAAQ,MAAM,GAAG,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;AACvC,QAAQ,OAAO,IAAI,CAAC,GAAG;AACvB;AACA,QAAQ,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,GAAG,CAAC,UAAU,CAAC;AACjD;AACA,QAAQ,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,GAAG,CAAC,UAAU,CAAC,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;AACpE,KAAK;AACL,IAAI,MAAM,KAAK,GAAG,UAAU,CAAC,qBAAqB,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC;AACxF,IAAI,OAAO,gBAAgB,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,aAAa,EAAE,CAAC,CAAC,CAAC;AAC/E,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,GAAG,CAAC,OAAO,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE,aAAa,EAAE,KAAK,EAAE;AAC3E,IAAI,IAAI,aAAa,GAAG,UAAU,CAAC,KAAK,CAAC;AACzC,UAAU,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,YAAY,EAAE,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;AACzF,UAAU,KAAK,CAAC;AAChB,IAAI,IAAI,aAAa,IAAI,IAAI,EAAE;AAC/B,QAAQ,OAAO;AACf,KAAK;AACL,IAAI,MAAM,QAAQ,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC;AAChC,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;AACzC;AACA,IAAI,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,EAAE;AAC7D,QAAQ,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;AAC/C,QAAQ,OAAO;AACf,KAAK;AACL;AACA,IAAI,MAAM,MAAM,GAAG,aAAa,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;AACrE,IAAI,MAAM,SAAS,GAAG,UAAU,CAAC,aAAa,CAAC,CAAC;AAChD,IAAI,aAAa;AACjB,QAAQ,gBAAgB,CAAC,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,aAAa,EAAE,CAAC,CAAC,CAAC;AAChF,aAAa,MAAM,IAAI,IAAI,CAAC,CAAC;AAC7B,IAAI,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;AAC3C,CAAC;AACD,IAAI,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,KAAK;AACvC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,QAAQ,KAAK;AACxF,QAAQ,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,UAAU,MAAM,EAAE,KAAK,EAAE;AAClD;AACA,YAAY,MAAM,KAAK,GAAG,SAAS,CAAC,MAAM,KAAK,SAAS,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;AACpF,YAAY,MAAM,aAAa,GAAG,MAAM,KAAK,IAAI,IAAI,KAAK,KAAK,IAAI,CAAC;AACpE;AACA,YAAY,IAAI,CAAC,KAAK,EAAE;AACxB,gBAAgB,OAAO,IAAI,CAAC,MAAM;AAClC,sBAAsB,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,aAAa,CAAC;AAClE,sBAAsB,SAAS,CAAC;AAChC,aAAa;AACb;AACA,YAAY,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC,CAAC;AAC9G,SAAS,CAAC;AACV,KAAK,CAAC,CAAC;AACP,CAAC,CAAC;;AC7HF,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,YAAY;AACxB,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;AACjC,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;AACpC,KAAK,CAAC,CAAC;AACP,CAAC;;ACAD,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,KAAK;AACnD,IAAI,MAAM,KAAK,GAAG;AAClB,QAAQ,CAAC,EAAE,OAAO;AAClB,QAAQ,CAAC,EAAE,WAAW;AACtB,QAAQ,CAAC,EAAE,aAAa;AACxB,KAAK,CAAC;AACN,IAAI,MAAM,QAAQ,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC;AACtC,IAAI,SAAS,GAAG,CAAC,SAAS,EAAE;AAC5B;AACA,QAAQ,IAAI,SAAS,KAAK,CAAC,EAAE;AAC7B;AACA,YAAY,OAAO,GAAG,CAAC,SAAS,EAAE,CAAC,OAAO,KAAK,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACtF,SAAS;AACT;AACA,QAAQ,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;AAC/B,YAAY,OAAO,SAAS,CAAC;AAC7B,SAAS;AACT;AACA,QAAQ,MAAM,YAAY,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AAC1C;AACA,QAAQ,IAAI,SAAS,KAAK,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,kBAAkB,CAAC,EAAE;AACvE,YAAY,OAAO,GAAG,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,OAAO,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC;AAC3F,SAAS;AACT;AACA,QAAQ,OAAO,YAAY,CAAC,QAAQ,CAAC,CAAC;AACtC,KAAK;AACL,IAAI,SAAS,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE;AACjC;AACA;AACA,QAAQ,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;AAChC,YAAY,IAAI,SAAS,KAAK,CAAC,EAAE;AACjC,gBAAgB,OAAO;AACvB,aAAa;AACb,YAAY,KAAK,GAAG,EAAE,CAAC;AACvB,SAAS;AACT,QAAQ,IAAI,SAAS,KAAK,CAAC,IAAI,SAAS,CAAC,KAAK,CAAC,EAAE;AACjD,YAAY,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC;AACpC,SAAS;AACT;AACA,QAAQ,OAAO,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;AAClC,KAAK;AACL,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,UAAU,KAAK,EAAE;AAClC;AACA,QAAQ,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;AAC/B,YAAY,OAAO,GAAG,CAAC,IAAI,CAAC,CAAC;AAC7B,SAAS;AACT;AACA,QAAQ,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;AACzC,YAAY,MAAM,aAAa,GAAG,UAAU,CAAC,KAAK,CAAC;AACnD,kBAAkB,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;AACzD,kBAAkB,KAAK,CAAC;AACxB;AACA,YAAY,IAAI,SAAS,KAAK,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;AACjE;AACA,gBAAgB,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,kBAAkB,CAAC,EAAE;AACvD,oBAAoB,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,MAAM,MAAM,CAAC,QAAQ;AAC/E,wBAAwB,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;AAC3D,4BAA4B,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,iBAAiB;AACjB;AACA,qBAAqB;AACrB,oBAAoB,OAAO,CAAC,OAAO;AACnC,wBAAwB,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AAClE,iBAAiB;AACjB,aAAa;AACb,iBAAiB;AACjB,gBAAgB,GAAG,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;AAC5C,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,CAAC,CAAC;;ACtEF,CAAC,CAAC,EAAE,CAAC,KAAK,GAAG,UAAU,QAAQ,EAAE;AACjC,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;AAC3B,QAAQ,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACrE,KAAK;AACL,IAAI,IAAI,QAAQ,CAAC,QAAQ,CAAC,EAAE;AAC5B,QAAQ,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAClD,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9C,CAAC;;ACZD,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,YAAY;AACxB,IAAI,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACvB,CAAC;;ACDD,IAAI,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,KAAK;AAChD,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,UAAU,QAAQ,EAAE,MAAM,EAAE;AACtD,QAAQ,OAAO,GAAG,CAAC,IAAI,EAAE,SAAS,EAAE,oBAAoB,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;AAC5E,KAAK,CAAC;AACN,CAAC,CAAC;;ACJF,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,UAAU,QAAQ,EAAE;AAC/B,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AAC5C,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,GAAG,OAAO,CAAC,CAAC;AACzF,CAAC;;ACHD;AACA;AACA;AACA,CAAC,CAAC,EAAE,CAAC,YAAY,GAAG,YAAY;AAChC,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,YAAY;AAChC,QAAQ,IAAI,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;AAC7C,QAAQ,OAAO,YAAY,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,QAAQ,EAAE;AAC7E,YAAY,YAAY,GAAG,YAAY,CAAC,YAAY,CAAC;AACrD,SAAS;AACT,QAAQ,OAAO,YAAY,IAAI,QAAQ,CAAC,eAAe,CAAC;AACxD,KAAK,CAAC,CAAC;AACP,CAAC;;ACTD,SAAS,UAAU,CAAC,QAAQ,EAAE,IAAI,EAAE;AACpC,IAAI,OAAO,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;AAC1C,CAAC;AACD,CAAC,CAAC,EAAE,CAAC,QAAQ,GAAG,YAAY;AAC5B,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AACtB,QAAQ,OAAO,SAAS,CAAC;AACzB,KAAK;AACL,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAChC,IAAI,IAAI,aAAa,CAAC;AACtB,IAAI,IAAI,YAAY,GAAG;AACvB,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,GAAG,EAAE,CAAC;AACd,KAAK,CAAC;AACN,IAAI,IAAI,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,OAAO,EAAE;AAC9C,QAAQ,aAAa,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC;AAC5D,KAAK;AACL,SAAS;AACT,QAAQ,aAAa,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;AAC1C,QAAQ,MAAM,aAAa,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;AACtD,QAAQ,YAAY,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC;AAC9C,QAAQ,YAAY,CAAC,GAAG,IAAI,UAAU,CAAC,aAAa,EAAE,kBAAkB,CAAC,CAAC;AAC1E,QAAQ,YAAY,CAAC,IAAI,IAAI,UAAU,CAAC,aAAa,EAAE,mBAAmB,CAAC,CAAC;AAC5E,KAAK;AACL,IAAI,OAAO;AACX,QAAQ,GAAG,EAAE,aAAa,CAAC,GAAG,GAAG,YAAY,CAAC,GAAG,GAAG,UAAU,CAAC,QAAQ,EAAE,YAAY,CAAC;AACtF,QAAQ,IAAI,EAAE,aAAa,CAAC,IAAI;AAChC,YAAY,YAAY,CAAC,IAAI;AAC7B,YAAY,UAAU,CAAC,QAAQ,EAAE,aAAa,CAAC;AAC/C,KAAK,CAAC;AACN,CAAC;;AC5BD,SAASA,KAAG,CAAC,OAAO,EAAE;AACtB,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC,MAAM,EAAE;AAC1C,QAAQ,OAAO,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;AACnC,KAAK;AACL,IAAI,MAAM,IAAI,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;AACjD,IAAI,MAAM,GAAG,GAAG,OAAO,CAAC,aAAa,CAAC,WAAW,CAAC;AAClD,IAAI,OAAO;AACX,QAAQ,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,WAAW;AACvC,QAAQ,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,WAAW;AACzC,KAAK,CAAC;AACN,CAAC;AACD,SAASC,KAAG,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE;AACpC,IAAI,MAAM,QAAQ,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC;AAChC,IAAI,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AAC9C,IAAI,IAAI,QAAQ,KAAK,QAAQ,EAAE;AAC/B,QAAQ,QAAQ,CAAC,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;AAC7C,KAAK;AACL,IAAI,MAAM,aAAa,GAAGD,KAAG,CAAC,OAAO,CAAC,CAAC;AACvC,IAAI,MAAM,gBAAgB,GAAG,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACjD,IAAI,MAAM,iBAAiB,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AACnD,IAAI,IAAI,UAAU,CAAC;AACnB,IAAI,IAAI,WAAW,CAAC;AACpB,IAAI,MAAM,iBAAiB,GAAG,CAAC,QAAQ,KAAK,UAAU,IAAI,QAAQ,KAAK,OAAO;AAC9E,QAAQ,CAAC,gBAAgB,GAAG,iBAAiB,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AACpE,IAAI,IAAI,iBAAiB,EAAE;AAC3B,QAAQ,MAAM,eAAe,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC;AACpD,QAAQ,UAAU,GAAG,eAAe,CAAC,GAAG,CAAC;AACzC,QAAQ,WAAW,GAAG,eAAe,CAAC,IAAI,CAAC;AAC3C,KAAK;AACL,SAAS;AACT,QAAQ,UAAU,GAAG,UAAU,CAAC,gBAAgB,CAAC,CAAC;AAClD,QAAQ,WAAW,GAAG,UAAU,CAAC,iBAAiB,CAAC,CAAC;AACpD,KAAK;AACL,IAAI,MAAM,aAAa,GAAG,UAAU,CAAC,KAAK,CAAC;AAC3C,UAAU,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE,EAAE,aAAa,CAAC,CAAC;AAC/D,UAAU,KAAK,CAAC;AAChB,IAAI,QAAQ,CAAC,GAAG,CAAC;AACjB,QAAQ,GAAG,EAAE,aAAa,CAAC,GAAG,IAAI,IAAI;AACtC,cAAc,aAAa,CAAC,GAAG,GAAG,aAAa,CAAC,GAAG,GAAG,UAAU;AAChE,cAAc,SAAS;AACvB,QAAQ,IAAI,EAAE,aAAa,CAAC,IAAI,IAAI,IAAI;AACxC,cAAc,aAAa,CAAC,IAAI,GAAG,aAAa,CAAC,IAAI,GAAG,WAAW;AACnE,cAAc,SAAS;AACvB,KAAK,CAAC,CAAC;AACP,CAAC;AACD,CAAC,CAAC,EAAE,CAAC,MAAM,GAAG,UAAU,KAAK,EAAE;AAC/B;AACA,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;AAC3B,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AAC1B,YAAY,OAAO,SAAS,CAAC;AAC7B,SAAS;AACT,QAAQ,OAAOA,KAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5B,KAAK;AACL;AACA,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,KAAK,EAAE;AACtC,QAAQC,KAAG,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AAChC,KAAK,CAAC,CAAC;AACP,CAAC;;AC7DD,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,UAAU,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE;AACtD;AACA,IAAI,OAAO,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;AAC1D,CAAC;;ACDD,IAAI,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,KAAK;AAChD,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,UAAU,QAAQ,EAAE,MAAM,EAAE;AACtD;AACA,QAAQ,MAAM,MAAM,GAAG,CAAC,SAAS,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC;AACnE,QAAQ,OAAO,GAAG,CAAC,MAAM,EAAE,SAAS,EAAE,wBAAwB,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;AAClF,KAAK,CAAC;AACN,CAAC,CAAC;;ACPF,CAAC,CAAC,EAAE,CAAC,UAAU,GAAG,UAAU,aAAa,EAAE;AAC3C,IAAI,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;AAClE,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;AACjC,QAAQ,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,KAAK;AACjC,YAAY,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;AACvC,SAAS,CAAC,CAAC;AACX,KAAK,CAAC,CAAC;AACP,CAAC;;ACPD,CAAC,CAAC,EAAE,CAAC,UAAU,GAAG,UAAU,IAAI,EAAE;AAClC,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;AACjC,QAAQ,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC/B,KAAK,CAAC,CAAC;AACP,CAAC;;ACLD,CAAC,CAAC,EAAE,CAAC,UAAU,GAAG,UAAU,IAAI,EAAE;AAClC,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;AACjC,QAAQ,IAAI;AACZ;AACA,YAAY,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC;AAC9B,SAAS;AACT,QAAQ,OAAO,CAAC,EAAE,GAAG;AACrB,KAAK,CAAC,CAAC;AACP,CAAC;;ACJD,CAAC,CAAC,EAAE,CAAC,WAAW,GAAG,UAAU,UAAU,EAAE;AACzC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK;AAClC,QAAQ,IAAI,OAAO,GAAG,UAAU,CAAC;AACjC,QAAQ,IAAI,UAAU,CAAC,OAAO,CAAC,EAAE;AACjC,YAAY,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;AACtE,SAAS;AACT,aAAa,IAAI,KAAK,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;AAC9C,YAAY,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,CAAC;AACzC,SAAS;AACT,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACnC,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;AACzB,CAAC;;ACbD,CAAC,CAAC,EAAE,CAAC,UAAU,GAAG,UAAU,MAAM,EAAE;AACpC,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK;AAC7C,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC;AAC5D,QAAQ,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC;AAC1B,KAAK,CAAC,CAAC;AACP,CAAC;;ACPD;AACA;AACA;AACA;AACA,CAAC,CAAC,EAAE,CAAC,cAAc,GAAG,YAAY;AAClC,IAAI,MAAM,MAAM,GAAG,EAAE,CAAC;AACtB,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;AAC9B,QAAQ,MAAM,QAAQ,GAAG,OAAO,YAAY,eAAe,GAAG,OAAO,CAAC,QAAQ,GAAG,CAAC,OAAO,CAAC,CAAC;AAC3F,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;AACzC,YAAY,MAAM,QAAQ,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC;AACxC,YAAY,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;AACtC,YAAY,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;AAC5D,YAAY,IAAI,QAAQ,KAAK,UAAU;AACvC,gBAAgB,OAAO,CAAC,IAAI;AAC5B,gBAAgB,CAAC,OAAO,CAAC,QAAQ;AACjC,gBAAgB,CAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AAChF,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACnF,iBAAiB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC3D,oBAAoB,OAAO,CAAC,OAAO,CAAC,EAAE;AACtC,gBAAgB,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC;AAC7C,gBAAgB,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC;AACxE,gBAAgB,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK;AAC5C,oBAAoB,MAAM,CAAC,IAAI,CAAC;AAChC,wBAAwB,IAAI,EAAE,OAAO,CAAC,IAAI;AAC1C,wBAAwB,KAAK;AAC7B,qBAAqB,CAAC,CAAC;AACvB,iBAAiB,CAAC,CAAC;AACnB,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,MAAM,CAAC;AAClB,CAAC;;AC/BD,CAAC,CAAC,EAAE,CAAC,SAAS,GAAG,YAAY;AAC7B,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;AACxC,CAAC;;ACFD,MAAM,cAAc,GAAG,EAAE,CAAC;AAC1B;AACA;AACA;AACA;AACA,SAAS,cAAc,CAAC,QAAQ,EAAE;AAClC,IAAI,IAAI,OAAO,CAAC;AAChB,IAAI,IAAI,OAAO,CAAC;AAChB,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;AACnC,QAAQ,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AACnD,QAAQ,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AAC3C,QAAQ,OAAO,GAAG,QAAQ,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;AAC/C,QAAQ,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AAChD,QAAQ,IAAI,OAAO,KAAK,MAAM,EAAE;AAChC,YAAY,OAAO,GAAG,OAAO,CAAC;AAC9B,SAAS;AACT,QAAQ,cAAc,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC;AAC3C,KAAK;AACL,IAAI,OAAO,cAAc,CAAC,QAAQ,CAAC,CAAC;AACpC,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,YAAY;AACxB,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;AACjC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,KAAK,MAAM,EAAE;AAC3C,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC;AACpC,SAAS;AACT,QAAQ,IAAI,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,KAAK,MAAM,EAAE;AAClD,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC/D,SAAS;AACT,KAAK,CAAC,CAAC;AACP,CAAC;;AChCD;AACA;AACA;AACA;AACA;AACA,CAAC,CAAC,EAAE,CAAC,QAAQ,GAAG,UAAU,QAAQ,EAAE;AACpC,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;AAC9D,CAAC;;ACND;AACA;AACA;AACA,CAAC,CAAC,EAAE,CAAC,MAAM,GAAG,YAAY;AAC1B,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;AACjC,QAAQ,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,KAAK,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;AAC/E,KAAK,CAAC,CAAC;AACP,CAAC;;ACMD,CAAC,CAAC,EAAE,CAAC,MAAM,GAAG;IACZ,OAAO,IAAI,CAAC,IAAI,CAAC;QACf,OAAO,IAAI,CAAC,UAAU,CAAC;KACxB,CAAC,CAAC;AACL,CAAC;;ACFD,CAAC,CAAC,EAAE,CAAC,UAAU,GAAG,UAAoB,QAAyB;IAC7D,IAAI,QAAQ,CAAC,QAAQ,CAAC,EAAE;QACtB,QAAQ,GAAG,GAAG,QAAQ,IAAI,CAAC;KAC5B;IAED,OAAO,IAAI,CAAC,IAAI,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,wBAAwB,GAAG,QAAkB,CAAC;QACzD,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,QAAkB,CAAC;KACpD,CAAC,CAAC;AACL,CAAC;;ACPD,CAAC,CAAC,EAAE,CAAC,aAAa,GAAG,UAEnB,QAA+C;;IAG/C,MAAM,IAAI,GAAG,IAAI,CAAC;IAClB,MAAM,MAAM,GAAG,CAAC,qBAAqB,EAAE,eAAe,CAAC,CAAC;IAExD,SAAS,YAAY,CAAoC,CAAQ;QAC/D,IAAI,CAAC,CAAC,MAAM,KAAK,IAAI,EAAE;YACrB,OAAO;SACR;;QAGD,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAEvB,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,KAAK;YACpB,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;SAC/B,CAAC,CAAC;KACJ;IAED,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,KAAK;QACpB,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;KAC9B,CAAC,CAAC;IAEH,OAAO,IAAI,CAAC;AACd,CAAC;;AC9BD,CAAC,CAAC,EAAE,CAAC,eAAe,GAAG,UAAoB,eAAuB;IAChE,OAAO,IAAI,CAAC,IAAI,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,qBAAqB,GAAG,eAAe,CAAC;QACnD,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,eAAe,CAAC;KAC9C,CAAC,CAAC;AACL,CAAC;;ACLD,CAAC,CAAC,EAAE,CAAC,SAAS,GAAG,UAAoB,SAAiB;IACpD,OAAO,IAAI,CAAC,IAAI,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,SAAS,CAAC;QACvC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;KAClC,CAAC,CAAC;AACL,CAAC;;ACdD;;;AAGA,MAAM,OAAO,GAA+B,EAAE,CAAC;AAE/C;;;;;;;AAOA,SAAS,QAAQ,CACf,QAAgB,EAChB,OAAsB,EACtB,CAAS,EACT,OAAoB;IAEpB,IAAI,SAAS,GAAG,IAAI,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;IAEhD,IAAI,CAAC,SAAS,EAAE;QACd,SAAS,GAAG,EAAE,CAAC;QACf,IAAI,CAAC,OAAO,EAAE,gBAAgB,EAAE,SAAS,CAAC,CAAC;KAC5C;IAED,IAAI,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;QACtC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACzB,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;KACnC;AACH;;ACrBA,CAAC,CAAC,EAAE,CAAC,QAAQ,GAAG;IACd,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO;QAC1B,MAAM,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC;QAEzB,IAAI,CAAC,OAAO,EAAE,CAAC,QAAgB,EAAE,OAAO;YACtC,IAAI,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE;gBACtB,QAAQ,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;aACzC;YAED,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO;gBACnC,QAAQ,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;aACzC,CAAC,CAAC;SACJ,CAAC,CAAC;KACJ,CAAC,CAAC;AACL,CAAC;;ACJD,CAAC,CAAC,WAAW,GAAG,UAAU,MAAe;IACvC,IAAI,QAAQ,GAAG,CAAC,CAAC,eAAe,CAAC,CAAC;IAElC,IAAI,QAAQ,CAAC,MAAM,EAAE;QACnB,QAAQ,CAAC,IAAI,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;QAE5C,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE;YACxB,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;SACjC;KACF;SAAM;QACL,IAAI,WAAW,CAAC,MAAM,CAAC,EAAE;YACvB,MAAM,GAAG,IAAI,CAAC;SACf;QAED,QAAQ,GAAG,CAAC,CAAC,4BAA4B,CAAC;aACvC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC;aACvB,MAAM,EAAE;aACR,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;KAC3B;IAED,IAAI,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAEjD,OAAO,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;AAChF,CAAC;;ACxBD,CAAC,CAAC,WAAW,GAAG,UAAU,KAAK,GAAG,KAAK;IACrC,MAAM,QAAQ,GAAG,CAAC,CAAC,eAAe,CAAC,CAAC;IAEpC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;QACpB,OAAO;KACR;IAED,IAAI,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAExD,IAAI,KAAK,GAAG,CAAC,EAAE;QACb,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,KAAK,CAAC,CAAC;QACzC,OAAO;KACR;IAED,QAAQ;SACL,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;SACzB,WAAW,CAAC,mBAAmB,CAAC;SAChC,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC;SACjC,aAAa,CAAC;QACb,IAAI,QAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE;YACxC,QAAQ,CAAC,MAAM,EAAE,CAAC;SACnB;KACF,CAAC,CAAC;AACP,CAAC;;AC/BD,CAAC,CAAC,UAAU,GAAG;IACb,MAAM,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;;IAGxB,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;IACnC,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;IAEjD,KAAK;SACF,QAAQ,CAAC,aAAa,CAAC;SACvB,KAAK,CAAC,YAAY,CAAC;SACnB,IAAI,CAAC,mBAAmB,EAAE,EAAE,KAAK,CAAC,CAAC;AACxC,CAAC;;ACJD,CAAC,CAAC,YAAY,GAAG,UAAU,KAAK,GAAG,KAAK;IACtC,MAAM,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;IACxB,IAAI,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;IAExD,IAAI,KAAK,GAAG,CAAC,EAAE;QACb,KAAK,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,KAAK,CAAC,CAAC;QACzC,OAAO;KACR;IAED,KAAK,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AAC1E,CAAC;;ACfD,CAAC,CAAC,QAAQ,GAAG,UAAU,EAAc,EAAE,KAAK,GAAG,EAAE;IAC/C,IAAI,KAAK,GAAQ,IAAI,CAAC;IAEtB,OAAO,UAAqB,GAAG,IAAS;QACtC,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE;YACjB,KAAK,GAAG,UAAU,CAAC;gBACjB,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBACrB,KAAK,GAAG,IAAI,CAAC;aACd,EAAE,KAAK,CAAC,CAAC;SACX;KACF,CAAC;AACJ,CAAC;;ACTD,MAAM,IAAI,GAAwB,EAAE,CAAC;AAErC,CAAC,CAAC,IAAI,GAAG,UAAU,IAAa;IAC9B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;QAClD,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC;KACnB;IAED,SAAS,EAAE;QACT,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,IAAI,OAAO,CAAC;aAC7C,QAAQ,CAAC,EAAE,CAAC;aACZ,SAAS,CAAC,CAAC,CAAC,CAAC;KACjB;IAED,MAAM,IAAI,GACR,GAAG;QACH,EAAE,EAAE;QACJ,EAAE,EAAE;QACJ,GAAG;QACH,EAAE,EAAE;QACJ,GAAG;QACH,EAAE,EAAE;QACJ,GAAG;QACH,EAAE,EAAE;QACJ,GAAG;QACH,EAAE,EAAE;QACJ,EAAE,EAAE;QACJ,EAAE,EAAE,CAAC;IAEP,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;QACtB,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;KACnB;IAED,OAAO,IAAI,CAAC;AACd,CAAC;;AC3BD,IAAI,CAAC,QAAQ,GAAG,UAAU,QAAiB,EAAE,OAAuB;IAClE,IAAI,WAAW,CAAC,QAAQ,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC,EAAE;QACjD,CAAC,CAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC;QACvB,OAAO;KACR;IAED,OAAO,CAAC,QAAQ,CAAC,GAAG,OAAQ,CAAC;IAC7B,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK,QAAQ,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;AAC5E,CAAC;;AC/BD;;;;;;;;AAQA,SAAS,cAAc,CACrB,SAAiB,EACjB,aAAqB,EACrB,MAAwC,EACxC,QAAc,EACd,UAAwB;IAExB,IAAI,CAAC,UAAU,EAAE;QACf,UAAU,GAAG,EAAE,CAAC;KACjB;;IAGD,UAAU,CAAC,IAAI,GAAG,QAAQ,CAAC;IAE3B,MAAM,aAAa,GAAG,GAAG,SAAS,SAAS,aAAa,EAAE,CAAC;;;IAI3D,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;;QAEjC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;KACnD;IAED,MAAM,OAAO,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;;IAG1B,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;IAS3C,MAAM,WAAW,GAAgB;QAC/B,OAAO,EAAE,IAAI;QACb,UAAU,EAAE,IAAI;QAChB,MAAM,EAAE,UAAU;KACnB,CAAC;IAEF,MAAM,WAAW,GAAgB,IAAI,WAAW,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;;IAG7E,WAAW,CAAC,OAAO,GAAG,UAAU,CAAC;IAEjC,OAAO,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;AACxC;;AC1DA,MAAM,SAAS,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;AAC9B,MAAM,OAAO,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;AAC1B,MAAM,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC;;AC2EvB,MAAM,eAAe,GAAY;IAC/B,SAAS,EAAE,CAAC;IACZ,MAAM,EAAE,CAAC;IACT,YAAY,EAAE,eAAe;IAC7B,WAAW,EAAE,0BAA0B;IACvC,aAAa,EAAE,4BAA4B;CAC5C,CAAC;AAEF,MAAM,QAAQ;IA+BZ,YACE,QAAyD,EACzD,UAAmB,EAAE;;;;QAxBhB,YAAO,GAAY,MAAM,CAAC,EAAE,EAAE,eAAe,CAAC,CAAC;;;;QAK9C,UAAK,GAAU,QAAQ,CAAC;;;;QAKxB,aAAQ,GAAG,KAAK,CAAC;;;;QAKjB,gBAAW,GAAG,CAAC,CAAC;;;;QAKhB,UAAK,GAAG,CAAC,CAAC;QAMhB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAC;QAEpC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;;QAG9B,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;QACzC,IAAI,QAAQ,CAAC,SAAS,CAAC,EAAE;YACvB,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG;gBACvB,IAAI,EAAE,SAAS;gBACf,EAAE,EAAE,SAAS;aACd,CAAC;SACH;QAED,IAAI,CAAC,MAAM,EAAE,CAAC;KACf;;;;IAKO,QAAQ;QACd,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,qBAAqB,CAAC;YACxC,MAAM,cAAc,GAAG,MAAM,CAAC,WAAW,CAAC;YAC1C,MAAM,SAAS,GAAG,cAAc,GAAG,IAAI,CAAC,WAAW,GAAG,MAAM,GAAG,IAAI,CAAC;YACpE,MAAM,SAAS,GAAI,IAAI,CAAC,OAAO,CAAC,SAAuB,CAAC,SAAS,CAAC,CAAC;YACnE,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;YAC7D,MAAM,iBAAiB,GAAG,QAAQ,IAAI,SAAS,CAAC;YAEhD,IACE,cAAc,GAAG,IAAI,CAAC,WAAW;gBACjC,cAAc,IAAI,IAAI,CAAC,OAAO,CAAC,MAAO;gBACtC,iBAAiB,EACjB;gBACA,IAAI,CAAC,KAAK,EAAE,CAAC;aACd;iBAAM,IACL,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW,IAAI,iBAAiB;gBACvD,cAAc,IAAI,IAAI,CAAC,OAAO,CAAC,MAAO,EACtC;gBACA,IAAI,CAAC,GAAG,EAAE,CAAC;aACZ;YAED,IAAI,CAAC,WAAW,GAAG,cAAc,CAAC;SACnC,CAAC,CAAC;KACJ;;;;;IAMO,YAAY,CAAC,IAAW;QAC9B,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;KACvD;;;;IAKO,aAAa;QACnB,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;YAC5B,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;YACtB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;SAC7B;QAED,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW,EAAE;YAC9B,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC;YACxB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;SAC/B;KACF;;;;IAKM,GAAG;QACR,IACE,IAAI,CAAC,KAAK,KAAK,SAAS;YACxB,IAAI,CAAC,KAAK,KAAK,QAAQ;YACvB,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,YAAa,CAAC,EACnD;YACA,OAAO;SACR;QAED,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACzB,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;QACvB,IAAI,CAAC,QAAQ;aACV,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;aACvC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,WAAY,CAAC;aACnC,aAAa,CAAC,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;KAC9C;;;;IAKM,KAAK;QACV,IACE,IAAI,CAAC,KAAK,KAAK,WAAW;YAC1B,IAAI,CAAC,KAAK,KAAK,UAAU;YACzB,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,YAAa,CAAC,EACnD;YACA,OAAO;SACR;QAED,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAC3B,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC;QACzB,IAAI,CAAC,QAAQ;aACV,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;aACrC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,aAAc,CAAC;aACrC,aAAa,CAAC,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;KAC9C;;;;IAKM,MAAM;QACX,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,OAAO;SACR;QAED,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;QACtB,IAAI,CAAC,QAAQ;aACV,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,YAAa,CAAC;aACpC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;aACrC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAC3C,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;QAEtC,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;KAC7C;;;;IAKM,OAAO;QACZ,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,OAAO;SACR;QAED,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,QAAQ;aACV,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;aACtC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;aACrC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAE3C,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC7C,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACzC;;;;IAKM,QAAQ;QACb,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;CACF;AAED,IAAI,CAAC,QAAQ,GAAG,QAAQ;;AC9QxB;;;;;AAKA,SAAS,YAAY,CAAC,OAAoB,EAAE,IAAY;IACtD,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEnC,IAAI,CAAC,IAAI,EAAE;QACT,OAAO,EAAE,CAAC;KACX;IAED,OAAO,IAAI,QAAQ,CACjB,EAAE,EACF,cAAc,IAAI,4CAA4C,CAC/D,EAAE,CAAC;AACN;;ACdA,MAAM,UAAU,GAAG,eAAe,CAAC;AAEnC,CAAC,CAAC;IACA,IAAI,CAAC,QAAQ,CAAC,IAAI,UAAU,GAAG,EAAE;QAC/B,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,YAAY,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC;KACzD,CAAC,CAAC;AACL,CAAC,CAAC;;ACqBF,MAAMC,iBAAe,GAAY;IAC/B,SAAS,EAAE,KAAK;CACjB,CAAC;AAEF,MAAe,gBAAgB;IAoC7B,YACE,QAAyD,EACzD,UAAmB,EAAE;;;;QA7BhB,YAAO,GAAY,MAAM,CAAC,EAAE,EAAEA,iBAAe,CAAC,CAAC;;QAgCpD,MAAM,WAAW,GAAG,QAAQ,IAAI,CAAC,YAAY,EAAE,OAAO,CAAC;QACvD,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC;QAC7B,IAAI,CAAC,aAAa,GAAG,GAAG,WAAW,OAAO,CAAC;QAC3C,IAAI,CAAC,WAAW,GAAG,GAAG,WAAW,SAAS,CAAC;QAC3C,IAAI,CAAC,SAAS,GAAG,GAAG,WAAW,OAAO,CAAC;QAEvC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAC;QAEpC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAE9B,IAAI,CAAC,SAAS,EAAE,CAAC;KAClB;;;;IAKO,SAAS;;QAEf,MAAM,IAAI,GAAG,IAAI,CAAC;;QAGlB,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YAChD,MAAM,OAAO,GAAG,CAAC,CAAC,IAAmB,CAAC,CAAC;YACvC,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;YAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAE/B,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI;gBAClB,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;oBAClB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;iBACnB;aACF,CAAC,CAAC;SACJ,CAAC,CAAC;;QAGH,IAAI,CAAC,QAAQ,CAAC,EAAE,CACd,OAAO,EACP,SAAS,IAAI,CAAC,YAAY,EAAE,cAAc,EAC1C;YACE,MAAM,OAAO,GAAG,CAAC,CAAC,IAAmB,CAAC,CAAC;YACvC,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC;YAE5D,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;SACnB,CACF,CAAC;KACH;;;;;IAMO,MAAM,CAAC,KAAS;QACtB,OAAO,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;KAC3C;;;;IAKO,QAAQ;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;KACrD;;;;;IAMO,OAAO,CACb,IAA8D;QAE9D,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE;YAClB,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;SACjC;QAED,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;KACxB;;;;;;IAOO,YAAY,CAAC,IAAW,EAAE,KAAS;QACzC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;KACxD;;;;;;IAOO,aAAa,CAAC,QAAY,EAAE,KAAS;QAC3C,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACtB,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YAE9D,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;SACpC;aAAM;YACL,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAEpB,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;SACpC;KACF;;;;;IAMM,IAAI,CACT,IAA8D;QAE9D,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAEjC,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACtB,OAAO;SACR;;QAGD,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;YAC1B,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO;gBAC/D,MAAM,QAAQ,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC;gBAE5B,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE;oBACvB,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;iBACtB;aACF,CAAC,CAAC;SACJ;QAED,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QAEtD,QAAQ;aACL,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;aAChC,aAAa,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;QAE5D,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAEjC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;KACpC;;;;;IAMM,KAAK,CACV,IAA8D;QAE9D,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAEjC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACvB,OAAO;SACR;QAED,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QAEtD,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAElC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAEtC,QAAQ;aACL,UAAU,CAAC,CAAC,CAAC;aACb,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;aAChC,MAAM,EAAE;aACR,UAAU,CAAC,EAAE,CAAC;aACd,MAAM,CAAC,EAAE,CAAC;aACV,aAAa,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;KAC7D;;;;;IAMM,MAAM,CACX,IAA8D;QAE9D,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAEjC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC3D;;;;IAKM,OAAO;QACZ,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;KAC1D;;;;IAKM,QAAQ;QACb,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;KAC3D;;;ACjPH,MAAM,QAAS,SAAQ,gBAAgB;IAC3B,YAAY;QACpB,OAAO,UAAU,CAAC;KACnB;CACF;AAED,IAAI,CAAC,QAAQ,GAAG,QAAQ;;ACzBxB,MAAMC,YAAU,GAAG,eAAe,CAAC;AAEnC,CAAC,CAAC;IACA,IAAI,CAAC,QAAQ,CAAC,IAAIA,YAAU,GAAG,EAAE;QAC/B,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,YAAY,CAAC,IAAI,EAAEA,YAAU,CAAC,CAAC,CAAC;KACzD,CAAC,CAAC;AACL,CAAC,CAAC;;ACaF,MAAM,KAAM,SAAQ,gBAAgB;IACxB,YAAY;QACpB,OAAO,OAAO,CAAC;KAChB;CACF;AAED,IAAI,CAAC,KAAK,GAAG,KAAK;;ACzBlB,MAAMA,YAAU,GAAG,YAAY,CAAC;AAEhC,CAAC,CAAC;IACA,IAAI,CAAC,QAAQ,CAAC,IAAIA,YAAU,GAAG,EAAE;QAC/B,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,YAAY,CAAC,IAAI,EAAEA,YAAU,CAAC,CAAC,CAAC;KACtD,CAAC,CAAC;AACL,CAAC,CAAC;;ACqBF,MAAM,KAAK;IAoCT,YACE,QAAyD;;;;QA5BnD,WAAM,GAAO,CAAC,EAAE,CAAC;;;;QAKjB,YAAO,GAAO,CAAC,EAAE,CAAC;;;;QAKlB,gBAAW,GAAyB,CAAC,EAAE,CAAC;;;;QAKxC,iBAAY,GAAyB,CAAC,EAAE,CAAC;;;;QAKzC,eAAU,GAAG,KAAK,CAAC;;;;QAKnB,gBAAW,GAAG,CAAC,CAAC;QAKtB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAC;QACpC,IAAI,CAAC,IAAI,EAAE,CAAC;KACb;;;;IAKM,IAAI;QACT,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC7C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC9C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,uBAAuB,CAAC,CAAC;QAElE,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,gBAAgB,EAAE,CAAC;KACzB;;;;;IAMO,kBAAkB,CAAC,GAAW;QACpC,QACE,IAAI,GAAG,oCAAoC;YAC3C,+BAA+B;YAC/B,0BAA0B;YAC1B,oCAAoC;YACpC,UAAU;YACV,KAAK,GAAG,GAAG,EACX;KACH;;;;IAKO,sBAAsB;QAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QACrC,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QAEzC,QAAQ,CAAC,OAAO,GAAG,WAAW,KAAK,YAAY,CAAC;QAChD,QAAQ,CAAC,aAAa,GAAG,CAAC,CAAC,WAAW,IAAI,WAAW,KAAK,YAAY,CAAC;KACxE;;;;IAKO,gBAAgB;QACtB,MAAM,gBAAgB,GAAG,yBAAyB,CAAC;QAEnD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG;YACvB,MAAM,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;;YAGpB,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC,MAAM,EAAE,CAAC;YAEhD,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;gBACpB,OAAO;aACR;;YAGD,MAAM,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;iBAC/C,SAAS,CAAC,IAAI,CAAC;iBACf,IAAI,CAAC,wBAAwB,CAAyB,CAAC;;YAG1D,IAAI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;gBACnC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC;gBAC5B,IAAI,CAAC,WAAW,EAAE,CAAC;aACpB;YAED,IAAI,CAAC,sBAAsB,EAAE,CAAC;;YAG9B,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE;gBACrB,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE;oBACxB,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;oBAChC,IAAI,CAAC,WAAW,EAAE,CAAC;iBACpB;qBAAM;oBACL,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;oBACnC,IAAI,CAAC,WAAW,EAAE,CAAC;iBACpB;gBAED,IAAI,CAAC,sBAAsB,EAAE,CAAC;aAC/B,CAAC,CAAC;YAEH,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;SACtD,CAAC,CAAC;KACJ;;;;IAKO,gBAAgB;;QAEtB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC,MAAM,EAAE,CAAC;QAEvD,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACpB,OAAO;SACR;QAED,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;aAChD,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC;aACtB,IAAI,CAAC,wBAAwB,CAAC;aAC9B,EAAE,CAAC,QAAQ,EAAE;YACZ,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;YACjD,IAAI,CAAC,WAAW,GAAG,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;YAE1D,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ;gBACjC,QAAQ,CAAC,OAAO,GAAG,YAAY,CAAC;aACjC,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG;gBACvB,YAAY;sBACR,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,yBAAyB,CAAC;sBAC1C,CAAC,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,yBAAyB,CAAC,CAAC;aACnD,CAAC,CAAC;SACJ,CAAyB,CAAC;KAC9B;;;;IAKO,gBAAgB;QACtB,MAAM,YAAY,GAAG,wBAAwB,CAAC;QAE9C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE;YAChC,MAAM,YAAY,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAElD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG;gBACvB,MAAM,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBAEpC,YAAY;sBACR,GAAG,CAAC,QAAQ,CAAC,YAAY,CAAC;sBAC1B,GAAG,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;aACnC,CAAC,CAAC;SACJ,CAAC,CAAC;KACJ;CACF;AAED,MAAM,QAAQ,GAAG,aAAa,CAAC;AAE/B,CAAC,CAAC;IACA,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE;QAC3B,MAAM,QAAQ,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;QAEzB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YAC5B,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;SAC9C;KACF,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,YAAY,GAAG,UAClB,QAA0D;IAE1D,MAAM,SAAS,GAAG,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;IAEzE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO;QACxB,MAAM,QAAQ,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC;QAC5B,MAAM,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEzC,IAAI,QAAQ,EAAE;YACZ,QAAQ,CAAC,IAAI,EAAE,CAAC;SACjB;aAAM;YACL,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;SAC9C;KACF,CAAC,CAAC;AACL,CAAC;;AC/OD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,MAAM,UAAU,GAAG,sBAAsB,CAAC;AAC1C,MAAM,SAAS,GAAG,qBAAqB,CAAC;AACxC,MAAM,QAAQ,GAAG,kBAAkB,CAAC;AACpC,MAAM,WAAW,GAAG,wBAAwB,CAAC;AAC7C,MAAM,WAAW,GAAG,gCAAgC,CAAC;AAErD,IAAI,OAAO,GAAG,CAAC,CAAC;AAEhB;;;;;AAKA,SAAS,OAAO,CAAC,KAAY;IAC3B,OAAO,EACL,OAAO;QACP;YACE,WAAW;YACX,SAAS;YACT,WAAW;YACX,OAAO;YACP,WAAW;YACX,UAAU;YACV,YAAY;YACZ,YAAY;SACb,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAC3B,CAAC;AACJ,CAAC;AAED;;;;AAIA,SAAS,QAAQ,CAAC,KAAY;IAC5B,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE;;QAE/B,OAAO,IAAI,CAAC,CAAC;KACd;SAAM,IACL,CAAC,WAAW,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EACjE;;QAEA,UAAU,CAAC;YACT,IAAI,OAAO,EAAE;gBACX,OAAO,IAAI,CAAC,CAAC;aACd;SACF,EAAE,GAAG,CAAC,CAAC;KACT;AACH;;ACjFA;;;;;;AAwCA;;;;;AAKA,SAAS,IAAI,CAAC,KAAY,EAAE,OAAW;;IAErC,IAAI,KAAK,YAAY,UAAU,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;QACrD,OAAO;KACR;;IAGD,MAAM,aAAa,GACjB,OAAO,UAAU,KAAK,WAAW;QACjC,KAAK,YAAY,UAAU;QAC3B,KAAK,CAAC,OAAO,CAAC,MAAM;UAChB,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;UACf,KAAoB,CAAC;IAE5B,MAAM,WAAW,GAAG,aAAa,CAAC,KAAK,CAAC;IACxC,MAAM,WAAW,GAAG,aAAa,CAAC,KAAK,CAAC;;IAGxC,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;IAChC,MAAM,MAAM,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;IACrC,MAAM,KAAK,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC;IACnC,MAAM,MAAM,GAAG;QACb,CAAC,EAAE,WAAW,GAAG,MAAM,CAAC,IAAI;QAC5B,CAAC,EAAE,WAAW,GAAG,MAAM,CAAC,GAAG;KAC5B,CAAC;IACF,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CACvB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,EACvD,EAAE,CACH,CAAC;;IAGF,MAAM,SAAS,GACb,eAAe,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK;QACzC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,iBAAiB,CAAC;;IAG7C,CAAC,CACC,gCAAgC;QAC9B,gBAAgB,QAAQ,aAAa,QAAQ,KAAK;QAClD,eAAe,QAAQ,GAAG,CAAC,mBAAmB,QAAQ,GAAG,CAAC,KAAK;QAC/D,QAAQ,MAAM,CAAC,CAAC,UAAU,MAAM,CAAC,CAAC,aAAa,CAClD;SACE,IAAI,CAAC,wBAAwB,EAAE,SAAS,CAAC;SACzC,SAAS,CAAC,OAAO,CAAC;SAClB,MAAM,EAAE;SACR,SAAS,CAAC,SAAS,CAAC,CAAC;AAC1B,CAAC;AAED;;;;AAIA,SAAS,YAAY,CAAC,KAAS;IAC7B,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,IAAI,CAAC,sBAAsB,CAAC,EAAE;QACvD,OAAO;KACR;IAED,KAAK,CAAC,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,CAAC;IAEzC,IAAI,WAAW,GAAG,UAAU,CAAC,MAAM,KAAK,CAAC,MAAM,EAAE,EAAE,GAAG,CAAC,CAAC;IACxD,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;IAEvD,KAAK;SACF,QAAQ,CAAC,uBAAuB,CAAC;SACjC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;SACvD,aAAa,CAAC;QACb,YAAY,CAAC,WAAW,CAAC,CAAC;QAE1B,KAAK;aACF,QAAQ,CAAC,sBAAsB,CAAC;aAChC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC,CAAC;QAE3D,WAAW,GAAG,UAAU,CAAC,MAAM,KAAK,CAAC,MAAM,EAAE,EAAE,GAAG,CAAC,CAAC;QAEpD,UAAU,CAAC;YACT,KAAK,CAAC,aAAa,CAAC;gBAClB,YAAY,CAAC,WAAW,CAAC,CAAC;gBAC1B,KAAK,CAAC,MAAM,EAAE,CAAC;aAChB,CAAC,CAAC;SACJ,EAAE,CAAC,CAAC,CAAC;KACP,CAAC,CAAC;AACP,CAAC;AAED;;;;AAIA,SAAS,IAAI;IACX,MAAM,OAAO,GAAG,CAAC,CAAC,IAAmB,CAAC,CAAC;IAEvC,OAAO,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI;QACjD,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;KACvB,CAAC,CAAC;IAEH,OAAO,CAAC,GAAG,CAAC,GAAG,SAAS,IAAI,QAAQ,IAAI,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC;AAC/D,CAAC;AAED;;;;AAIA,SAAS,UAAU,CAAC,KAAY;IAC9B,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QACnB,OAAO;KACR;IAED,QAAQ,CAAC,KAAK,CAAC,CAAC;;IAGhB,IAAI,KAAK,CAAC,MAAM,KAAK,QAAQ,EAAE;QAC7B,OAAO;KACR;IAED,MAAM,OAAO,GAAG,CAAC,CAAC,KAAK,CAAC,MAAqB,CAAC,CAAC;;IAG/C,MAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC;UAC3C,OAAO;UACP,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,KAAK,EAAE,CAAC;IAE5C,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;QACnB,OAAO;KACR;;IAGD,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE;QACtE,OAAO;KACR;IAED,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE;QAC/B,IAAI,MAAM,GAAG,KAAK,CAAC;;QAGnB,IAAI,KAAK,GAAG,UAAU,CAAC;YACrB,KAAK,GAAG,CAAC,CAAC;YACV,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;SACtB,EAAE,GAAG,CAAC,CAAC;QAER,MAAM,UAAU,GAAG;;YAEjB,IAAI,KAAK,EAAE;gBACT,YAAY,CAAC,KAAK,CAAC,CAAC;gBACpB,KAAK,GAAG,CAAC,CAAC;gBACV,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;aACtB;YAED,IAAI,CAAC,MAAM,EAAE;gBACX,MAAM,GAAG,IAAI,CAAC;gBACd,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aACpB;SACF,CAAC;;QAGF,MAAM,SAAS,GAAG;YAChB,IAAI,KAAK,EAAE;gBACT,YAAY,CAAC,KAAK,CAAC,CAAC;gBACpB,KAAK,GAAG,CAAC,CAAC;aACX;YAED,UAAU,EAAE,CAAC;SACd,CAAC;QAEF,OAAO,CAAC,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,sBAAsB,EAAE,UAAU,CAAC,CAAC;KAC3E;SAAM;QACL,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QACrB,OAAO,CAAC,EAAE,CAAC,GAAG,SAAS,IAAI,QAAQ,IAAI,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC;KAC7D;AACH,CAAC;AAED,CAAC,CAAC;IACA,SAAS,CAAC,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;AACjE,CAAC,CAAC;;AC9KF,MAAM,WAAW,GAAqB;IACpC,MAAM,EAAE,KAAK;IACb,cAAc,EAAE,KAAK;CACtB,CAAC;AAEF;;;;;AAKA,SAAS,UAAU,CAAC,KAAY,EAAE,OAAyB,EAAE;IAC3D,IAAI,GAAG,MAAM,CAAC,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;IAErC,MAAM,KAAK,GAAG,KAAK,CAAC,MAA0B,CAAC;IAC/C,MAAM,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;IACxB,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC;IAC7B,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,EAAY,CAAC;;IAGrC,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;IAC5C,IACE,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,OAAO,CACjE,SAAS,CACV,GAAG,CAAC,CAAC,EACN;QACA,OAAO;KACR;IAED,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;;IAGpD,IAAI,SAAS,KAAK,OAAO,EAAE;QACzB,UAAU,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC;KAC7C;IAED,IAAI,SAAS,KAAK,MAAM,EAAE;QACxB,UAAU,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC;KAChD;;IAGD,IAAI,SAAS,KAAK,MAAM,IAAI,SAAS,KAAK,OAAO,EAAE;QACjD,KAAK;cACD,UAAU,CAAC,QAAQ,CAAC,0BAA0B,CAAC;cAC/C,UAAU,CAAC,WAAW,CAAC,0BAA0B,CAAC,CAAC;KACxD;;IAGD,KAAK,CAAC,QAAQ;UACV,UAAU,CAAC,QAAQ,CAAC,yBAAyB,CAAC;UAC9C,UAAU,CAAC,WAAW,CAAC,yBAAyB,CAAC,CAAC;;IAGtD,IACE,CAAC,SAAS,KAAK,OAAO,IAAI,SAAS,KAAK,MAAM;QAC9C,CAAC,IAAI,CAAC,cAAc;QACpB,KAAK,CAAC,QAAQ,EACd;QACA,KAAK,CAAC,QAAQ,CAAC,KAAK;cAChB,UAAU,CAAC,WAAW,CAAC,8BAA8B,CAAC;cACtD,UAAU,CAAC,QAAQ,CAAC,8BAA8B,CAAC,CAAC;KACzD;;IAGD,IAAI,MAAM,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE;;;QAGzB,MAAM,UAAU,GAAG,KAAK,CAAC;QACzB,IAAI,aAAa,GAAG,KAAK,CAAC;QAE1B,IAAI,UAAU,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,KAAK,EAAE,EAAE;YAC5C,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,CAAC,CAAC;YAC7B,aAAa,GAAG,IAAI,CAAC;SACtB;;QAGD,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QACvB,MAAM,MAAM,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;QACpC,MAAM,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC;QAExC,IAAI,YAAY,GAAG,MAAM,EAAE;YACzB,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;SAClC;;QAGD,IAAI,aAAa,EAAE;YACjB,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;SACxB;KACF;;IAGD,IAAI,IAAI,CAAC,MAAM,EAAE;QACf,UAAU,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,MAAM,EAAE,CAAC;KACrD;IAED,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAC3C,IAAI,SAAS,EAAE;QACb,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,cAAc,EAAE;YACtC,CAAC,CACC,sCAAsC;gBACpC,0DAA0D,SAAS,EAAE;gBACrE,QAAQ,CACX,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;SACxB;QAED,UAAU;aACP,IAAI,CAAC,iCAAiC,CAAC;aACvC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;KAClC;;IAGD,IACE,UAAU,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,MAAM;QAChD,UAAU,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,MAAM;QAC/C,SAAS,EACT;QACA,UAAU,CAAC,QAAQ,CAAC,2BAA2B,CAAC,CAAC;KAClD;AACH,CAAC;AAED,CAAC,CAAC;;IAEA,SAAS,CAAC,EAAE,CACV,kBAAkB,EAClB,uBAAuB,EACvB,EAAE,UAAU,EAAE,IAAI,EAAE,EACpB,UAAU,CACX,CAAC;;IAGF,SAAS,CAAC,EAAE,CACV,OAAO,EACP,iDAAiD,EACjD;QACE,CAAC,CAAC,IAAmB,CAAC;aACnB,OAAO,CAAC,iBAAiB,CAAC;aAC1B,QAAQ,CAAC,yBAAyB,CAAC;aACnC,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;aAChC,KAAK,EAAE,CAAC;KACZ,CACF,CAAC;;IAGF,SAAS,CAAC,EAAE,CACV,OAAO,EACP,gDAAgD,EAChD;QACE,CAAC,CAAC,IAAI,CAAC;aACJ,OAAO,CAAC,iBAAiB,CAAC;aAC1B,WAAW,CAAC,yBAAyB,CAAC;aACtC,IAAI,CAAC,uBAAuB,CAAC;aAC7B,GAAG,CAAC,EAAE,CAAC,CAAC;KACZ,CACF,CAAC;;;;IAKF,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE;QAC/B,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE;YACrD,cAAc,EAAE,IAAI;SACrB,CAAC,CAAC;KACJ,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,gBAAgB,GAAG,UACtB,QAA0D;IAE1D,MAAM,SAAS,GAAG,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;IAE7E,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO;QACxB,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE;YACxD,MAAM,EAAE,IAAI;SACb,CAAC,CAAC;KACJ,CAAC,CAAC;AACL,CAAC;;AC5KD;;;;AAIA,SAAS,gBAAgB,CAAC,OAAW;IACnC,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;IAE5B,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC;IACnC,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC;IACjC,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC;IACnC,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC;IACnC,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC;IAC7B,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC;IAC7B,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC;IACzC,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC;IACzC,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC;IAC3C,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,EAAE,CAAC;IAC3B,MAAM,OAAO,GAAG,CAAC,CAAC,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,IAAI,GAAG,CAAC;IAEpD,KAAK,CAAC,KAAK,CAAC,GAAG,OAAO,GAAG,CAAC,CAAC;IAC3B,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,OAAO,GAAG,CAAC,CAAC;IAElC,IAAI,UAAU,EAAE;QACd,KAAK,CAAC,GAAG,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;QAClC,MAAM,CAAC,GAAG,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;KACnC;IAED,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,OAAO,GAAG,CAAC,CAAC;IAElC,IAAI,UAAU,EAAE;QACd,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACxB;IAED,OAAO,KAAK,CAAC;UACT,OAAO,CAAC,QAAQ,CAAC,kBAAkB,CAAC;UACpC,OAAO,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC;AAC9C,CAAC;AAED;;;;AAIA,SAAS,MAAM,CAAC,OAAW;IACzB,MAAM,MAAM,GAAG,CAAC,CAAC,uCAAuC,CAAC,CAAC;IAC1D,MAAM,KAAK,GAAG,CAAC,CAAC,sCAAsC,CAAC,CAAC;IACxD,MAAM,MAAM,GAAG,CAAC,CAAC,uCAAuC,CAAC,CAAC;IAC1D,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAyB,CAAC;IAC3E,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;IACtC,MAAM,UAAU,GAAG,OAAO,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC;;IAG5D,UAAU;UACN,OAAO,CAAC,QAAQ,CAAC,sBAAsB,CAAC;UACxC,OAAO,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC;;IAGhD,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,MAAM,EAAE,CAAC;IAC5C,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,MAAM,EAAE,CAAC;IAC3C,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,MAAM,EAAE,CAAC;IAC5C,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;;IAGpD,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;IACrB,IAAI,UAAU,EAAE;QACd,UAAU,GAAG,CAAC,CAAC,eAAe,CAAC,CAAC;QAChC,MAAM,CAAC,KAAK,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;KACnC;IAED,OAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;IACvC,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;IACrC,OAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;IACvC,OAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;IACvC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAChD,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAChD,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;IAC7C,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;IAC7C,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,UAAU,CAAC,CAAC;;IAG/C,gBAAgB,CAAC,OAAO,CAAC,CAAC;AAC5B,CAAC;AAED,MAAM,aAAa,GAAG,kCAAkC,CAAC;AAEzD,CAAC,CAAC;;IAEA,SAAS,CAAC,EAAE,CAAC,cAAc,EAAE,aAAa,EAAE;QAC1C,MAAM,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAqB,CAAC;QAEpD,gBAAgB,CAAC,OAAO,CAAC,CAAC;KAC3B,CAAC,CAAC;;IAGH,SAAS,CAAC,EAAE,CAAC,UAAU,EAAE,aAAa,EAAE,UAAU,KAAY;QAC5D,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACnB,OAAO;SACR;QAED,QAAQ,CAAC,KAAK,CAAC,CAAC;QAEhB,IAAK,IAAyB,CAAC,QAAQ,EAAE;YACvC,OAAO;SACR;QAED,MAAM,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAqB,CAAC;QAEpD,OAAO,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;KACvC,CAAC,CAAC;;IAGH,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE,aAAa,EAAE,UAAU,KAAY;QAC1D,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACnB,OAAO;SACR;QAED,IAAK,IAAyB,CAAC,QAAQ,EAAE;YACvC,OAAO;SACR;QAED,MAAM,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAqB,CAAC;QAEpD,OAAO,CAAC,WAAW,CAAC,mBAAmB,CAAC,CAAC;KAC1C,CAAC,CAAC;IAEH,SAAS,CAAC,EAAE,CAAC,WAAW,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC;;;;IAKnD,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;QAC5B,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;KACjB,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,aAAa,GAAG,UACnB,QAA0D;IAE1D,MAAM,SAAS,GAAG,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;IAE1E,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO;QACxB,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;KACpB,CAAC,CAAC;AACL,CAAC;;ACrID,MAAMD,iBAAe,GAAY;IAC/B,OAAO,EAAE,OAAO;CACjB,CAAC;AAEF,MAAM,GAAG;IA+BP,YACE,QAAyD,EACzD,UAAmB,EAAE;;;;QAxBhB,YAAO,GAAY,MAAM,CAAC,EAAE,EAAEA,iBAAe,CAAC,CAAC;;;;QAK9C,UAAK,GAAU,QAAQ,CAAC;QAqB9B,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAC;QAEpC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAE9B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC5C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAClD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAE9C,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,OAAO,EAAE;YACpC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,uBAAuB,EAAE,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YACzD,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,YAAY,EAAE,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;SACpD;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,OAAO,EAAE;YACpC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,EAAE,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;SAC7C;;QAGD,SAAS,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,KAAK;YAC7B,IAAI,CAAC,CAAC,KAAK,CAAC,MAAqB,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,MAAM,EAAE;gBACtE,OAAO;aACR;YAED,IAAI,CAAC,KAAK,EAAE,CAAC;SACd,CAAC,CAAC;KACJ;;;;;IAMO,YAAY,CAAC,IAAW;QAC9B,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;KAClD;;;;IAKO,MAAM;QACZ,OAAO,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC;KAC5D;;;;IAKM,IAAI;QACT,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO;SACR;;QAGD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG;YAC7B,MAAM,KAAK,GAAG,GAAG,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC;YAE1D,GAAG,CAAC,KAAK,CAAC,eAAe,GAAG,KAAK,CAAC;YAClC,GAAG,CAAC,KAAK,CAAC,qBAAqB,GAAG,KAAK,CAAC;SACzC,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;;QAGhE,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,MAAM,EAAE;YAC7C,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;SACvC;QAED,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;QACvB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;;QAG1B,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,aAAa,CAAC;YACnC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;gBACzC,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;gBACtB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;aAC7B;SACF,CAAC,CAAC;KACJ;;;;IAKM,KAAK;QACV,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE;YAClB,OAAO;SACR;;QAGD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG;YAC7B,MAAM,KAAK,GAAG,GAAG,EAAE,GAAG,KAAK,IAAI,CAAC;YAEhC,GAAG,CAAC,KAAK,CAAC,eAAe,GAAG,KAAK,CAAC;YAClC,GAAG,CAAC,KAAK,CAAC,qBAAqB,GAAG,KAAK,CAAC;SACzC,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC;QAC7C,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC;QACzC,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;QACvB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;;QAG3B,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,aAAa,CAAC;YAClC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;gBACzC,OAAO;aACR;YAED,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;YACtB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAC5B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;SAC7B,CAAC,CAAC;KACJ;;;;IAKM,MAAM;QACX,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;KAC5C;;;;IAKM,IAAI;QACT,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;KAC5C;;;;IAKM,IAAI;QACT,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;KACzC;;;;IAKM,QAAQ;QACb,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;CACF;AAED,IAAI,CAAC,GAAG,GAAG,GAAG;;ACjOd,MAAMC,YAAU,GAAG,UAAU,CAAC;AAE9B,CAAC,CAAC;;;IAIA,SAAS,CAAC,EAAE,CACV,gCAAgC,EAChC,IAAIA,YAAU,GAAG,EACjB;QACE,IAAI,IAAI,CAAC,GAAG,CACV,IAAmB,EACnB,YAAY,CAAC,IAAmB,EAAEA,YAAU,CAAC,CAC9C,CAAC;KACH,CACF,CAAC;AACJ,CAAC,CAAC;;ACtBF;;;;;;;;;;;;;;;;AAuFA,MAAMD,iBAAe,GAAY;IAC/B,QAAQ,EAAE,MAAM;IAChB,MAAM,EAAE,EAAE;CACX,CAAC;AAEF,MAAM,MAAM;IA6DV,YACE,QAAyD,EACzD,UAAmB,EAAE;;;;QAtDhB,aAAQ,GAAO,CAAC,EAAE,CAAC;;;;QAKnB,YAAO,GAAY,MAAM,CAAC,EAAE,EAAEA,iBAAe,CAAC,CAAC;;;;QAK9C,SAAI,GAAG,CAAC,CAAC;;;;QAKT,cAAS,GAAO,CAAC,EAAE,CAAC;;;;QAKpB,UAAK,GAAO,CAAC,EAAE,CAAC;;;;QAKhB,WAAM,GAAO,CAAC,EAAE,CAAC;;;;QAKjB,kBAAa,GAAG,CAAC,CAAC;;;;QAKlB,iBAAY,GAAG,EAAE,CAAC;;;;QAKlB,kBAAa,GAAG,EAAE,CAAC;;;;QAUnB,UAAK,GAAU,QAAQ,CAAC;QAM9B,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,EAA2B,CAAC;QAC5D,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QAEpB,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;;QAG9B,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;;QAGzB,IAAI,CAAC,YAAY,EAAE,CAAC;;QAGpB,SAAS,CAAC,EAAE,CAAC,kBAAkB,EAAE,CAAC,KAAY;YAC5C,MAAM,OAAO,GAAG,CAAC,CAAC,KAAK,CAAC,MAAqB,CAAC,CAAC;YAE/C,IACE,IAAI,CAAC,MAAM,EAAE;gBACb,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC;gBAC1B,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EACvC;gBACA,IAAI,CAAC,KAAK,EAAE,CAAC;aACd;SACF,CAAC,CAAC;KACJ;;;;IAKO,YAAY;QAClB,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;;QAGtC,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;;QAG7C,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;QACvC,MAAM,UAAU,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC;QACvC,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;;QAG1D,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,GAAG,IAAI,CAAC;QACpD,IAAI,UAAU,GAAG,UAAU,GAAG,IAAI,CAAC,IAAI,GAAG,UAAU,GAAG,CAAC,CAAC;;QAGzD,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC,GAAG,CAAC;QAEhE,IAAI,gBAAwB,CAAC;QAC7B,IAAI,aAAqB,CAAC;QAE1B,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE;YACtC,aAAa,GAAG,aAAa,CAAC;YAC9B,gBAAgB,GAAG,KAAK,CAAC;SAC1B;aAAM,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,KAAK,EAAE;YAC1C,aAAa,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC;YAChC,gBAAgB,GAAG,MAAM,CAAC;SAC3B;aAAM;;YAEL,MAAM,aAAa,GAAG,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,MAAO,GAAG,CAAC,CAAC;YAC9D,IAAI,UAAU,GAAG,aAAa,EAAE;gBAC9B,UAAU,GAAG,aAAa,CAAC;aAC5B;;YAGD,aAAa,GAAG,EACd,UAAU;gBACV,IAAI,CAAC,aAAa,GAAG,UAAU;gBAC/B,CAAC,UAAU,GAAG,aAAa,IAAI,CAAC,CACjC,CAAC;YAEF,MAAM,gBAAgB,GAAG,EACvB,UAAU;gBACV,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,UAAU;gBAC5B,CAAC,UAAU,GAAG,aAAa,IAAI,CAAC,CACjC,CAAC;YACF,IAAI,aAAa,GAAG,gBAAgB,EAAE;gBACpC,aAAa,GAAG,gBAAgB,CAAC;aAClC;;YAGD,MAAM,OAAO,GAAG,UAAU,GAAG,aAAa,CAAC;YAC3C,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAO,EAAE;;gBAElC,aAAa,GAAG,EAAE,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,MAAO,CAAC,CAAC;aACtD;iBAAM,IAAI,OAAO,GAAG,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,MAAO,GAAG,YAAY,EAAE;;gBAErE,aAAa,GAAG,EACd,UAAU;oBACV,UAAU;oBACV,IAAI,CAAC,OAAO,CAAC,MAAO;oBACpB,YAAY,CACb,CAAC;aACH;;YAGD,gBAAgB,GAAG,GACjB,IAAI,CAAC,aAAa,GAAG,UAAU,GAAG,UAAU,GAAG,CAAC,GAAG,UACrD,IAAI,CAAC;SACN;;QAGD,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QACpC,IAAI,CAAC,KAAK;aACP,UAAU,CAAC,SAAS,CAAC;aACrB,MAAM,CAAC,UAAU,CAAC;aAClB,GAAG,CAAC;YACH,YAAY,EAAE,aAAa,GAAG,IAAI;YAClC,kBAAkB,EAAE,SAAS,GAAG,gBAAgB,GAAG,IAAI;SACxD,CAAC,CAAC;KACN;;;;IAKO,MAAM;QACZ,OAAO,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC;KAC5D;;;;IAKM,YAAY;QACjB,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;YACjB,IAAI,CAAC,KAAK,EAAE,CAAC;SACd;QAED,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAY,CAAC;QAUlD,MAAM,SAAS,GAAoB,EAAE,CAAC;QACtC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;;QAGlB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM;YAC7C,MAAM,IAAI,GAAG,MAAM,CAAC,WAAW,IAAI,EAAE,CAAC;YACtC,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;YAC3B,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;YACjC,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,KAAK,KAAK,CAAC;YAE9C,SAAS,CAAC,IAAI,CAAC;gBACb,KAAK;gBACL,IAAI;gBACJ,QAAQ;gBACR,QAAQ;gBACR,KAAK;aACN,CAAC,CAAC;YAEH,IAAI,QAAQ,EAAE;gBACZ,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;gBACzB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;aAC5B;YAED,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAC3B,gDAAgD;iBAC7C,QAAQ,GAAG,WAAW,GAAG,EAAE,CAAC;iBAC5B,QAAQ,GAAG,WAAW,GAAG,EAAE,CAAC;gBAC7B,IAAI,IAAI,QAAQ,CACnB,CAAC;SACH,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,GAAG,CAAC,CAChB,sCAAsC,IAAI,CAAC,YAAY,SAAS,CACjE,CAAC;QAEF,IAAI,CAAC,QAAQ,GAAG,CAAC,CACf,gDAAgD,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI;YACvE,UAAU,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI;YACxC,OAAO,IAAI,CAAC,QAAQ,UAAU,CACjC;aACE,IAAI,EAAE;aACN,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAE1B,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,sCAAsC,CAAC;aACnD,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC;aACvB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEvB,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC;QAChC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;;QAGlC,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC;QAEvD,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE;YAClB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;YAE/B,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE;gBACjB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;aACf;SACF;;;QAID,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE;YACtB,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;gBAC5B,OAAO;aACR;YAED,MAAM,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;YACtB,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;YAC5B,MAAM,IAAI,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;YAE9B,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,OAAO;aACR;YAED,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC7B,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YACnC,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;YAC3B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC;YAChC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC;YAChC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC;YAC9B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC/B,IAAI,CAAC,KAAK,EAAE,CAAC;SACd,CAAC,CAAC;;QAGH,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAY;YACrC,MAAM,OAAO,GAAG,CAAC,CAAC,KAAK,CAAC,MAAqB,CAAC,CAAC;;YAG/C,IACE,OAAO,CAAC,EAAE,CAAC,mBAAmB,CAAC;gBAC/B,OAAO,CAAC,EAAE,CAAC,wBAAwB,CAAC,EACpC;gBACA,OAAO;aACR;YAED,IAAI,CAAC,MAAM,EAAE,CAAC;SACf,CAAC,CAAC;KACJ;;;;IAKO,aAAa;QACnB,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,qBAAqB,CAAC,CAAC;QAEjD,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;YAC5B,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;YACtB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAC5B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;SACtC;QAED,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;YAC5B,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;YACtB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;;YAG5B,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YAC7B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;gBACb,YAAY,EAAE,EAAE;gBAChB,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,EAAE;aACV,CAAC,CAAC;SACJ;KACF;;;;;IAMO,YAAY,CAAC,IAAW;QAC9B,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;KACpD;;;;IAKM,MAAM;QACX,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;KAC5C;;;;IAKM,IAAI;QACT,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO;SACR;QAED,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;QACvB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC1B,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;QAC3C,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;KACtD;;;;IAKM,KAAK;QACV,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE;YAClB,OAAO;SACR;QAED,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;QACvB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAC3B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;QACjC,IAAI,CAAC,QAAQ;aACV,WAAW,CAAC,kBAAkB,CAAC;aAC/B,QAAQ,CAAC,qBAAqB,CAAC,CAAC;QACnC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;KACtD;;;;IAKM,QAAQ;QACb,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;CACF;AAED,IAAI,CAAC,MAAM,GAAG,MAAM;;ACvdpB,MAAMC,YAAU,GAAG,aAAa,CAAC;AAEjC,CAAC,CAAC;IACA,IAAI,CAAC,QAAQ,CAAC,IAAIA,YAAU,GAAG,EAAE;QAC/B,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,YAAY,CAAC,IAAI,EAAEA,YAAU,CAAC,CAAC,CAAC;KACvD,CAAC,CAAC;AACL,CAAC,CAAC;;ACPF,CAAC,CAAC;;IAEA,IAAI,CAAC,QAAQ,CAAC,0BAA0B,EAAE;QACxC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;KACzB,CAAC,CAAC;;IAGH,IAAI,CAAC,QAAQ,CAAC,kCAAkC,EAAE;QAChD,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;YACtB,WAAW,EAAE,8BAA8B;YAC3C,aAAa,EAAE,gCAAgC;SAChD,CAAC,CAAC;KACJ,CAAC,CAAC;AACL,CAAC,CAAC;;AC4CF,MAAMD,iBAAe,GAAY;IAC/B,OAAO,EAAE,OAAO;IAChB,IAAI,EAAE,KAAK;CACZ,CAAC;AAEF,MAAM,GAAG;IA0BP,YACE,QAAyD,EACzD,UAAmB,EAAE;;;;QAnBhB,YAAO,GAAY,MAAM,CAAC,EAAE,EAAEA,iBAAe,CAAC,CAAC;;;;QAK/C,gBAAW,GAAG,CAAC,CAAC,CAAC;QAgBtB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAC;QAEpC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAE9B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QACzC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,wCAAwC,CAAC,CAAC,QAAQ,CACpE,IAAI,CAAC,QAAQ,CACd,CAAC;;QAGF,MAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;QAClC,IAAI,IAAI,EAAE;YACR,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG;gBACzB,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,EAAE;oBAChC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;oBACzB,OAAO,KAAK,CAAC;iBACd;gBAED,OAAO,IAAI,CAAC;aACb,CAAC,CAAC;SACJ;;QAGD,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,CAAC,EAAE;YAC3B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG;gBACzB,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;oBACtC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;oBACzB,OAAO,KAAK,CAAC;iBACd;gBAED,OAAO,IAAI,CAAC;aACb,CAAC,CAAC;SACJ;;QAGD,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,CAAC,EAAE;YAChD,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;SACtB;;QAGD,IAAI,CAAC,SAAS,EAAE,CAAC;;QAGjB,OAAO,CAAC,EAAE,CACR,QAAQ,EACR,CAAC,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,oBAAoB,EAAE,EAAE,GAAG,CAAC,CACnD,CAAC;;QAGF,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG;YACrB,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;SACxB,CAAC,CAAC;KACJ;;;;;IAMO,UAAU,CAAC,IAAQ;QACzB,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,SAAS,CAAC;KAC5C;;;;;IAMO,YAAY,CAAC,GAAgB;QACnC,MAAM,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;;QAGpB,MAAM,UAAU,GAAG;;YAEjB,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;gBACzB,OAAO,KAAK,CAAC;aACd;YAED,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACzC,IAAI,CAAC,SAAS,EAAE,CAAC;SAClB,CAAC;;QAGF,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;;QAG7B,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,OAAO,EAAE;YACpC,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;SACnC;;QAGD,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE;YACf,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;gBAChD,OAAO,KAAK,CAAC;aACd;SACF,CAAC,CAAC;KACJ;;;;;;;IAQO,YAAY,CAAC,IAAW,EAAE,QAAY,EAAE,UAAU,GAAG,EAAE;QAC7D,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;KACzD;;;;IAKO,SAAS;QACf,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG;YACzB,MAAM,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;;YAGzC,IAAI,KAAK,KAAK,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;gBACxD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;oBACrC,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE;wBACzC,KAAK,EAAE,IAAI,CAAC,WAAW;wBACvB,EAAE,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;qBACvB,CAAC,CAAC;oBACH,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;oBAEhC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;iBAClC;gBAED,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,EAAE,CAAC;gBACnB,IAAI,CAAC,oBAAoB,EAAE,CAAC;aAC7B;iBAAM;gBACL,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC;gBACpC,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,EAAE,CAAC;aACpB;SACF,CAAC,CAAC;KACJ;;;;IAKO,oBAAoB;;QAE1B,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,CAAC,EAAE;YAC3B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;gBAClB,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,CAAC;aACT,CAAC,CAAC;YAEH,OAAO;SACR;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAEnD,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;YAC/B,OAAO;SACR;QAED,MAAM,eAAe,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC;QAE5C,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;YAClB,IAAI,EAAE,GACJ,eAAe,CAAC,IAAI;gBACpB,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU;gBAC3B,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC,IAC3C,IAAI;YACJ,KAAK,EAAE,GAAG,UAAU,CAAC,UAAU,EAAE,IAAI;SACtC,CAAC,CAAC;KACJ;;;;IAKM,IAAI;QACT,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,CAAC,EAAE;YAC3B,OAAO;SACR;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE;YAC5C,IAAI,CAAC,WAAW,EAAE,CAAC;SACpB;aAAM,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;YAC5B,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;SACtB;QAED,IAAI,CAAC,SAAS,EAAE,CAAC;KAClB;;;;IAKM,IAAI;QACT,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,CAAC,EAAE;YAC3B,OAAO;SACR;QAED,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE;YACxB,IAAI,CAAC,WAAW,EAAE,CAAC;SACpB;aAAM,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;YAC5B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;SAC1C;QAED,IAAI,CAAC,SAAS,EAAE,CAAC;KAClB;;;;;IAMM,IAAI,CAAC,KAAsB;QAChC,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,CAAC,EAAE;YAC3B,OAAO;SACR;QAED,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;YACnB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;SAC1B;aAAM;YACL,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG;gBACrB,IAAI,GAAG,CAAC,EAAE,KAAK,KAAK,EAAE;oBACpB,IAAI,CAAC,WAAW,KAAK,CAAC,CAAC;oBACvB,OAAO,KAAK,CAAC;iBACd;aACF,CAAC,CAAC;SACJ;QAED,IAAI,CAAC,SAAS,EAAE,CAAC;KAClB;;;;;IAMM,YAAY;QACjB,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;QAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC7C,MAAM,cAAc,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC;QACtC,MAAM,cAAc,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC;QAEtC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YACpB,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;YACtB,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;YACtB,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAE5B,OAAO;SACR;;QAGD,QAAQ,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG;;YAEvB,IAAI,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;gBACnC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;gBAEvB,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,CAAC,EAAE;oBAC3B,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;iBACtB;qBAAM,IAAI,KAAK,IAAI,IAAI,CAAC,WAAW,EAAE;oBACpC,IAAI,CAAC,WAAW,EAAE,CAAC;iBACpB;aACF;SACF,CAAC,CAAC;;QAGH,QAAQ,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,GAAG;;YAEvB,IAAI,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;gBACnC,IAAI,KAAK,GAAG,IAAI,CAAC,WAAW,EAAE;oBAC5B,IAAI,CAAC,WAAW,EAAE,CAAC;iBACpB;qBAAM,IAAI,KAAK,KAAK,IAAI,CAAC,WAAW,EAAE;oBACrC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;iBACtB;aACF;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;QAEtB,IAAI,CAAC,SAAS,EAAE,CAAC;KAClB;CACF;AAED,IAAI,CAAC,GAAG,GAAG,GAAG;;AC7Wd,MAAMC,YAAU,GAAG,UAAU,CAAC;AAE9B,CAAC,CAAC;IACA,IAAI,CAAC,QAAQ,CAAC,IAAIA,YAAU,GAAG,EAAE;QAC/B,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,YAAY,CAAC,IAAI,EAAEA,YAAU,CAAC,CAAC,CAAC;KACpD,CAAC,CAAC;AACL,CAAC,CAAC;;ACZF;;;;AAiEA,MAAMD,iBAAe,GAAY;IAC/B,OAAO,EAAE,KAAK;IACd,KAAK,EAAE,KAAK;CACb,CAAC;AAEF,MAAM,MAAM;IA0BV,YACE,QAAyD,EACzD,UAAmB,EAAE;;;;QAnBhB,YAAO,GAAY,MAAM,CAAC,EAAE,EAAEA,iBAAe,CAAC,CAAC;;;;QAK9C,YAAO,GAAG,KAAK,CAAC;QAgBtB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAC;QAEpC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAE9B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,mBAAmB,CAAC;cACvD,OAAO;cACP,MAAM,CAAC;QAEX,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;YAC/C,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;SACvB;aAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;YACrD,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;SACvB;aAAM,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;YAC3B,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;SACvB;aAAM;YACL,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;SACvB;;QAGD,OAAO,CAAC,EAAE,CACR,QAAQ,EACR,CAAC,CAAC,QAAQ,CAAC;YACT,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;;;gBAGpB,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;oBACzC,CAAC,CAAC,WAAW,EAAE,CAAC;oBAChB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;oBACrB,CAAC,CAAC,YAAY,EAAE,CAAC;iBAClB;;gBAGD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;oBAChD,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;iBACvB;aACF;iBAAM,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;;gBAEnD,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;oBAC9C,CAAC,CAAC,WAAW,EAAE,CAAC;oBAChB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;oBACpB,CAAC,CAAC,UAAU,EAAE,CAAC;oBAEf,CAAC,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;iBACrD;qBAAM;oBACL,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;iBACvB;aACF;SACF,EAAE,GAAG,CAAC,CACR,CAAC;;QAGF,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK;YACtD,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;SAC1C,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,EAAE,CAAC;KACrB;;;;IAKO,SAAS;QACf,OAAO,OAAO,CAAC,KAAK,EAAE,IAAI,IAAI,CAAC;KAChC;;;;IAKO,YAAY;;QAElB,MAAM,IAAI,GAAG,IAAI,CAAC;;QAGlB,IAAI,mBAA2C,CAAC;QAChD,IAAI,WAAmB,CAAC;QACxB,IAAI,WAAmB,CAAC;QACxB,IAAI,WAAmB,CAAC;QACxB,IAAI,OAAO,GAAiC,IAAI,CAAC;QACjD,IAAI,YAAY,GAAG,KAAK,CAAC;QACzB,MAAM,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;;QAGxB,MAAM,cAAc,GAAG,EAAE,CAAC;QAE1B,SAAS,WAAW,CAAC,UAAkB;YACrC,MAAM,sBAAsB,GAAG,IAAI,CAAC,QAAQ,KAAK,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YAClE,MAAM,YAAY,GAAG,aACnB,CAAC,CAAC,GAAG,sBAAsB,GAAG,UAChC,oBAAoB,CAAC;YACrB,MAAM,aAAa,GAAG,qBAAqB,CAAC;YAE5C,IAAI,CAAC,QAAQ,CAAC,GAAG,CACf,SAAS,EACT,cAAc,YAAY,iBAAiB,aAAa,GAAG,CAC5D,CAAC;SACH;QAED,SAAS,aAAa;YACpB,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC;YACtC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,eAAe,GAAG,EAAE,CAAC;YAC5C,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,GAAG,EAAE,CAAC;YACvC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,gBAAgB,GAAG,EAAE,CAAC;SAC9C;QAED,SAAS,gBAAgB;YACvB,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC;SACnC;QAED,SAAS,aAAa,CAAC,QAAgB;YACrC,OAAO,IAAI,CAAC,GAAG,CACb,IAAI,CAAC,GAAG,CACN,OAAO,KAAK,SAAS;kBACjB,WAAW,GAAG,QAAQ;kBACtB,gBAAgB,EAAE,GAAG,WAAW,GAAG,QAAQ,EAC/C,CAAC,CACF,EACD,gBAAgB,EAAE,CACnB,CAAC;SACH;QAED,SAAS,cAAc,CAAC,KAAa;YACnC,IAAI,OAAO,EAAE;gBACX,IAAI,MAAM,GAAI,KAAoB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;gBAC3D,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;oBAC7B,MAAM,GAAG,KAAK,CAAC,KAAK,EAAE,GAAG,MAAM,CAAC;iBACjC;gBAED,MAAM,cAAc,GAAG,aAAa,CAAC,MAAM,CAAC,GAAG,gBAAgB,EAAE,CAAC;gBAElE,YAAY,GAAG,KAAK,CAAC;gBACrB,MAAM,YAAY,GAAG,OAAO,CAAC;gBAC7B,OAAO,GAAG,IAAI,CAAC;gBAEf,IAAI,YAAY,KAAK,SAAS,EAAE;oBAC9B,IAAI,cAAc,GAAG,IAAI,EAAE;wBACzB,aAAa,EAAE,CAAC;wBAChB,IAAI,CAAC,IAAI,EAAE,CAAC;qBACb;yBAAM;wBACL,aAAa,EAAE,CAAC;qBACjB;iBACF;qBAAM;oBACL,IAAI,cAAc,GAAG,IAAI,EAAE;wBACzB,aAAa,EAAE,CAAC;wBAChB,IAAI,CAAC,KAAK,EAAE,CAAC;qBACd;yBAAM;wBACL,aAAa,EAAE,CAAC;qBACjB;iBACF;gBAED,CAAC,CAAC,YAAY,EAAE,CAAC;aAClB;iBAAM;gBACL,YAAY,GAAG,KAAK,CAAC;aACtB;YAED,KAAK,CAAC,GAAG,CAAC;;gBAER,SAAS,EAAE,eAAe;gBAC1B,QAAQ,EAAE,cAAc;;gBAExB,WAAW,EAAE,eAAe;aAC7B,CAAC,CAAC;SACJ;QAED,SAAS,eAAe,CAAC,KAAY;YACnC,IAAI,MAAM,GAAI,KAAoB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACpD,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;gBAC7B,MAAM,GAAG,KAAK,CAAC,KAAK,EAAE,GAAG,MAAM,CAAC;aACjC;YAED,MAAM,MAAM,GAAI,KAAoB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YAEtD,IAAI,OAAO,EAAE;gBACX,WAAW,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;aACpC;iBAAM,IAAI,YAAY,EAAE;gBACvB,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,WAAW,CAAC,CAAC;gBAC7C,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,WAAW,CAAC,CAAC;gBAC7C,MAAM,SAAS,GAAG,CAAC,CAAC;gBAEpB,IAAI,KAAK,GAAG,SAAS,IAAI,KAAK,IAAI,SAAS,EAAE;oBAC3C,WAAW,GAAG,MAAM,CAAC;oBACrB,OAAO,GAAG,IAAI,CAAC,KAAK,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS,CAAC;oBAC1D,CAAC,CAAC,UAAU,EAAE,CAAC;oBACf,WAAW,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;iBACpC;qBAAM,IAAI,KAAK,IAAI,SAAS,IAAI,KAAK,GAAG,SAAS,EAAE;oBAClD,cAAc,EAAE,CAAC;iBAClB;aACF;SACF;QAED,SAAS,gBAAgB,CAAC,KAAY;YACpC,WAAW,GAAI,KAAoB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACrD,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;gBAC7B,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,GAAG,WAAW,CAAC;aAC3C;YAED,WAAW,GAAI,KAAoB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YAErD,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;gBAC3B,IACE,WAAW,GAAG,cAAc;oBAC5B,mBAAmB,KAAK,gBAAgB,EACxC;oBACA,OAAO;iBACR;aACF;YAED,YAAY,GAAG,IAAI,CAAC;YAEpB,KAAK,CAAC,EAAE,CAAC;gBACP,SAAS,EAAE,eAAe;gBAC1B,QAAQ,EAAE,cAAc;gBACxB,WAAW,EAAE,eAAe;aAC7B,CAAC,CAAC;SACJ;QAED,SAAS,mBAAmB;YAC1B,IAAI,CAAC,mBAAmB,EAAE;gBACxB,KAAK,CAAC,EAAE,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC;gBACzC,mBAAmB,GAAG,gBAAgB,CAAC;aACxC;SACF;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;YACtB,mBAAmB,EAAE,CAAC;SACvB;KACF;;;;;IAMO,YAAY,CAAC,IAAW;QAC9B,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;KACrD;;;;IAKO,aAAa;QACnB,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;YAC9C,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;YACtB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;SAC7B;aAAM;YACL,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;YACtB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;SAC7B;KACF;;;;IAKO,MAAM;QACZ,OAAO,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC;KAC5D;;;;IAKM,IAAI;QACT,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO;SACR;QAED,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;QACvB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAE1B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YACzB,CAAC,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,oBAAoB,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;SACzD;QAED,IAAI,CAAC,QAAQ;aACV,WAAW,CAAC,mBAAmB,CAAC;aAChC,QAAQ,CAAC,kBAAkB,CAAC;aAC5B,aAAa,CAAC,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;QAE7C,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YAC7C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YACpB,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;YACjD,CAAC,CAAC,UAAU,EAAE,CAAC;SAChB;KACF;;;;IAKM,KAAK;QACV,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE;YAClB,OAAO;SACR;QAED,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;QACvB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAE3B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YACzB,CAAC,CAAC,MAAM,CAAC,CAAC,WAAW,CAAC,oBAAoB,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;SAC5D;QAED,IAAI,CAAC,QAAQ;aACV,QAAQ,CAAC,mBAAmB,CAAC;aAC7B,WAAW,CAAC,kBAAkB,CAAC;aAC/B,aAAa,CAAC,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;QAE7C,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,CAAC,CAAC,WAAW,EAAE,CAAC;YAChB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;YACrB,CAAC,CAAC,YAAY,EAAE,CAAC;SAClB;KACF;;;;IAKM,MAAM;QACX,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;KAC5C;;;;IAKM,QAAQ;QACb,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;CACF;AAED,IAAI,CAAC,MAAM,GAAG,MAAM;;AChapB,MAAMC,YAAU,GAAG,aAAa,CAAC;AAQjC,CAAC,CAAC;IACA,IAAI,CAAC,QAAQ,CAAC,IAAIA,YAAU,GAAG,EAAE;QAC/B,MAAM,QAAQ,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;QACzB,MAAM,OAAO,GAAG,YAAY,CAAC,IAAI,EAAEA,YAAU,CAAY,CAAC;QAC1D,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC;;QAEhC,OAAO,OAAO,CAAC,MAAM,CAAC;QAEtB,MAAM,OAAO,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAC;QACpC,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAEnD,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;KAC/C,CAAC,CAAC;AACL,CAAC,CAAC;;ACxBF,MAAM,SAAS,GAAwB,EAAE,CAAC;AAe1C,SAAS,KAAK,CAAC,IAAY,EAAE,IAAW;IACtC,IAAI,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE;QAChC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;KACtB;IAED,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE;QACrB,OAAO,SAAS,CAAC,IAAI,CAAC,CAAC;KACxB;IAED,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC7B,CAAC;AAED;;;;AAIA,SAAS,OAAO,CAAC,IAAY;IAC3B,IAAI,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE;QAChC,OAAO;KACR;IAED,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE;QAC3B,OAAO;KACR;IAED,MAAM,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,KAAK,EAAG,CAAC;IAEtC,IAAI,EAAE,CAAC;AACT;;ACuBA,MAAMD,iBAAe,GAAY;IAC/B,OAAO,EAAE,IAAI;IACb,OAAO,EAAE,IAAI;IACb,KAAK,EAAE,KAAK;IACZ,UAAU,EAAE,IAAI;IAChB,aAAa,EAAE,IAAI;IACnB,cAAc,EAAE,IAAI;IACpB,eAAe,EAAE,KAAK;CACvB,CAAC;AAEF;;;AAGA,IAAI,WAAW,GAAkB,IAAI,CAAC;AAEtC;;;AAGA,MAAM,SAAS,GAAG,cAAc,CAAC;AAEjC;;;AAGA,IAAI,YAAY,GAAG,KAAK,CAAC;AAEzB;;;AAGA,IAAI,QAAmB,CAAC;AAExB,MAAM,MAAM;IAqBV,YACE,QAAyD,EACzD,UAAmB,EAAE;;;;QAdhB,YAAO,GAAY,MAAM,CAAC,EAAE,EAAEA,iBAAe,CAAC,CAAC;;;;QAK/C,UAAK,GAAU,QAAQ,CAAC;;;;QAKvB,WAAM,GAAG,KAAK,CAAC;QAMrB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAC;;QAGpC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;YAC9C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACjC;QAED,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;;QAG9B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM;YACxD,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE;gBACpB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;gBAE5B,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;oBAC9B,IAAI,CAAC,KAAK,EAAE,CAAC;iBACd;aACF,CAAC,CAAC;SACJ,CAAC,CAAC;;QAGH,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO;YAC1D,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE;gBACrB,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;gBAE7B,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;oBAC/B,IAAI,CAAC,KAAK,EAAE,CAAC;iBACd;aACF,CAAC,CAAC;SACJ,CAAC,CAAC;;QAGH,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK;YACtD,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;SAC1C,CAAC,CAAC;KACJ;;;;;IAMO,YAAY,CAAC,IAAW;QAC9B,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;KACrD;;;;IAKO,QAAQ;QACd,IAAI,CAAC,WAAW,EAAE;YAChB,OAAO;SACR;QAED,MAAM,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;QACtC,MAAM,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;QACvD,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC;QAC3D,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC;;QAG3D,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACpB,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAEpB,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;QACxC,QAAQ,CAAC,GAAG,CAAC;YACX,GAAG,EAAE,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,aAAa,IAAI,CAAC,IAAI;YAClD,MAAM,EAAE,GAAG,aAAa,IAAI;SAC7B,CAAC,CAAC;;QAGH,QAAQ,CAAC,WAAW,CAClB,aAAa;aACV,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;aAC1B,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAChC,CAAC;KACH;;;;IAKO,eAAe;QACrB,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE;YAChE,WAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SAC1B;KACF;;;;;IAMO,YAAY,CAAC,KAAY;QAC/B,IACE,CAAC,CAAC,KAAK,CAAC,MAAqB,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC;YACvD,WAAW,EACX;YACA,WAAW,CAAC,KAAK,EAAE,CAAC;SACrB;KACF;;;;IAKO,aAAa;QACnB,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;YAC9C,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;YACtB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;SAC7B;aAAM;YACL,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;YACtB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAC5B,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;;YAGrB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,IAAI,CAAC,WAAW,IAAI,YAAY,EAAE;gBAC5D,CAAC,CAAC,YAAY,EAAE,CAAC;gBACjB,YAAY,GAAG,KAAK,CAAC;aACtB;YAED,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;YAEtD,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;gBAChC,IAAI,CAAC,OAAO,EAAE,CAAC;aAChB;SACF;KACF;;;;IAKO,MAAM;QACZ,WAAW,GAAG,IAAI,CAAC;QAEnB,IAAI,CAAC,YAAY,EAAE;YACjB,CAAC,CAAC,UAAU,EAAE,CAAC;YACf,YAAY,GAAG,IAAI,CAAC;SACrB;QAED,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QACrB,IAAI,CAAC,QAAQ,EAAE,CAAC;QAEhB,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;;QAGrD,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;QACvB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC1B,IAAI,CAAC,QAAQ;aACV,QAAQ,CAAC,kBAAkB,CAAC;aAC5B,aAAa,CAAC,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;;QAG7C,IAAI,CAAC,QAAQ,EAAE;YACb,QAAQ,GAAG,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;SAChC;;QAGD,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;YACtB,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;SAC1C;aAAM;YACL,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;SACzC;;QAGD,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QAEvD,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;;;YAGxB,IAAI,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC7C,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,EAAE;gBACpC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,EAAE,CAAC,CAAC;aAC9C;;YAGD,IAAI,IAAI,EAAE;gBACR,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,GAAG,IAAI,GAC5B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,GACjC,aAAa,CAAC;aACf;iBAAM;gBACL,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC;aACtC;YAED,OAAO,CAAC,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;SAChD;KACF;;;;IAKO,MAAM;QACZ,OAAO,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC;KAC5D;;;;IAKM,IAAI;QACT,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO;SACR;;QAGD,IACE,CAAC,WAAW;aACT,WAAW,CAAC,KAAK,KAAK,SAAS,IAAI,WAAW,CAAC,KAAK,KAAK,QAAQ,CAAC;YACrE,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,EACvB;YACA,KAAK,CAAC,SAAS,EAAE,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;YAEtC,OAAO;SACR;QAED,IAAI,CAAC,MAAM,EAAE,CAAC;KACf;;;;IAKM,KAAK,CAAC,WAAW,GAAG,KAAK;;;;;;QAO9B,UAAU,CAAC;YACT,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE;gBAClB,OAAO;aACR;YAED,WAAW,GAAG,IAAI,CAAC;YAEnB,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;YACvB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;;YAG3B,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,IAAI,QAAQ,EAAE;gBACxC,CAAC,CAAC,WAAW,EAAE,CAAC;gBAChB,QAAQ,GAAG,IAAI,CAAC;;gBAGhB,CAAC,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;aACzC;YAED,IAAI,CAAC,QAAQ;iBACV,WAAW,CAAC,kBAAkB,CAAC;iBAC/B,aAAa,CAAC,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;YAE7C,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE;gBACpD,IAAI,CAAC,WAAW,EAAE;oBAChB,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;iBACvB;gBAED,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;aACjD;;;YAID,UAAU,CAAC;gBACT,OAAO,CAAC,SAAS,CAAC,CAAC;aACpB,EAAE,GAAG,CAAC,CAAC;SACT,CAAC,CAAC;KACJ;;;;IAKM,MAAM;QACX,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;KAC5C;;;;IAKM,QAAQ;QACb,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;;;;IAKM,OAAO;QACZ,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;SACxB;QAED,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,IAAI,CAAC,WAAW,EAAE;YAC5C,IAAI,QAAQ,EAAE;gBACZ,CAAC,CAAC,WAAW,EAAE,CAAC;gBAChB,QAAQ,GAAG,IAAI,CAAC;aACjB;YAED,IAAI,YAAY,EAAE;gBAChB,CAAC,CAAC,YAAY,EAAE,CAAC;gBACjB,YAAY,GAAG,KAAK,CAAC;aACtB;SACF;KACF;;;;IAKM,YAAY;QACjB,IAAI,CAAC,QAAQ,EAAE,CAAC;KACjB;;;ACjZH;AACA,SAAS,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,KAAY;IACnC,IACE,WAAW;QACX,WAAW,CAAC,OAAO,CAAC,UAAU;QAC9B,WAAW,CAAC,KAAK,KAAK,QAAQ;QAC7B,KAAuB,CAAC,OAAO,KAAK,EAAE,EACvC;QACA,WAAW,CAAC,KAAK,EAAE,CAAC;KACrB;AACH,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,MAAM,GAAG,MAAM;;AC9BpB,MAAMC,YAAU,GAAG,aAAa,CAAC;AACjC,MAAMC,UAAQ,GAAG,cAAc,CAAC;AAahC,CAAC,CAAC;IACA,SAAS,CAAC,EAAE,CAAC,OAAO,EAAE,IAAID,YAAU,GAAG,EAAE;QACvC,MAAM,OAAO,GAAG,YAAY,CAAC,IAAmB,EAAEA,YAAU,CAAY,CAAC;QACzE,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC;;QAEhC,OAAO,OAAO,CAAC,MAAM,CAAC;QAEtB,MAAM,OAAO,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAC;QACpC,IAAI,QAAQ,GAAG,OAAO,CAAC,IAAI,CAACC,UAAQ,CAAC,CAAC;QAEtC,IAAI,CAAC,QAAQ,EAAE;YACb,QAAQ,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAC7C,OAAO,CAAC,IAAI,CAACA,UAAQ,EAAE,QAAQ,CAAC,CAAC;SAClC;QAED,QAAQ,CAAC,IAAI,EAAE,CAAC;KACjB,CAAC,CAAC;AACL,CAAC,CAAC;;AC0EF,MAAM,cAAc,GAAW;IAC7B,IAAI,EAAE,EAAE;IACR,IAAI,EAAE,KAAK;IACX,KAAK,EAAE,IAAI;;IAEX,OAAO,EAAE,SAAQ;CAClB,CAAC;AAEF,MAAMF,iBAAe,GAAY;IAC/B,KAAK,EAAE,EAAE;IACT,OAAO,EAAE,EAAE;IACX,OAAO,EAAE,EAAE;IACX,cAAc,EAAE,KAAK;IACrB,QAAQ,EAAE,EAAE;IACZ,OAAO,EAAE,IAAI;IACb,OAAO,EAAE,IAAI;IACb,KAAK,EAAE,KAAK;IACZ,UAAU,EAAE,IAAI;IAChB,eAAe,EAAE,IAAI;;IAErB,MAAM,EAAE,SAAQ;;IAEhB,QAAQ,EAAE,SAAQ;;IAElB,OAAO,EAAE,SAAQ;;IAEjB,QAAQ,EAAE,SAAQ;CACnB,CAAC;AAEF,IAAI,CAAC,MAAM,GAAG,UAAU,OAAgB;;;IAEtC,OAAO,GAAG,MAAM,CAAC,EAAE,EAAEA,iBAAe,EAAE,OAAO,CAAC,CAAC;IAE/C,IAAI,CAAC,OAAO,CAAC,OAAQ,EAAE,CAAC,CAAC,EAAE,MAAM;QAC/B,OAAO,CAAC,OAAQ,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;KAC1D,CAAC,CAAC;;IAGH,IAAI,WAAW,GAAG,EAAE,CAAC;IACrB,UAAI,OAAO,CAAC,OAAO,0CAAE,MAAM,EAAE;QAC3B,WAAW,GAAG,kCACZ,OAAO,CAAC,cAAc,GAAG,8BAA8B,GAAG,EAC5D,IAAI,CAAC;QAEL,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,MAAM;YAC9B,WAAW;gBACT,+BAA+B;oBAC/B,uDACE,MAAM,CAAC,IAAI,GAAG,eAAe,GAAG,EAClC,KAAK,MAAM,CAAC,IAAI,MAAM,CAAC;SAC1B,CAAC,CAAC;QAEH,WAAW,IAAI,QAAQ,CAAC;KACzB;;IAGD,MAAM,IAAI,GACR,2BAA2B,OAAO,CAAC,QAAQ,IAAI;SAC9C,OAAO,CAAC,KAAK;cACV,kCAAkC,OAAO,CAAC,KAAK,QAAQ;cACvD,EAAE,CAAC;SACN,OAAO,CAAC,OAAO;cACZ,oCAAoC,OAAO,CAAC,OAAO,QAAQ;cAC3D,EAAE,CAAC;QACP,WAAW;QACX,QAAQ,CAAC;;IAGX,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;QACrC,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,KAAK,EAAE,OAAO,CAAC,KAAK;QACpB,UAAU,EAAE,OAAO,CAAC,UAAU;QAC9B,eAAe,EAAE,OAAO,CAAC,eAAe;KACzC,CAAC,CAAC;;IAGH,UAAI,OAAO,CAAC,OAAO,0CAAE,MAAM,EAAE;QAC3B,QAAQ,CAAC,QAAQ;aACd,IAAI,CAAC,gCAAgC,CAAC;aACtC,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM;YAClB,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE;gBACpB,OAAO,CAAC,OAAQ,CAAC,KAAK,CAAC,CAAC,OAAQ,CAAC,QAAQ,CAAC,CAAC;gBAE3C,IAAI,OAAO,CAAC,OAAQ,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE;oBACjC,QAAQ,CAAC,KAAK,EAAE,CAAC;iBAClB;aACF,CAAC,CAAC;SACJ,CAAC,CAAC;KACN;;IAGD,QAAQ,CAAC,QAAQ;SACd,EAAE,CAAC,kBAAkB,EAAE;QACtB,OAAO,CAAC,MAAO,CAAC,QAAQ,CAAC,CAAC;KAC3B,CAAC;SACD,EAAE,CAAC,oBAAoB,EAAE;QACxB,OAAO,CAAC,QAAS,CAAC,QAAQ,CAAC,CAAC;KAC7B,CAAC;SACD,EAAE,CAAC,mBAAmB,EAAE;QACvB,OAAO,CAAC,OAAQ,CAAC,QAAQ,CAAC,CAAC;KAC5B,CAAC;SACD,EAAE,CAAC,oBAAoB,EAAE;QACxB,OAAO,CAAC,QAAS,CAAC,QAAQ,CAAC,CAAC;KAC7B,CAAC,CAAC;IAEL,QAAQ,CAAC,IAAI,EAAE,CAAC;IAEhB,OAAO,QAAQ,CAAC;AAClB,CAAC;;AChKD,MAAMA,iBAAe,GAAY;IAC/B,WAAW,EAAE,IAAI;IACjB,OAAO,EAAE,IAAI;IACb,KAAK,EAAE,KAAK;IACZ,UAAU,EAAE,IAAI;IAChB,cAAc,EAAE,IAAI;CACrB,CAAC;AAEF,IAAI,CAAC,KAAK,GAAG,UACX,IAAY,EACZ,KAAW,EACX,SAAe,EACf,OAAa;IAEb,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE;QACrB,OAAO,GAAG,SAAS,CAAC;QACpB,SAAS,GAAG,KAAK,CAAC;QAClB,KAAK,GAAG,EAAE,CAAC;KACZ;IAED,IAAI,WAAW,CAAC,SAAS,CAAC,EAAE;;QAE1B,SAAS,GAAG,SAAc,CAAC;KAC5B;IAED,IAAI,WAAW,CAAC,OAAO,CAAC,EAAE;QACxB,OAAO,GAAG,EAAE,CAAC;KACd;IAED,OAAO,GAAG,MAAM,CAAC,EAAE,EAAEA,iBAAe,EAAE,OAAO,CAAC,CAAC;IAE/C,OAAO,IAAI,CAAC,MAAM,CAAC;QACjB,KAAK,EAAE,KAAK;QACZ,OAAO,EAAE,IAAI;QACb,OAAO,EAAE;YACP;gBACE,IAAI,EAAE,OAAO,CAAC,WAAW;gBACzB,IAAI,EAAE,KAAK;gBACX,KAAK,EAAE,OAAO,CAAC,cAAc;gBAC7B,OAAO,EAAE,SAAS;aACnB;SACF;QACD,QAAQ,EAAE,mBAAmB;QAC7B,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,KAAK,EAAE,OAAO,CAAC,KAAK;QACpB,UAAU,EAAE,OAAO,CAAC,UAAU;KAC/B,CAAC,CAAC;AACL,CAAC;;ACjCD,MAAMA,iBAAe,GAAY;IAC/B,WAAW,EAAE,IAAI;IACjB,UAAU,EAAE,QAAQ;IACpB,OAAO,EAAE,IAAI;IACb,KAAK,EAAE,KAAK;IACZ,UAAU,EAAE,IAAI;IAChB,aAAa,EAAE,IAAI;IACnB,cAAc,EAAE,IAAI;CACrB,CAAC;AAEF,IAAI,CAAC,OAAO,GAAG,UACb,IAAY,EACZ,KAAW,EACX,SAAe,EACf,QAAc,EACd,OAAa;IAEb,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE;QACrB,OAAO,GAAG,QAAQ,CAAC;QACnB,QAAQ,GAAG,SAAS,CAAC;QACrB,SAAS,GAAG,KAAK,CAAC;QAClB,KAAK,GAAG,EAAE,CAAC;KACZ;IAED,IAAI,WAAW,CAAC,SAAS,CAAC,EAAE;;QAE1B,SAAS,GAAG,SAAc,CAAC;KAC5B;IAED,IAAI,WAAW,CAAC,QAAQ,CAAC,EAAE;;QAEzB,QAAQ,GAAG,SAAc,CAAC;KAC3B;IAED,IAAI,WAAW,CAAC,OAAO,CAAC,EAAE;QACxB,OAAO,GAAG,EAAE,CAAC;KACd;IAED,OAAO,GAAG,MAAM,CAAC,EAAE,EAAEA,iBAAe,EAAE,OAAO,CAAC,CAAC;IAE/C,OAAO,IAAI,CAAC,MAAM,CAAC;QACjB,KAAK,EAAE,KAAK;QACZ,OAAO,EAAE,IAAI;QACb,OAAO,EAAE;YACP;gBACE,IAAI,EAAE,OAAO,CAAC,UAAU;gBACxB,IAAI,EAAE,KAAK;gBACX,KAAK,EAAE,OAAO,CAAC,aAAa;gBAC5B,OAAO,EAAE,QAAQ;aAClB;YACD;gBACE,IAAI,EAAE,OAAO,CAAC,WAAW;gBACzB,IAAI,EAAE,KAAK;gBACX,KAAK,EAAE,OAAO,CAAC,cAAc;gBAC7B,OAAO,EAAE,SAAS;aACnB;SACF;QACD,QAAQ,EAAE,qBAAqB;QAC/B,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,KAAK,EAAE,OAAO,CAAC,KAAK;QACpB,UAAU,EAAE,OAAO,CAAC,UAAU;KAC/B,CAAC,CAAC;AACL,CAAC;;ACtCD,MAAMA,iBAAe,GAAY;IAC/B,WAAW,EAAE,IAAI;IACjB,UAAU,EAAE,QAAQ;IACpB,OAAO,EAAE,IAAI;IACb,KAAK,EAAE,KAAK;IACZ,UAAU,EAAE,IAAI;IAChB,aAAa,EAAE,IAAI;IACnB,cAAc,EAAE,IAAI;IACpB,IAAI,EAAE,MAAM;IACZ,SAAS,EAAE,CAAC;IACZ,YAAY,EAAE,EAAE;IAChB,cAAc,EAAE,KAAK;CACtB,CAAC;AAEF,IAAI,CAAC,MAAM,GAAG,UACZ,KAAa,EACb,KAAW,EACX,SAAe,EACf,QAAc,EACd,OAAa;IAEb,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE;QACrB,OAAO,GAAG,QAAQ,CAAC;QACnB,QAAQ,GAAG,SAAS,CAAC;QACrB,SAAS,GAAG,KAAK,CAAC;QAClB,KAAK,GAAG,EAAE,CAAC;KACZ;IAED,IAAI,WAAW,CAAC,SAAS,CAAC,EAAE;;QAE1B,SAAS,GAAG,SAAc,CAAC;KAC5B;IAED,IAAI,WAAW,CAAC,QAAQ,CAAC,EAAE;;QAEzB,QAAQ,GAAG,SAAc,CAAC;KAC3B;IAED,IAAI,WAAW,CAAC,OAAO,CAAC,EAAE;QACxB,OAAO,GAAG,EAAE,CAAC;KACd;IAED,OAAO,GAAG,MAAM,CAAC,EAAE,EAAEA,iBAAe,EAAE,OAAO,CAAC,CAAC;IAE/C,MAAM,OAAO,GACX,8BAA8B;SAC7B,KAAK,GAAG,uCAAuC,KAAK,UAAU,GAAG,EAAE,CAAC;SACpE,OAAO,CAAC,IAAI,KAAK,MAAM;cACpB,0DACE,OAAO,CAAC,YACV,KACE,OAAO,CAAC,SAAS,GAAG,aAAa,GAAG,OAAO,CAAC,SAAS,GAAG,GAAG,GAAG,EAChE,IAAI;cACJ,EAAE,CAAC;SACN,OAAO,CAAC,IAAI,KAAK,UAAU;cACxB,0CACE,OAAO,CAAC,SAAS,GAAG,aAAa,GAAG,OAAO,CAAC,SAAS,GAAG,GAAG,GAAG,EAChE,IAAI,OAAO,CAAC,YAAY,aAAa;cACrC,EAAE,CAAC;QACP,QAAQ,CAAC;IAEX,MAAM,aAAa,GAAG,CAAC,MAAc;QACnC,MAAM,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,GAAG,EAAE,CAAC;QAClE,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;KACzB,CAAC;IAEF,MAAM,cAAc,GAAG,CAAC,MAAc;QACpC,MAAM,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,GAAG,EAAE,CAAC;QAClE,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;KAC1B,CAAC;IAEF,OAAO,IAAI,CAAC,MAAM,CAAC;QACjB,KAAK;QACL,OAAO;QACP,OAAO,EAAE;YACP;gBACE,IAAI,EAAE,OAAO,CAAC,UAAU;gBACxB,IAAI,EAAE,KAAK;gBACX,KAAK,EAAE,OAAO,CAAC,aAAa;gBAC5B,OAAO,EAAE,aAAa;aACvB;YACD;gBACE,IAAI,EAAE,OAAO,CAAC,WAAW;gBACzB,IAAI,EAAE,KAAK;gBACX,KAAK,EAAE,OAAO,CAAC,cAAc;gBAC7B,OAAO,EAAE,cAAc;aACxB;SACF;QACD,QAAQ,EAAE,oBAAoB;QAC9B,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,KAAK,EAAE,OAAO,CAAC,KAAK;QACpB,UAAU,EAAE,OAAO,CAAC,UAAU;QAC9B,MAAM,EAAE,CAAC,MAAM;;YAEb,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YAC7D,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;;YAG9B,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;;YAGlB,IAAI,OAAO,CAAC,IAAI,KAAK,UAAU,IAAI,OAAO,CAAC,cAAc,KAAK,IAAI,EAAE;gBAClE,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,KAAK;oBACzB,IAAK,KAAuB,CAAC,OAAO,KAAK,EAAE,EAAE;wBAC3C,MAAM,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,GAAG,EAAE,CAAC;wBAClE,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;wBAEzB,IAAI,OAAO,CAAC,cAAc,EAAE;4BAC1B,MAAM,CAAC,KAAK,EAAE,CAAC;yBAChB;wBAED,OAAO,KAAK,CAAC;qBACd;oBAED,OAAO;iBACR,CAAC,CAAC;aACJ;;YAGD,IAAI,OAAO,CAAC,IAAI,KAAK,UAAU,EAAE;gBAC/B,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;aACjD;;YAGD,IAAI,OAAO,CAAC,SAAS,EAAE;gBACrB,MAAM,CAAC,YAAY,EAAE,CAAC;aACvB;SACF;KACF,CAAC,CAAC;AACL,CAAC;;ACjKD,MAAMA,iBAAe,GAAY;IAC/B,QAAQ,EAAE,MAAM;IAChB,KAAK,EAAE,CAAC;IACR,OAAO,EAAE,EAAE;CACZ,CAAC;AAEF,MAAM,OAAO;IA0BX,YACE,QAAyD,EACzD,UAAmB,EAAE;;;;QAdhB,YAAO,GAAY,MAAM,CAAC,EAAE,EAAEA,iBAAe,CAAC,CAAC;;;;QAK9C,UAAK,GAAU,QAAQ,CAAC;;;;QAKxB,cAAS,GAAQ,IAAI,CAAC;QAM5B,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAC;QAEnC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;;QAG9B,IAAI,CAAC,QAAQ,GAAG,CAAC,CACf,iCAAiC,CAAC,CAAC,IAAI,EAAE,KACvC,IAAI,CAAC,OAAO,CAAC,OACf,QAAQ,CACT,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;;;QAI1B,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,OAAO;aACT,EAAE,CAAC,uBAAuB,EAAE,UAAU,KAAK;YAC1C,IAAI,IAAI,CAAC,UAAU,CAAC,IAAmB,CAAC,EAAE;gBACxC,OAAO;aACR;YAED,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACnB,OAAO;aACR;YAED,QAAQ,CAAC,KAAK,CAAC,CAAC;YAEhB,IAAI,CAAC,IAAI,EAAE,CAAC;SACb,CAAC;aACD,EAAE,CAAC,qBAAqB,EAAE,UAAU,KAAK;YACxC,IAAI,IAAI,CAAC,UAAU,CAAC,IAAmB,CAAC,EAAE;gBACxC,OAAO;aACR;YAED,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACnB,OAAO;aACR;YAED,IAAI,CAAC,KAAK,EAAE,CAAC;SACd,CAAC;aACD,EAAE,CAAC,WAAW,EAAE,UAAU,KAAK;YAC9B,IAAI,IAAI,CAAC,UAAU,CAAC,IAAmB,CAAC,EAAE;gBACxC,OAAO;aACR;YAED,QAAQ,CAAC,KAAK,CAAC,CAAC;SACjB,CAAC,CAAC;KACN;;;;;IAMO,UAAU,CAAC,OAAoB;QACrC,QACG,OAA4B,CAAC,QAAQ;YACtC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,SAAS,EACzC;KACH;;;;IAKO,SAAS;QACf,OAAO,OAAO,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC;KAC/B;;;;IAKO,WAAW;QACjB,IAAI,UAAkB,CAAC;QACvB,IAAI,SAAiB,CAAC;;QAGtB,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC;;QAG5D,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;;QAGhD,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;QAClD,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;;QAGpD,IAAI,QAAQ,GAAa,IAAI,CAAC,OAAO,CAAC,QAAS,CAAC;;QAGhD,IAAI,QAAQ,KAAK,MAAM,EAAE;YACvB,IACE,WAAW,CAAC,GAAG;gBACb,WAAW,CAAC,MAAM;gBAClB,YAAY;gBACZ,aAAa;gBACb,CAAC;gBACH,OAAO,CAAC,MAAM,EAAE,EAChB;gBACA,QAAQ,GAAG,QAAQ,CAAC;aACrB;iBAAM,IAAI,YAAY,GAAG,aAAa,GAAG,CAAC,GAAG,WAAW,CAAC,GAAG,EAAE;gBAC7D,QAAQ,GAAG,KAAK,CAAC;aAClB;iBAAM,IAAI,YAAY,GAAG,YAAY,GAAG,CAAC,GAAG,WAAW,CAAC,IAAI,EAAE;gBAC7D,QAAQ,GAAG,MAAM,CAAC;aACnB;iBAAM,IACL,WAAW,CAAC,KAAK,GAAG,YAAY,GAAG,YAAY,GAAG,CAAC;gBACnD,OAAO,CAAC,KAAK,EAAE,GAAG,WAAW,CAAC,IAAI,EAClC;gBACA,QAAQ,GAAG,OAAO,CAAC;aACpB;iBAAM;gBACL,QAAQ,GAAG,QAAQ,CAAC;aACrB;SACF;;QAGD,QAAQ,QAAQ;YACd,KAAK,QAAQ;gBACX,UAAU,GAAG,CAAC,CAAC,IAAI,YAAY,GAAG,CAAC,CAAC,CAAC;gBACrC,SAAS,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC,GAAG,YAAY,CAAC;gBAClD,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;gBAC5C,MAAM;YAER,KAAK,KAAK;gBACR,UAAU,GAAG,CAAC,CAAC,IAAI,YAAY,GAAG,CAAC,CAAC,CAAC;gBACrC,SAAS;oBACP,CAAC,CAAC,IAAI,aAAa,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC;gBAC/D,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;gBAC/C,MAAM;YAER,KAAK,MAAM;gBACT,UAAU,GAAG,CAAC,CAAC,IAAI,YAAY,GAAG,WAAW,CAAC,KAAK,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC;gBACxE,SAAS,GAAG,CAAC,CAAC,IAAI,aAAa,GAAG,CAAC,CAAC,CAAC;gBACrC,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;gBAC9C,MAAM;YAER,KAAK,OAAO;gBACV,UAAU,GAAG,WAAW,CAAC,KAAK,GAAG,CAAC,GAAG,YAAY,CAAC;gBAClD,SAAS,GAAG,CAAC,CAAC,IAAI,aAAa,GAAG,CAAC,CAAC,CAAC;gBACrC,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;gBAC7C,MAAM;SACT;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;QAE3C,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;YAChB,GAAG,EAAE,GAAG,YAAY,CAAC,GAAG,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC,IAAI;YACrD,IAAI,EAAE,GAAG,YAAY,CAAC,IAAI,GAAG,WAAW,CAAC,KAAK,GAAG,CAAC,IAAI;YACtD,aAAa,EAAE,GAAG,UAAU,IAAI;YAChC,YAAY,EAAE,GAAG,SAAS,IAAI;SAC/B,CAAC,CAAC;KACJ;;;;;IAMO,YAAY,CAAC,IAAW;QAC9B,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;KACrD;;;;IAKO,aAAa;QACnB,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;YAC/C,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;YACtB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;SAC7B;aAAM;YACL,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;YACtB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;SAC7B;KACF;;;;IAKO,MAAM;QACZ,OAAO,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC;KAC5D;;;;IAKO,MAAM;QACZ,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;QACvB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAE1B,IAAI,CAAC,QAAQ;aACV,QAAQ,CAAC,mBAAmB,CAAC;aAC7B,aAAa,CAAC,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;KAC9C;;;;;IAMM,IAAI,CAAC,OAAiB;QAC3B,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO;SACR;QAED,MAAM,UAAU,GAAG,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAE5C,IAAI,OAAO,EAAE;YACX,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;SAC/B;;QAGD,IAAI,UAAU,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YAC/C,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;SAC1C;QAED,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnB,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;YACtB,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACtE;aAAM;YACL,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,MAAM,EAAE,CAAC;SACf;KACF;;;;IAKM,KAAK;QACV,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC7B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;SACvB;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE;YAClB,OAAO;SACR;QAED,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;QACvB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAE3B,IAAI,CAAC,QAAQ;aACV,WAAW,CAAC,mBAAmB,CAAC;aAChC,aAAa,CAAC,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;KAC9C;;;;IAKM,MAAM;QACX,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;KAC5C;;;;IAKM,QAAQ;QACb,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;CACF;AAED,IAAI,CAAC,OAAO,GAAG,OAAO;;AChWtB,MAAMC,YAAU,GAAG,cAAc,CAAC;AAClC,MAAMC,UAAQ,GAAG,eAAe,CAAC;AAEjC,CAAC,CAAC;;IAEA,SAAS,CAAC,EAAE,CAAC,sBAAsB,EAAE,IAAID,YAAU,GAAG,EAAE;QACtD,MAAM,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;QACxB,IAAI,QAAQ,GAAG,OAAO,CAAC,IAAI,CAACC,UAAQ,CAAC,CAAC;QAEtC,IAAI,CAAC,QAAQ,EAAE;YACb,QAAQ,GAAG,IAAI,IAAI,CAAC,OAAO,CACzB,IAAmB,EACnB,YAAY,CAAC,IAAmB,EAAED,YAAU,CAAC,CAC9C,CAAC;YACF,OAAO,CAAC,IAAI,CAACC,UAAQ,EAAE,QAAQ,CAAC,CAAC;SAClC;KACF,CAAC,CAAC;AACL,CAAC,CAAC;;AC8FF,MAAMF,iBAAe,GAAY;IAC/B,OAAO,EAAE,EAAE;IACX,OAAO,EAAE,IAAI;IACb,QAAQ,EAAE,QAAQ;IAClB,UAAU,EAAE,EAAE;IACd,WAAW,EAAE,EAAE;IACf,kBAAkB,EAAE,IAAI;IACxB,mBAAmB,EAAE,IAAI;;IAEzB,OAAO,EAAE,SAAQ;;IAEjB,aAAa,EAAE,SAAQ;;IAEvB,MAAM,EAAE,SAAQ;;IAEhB,QAAQ,EAAE,SAAQ;;IAElB,OAAO,EAAE,SAAQ;;IAEjB,QAAQ,EAAE,SAAQ;CACnB,CAAC;AAEF;;;AAGA,IAAIG,aAAW,GAAoB,IAAI,CAAC;AAExC;;;AAGA,MAAMC,WAAS,GAAG,gBAAgB,CAAC;AAEnC,MAAM,QAAQ;IAoBZ,YAAmB,OAAgB;;;;QAZ5B,YAAO,GAAY,MAAM,CAAC,EAAE,EAAEJ,iBAAe,CAAC,CAAC;;;;QAK9C,UAAK,GAAU,QAAQ,CAAC;;;;QAKxB,cAAS,GAAQ,IAAI,CAAC;QAG5B,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;;QAG9B,IAAI,gBAAgB,GAAG,EAAE,CAAC;QAC1B,IAAI,gBAAgB,GAAG,EAAE,CAAC;QAE1B,IACE,IAAI,CAAC,OAAO,CAAC,WAAY,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC;YAC5C,IAAI,CAAC,OAAO,CAAC,WAAY,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAC9C;YACA,gBAAgB,GAAG,gBAAgB,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,CAAC;SAChE;aAAM,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK,EAAE,EAAE;YAC1C,gBAAgB,GAAG,mBAAmB,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;SAClE;;QAGD,IAAI,CAAC,QAAQ,GAAG,CAAC,CACf,6BAA6B;YAC3B,mCAAmC,IAAI,CAAC,OAAO,CAAC,OAAO,QAAQ;aAC9D,IAAI,CAAC,OAAO,CAAC,UAAU;kBACpB,mGAAmG,gBAAgB,KAAK,gBAAgB,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,MAAM;kBACzK,EAAE,CAAC;YACP,QAAQ,CACX,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;;QAG1B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAE1B,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,iBAAiB,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;KAC3E;;;;;IAMO,mBAAmB,CAAC,KAAY;QACtC,MAAM,OAAO,GAAG,CAAC,CAAC,KAAK,CAAC,MAAqB,CAAC,CAAC;QAE/C,IACE,CAAC,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC;YAClC,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,MAAM,EACzC;YACAG,aAAY,CAAC,KAAK,EAAE,CAAC;SACtB;KACF;;;;;IAMO,WAAW,CAAC,KAAuB;QACzC,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;QACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;QAEvC,IAAI,UAAU,CAAC;QACf,IAAI,UAAU,CAAC;;QAGf,IAAI,QAAQ,KAAK,QAAQ,IAAI,QAAQ,KAAK,KAAK,EAAE;YAC/C,UAAU,GAAG,MAAM,CAAC;SACrB;aAAM;YACL,UAAU,GAAG,GAAG,CAAC;SAClB;;QAGD,IAAI,KAAK,KAAK,MAAM,EAAE;YACpB,UAAU,GAAG,GAAG,CAAC;SAClB;aAAM;YACL,IAAI,QAAQ,KAAK,QAAQ,EAAE;gBACzB,UAAU,GAAG,cAAc,CAAC;aAC7B;YAED,IAAI,QAAQ,KAAK,KAAK,EAAE;gBACtB,UAAU,GAAG,CAAC,cAAc,CAAC;aAC9B;YAED,IAAI,QAAQ,KAAK,UAAU,IAAI,QAAQ,KAAK,WAAW,EAAE;gBACvD,UAAU,GAAG,CAAC,cAAc,GAAG,EAAE,CAAC;aACnC;YAED,IAAI,QAAQ,KAAK,aAAa,IAAI,QAAQ,KAAK,cAAc,EAAE;gBAC7D,UAAU,GAAG,cAAc,GAAG,EAAE,CAAC;aAClC;SACF;QAED,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,aAAa,UAAU,IAAI,UAAU,IAAI,CAAC,CAAC;KACpE;;;;IAKM,IAAI;QACT,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;YACvD,OAAO;SACR;;QAGD,IAAIA,aAAW,EAAE;YACf,KAAK,CAACC,WAAS,EAAE,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YACpC,OAAO;SACR;QAEDD,aAAW,GAAG,IAAI,CAAC;;QAGnB,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;QACvB,IAAI,CAAC,OAAO,CAAC,MAAO,CAAC,IAAI,CAAC,CAAC;QAE3B,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAEzB,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC;YAC1B,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;gBAC5B,OAAO;aACR;YAED,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;YACtB,IAAI,CAAC,OAAO,CAAC,QAAS,CAAC,IAAI,CAAC,CAAC;;YAG7B,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;gBAC3B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE;oBACtD,IAAI,CAAC,OAAO,CAAC,aAAc,CAAC,IAAI,CAAC,CAAC;oBAClC,IAAI,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE;wBACnC,IAAI,CAAC,KAAK,EAAE,CAAC;qBACd;iBACF,CAAC,CAAC;aACJ;;YAGD,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK;gBAC9B,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,MAAqB,CAAC,CAAC,QAAQ,CAAC,sBAAsB,CAAC,EAAE;oBACpE,IAAI,CAAC,OAAO,CAAC,OAAQ,CAAC,IAAI,CAAC,CAAC;iBAC7B;aACF,CAAC,CAAC;;YAGH,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;gBACpC,SAAS,CAAC,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;aACpD;;YAGD,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;gBACxB,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,MAAM,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;aACvE;SACF,CAAC,CAAC;KACJ;;;;IAKM,KAAK;QACV,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;YACvD,OAAO;SACR;QAED,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SAC9B;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;YACpC,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;SACrD;QAED,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;QACvB,IAAI,CAAC,OAAO,CAAC,OAAQ,CAAC,IAAI,CAAC,CAAC;QAE5B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAE1B,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC;YAC1B,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;gBAC5B,OAAO;aACR;YAEDA,aAAW,GAAG,IAAI,CAAC;YACnB,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;YACtB,IAAI,CAAC,OAAO,CAAC,QAAS,CAAC,IAAI,CAAC,CAAC;YAC7B,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;YACvB,OAAO,CAACC,WAAS,CAAC,CAAC;SACpB,CAAC,CAAC;KACJ;CACF;AAED,IAAI,CAAC,QAAQ,GAAG,UAAU,OAAY,EAAE,UAAe,EAAE;IACvD,IAAI,QAAQ,CAAC,OAAO,CAAC,EAAE;QACrB,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;KAC3B;SAAM;QACL,OAAO,GAAG,OAAO,CAAC;KACnB;IAED,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC;IAEvC,QAAQ,CAAC,IAAI,EAAE,CAAC;IAEhB,OAAO,QAAQ,CAAC;AAClB,CAAC;;AChWD,CAAC,CAAC;;IAEA,SAAS,CAAC,EAAE,CAAC,OAAO,EAAE,oBAAoB,EAAE;QAC1C,MAAM,KAAK,GAAG,CAAC,CAAC,IAAmB,CAAC,CAAC;QACrC,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;QAElC,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI;YACxC,MAAM,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;YAE9B,IAAI,MAAM,EAAE;gBACV,cAAc,CAAC,QAAQ,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE;oBAC9D,KAAK;iBACN,CAAC,CAAC;aACJ;YAED,MAAM;kBACF,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,wBAAwB,CAAC;kBAC1C,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,wBAAwB,CAAC,CAAC;SACnD,CAAC,CAAC;KACJ,CAAC,CAAC;;IAGH,IAAI,CAAC,QAAQ,CAAC,8BAA8B,EAAE;QAC5C,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;YACtB,WAAW,EAAE,2BAA2B;YACxC,aAAa,EAAE,6BAA6B;SAC7C,CAAC,CAAC;KACJ,CAAC,CAAC;AACL,CAAC,CAAC;;ACnBF;;;;AAIA,SAAS,SAAS,CAAC,QAAwB,KAAK;IAC9C,QACE,kCACE,KAAK,GAAG,sBAAsB,KAAK,EAAE,GAAG,EAC1C,IAAI;QACJ,6DAA6D;QAC7D,yCAAyC;QACzC,QAAQ;QACR,sCAAsC;QACtC,yCAAyC;QACzC,QAAQ;QACR,8DAA8D;QAC9D,yCAAyC;QACzC,QAAQ;QACR,QAAQ,EACR;AACJ,CAAC;AAED;;;;AAIA,SAAS,QAAQ,CAAC,OAAoB;IACpC,MAAM,QAAQ,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC;IAE5B,MAAM,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAAC,uBAAuB,CAAC;UACpD,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;UACzD,SAAS,EAAE,CAAC;IAEhB,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACvB,CAAC;AAED,CAAC,CAAC;;IAEA,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE;QAC7B,QAAQ,CAAC,IAAI,CAAC,CAAC;KAChB,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,cAAc,GAAG,UACpB,QAA0D;IAE1D,MAAM,SAAS,GAAG,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;IAE3E,SAAS,CAAC,IAAI,CAAC;QACb,QAAQ,CAAC,IAAI,CAAC,CAAC;KAChB,CAAC,CAAC;AACL,CAAC;;ACkCD,MAAMJ,iBAAe,GAAY;IAC/B,QAAQ,EAAE,MAAM;IAChB,KAAK,EAAE,MAAM;IACb,MAAM,EAAE,EAAE;IACV,KAAK,EAAE,KAAK;IACZ,OAAO,EAAE,MAAM;IACf,cAAc,EAAE,OAAO;IACvB,YAAY,EAAE,GAAG;CAClB,CAAC;AAEF,MAAM,IAAI;IA+BR,YACE,cAA+D,EAC/D,YAA6D,EAC7D,UAAmB,EAAE;;;;QApBhB,YAAO,GAAY,MAAM,CAAC,EAAE,EAAEA,iBAAe,CAAC,CAAC;;;;QAK9C,UAAK,GAAU,QAAQ,CAAC;QAiB9B,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,cAAc,CAAC,CAAC,KAAK,EAAE,CAAC;QACzC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,YAAY,CAAC,CAAC,KAAK,EAAE,CAAC;;QAGxC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE;YACrD,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;SACrE;QAED,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;;QAG9B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;;QAG7D,IAAI,CAAC,SAAS;YACZ,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,MAAM,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,OAAQ,CAAC;;QAG5E,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;;QAG9C,SAAS,CAAC,EAAE,CAAC,kBAAkB,EAAE,CAAC,KAAY;YAC5C,MAAM,OAAO,GAAG,CAAC,CAAC,KAAK,CAAC,MAAqB,CAAC,CAAC;YAE/C,IACE,IAAI,CAAC,MAAM,EAAE;gBACb,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC;gBAC1B,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;gBACvC,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC;gBACzB,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EACtC;gBACA,IAAI,CAAC,KAAK,EAAE,CAAC;aACd;SACF,CAAC,CAAC;;;QAIH,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,SAAS,CAAC,EAAE,CAAC,OAAO,EAAE,iBAAiB,EAAE;YACvC,MAAM,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;YAEtB,IACE,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM;gBAChC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,SAAS,EACpC;gBACA,IAAI,CAAC,KAAK,EAAE,CAAC;aACd;SACF,CAAC,CAAC;;QAGH,IAAI,CAAC,gBAAgB,EAAE,CAAC;;QAGxB,OAAO,CAAC,EAAE,CACR,QAAQ,EACR,CAAC,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,EAAE,GAAG,CAAC,CACvC,CAAC;KACH;;;;IAKO,MAAM;QACZ,OAAO,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC;KAC5D;;;;;IAMO,YAAY,CAAC,IAAW;QAC9B,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;KACnD;;;;IAKO,QAAQ;QACd,IAAI,QAAQ,CAAC;QACb,IAAI,OAAO,CAAC;;QAGZ,IAAI,QAAqC,CAAC;QAC1C,IAAI,KAAkC,CAAC;;QAGvC,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;QACtC,MAAM,WAAW,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;;QAGpC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAO,CAAC;QACpC,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QACjC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;;QAGnC,IAAI,gBAAgB,CAAC;QACrB,IAAI,gBAAgB,CAAC;;QAGrB,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QACxC,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;;QAG1C,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC;QAC3D,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC;QACjC,MAAM,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC;QACnC,MAAM,YAAY,GAAG,UAAU,CAAC,MAAM,CAAC;QACvC,MAAM,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC;QACrC,MAAM,YAAY,GAAG,YAAY,GAAG,SAAS,GAAG,YAAY,CAAC;QAC7D,MAAM,WAAW,GAAG,WAAW,GAAG,UAAU,GAAG,WAAW,CAAC;;QAG3D,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAClD,MAAM,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;;QAGpD,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,MAAM,EAAE;YACpC,IAAI,YAAY,IAAI,SAAS,GAAG,YAAY,GAAG,CAAC,CAAC,GAAG,UAAU,GAAG,MAAM,EAAE;;gBAEvE,QAAQ,GAAG,QAAQ,CAAC;aACrB;iBAAM,IACL,SAAS,IAAI,SAAS,GAAG,YAAY,GAAG,CAAC,CAAC;gBAC1C,UAAU,GAAG,MAAM,EACnB;;gBAEA,QAAQ,GAAG,KAAK,CAAC;aAClB;iBAAM;;gBAEL,QAAQ,GAAG,QAAQ,CAAC;aACrB;SACF;aAAM;YACL,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAS,CAAC;SACnC;;QAGD,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,MAAM,EAAE;YACjC,IAAI,WAAW,GAAG,WAAW,GAAG,SAAS,GAAG,MAAM,EAAE;;gBAElD,KAAK,GAAG,MAAM,CAAC;aAChB;iBAAM,IAAI,UAAU,GAAG,WAAW,GAAG,SAAS,GAAG,MAAM,EAAE;;gBAExD,KAAK,GAAG,OAAO,CAAC;aACjB;iBAAM;;gBAEL,KAAK,GAAG,QAAQ,CAAC;aAClB;SACF;aAAM;YACL,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAM,CAAC;SAC7B;;QAGD,IAAI,QAAQ,KAAK,QAAQ,EAAE;YACzB,gBAAgB,GAAG,GAAG,CAAC;YACvB,OAAO;gBACL,CAAC,SAAS,GAAG,CAAC,GAAG,YAAY;qBAC5B,OAAO,GAAG,SAAS,GAAG,eAAe,CAAC,CAAC;SAC3C;aAAM,IAAI,QAAQ,KAAK,KAAK,EAAE;YAC7B,gBAAgB,GAAG,MAAM,CAAC;YAC1B,OAAO;gBACL,CAAC,SAAS,GAAG,YAAY,GAAG,CAAC;qBAC5B,OAAO,GAAG,SAAS,GAAG,UAAU,GAAG,eAAe,GAAG,UAAU,CAAC,CAAC;SACrE;aAAM;YACL,gBAAgB,GAAG,KAAK,CAAC;;;;YAKzB,IAAI,cAAc,GAAG,UAAU,CAAC;;YAGhC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACnB,IAAI,UAAU,GAAG,MAAM,GAAG,CAAC,GAAG,YAAY,EAAE;oBAC1C,cAAc,GAAG,YAAY,GAAG,MAAM,GAAG,CAAC,CAAC;oBAC3C,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;iBACtC;aACF;YAED,OAAO;gBACL,CAAC,YAAY,GAAG,cAAc,IAAI,CAAC;qBAClC,OAAO,GAAG,CAAC,GAAG,eAAe,GAAG,SAAS,CAAC,CAAC;SAC/C;QAED,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,OAAO,IAAI,CAAC,CAAC;;QAGzC,IAAI,KAAK,KAAK,MAAM,EAAE;YACpB,gBAAgB,GAAG,GAAG,CAAC;YACvB,QAAQ,GAAG,OAAO,GAAG,UAAU,GAAG,gBAAgB,CAAC;SACpD;aAAM,IAAI,KAAK,KAAK,OAAO,EAAE;YAC5B,gBAAgB,GAAG,MAAM,CAAC;YAC1B,QAAQ,GAAG,OAAO;kBACd,UAAU,GAAG,WAAW,GAAG,SAAS;kBACpC,gBAAgB,GAAG,WAAW,GAAG,SAAS,CAAC;SAChD;aAAM;YACL,gBAAgB,GAAG,KAAK,CAAC;;;YAIzB,IAAI,aAAa,GAAG,SAAS,CAAC;;YAG9B,IAAI,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,WAAW,EAAE;gBACxC,aAAa,GAAG,WAAW,GAAG,MAAM,GAAG,CAAC,CAAC;gBACzC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;aACpC;YAED,QAAQ;gBACN,CAAC,WAAW,GAAG,aAAa,IAAI,CAAC;qBAChC,OAAO,GAAG,CAAC,GAAG,gBAAgB,GAAG,UAAU,CAAC,CAAC;SACjD;QAED,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,QAAQ,IAAI,CAAC,CAAC;;QAG3C,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,gBAAgB,IAAI,gBAAgB,EAAE,CAAC,CAAC;KAC1E;;;;;IAMO,eAAe,CAAC,QAAY;QAClC,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;QAEjD,IAAI,UAAU,CAAC;QACf,IAAI,WAAW,CAAC;;QAGhB,IAAI,QAA0B,CAAC;QAC/B,IAAI,KAAuB,CAAC;;QAG5B,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;QACtC,MAAM,WAAW,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;;QAGpC,IAAI,gBAAgB,CAAC;QACrB,IAAI,gBAAgB,CAAC;;QAGrB,MAAM,YAAY,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC;QACtC,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;;QAGxC,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC;QAClD,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC;QACjC,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC;QACnC,MAAM,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC;QAC/B,MAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC;;QAG7B,IAAI,YAAY,GAAG,OAAO,GAAG,aAAa,EAAE;;YAE1C,QAAQ,GAAG,QAAQ,CAAC;SACrB;aAAM,IAAI,OAAO,GAAG,UAAU,GAAG,aAAa,EAAE;;YAE/C,QAAQ,GAAG,KAAK,CAAC;SAClB;aAAM;;YAEL,QAAQ,GAAG,QAAQ,CAAC;SACrB;;QAGD,IAAI,WAAW,GAAG,QAAQ,GAAG,SAAS,GAAG,YAAY,EAAE;;YAErD,KAAK,GAAG,MAAM,CAAC;SAChB;aAAM,IAAI,QAAQ,GAAG,YAAY,EAAE;;YAElC,KAAK,GAAG,OAAO,CAAC;SACjB;aAAM;;YAEL,KAAK,GAAG,MAAM,CAAC;SAChB;;QAGD,IAAI,QAAQ,KAAK,QAAQ,EAAE;YACzB,gBAAgB,GAAG,GAAG,CAAC;YACvB,UAAU,GAAG,GAAG,CAAC;SAClB;aAAM,IAAI,QAAQ,KAAK,KAAK,EAAE;YAC7B,gBAAgB,GAAG,MAAM,CAAC;YAC1B,UAAU,GAAG,CAAC,aAAa,GAAG,UAAU,CAAC;SAC1C;QAED,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,UAAU,IAAI,CAAC,CAAC;;QAGvC,IAAI,KAAK,KAAK,MAAM,EAAE;YACpB,gBAAgB,GAAG,GAAG,CAAC;YACvB,WAAW,GAAG,SAAS,CAAC;SACzB;aAAM,IAAI,KAAK,KAAK,OAAO,EAAE;YAC5B,gBAAgB,GAAG,MAAM,CAAC;YAC1B,WAAW,GAAG,CAAC,YAAY,CAAC;SAC7B;QAED,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,WAAW,IAAI,CAAC,CAAC;;QAGzC,QAAQ,CAAC,eAAe,CAAC,GAAG,gBAAgB,IAAI,gBAAgB,EAAE,CAAC,CAAC;KACrE;;;;;IAMO,WAAW,CAAC,QAAY;QAC9B,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QAE/B,QAAQ;aACL,QAAQ,CAAC,gBAAgB,CAAC;aAC1B,MAAM,CAAC,iBAAiB,CAAC;aACzB,QAAQ,CAAC,uBAAuB,CAAC,CAAC;KACtC;;;;;IAMO,YAAY,CAAC,QAAY;;QAE/B,QAAQ;aACL,WAAW,CAAC,gBAAgB,CAAC;aAC7B,QAAQ,CAAC,mBAAmB,CAAC;aAC7B,aAAa,CAAC,MAAM,QAAQ,CAAC,WAAW,CAAC,mBAAmB,CAAC,CAAC;;aAG9D,MAAM,CAAC,iBAAiB,CAAC;aACzB,WAAW,CAAC,uBAAuB,CAAC,CAAC;;QAGxC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI;YACvC,MAAM,WAAW,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;YAE5B,WAAW;iBACR,WAAW,CAAC,gBAAgB,CAAC;iBAC7B,QAAQ,CAAC,mBAAmB,CAAC;iBAC7B,aAAa,CAAC,MAAM,WAAW,CAAC,WAAW,CAAC,mBAAmB,CAAC,CAAC;iBACjE,MAAM,CAAC,iBAAiB,CAAC;iBACzB,WAAW,CAAC,uBAAuB,CAAC,CAAC;SACzC,CAAC,CAAC;KACJ;;;;;IAMO,aAAa,CAAC,QAAY;QAChC,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,CAAC;cAC/B,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;cAC3B,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;KAChC;;;;IAKO,gBAAgB;;QAEtB,MAAM,IAAI,GAAG,IAAI,CAAC;;QAGlB,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,iBAAiB,EAAE,UAAU,KAAK;YAC1D,MAAM,KAAK,GAAG,CAAC,CAAC,IAAmB,CAAC,CAAC;YACrC,MAAM,OAAO,GAAG,CAAC,CAAC,KAAK,CAAC,MAAqB,CAAC,CAAC;;YAG/C,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,SAAS,EAAE;gBACxC,OAAO;aACR;;YAGD,IAAI,OAAO,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,OAAO,CAAC,EAAE,CAAC,eAAe,CAAC,EAAE;gBAC3D,OAAO;aACR;;YAGD,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE;gBACzD,OAAO;aACR;;YAGD,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;;YAG9C,KAAK;iBACF,MAAM,CAAC,YAAY,CAAC;iBACpB,QAAQ,CAAC,iBAAiB,CAAC;iBAC3B,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI;gBACZ,MAAM,WAAW,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;gBAEnD,IACE,WAAW,CAAC,MAAM;qBACjB,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,EAC/C;oBACA,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;iBAChC;aACF,CAAC,CAAC;;YAGL,IAAI,QAAQ,CAAC,MAAM,EAAE;gBACnB,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;aAC9B;SACF,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,KAAK,OAAO,EAAE;;YAE3C,IAAI,OAAO,GAAQ,IAAI,CAAC;YACxB,IAAI,WAAW,GAAQ,IAAI,CAAC;YAE5B,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,oBAAoB,EAAE,iBAAiB,EAAE,UACxD,KAAK;gBAEL,MAAM,KAAK,GAAG,CAAC,CAAC,IAAmB,CAAC,CAAC;gBACrC,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC;gBAC7B,MAAM,cAAc,GAAG,CAAC,CACrB,KAAoB,CAAC,aAA4B,CACnD,CAAC;;gBAGF,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,SAAS,EAAE;oBACxC,OAAO;iBACR;;gBAGD,IAAI,SAAS,KAAK,WAAW,EAAE;oBAC7B,IACE,CAAC,KAAK,CAAC,EAAE,CAAC,cAAc,CAAC;wBACzB,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,EACrC;wBACA,OAAO;qBACR;iBACF;;qBAGI,IAAI,SAAS,KAAK,UAAU,EAAE;oBACjC,IACE,KAAK,CAAC,EAAE,CAAC,cAAc,CAAC;wBACxB,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,EACrC;wBACA,OAAO;qBACR;iBACF;;gBAGD,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;;gBAG9C,IAAI,SAAS,KAAK,WAAW,EAAE;oBAC7B,IAAI,QAAQ,CAAC,MAAM,EAAE;;wBAEnB,MAAM,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;wBACzD,IAAI,QAAQ,EAAE;4BACZ,YAAY,CAAC,QAAQ,CAAC,CAAC;yBACxB;;wBAGD,IAAI,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;4BACvC,OAAO;yBACR;;wBAGD,YAAY,CAAC,WAAW,CAAC,CAAC;;wBAG1B,OAAO,GAAG,WAAW,GAAG,UAAU,CAChC,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,EAChC,IAAI,CAAC,OAAO,CAAC,YAAY,CAC1B,CAAC;wBAEF,QAAQ,CAAC,IAAI,CAAC,uBAAuB,EAAE,OAAO,CAAC,CAAC;qBACjD;iBACF;;qBAGI,IAAI,SAAS,KAAK,UAAU,EAAE;oBACjC,IAAI,QAAQ,CAAC,MAAM,EAAE;;wBAEnB,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;wBACvD,IAAI,OAAO,EAAE;4BACX,YAAY,CAAC,OAAO,CAAC,CAAC;yBACvB;;wBAGD,OAAO,GAAG,UAAU,CAClB,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EACjC,IAAI,CAAC,OAAO,CAAC,YAAY,CAC1B,CAAC;wBAEF,QAAQ,CAAC,IAAI,CAAC,wBAAwB,EAAE,OAAO,CAAC,CAAC;qBAClD;iBACF;aACF,CAAC,CAAC;SACJ;KACF;;;;IAKO,aAAa;QACnB,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,mBAAmB,CAAC,CAAC;QAE/C,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;YAC5B,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;YACtB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;SAC7B;QAED,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;YAC5B,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;YACtB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;;YAG5B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;gBAChB,GAAG,EAAE,EAAE;gBACP,IAAI,EAAE,EAAE;gBACR,KAAK,EAAE,EAAE;gBACT,QAAQ,EAAE,OAAO;aAClB,CAAC,CAAC;SACJ;KACF;;;;IAKM,MAAM;QACX,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;KAC5C;;;;IAKM,IAAI;QACT,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO;SACR;QAED,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;QACvB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAE1B,IAAI,CAAC,QAAQ,EAAE,CAAC;QAEhB,IAAI,CAAC,QAAQ;;aAEV,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,OAAO,GAAG,UAAU,CAAC;aAC1D,QAAQ,CAAC,gBAAgB,CAAC;aAC1B,aAAa,CAAC,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;KAC9C;;;;IAKM,KAAK;QACV,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE;YAClB,OAAO;SACR;QAED,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;QACvB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;;QAG3B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO;YAC/C,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;SAC/B,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ;aACV,WAAW,CAAC,gBAAgB,CAAC;aAC7B,QAAQ,CAAC,mBAAmB,CAAC;aAC7B,aAAa,CAAC,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;KAC9C;CACF;AAED,IAAI,CAAC,IAAI,GAAG,IAAI;;AC1sBhB,MAAMC,YAAU,GAAG,WAAW,CAAC;AAC/B,MAAMC,UAAQ,GAAG,YAAY,CAAC;AAa9B,CAAC,CAAC;IACA,SAAS,CAAC,EAAE,CAAC,OAAO,EAAE,IAAID,YAAU,GAAG,EAAE;QACvC,MAAM,KAAK,GAAG,CAAC,CAAC,IAAmB,CAAC,CAAC;QACrC,IAAI,QAAQ,GAAG,KAAK,CAAC,IAAI,CAACC,UAAQ,CAAC,CAAC;QAEpC,IAAI,CAAC,QAAQ,EAAE;YACb,MAAM,OAAO,GAAG,YAAY,CAAC,IAAmB,EAAED,YAAU,CAAY,CAAC;YACzE,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC;;YAEpC,OAAO,OAAO,CAAC,MAAM,CAAC;YAEtB,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;YACvD,KAAK,CAAC,IAAI,CAACC,UAAQ,EAAE,QAAQ,CAAC,CAAC;YAE/B,QAAQ,CAAC,MAAM,EAAE,CAAC;SACnB;KACF,CAAC,CAAC;AACL,CAAC,CAAC;;;;"}