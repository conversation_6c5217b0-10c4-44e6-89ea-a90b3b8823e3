{"version": 3, "file": "mdui.min.js", "sources": ["../../node_modules/mdn-polyfills/CustomEvent.js", "../../node_modules/promise-polyfill/src/finally.js", "../../node_modules/promise-polyfill/src/allSettled.js", "../../node_modules/mdn-polyfills/MouseEvent.js", "../../node_modules/promise-polyfill/src/index.js", "../../node_modules/promise-polyfill/src/polyfill.js", "../../node_modules/mdui.jq/es/utils.js", "../../node_modules/mdui.jq/es/functions/each.js", "../../node_modules/mdui.jq/es/JQ.js", "../../node_modules/mdui.jq/es/$.js", "../../src/mdui.ts", "../../node_modules/mdui.jq/es/functions/contains.js", "../../node_modules/mdui.jq/es/functions/merge.js", "../../node_modules/mdui.jq/es/methods/each.js", "../../node_modules/mdui.jq/es/methods/get.js", "../../node_modules/mdui.jq/es/methods/find.js", "../../node_modules/mdui.jq/es/methods/utils/event.js", "../../node_modules/mdui.jq/es/functions/extend.js", "../../node_modules/mdui.jq/es/functions/param.js", "../../node_modules/mdui.jq/es/methods/trigger.js", "../../node_modules/mdui.jq/es/functions/utils/ajax.js", "../../node_modules/mdui.jq/es/functions/ajax.js", "../../node_modules/mdui.jq/es/static/ajax.js", "../../node_modules/mdui.jq/es/static/ajaxSetup.js", "../../node_modules/mdui.jq/es/functions/ajaxSetup.js", "../../node_modules/mdui.jq/es/static/contains.js", "../../node_modules/mdui.jq/es/functions/utils/data.js", "../../node_modules/mdui.jq/es/functions/data.js", "../../node_modules/mdui.jq/es/functions/map.js", "../../node_modules/mdui.jq/es/functions/removeData.js", "../../node_modules/mdui.jq/es/functions/unique.js", "../../node_modules/mdui.jq/es/methods/utils/dir.js", "../../node_modules/mdui.jq/es/static/data.js", "../../node_modules/mdui.jq/es/static/each.js", "../../node_modules/mdui.jq/es/static/extend.js", "../../node_modules/mdui.jq/es/static/map.js", "../../node_modules/mdui.jq/es/static/merge.js", "../../node_modules/mdui.jq/es/static/param.js", "../../node_modules/mdui.jq/es/static/removeData.js", "../../node_modules/mdui.jq/es/static/unique.js", "../../node_modules/mdui.jq/es/methods/add.js", "../../node_modules/mdui.jq/es/methods/addClass.js", "../../node_modules/mdui.jq/es/methods/insertBefore.js", "../../node_modules/mdui.jq/es/methods/before.js", "../../node_modules/mdui.jq/es/methods/off.js", "../../node_modules/mdui.jq/es/methods/on.js", "../../node_modules/mdui.jq/es/methods/ajaxStart.js", "../../node_modules/mdui.jq/es/methods/map.js", "../../node_modules/mdui.jq/es/methods/clone.js", "../../node_modules/mdui.jq/es/methods/is.js", "../../node_modules/mdui.jq/es/methods/remove.js", "../../node_modules/mdui.jq/es/methods/append.js", "../../node_modules/mdui.jq/es/methods/appendTo.js", "../../node_modules/mdui.jq/es/methods/attr.js", "../../node_modules/mdui.jq/es/methods/children.js", "../../node_modules/mdui.jq/es/methods/slice.js", "../../node_modules/mdui.jq/es/methods/eq.js", "../../node_modules/mdui.jq/es/methods/parent.js", "../../node_modules/mdui.jq/es/methods/closest.js", "../../node_modules/mdui.jq/es/methods/data.js", "../../node_modules/mdui.jq/es/methods/width.js", "../../node_modules/mdui.jq/es/methods/position.js", "../../node_modules/mdui.jq/es/methods/offset.js", "../../node_modules/mdui.jq/es/methods/empty.js", "../../node_modules/mdui.jq/es/methods/extend.js", "../../node_modules/mdui.jq/es/methods/filter.js", "../../node_modules/mdui.jq/es/methods/first.js", "../../node_modules/mdui.jq/es/methods/has.js", "../../node_modules/mdui.jq/es/methods/hasClass.js", "../../node_modules/mdui.jq/es/methods/hide.js", "../../node_modules/mdui.jq/es/methods/val.js", "../../node_modules/mdui.jq/es/methods/index.js", "../../node_modules/mdui.jq/es/methods/last.js", "../../node_modules/mdui.jq/es/methods/next.js", "../../node_modules/mdui.jq/es/methods/not.js", "../../node_modules/mdui.jq/es/methods/offsetParent.js", "../../node_modules/mdui.jq/es/methods/one.js", "../../node_modules/mdui.jq/es/methods/prev.js", "../../node_modules/mdui.jq/es/methods/removeAttr.js", "../../node_modules/mdui.jq/es/methods/removeData.js", "../../node_modules/mdui.jq/es/methods/removeProp.js", "../../node_modules/mdui.jq/es/methods/replaceWith.js", "../../node_modules/mdui.jq/es/methods/replaceAll.js", "../../node_modules/mdui.jq/es/methods/serializeArray.js", "../../node_modules/mdui.jq/es/methods/serialize.js", "../../node_modules/mdui.jq/es/methods/show.js", "../../node_modules/mdui.jq/es/methods/siblings.js", "../../node_modules/mdui.jq/es/methods/toggle.js", "../../src/jq_extends/methods/reflow.ts", "../../src/jq_extends/methods/transition.ts", "../../src/jq_extends/methods/transitionEnd.ts", "../../src/jq_extends/methods/transformOrigin.ts", "../../src/jq_extends/methods/transform.ts", "../../src/utils/mutation.ts", "../../src/jq_extends/methods/mutation.ts", "../../src/jq_extends/static/showOverlay.ts", "../../src/jq_extends/static/hideOverlay.ts", "../../src/jq_extends/static/lockScreen.ts", "../../src/jq_extends/static/unlockScreen.ts", "../../src/jq_extends/static/throttle.ts", "../../src/jq_extends/static/guid.ts", "../../src/utils/componentEvent.ts", "../../src/global/mutation.ts", "../../src/components/headroom/index.ts", "../../src/utils/dom.ts", "../../src/utils/parseOptions.ts", "../../src/components/headroom/customAttr.ts", "../../src/components/collapse/collapseAbstract.ts", "../../src/components/collapse/index.ts", "../../src/components/collapse/customAttr.ts", "../../src/components/panel/index.ts", "../../src/components/panel/customAttr.ts", "../../src/components/table/index.ts", "../../src/utils/touchHandler.ts", "../../src/components/ripple/index.ts", "../../src/components/textfield/index.ts", "../../src/components/slider/index.ts", "../../src/components/fab/index.ts", "../../src/components/fab/customAttr.ts", "../../src/components/select/index.ts", "../../src/components/select/customAttr.ts", "../../src/components/appbar/index.ts", "../../src/components/tab/index.ts", "../../src/components/tab/customAttr.ts", "../../src/components/drawer/index.ts", "../../src/components/drawer/customAttr.ts", "../../src/utils/queue.ts", "../../src/components/dialog/class.ts", "../../src/components/dialog/index.ts", "../../src/components/dialog/customAttr.ts", "../../src/components/dialog/dialog.ts", "../../src/components/dialog/alert.ts", "../../src/components/dialog/confirm.ts", "../../src/components/dialog/prompt.ts", "../../src/components/tooltip/index.ts", "../../src/components/tooltip/customAttr.ts", "../../src/components/snackbar/index.ts", "../../src/components/progress/spinner.ts", "../../src/components/bottom_nav/index.ts", "../../src/components/menu/index.ts", "../../src/components/menu/customAttr.ts"], "sourcesContent": ["!function(){function t(t,e){e=e||{bubbles:!1,cancelable:!1,detail:void 0};var n=document.createEvent(\"CustomEvent\");return n.initCustomEvent(t,e.bubbles,e.cancelable,e.detail),n}\"function\"!=typeof window.CustomEvent&&(t.prototype=window.Event.prototype,window.CustomEvent=t)}();\n", "/**\n * @this {Promise}\n */\nfunction finallyConstructor(callback) {\n  var constructor = this.constructor;\n  return this.then(\n    function(value) {\n      // @ts-ignore\n      return constructor.resolve(callback()).then(function() {\n        return value;\n      });\n    },\n    function(reason) {\n      // @ts-ignore\n      return constructor.resolve(callback()).then(function() {\n        // @ts-ignore\n        return constructor.reject(reason);\n      });\n    }\n  );\n}\n\nexport default finallyConstructor;\n", "function allSettled(arr) {\n  var P = this;\n  return new P(function(resolve, reject) {\n    if (!(arr && typeof arr.length !== 'undefined')) {\n      return reject(\n        new TypeError(\n          typeof arr +\n            ' ' +\n            arr +\n            ' is not iterable(cannot read property Symbol(Symbol.iterator))'\n        )\n      );\n    }\n    var args = Array.prototype.slice.call(arr);\n    if (args.length === 0) return resolve([]);\n    var remaining = args.length;\n\n    function res(i, val) {\n      if (val && (typeof val === 'object' || typeof val === 'function')) {\n        var then = val.then;\n        if (typeof then === 'function') {\n          then.call(\n            val,\n            function(val) {\n              res(i, val);\n            },\n            function(e) {\n              args[i] = { status: 'rejected', reason: e };\n              if (--remaining === 0) {\n                resolve(args);\n              }\n            }\n          );\n          return;\n        }\n      }\n      args[i] = { status: 'fulfilled', value: val };\n      if (--remaining === 0) {\n        resolve(args);\n      }\n    }\n\n    for (var i = 0; i < args.length; i++) {\n      res(i, args[i]);\n    }\n  });\n}\n\nexport default allSettled;\n", "!function(){try{return new MouseEvent(\"test\")}catch(e){}var e=function(e,t){t=t||{bubbles:!1,cancelable:!1};var n=document.createEvent(\"MouseEvent\");return n.initMouseEvent(e,t.bubbles,t.cancelable,window,0,t.screenX||0,t.screenY||0,t.clientX||0,t.clientY||0,t.ctrlKey||!1,t.altKey||!1,t.shiftKey||!1,t.metaKey||!1,t.button||0,t.relatedTarget||null),n};e.prototype=Event.prototype,window.MouseEvent=e}();\n", "import promiseFinally from './finally';\nimport allSettled from './allSettled';\n\n// Store setTimeout reference so promise-polyfill will be unaffected by\n// other code modifying setTimeout (like sinon.useFakeTimers())\nvar setTimeoutFunc = setTimeout;\n\nfunction isArray(x) {\n  return Boolean(x && typeof x.length !== 'undefined');\n}\n\nfunction noop() {}\n\n// Polyfill for Function.prototype.bind\nfunction bind(fn, thisArg) {\n  return function() {\n    fn.apply(thisArg, arguments);\n  };\n}\n\n/**\n * @constructor\n * @param {Function} fn\n */\nfunction Promise(fn) {\n  if (!(this instanceof Promise))\n    throw new TypeError('Promises must be constructed via new');\n  if (typeof fn !== 'function') throw new TypeError('not a function');\n  /** @type {!number} */\n  this._state = 0;\n  /** @type {!boolean} */\n  this._handled = false;\n  /** @type {Promise|undefined} */\n  this._value = undefined;\n  /** @type {!Array<!Function>} */\n  this._deferreds = [];\n\n  doResolve(fn, this);\n}\n\nfunction handle(self, deferred) {\n  while (self._state === 3) {\n    self = self._value;\n  }\n  if (self._state === 0) {\n    self._deferreds.push(deferred);\n    return;\n  }\n  self._handled = true;\n  Promise._immediateFn(function() {\n    var cb = self._state === 1 ? deferred.onFulfilled : deferred.onRejected;\n    if (cb === null) {\n      (self._state === 1 ? resolve : reject)(deferred.promise, self._value);\n      return;\n    }\n    var ret;\n    try {\n      ret = cb(self._value);\n    } catch (e) {\n      reject(deferred.promise, e);\n      return;\n    }\n    resolve(deferred.promise, ret);\n  });\n}\n\nfunction resolve(self, newValue) {\n  try {\n    // Promise Resolution Procedure: https://github.com/promises-aplus/promises-spec#the-promise-resolution-procedure\n    if (newValue === self)\n      throw new TypeError('A promise cannot be resolved with itself.');\n    if (\n      newValue &&\n      (typeof newValue === 'object' || typeof newValue === 'function')\n    ) {\n      var then = newValue.then;\n      if (newValue instanceof Promise) {\n        self._state = 3;\n        self._value = newValue;\n        finale(self);\n        return;\n      } else if (typeof then === 'function') {\n        doResolve(bind(then, newValue), self);\n        return;\n      }\n    }\n    self._state = 1;\n    self._value = newValue;\n    finale(self);\n  } catch (e) {\n    reject(self, e);\n  }\n}\n\nfunction reject(self, newValue) {\n  self._state = 2;\n  self._value = newValue;\n  finale(self);\n}\n\nfunction finale(self) {\n  if (self._state === 2 && self._deferreds.length === 0) {\n    Promise._immediateFn(function() {\n      if (!self._handled) {\n        Promise._unhandledRejectionFn(self._value);\n      }\n    });\n  }\n\n  for (var i = 0, len = self._deferreds.length; i < len; i++) {\n    handle(self, self._deferreds[i]);\n  }\n  self._deferreds = null;\n}\n\n/**\n * @constructor\n */\nfunction Handler(onFulfilled, onRejected, promise) {\n  this.onFulfilled = typeof onFulfilled === 'function' ? onFulfilled : null;\n  this.onRejected = typeof onRejected === 'function' ? onRejected : null;\n  this.promise = promise;\n}\n\n/**\n * Take a potentially misbehaving resolver function and make sure\n * onFulfilled and onRejected are only called once.\n *\n * Makes no guarantees about asynchrony.\n */\nfunction doResolve(fn, self) {\n  var done = false;\n  try {\n    fn(\n      function(value) {\n        if (done) return;\n        done = true;\n        resolve(self, value);\n      },\n      function(reason) {\n        if (done) return;\n        done = true;\n        reject(self, reason);\n      }\n    );\n  } catch (ex) {\n    if (done) return;\n    done = true;\n    reject(self, ex);\n  }\n}\n\nPromise.prototype['catch'] = function(onRejected) {\n  return this.then(null, onRejected);\n};\n\nPromise.prototype.then = function(onFulfilled, onRejected) {\n  // @ts-ignore\n  var prom = new this.constructor(noop);\n\n  handle(this, new Handler(onFulfilled, onRejected, prom));\n  return prom;\n};\n\nPromise.prototype['finally'] = promiseFinally;\n\nPromise.all = function(arr) {\n  return new Promise(function(resolve, reject) {\n    if (!isArray(arr)) {\n      return reject(new TypeError('Promise.all accepts an array'));\n    }\n\n    var args = Array.prototype.slice.call(arr);\n    if (args.length === 0) return resolve([]);\n    var remaining = args.length;\n\n    function res(i, val) {\n      try {\n        if (val && (typeof val === 'object' || typeof val === 'function')) {\n          var then = val.then;\n          if (typeof then === 'function') {\n            then.call(\n              val,\n              function(val) {\n                res(i, val);\n              },\n              reject\n            );\n            return;\n          }\n        }\n        args[i] = val;\n        if (--remaining === 0) {\n          resolve(args);\n        }\n      } catch (ex) {\n        reject(ex);\n      }\n    }\n\n    for (var i = 0; i < args.length; i++) {\n      res(i, args[i]);\n    }\n  });\n};\n\nPromise.allSettled = allSettled;\n\nPromise.resolve = function(value) {\n  if (value && typeof value === 'object' && value.constructor === Promise) {\n    return value;\n  }\n\n  return new Promise(function(resolve) {\n    resolve(value);\n  });\n};\n\nPromise.reject = function(value) {\n  return new Promise(function(resolve, reject) {\n    reject(value);\n  });\n};\n\nPromise.race = function(arr) {\n  return new Promise(function(resolve, reject) {\n    if (!isArray(arr)) {\n      return reject(new TypeError('Promise.race accepts an array'));\n    }\n\n    for (var i = 0, len = arr.length; i < len; i++) {\n      Promise.resolve(arr[i]).then(resolve, reject);\n    }\n  });\n};\n\n// Use polyfill for setImmediate for performance gains\nPromise._immediateFn =\n  // @ts-ignore\n  (typeof setImmediate === 'function' &&\n    function(fn) {\n      // @ts-ignore\n      setImmediate(fn);\n    }) ||\n  function(fn) {\n    setTimeoutFunc(fn, 0);\n  };\n\nPromise._unhandledRejectionFn = function _unhandledRejectionFn(err) {\n  if (typeof console !== 'undefined' && console) {\n    console.warn('Possible Unhandled Promise Rejection:', err); // eslint-disable-line no-console\n  }\n};\n\nexport default Promise;\n", "import Promise from './index';\nimport promiseFinally from './finally';\nimport allSettled from './allSettled';\n\n/** @suppress {undefinedVars} */\nvar globalNS = (function() {\n  // the only reliable means to get the global object is\n  // `Function('return this')()`\n  // However, this causes CSP violations in Chrome apps.\n  if (typeof self !== 'undefined') {\n    return self;\n  }\n  if (typeof window !== 'undefined') {\n    return window;\n  }\n  if (typeof global !== 'undefined') {\n    return global;\n  }\n  throw new Error('unable to locate global object');\n})();\n\n// Expose the polyfill if Promise is undefined or set to a\n// non-function value. The latter can be due to a named HTMLElement\n// being exposed by browsers for legacy reasons.\n// https://github.com/taylorhakes/promise-polyfill/issues/114\nif (typeof globalNS['Promise'] !== 'function') {\n  globalNS['Promise'] = Promise;\n} else if (!globalNS.Promise.prototype['finally']) {\n  globalNS.Promise.prototype['finally'] = promiseFinally;\n} else if (!globalNS.Promise.allSettled) {\n  globalNS.Promise.allSettled = allSettled;\n}\n", "function isNodeName(element, name) {\n    return element.nodeName.toLowerCase() === name.toLowerCase();\n}\nfunction isFunction(target) {\n    return typeof target === 'function';\n}\nfunction isString(target) {\n    return typeof target === 'string';\n}\nfunction isNumber(target) {\n    return typeof target === 'number';\n}\nfunction isBoolean(target) {\n    return typeof target === 'boolean';\n}\nfunction isUndefined(target) {\n    return typeof target === 'undefined';\n}\nfunction isNull(target) {\n    return target === null;\n}\nfunction isWindow(target) {\n    return target instanceof Window;\n}\nfunction isDocument(target) {\n    return target instanceof Document;\n}\nfunction isElement(target) {\n    return target instanceof Element;\n}\nfunction isNode(target) {\n    return target instanceof Node;\n}\n/**\n * 是否是 IE 浏览器\n */\nfunction isIE() {\n    // @ts-ignore\n    return !!window.document.documentMode;\n}\nfunction isArrayLike(target) {\n    if (isFunction(target) || isWindow(target)) {\n        return false;\n    }\n    return isNumber(target.length);\n}\nfunction isObjectLike(target) {\n    return typeof target === 'object' && target !== null;\n}\nfunction toElement(target) {\n    return isDocument(target) ? target.documentElement : target;\n}\n/**\n * 把用 - 分隔的字符串转为驼峰（如 box-sizing 转换为 boxSizing）\n * @param string\n */\nfunction toCamelCase(string) {\n    return string\n        .replace(/^-ms-/, 'ms-')\n        .replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());\n}\n/**\n * 把驼峰法转为用 - 分隔的字符串（如 boxSizing 转换为 box-sizing）\n * @param string\n */\nfunction toKebabCase(string) {\n    return string.replace(/[A-Z]/g, (replacer) => '-' + replacer.toLowerCase());\n}\n/**\n * 获取元素的样式值\n * @param element\n * @param name\n */\nfunction getComputedStyleValue(element, name) {\n    return window.getComputedStyle(element).getPropertyValue(toKebabCase(name));\n}\n/**\n * 检查元素的 box-sizing 是否是 border-box\n * @param element\n */\nfunction isBorderBox(element) {\n    return getComputedStyleValue(element, 'box-sizing') === 'border-box';\n}\n/**\n * 获取元素的 padding, border, margin 宽度（两侧宽度的和，单位为px）\n * @param element\n * @param direction\n * @param extra\n */\nfunction getExtraWidth(element, direction, extra) {\n    const position = direction === 'width' ? ['Left', 'Right'] : ['Top', 'Bottom'];\n    return [0, 1].reduce((prev, _, index) => {\n        let prop = extra + position[index];\n        if (extra === 'border') {\n            prop += 'Width';\n        }\n        return prev + parseFloat(getComputedStyleValue(element, prop) || '0');\n    }, 0);\n}\n/**\n * 获取元素的样式值，对 width 和 height 进行过处理\n * @param element\n * @param name\n */\nfunction getStyle(element, name) {\n    // width、height 属性使用 getComputedStyle 得到的值不准确，需要使用 getBoundingClientRect 获取\n    if (name === 'width' || name === 'height') {\n        const valueNumber = element.getBoundingClientRect()[name];\n        if (isBorderBox(element)) {\n            return `${valueNumber}px`;\n        }\n        return `${valueNumber -\n            getExtraWidth(element, name, 'border') -\n            getExtraWidth(element, name, 'padding')}px`;\n    }\n    return getComputedStyleValue(element, name);\n}\n/**\n * 获取子节点组成的数组\n * @param target\n * @param parent\n */\nfunction getChildNodesArray(target, parent) {\n    const tempParent = document.createElement(parent);\n    tempParent.innerHTML = target;\n    return [].slice.call(tempParent.childNodes);\n}\n/**\n * 始终返回 false 的函数\n */\nfunction returnFalse() {\n    return false;\n}\n/**\n * 数值单位的 CSS 属性\n */\nconst cssNumber = [\n    'animationIterationCount',\n    'columnCount',\n    'fillOpacity',\n    'flexGrow',\n    'flexShrink',\n    'fontWeight',\n    'gridArea',\n    'gridColumn',\n    'gridColumnEnd',\n    'gridColumnStart',\n    'gridRow',\n    'gridRowEnd',\n    'gridRowStart',\n    'lineHeight',\n    'opacity',\n    'order',\n    'orphans',\n    'widows',\n    'zIndex',\n    'zoom',\n];\nexport { isNodeName, isArrayLike, isObjectLike, isFunction, isString, isNumber, isBoolean, isUndefined, isNull, isWindow, isDocument, isElement, isNode, isIE, toElement, toCamelCase, toKebabCase, getComputedStyleValue, isBorderBox, getExtraWidth, getStyle, getChildNodesArray, returnFalse, cssNumber, };\n", "import { isArrayLike } from '../utils';\nfunction each(target, callback) {\n    if (isArrayLike(target)) {\n        for (let i = 0; i < target.length; i += 1) {\n            if (callback.call(target[i], i, target[i]) === false) {\n                return target;\n            }\n        }\n    }\n    else {\n        const keys = Object.keys(target);\n        for (let i = 0; i < keys.length; i += 1) {\n            if (callback.call(target[keys[i]], keys[i], target[keys[i]]) === false) {\n                return target;\n            }\n        }\n    }\n    return target;\n}\nexport default each;\n", "import each from './functions/each';\n/**\n * 为了使用模块扩充，这里不能使用默认导出\n */\nexport class JQ {\n    constructor(arr) {\n        this.length = 0;\n        if (!arr) {\n            return this;\n        }\n        each(arr, (i, item) => {\n            // @ts-ignore\n            this[i] = item;\n        });\n        this.length = arr.length;\n        return this;\n    }\n}\n", "import each from './functions/each';\nimport { JQ } from './JQ';\nimport { getChildNodesArray, isArrayLike, isFunction, isNode, isString, } from './utils';\nfunction get$() {\n    const $ = function (selector) {\n        if (!selector) {\n            return new JQ();\n        }\n        // JQ\n        if (selector instanceof JQ) {\n            return selector;\n        }\n        // function\n        if (isFunction(selector)) {\n            if (/complete|loaded|interactive/.test(document.readyState) &&\n                document.body) {\n                selector.call(document, $);\n            }\n            else {\n                document.addEventListener('DOMContentLoaded', () => selector.call(document, $), false);\n            }\n            return new JQ([document]);\n        }\n        // String\n        if (isString(selector)) {\n            const html = selector.trim();\n            // 根据 HTML 字符串创建 JQ 对象\n            if (html[0] === '<' && html[html.length - 1] === '>') {\n                let toCreate = 'div';\n                const tags = {\n                    li: 'ul',\n                    tr: 'tbody',\n                    td: 'tr',\n                    th: 'tr',\n                    tbody: 'table',\n                    option: 'select',\n                };\n                each(tags, (childTag, parentTag) => {\n                    if (html.indexOf(`<${childTag}`) === 0) {\n                        toCreate = parentTag;\n                        return false;\n                    }\n                    return;\n                });\n                return new JQ(getChildNodesArray(html, toCreate));\n            }\n            // 根据 CSS 选择器创建 JQ 对象\n            const isIdSelector = selector[0] === '#' && !selector.match(/[ .<>:~]/);\n            if (!isIdSelector) {\n                return new JQ(document.querySelectorAll(selector));\n            }\n            const element = document.getElementById(selector.slice(1));\n            if (element) {\n                return new JQ([element]);\n            }\n            return new JQ();\n        }\n        if (isArrayLike(selector) && !isNode(selector)) {\n            return new JQ(selector);\n        }\n        return new JQ([selector]);\n    };\n    $.fn = JQ.prototype;\n    return $;\n}\nconst $ = get$();\nexport default $;\n", "import { MduiStatic } from './interfaces/MduiStatic';\nimport $ from 'mdui.jq/es/$';\n\n// 避免页面加载完后直接执行css动画\n// https://css-tricks.com/transitions-only-after-page-load/\nsetTimeout(() => $('body').addClass('mdui-loaded'));\n\nconst mdui = {\n  $: $,\n} as MduiStatic;\n\nexport default mdui;\n", "import { toElement } from '../utils';\n/**\n * 检查 container 元素内是否包含 contains 元素\n * @param container 父元素\n * @param contains 子元素\n * @example\n```js\ncontains( document, document.body ); // true\ncontains( document.getElementById('test'), document ); // false\ncontains( $('.container').get(0), $('.contains').get(0) ); // false\n```\n */\nfunction contains(container, contains) {\n    return container !== contains && toElement(container).contains(contains);\n}\nexport default contains;\n", "import each from './each';\n/**\n * 把第二个数组的元素追加到第一个数组中，并返回合并后的数组\n * @param first 第一个数组\n * @param second 该数组的元素将被追加到第一个数组中\n * @example\n```js\nmerge( [ 0, 1, 2 ], [ 2, 3, 4 ] )\n// [ 0, 1, 2, 2, 3, 4 ]\n```\n */\nfunction merge(first, second) {\n    each(second, (_, value) => {\n        first.push(value);\n    });\n    return first;\n}\nexport default merge;\n", "import $ from '../$';\nimport each from '../functions/each';\n$.fn.each = function (callback) {\n    return each(this, callback);\n};\n", "import $ from '../$';\n$.fn.get = function (index) {\n    return index === undefined\n        ? [].slice.call(this)\n        : this[index >= 0 ? index : index + this.length];\n};\n", "import $ from '../$';\nimport merge from '../functions/merge';\nimport { JQ } from '../JQ';\nimport './each';\nimport './get';\n$.fn.find = function (selector) {\n    const foundElements = [];\n    this.each((_, element) => {\n        merge(foundElements, $(element.querySelectorAll(selector)).get());\n    });\n    return new JQ(foundElements);\n};\n", "import $ from '../../$';\nimport contains from '../../functions/contains';\nimport { isObjectLike } from '../../utils';\nimport '../find';\n// 存储事件\nconst handlers = {};\n// 元素ID\nlet mduiElementId = 1;\n/**\n * 为元素赋予一个唯一的ID\n */\nfunction getElementId(element) {\n    const key = '_mduiEventId';\n    // @ts-ignore\n    if (!element[key]) {\n        // @ts-ignore\n        element[key] = ++mduiElementId;\n    }\n    // @ts-ignore\n    return element[key];\n}\n/**\n * 解析事件名中的命名空间\n */\nfunction parse(type) {\n    const parts = type.split('.');\n    return {\n        type: parts[0],\n        ns: parts.slice(1).sort().join(' '),\n    };\n}\n/**\n * 命名空间匹配规则\n */\nfunction matcherFor(ns) {\n    return new RegExp('(?:^| )' + ns.replace(' ', ' .* ?') + '(?: |$)');\n}\n/**\n * 获取匹配的事件\n * @param element\n * @param type\n * @param func\n * @param selector\n */\nfunction getHandlers(element, type, func, selector) {\n    const event = parse(type);\n    return (handlers[getElementId(element)] || []).filter((handler) => handler &&\n        (!event.type || handler.type === event.type) &&\n        (!event.ns || matcherFor(event.ns).test(handler.ns)) &&\n        (!func || getElementId(handler.func) === getElementId(func)) &&\n        (!selector || handler.selector === selector));\n}\n/**\n * 添加事件监听\n * @param element\n * @param types\n * @param func\n * @param data\n * @param selector\n */\nfunction add(element, types, func, data, selector) {\n    const elementId = getElementId(element);\n    if (!handlers[elementId]) {\n        handlers[elementId] = [];\n    }\n    // 传入 data.useCapture 来设置 useCapture: true\n    let useCapture = false;\n    if (isObjectLike(data) && data.useCapture) {\n        useCapture = true;\n    }\n    types.split(' ').forEach((type) => {\n        if (!type) {\n            return;\n        }\n        const event = parse(type);\n        function callFn(e, elem) {\n            // 因为鼠标事件模拟事件的 detail 属性是只读的，因此在 e._detail 中存储参数\n            const result = func.apply(elem, \n            // @ts-ignore\n            e._detail === undefined ? [e] : [e].concat(e._detail));\n            if (result === false) {\n                e.preventDefault();\n                e.stopPropagation();\n            }\n        }\n        function proxyFn(e) {\n            // @ts-ignore\n            if (e._ns && !matcherFor(e._ns).test(event.ns)) {\n                return;\n            }\n            // @ts-ignore\n            e._data = data;\n            if (selector) {\n                // 事件代理\n                $(element)\n                    .find(selector)\n                    .get()\n                    .reverse()\n                    .forEach((elem) => {\n                    if (elem === e.target ||\n                        contains(elem, e.target)) {\n                        callFn(e, elem);\n                    }\n                });\n            }\n            else {\n                // 不使用事件代理\n                callFn(e, element);\n            }\n        }\n        const handler = {\n            type: event.type,\n            ns: event.ns,\n            func,\n            selector,\n            id: handlers[elementId].length,\n            proxy: proxyFn,\n        };\n        handlers[elementId].push(handler);\n        element.addEventListener(handler.type, proxyFn, useCapture);\n    });\n}\n/**\n * 移除事件监听\n * @param element\n * @param types\n * @param func\n * @param selector\n */\nfunction remove(element, types, func, selector) {\n    const handlersInElement = handlers[getElementId(element)] || [];\n    const removeEvent = (handler) => {\n        delete handlersInElement[handler.id];\n        element.removeEventListener(handler.type, handler.proxy, false);\n    };\n    if (!types) {\n        handlersInElement.forEach((handler) => removeEvent(handler));\n    }\n    else {\n        types.split(' ').forEach((type) => {\n            if (type) {\n                getHandlers(element, type, func, selector).forEach((handler) => removeEvent(handler));\n            }\n        });\n    }\n}\nexport { parse, add, remove };\n", "import each from '../functions/each';\nimport { isUndefined } from '../utils';\nfunction extend(target, object1, ...objectN) {\n    objectN.unshift(object1);\n    each(objectN, (_, object) => {\n        each(object, (prop, value) => {\n            if (!isUndefined(value)) {\n                target[prop] = value;\n            }\n        });\n    });\n    return target;\n}\nexport default extend;\n", "import { isObjectLike } from '../utils';\nimport each from './each';\n/**\n * 将数组或对象序列化，序列化后的字符串可作为 URL 查询字符串使用\n *\n * 若传入数组，则格式必须和 serializeArray 方法的返回值一样\n * @param obj 对象或数组\n * @example\n```js\nparam({ width: 1680, height: 1050 });\n// width=1680&height=1050\n```\n * @example\n```js\nparam({ foo: { one: 1, two: 2 }})\n// foo[one]=1&foo[two]=2\n```\n * @example\n```js\nparam({ids: [1, 2, 3]})\n// ids[]=1&ids[]=2&ids[]=3\n```\n * @example\n```js\nparam([\n  {\"name\":\"name\",\"value\":\"mdui\"},\n  {\"name\":\"password\",\"value\":\"123456\"}\n])\n// name=mdui&password=123456\n```\n */\nfunction param(obj) {\n    if (!isObjectLike(obj) && !Array.isArray(obj)) {\n        return '';\n    }\n    const args = [];\n    function destructure(key, value) {\n        let keyTmp;\n        if (isObjectLike(value)) {\n            each(value, (i, v) => {\n                if (Array.isArray(value) && !isObjectLike(v)) {\n                    keyTmp = '';\n                }\n                else {\n                    keyTmp = i;\n                }\n                destructure(`${key}[${keyTmp}]`, v);\n            });\n        }\n        else {\n            if (value == null || value === '') {\n                keyTmp = '=';\n            }\n            else {\n                keyTmp = `=${encodeURIComponent(value)}`;\n            }\n            args.push(encodeURIComponent(key) + keyTmp);\n        }\n    }\n    if (Array.isArray(obj)) {\n        each(obj, function () {\n            destructure(this.name, this.value);\n        });\n    }\n    else {\n        each(obj, destructure);\n    }\n    return args.join('&');\n}\nexport default param;\n", "import $ from '../$';\nimport './each';\nimport { parse } from './utils/event';\n$.fn.trigger = function (type, extraParameters) {\n    const event = parse(type);\n    let eventObject;\n    const eventParams = {\n        bubbles: true,\n        cancelable: true,\n    };\n    const isMouseEvent = ['click', 'mousedown', 'mouseup', 'mousemove'].indexOf(event.type) > -1;\n    if (isMouseEvent) {\n        // Note: MouseEvent 无法传入 detail 参数\n        eventObject = new MouseEvent(event.type, eventParams);\n    }\n    else {\n        eventParams.detail = extraParameters;\n        eventObject = new CustomEvent(event.type, eventParams);\n    }\n    // @ts-ignore\n    eventObject._detail = extraParameters;\n    // @ts-ignore\n    eventObject._ns = event.ns;\n    return this.each(function () {\n        this.dispatchEvent(eventObject);\n    });\n};\n", "// 全局配置参数\nconst globalOptions = {};\n// 全局事件名\nconst ajaxEvents = {\n    ajaxStart: 'start.mdui.ajax',\n    ajaxSuccess: 'success.mdui.ajax',\n    ajaxError: 'error.mdui.ajax',\n    ajaxComplete: 'complete.mdui.ajax',\n};\nexport { globalOptions, ajaxEvents };\n", "import $ from '../$';\nimport '../methods/trigger';\nimport { isString, isUndefined } from '../utils';\nimport each from './each';\nimport extend from './extend';\nimport param from './param';\nimport { ajaxEvents, globalOptions } from './utils/ajax';\n/**\n * 判断此请求方法是否通过查询字符串提交参数\n * @param method 请求方法，大写\n */\nfunction isQueryStringData(method) {\n    return ['GET', 'HEAD'].indexOf(method) >= 0;\n}\n/**\n * 添加参数到 URL 上，且 URL 中不存在 ? 时，自动把第一个 & 替换为 ?\n * @param url\n * @param query\n */\nfunction appendQuery(url, query) {\n    return `${url}&${query}`.replace(/[&?]{1,2}/, '?');\n}\n/**\n * 合并请求参数，参数优先级：options > globalOptions > defaults\n * @param options\n */\nfunction mergeOptions(options) {\n    // 默认参数\n    const defaults = {\n        url: '',\n        method: 'GET',\n        data: '',\n        processData: true,\n        async: true,\n        cache: true,\n        username: '',\n        password: '',\n        headers: {},\n        xhrFields: {},\n        statusCode: {},\n        dataType: 'text',\n        contentType: 'application/x-www-form-urlencoded',\n        timeout: 0,\n        global: true,\n    };\n    // globalOptions 中的回调函数不合并\n    each(globalOptions, (key, value) => {\n        const callbacks = [\n            'beforeSend',\n            'success',\n            'error',\n            'complete',\n            'statusCode',\n        ];\n        // @ts-ignore\n        if (callbacks.indexOf(key) < 0 && !isUndefined(value)) {\n            defaults[key] = value;\n        }\n    });\n    return extend({}, defaults, options);\n}\n/**\n * 发送 ajax 请求\n * @param options\n * @example\n```js\najax({\n  method: \"POST\",\n  url: \"some.php\",\n  data: { name: \"John\", location: \"Boston\" }\n}).then(function( msg ) {\n  alert( \"Data Saved: \" + msg );\n});\n```\n */\nfunction ajax(options) {\n    // 是否已取消请求\n    let isCanceled = false;\n    // 事件参数\n    const eventParams = {};\n    // 参数合并\n    const mergedOptions = mergeOptions(options);\n    let url = mergedOptions.url || window.location.toString();\n    const method = mergedOptions.method.toUpperCase();\n    let data = mergedOptions.data;\n    const processData = mergedOptions.processData;\n    const async = mergedOptions.async;\n    const cache = mergedOptions.cache;\n    const username = mergedOptions.username;\n    const password = mergedOptions.password;\n    const headers = mergedOptions.headers;\n    const xhrFields = mergedOptions.xhrFields;\n    const statusCode = mergedOptions.statusCode;\n    const dataType = mergedOptions.dataType;\n    const contentType = mergedOptions.contentType;\n    const timeout = mergedOptions.timeout;\n    const global = mergedOptions.global;\n    // 需要发送的数据\n    // GET/HEAD 请求和 processData 为 true 时，转换为查询字符串格式，特殊格式不转换\n    if (data &&\n        (isQueryStringData(method) || processData) &&\n        !isString(data) &&\n        !(data instanceof ArrayBuffer) &&\n        !(data instanceof Blob) &&\n        !(data instanceof Document) &&\n        !(data instanceof FormData)) {\n        data = param(data);\n    }\n    // 对于 GET、HEAD 类型的请求，把 data 数据添加到 URL 中\n    if (data && isQueryStringData(method)) {\n        // 查询字符串拼接到 URL 中\n        url = appendQuery(url, data);\n        data = null;\n    }\n    /**\n     * 触发事件和回调函数\n     * @param event\n     * @param params\n     * @param callback\n     * @param args\n     */\n    function trigger(event, params, callback, ...args) {\n        // 触发全局事件\n        if (global) {\n            $(document).trigger(event, params);\n        }\n        // 触发 ajax 回调和事件\n        let result1;\n        let result2;\n        if (callback) {\n            // 全局回调\n            if (callback in globalOptions) {\n                // @ts-ignore\n                result1 = globalOptions[callback](...args);\n            }\n            // 自定义回调\n            if (mergedOptions[callback]) {\n                // @ts-ignore\n                result2 = mergedOptions[callback](...args);\n            }\n            // beforeSend 回调返回 false 时取消 ajax 请求\n            if (callback === 'beforeSend' &&\n                (result1 === false || result2 === false)) {\n                isCanceled = true;\n            }\n        }\n    }\n    // XMLHttpRequest 请求\n    function XHR() {\n        let textStatus;\n        return new Promise((resolve, reject) => {\n            // GET/HEAD 请求的缓存处理\n            if (isQueryStringData(method) && !cache) {\n                url = appendQuery(url, `_=${Date.now()}`);\n            }\n            // 创建 XHR\n            const xhr = new XMLHttpRequest();\n            xhr.open(method, url, async, username, password);\n            if (contentType ||\n                (data && !isQueryStringData(method) && contentType !== false)) {\n                xhr.setRequestHeader('Content-Type', contentType);\n            }\n            // 设置 Accept\n            if (dataType === 'json') {\n                xhr.setRequestHeader('Accept', 'application/json, text/javascript');\n            }\n            // 添加 headers\n            if (headers) {\n                each(headers, (key, value) => {\n                    // undefined 值不发送，string 和 null 需要发送\n                    if (!isUndefined(value)) {\n                        xhr.setRequestHeader(key, value + ''); // 把 null 转换成字符串\n                    }\n                });\n            }\n            // 检查是否是跨域请求，跨域请求时不添加 X-Requested-With\n            const crossDomain = /^([\\w-]+:)?\\/\\/([^/]+)/.test(url) &&\n                RegExp.$2 !== window.location.host;\n            if (!crossDomain) {\n                xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');\n            }\n            if (xhrFields) {\n                each(xhrFields, (key, value) => {\n                    // @ts-ignore\n                    xhr[key] = value;\n                });\n            }\n            eventParams.xhr = xhr;\n            eventParams.options = mergedOptions;\n            let xhrTimeout;\n            xhr.onload = function () {\n                if (xhrTimeout) {\n                    clearTimeout(xhrTimeout);\n                }\n                // AJAX 返回的 HTTP 响应码是否表示成功\n                const isHttpStatusSuccess = (xhr.status >= 200 && xhr.status < 300) ||\n                    xhr.status === 304 ||\n                    xhr.status === 0;\n                let responseData;\n                if (isHttpStatusSuccess) {\n                    if (xhr.status === 204 || method === 'HEAD') {\n                        textStatus = 'nocontent';\n                    }\n                    else if (xhr.status === 304) {\n                        textStatus = 'notmodified';\n                    }\n                    else {\n                        textStatus = 'success';\n                    }\n                    if (dataType === 'json') {\n                        try {\n                            responseData =\n                                method === 'HEAD' ? undefined : JSON.parse(xhr.responseText);\n                            eventParams.data = responseData;\n                        }\n                        catch (err) {\n                            textStatus = 'parsererror';\n                            trigger(ajaxEvents.ajaxError, eventParams, 'error', xhr, textStatus);\n                            reject(new Error(textStatus));\n                        }\n                        if (textStatus !== 'parsererror') {\n                            trigger(ajaxEvents.ajaxSuccess, eventParams, 'success', responseData, textStatus, xhr);\n                            resolve(responseData);\n                        }\n                    }\n                    else {\n                        responseData =\n                            method === 'HEAD'\n                                ? undefined\n                                : xhr.responseType === 'text' || xhr.responseType === ''\n                                    ? xhr.responseText\n                                    : xhr.response;\n                        eventParams.data = responseData;\n                        trigger(ajaxEvents.ajaxSuccess, eventParams, 'success', responseData, textStatus, xhr);\n                        resolve(responseData);\n                    }\n                }\n                else {\n                    textStatus = 'error';\n                    trigger(ajaxEvents.ajaxError, eventParams, 'error', xhr, textStatus);\n                    reject(new Error(textStatus));\n                }\n                // statusCode\n                each([globalOptions.statusCode, statusCode], (_, func) => {\n                    if (func && func[xhr.status]) {\n                        if (isHttpStatusSuccess) {\n                            func[xhr.status](responseData, textStatus, xhr);\n                        }\n                        else {\n                            func[xhr.status](xhr, textStatus);\n                        }\n                    }\n                });\n                trigger(ajaxEvents.ajaxComplete, eventParams, 'complete', xhr, textStatus);\n            };\n            xhr.onerror = function () {\n                if (xhrTimeout) {\n                    clearTimeout(xhrTimeout);\n                }\n                trigger(ajaxEvents.ajaxError, eventParams, 'error', xhr, xhr.statusText);\n                trigger(ajaxEvents.ajaxComplete, eventParams, 'complete', xhr, 'error');\n                reject(new Error(xhr.statusText));\n            };\n            xhr.onabort = function () {\n                let statusText = 'abort';\n                if (xhrTimeout) {\n                    statusText = 'timeout';\n                    clearTimeout(xhrTimeout);\n                }\n                trigger(ajaxEvents.ajaxError, eventParams, 'error', xhr, statusText);\n                trigger(ajaxEvents.ajaxComplete, eventParams, 'complete', xhr, statusText);\n                reject(new Error(statusText));\n            };\n            // ajax start 回调\n            trigger(ajaxEvents.ajaxStart, eventParams, 'beforeSend', xhr);\n            if (isCanceled) {\n                reject(new Error('cancel'));\n                return;\n            }\n            // Timeout\n            if (timeout > 0) {\n                xhrTimeout = setTimeout(() => {\n                    xhr.abort();\n                }, timeout);\n            }\n            // 发送 XHR\n            xhr.send(data);\n        });\n    }\n    return XHR();\n}\nexport default ajax;\n", "import $ from '../$';\nimport ajax from '../functions/ajax';\n$.ajax = ajax;\n", "import $ from '../$';\nimport ajaxSetup from '../functions/ajaxSetup';\n$.ajaxSetup = ajaxSetup;\n", "import extend from '../functions/extend';\nimport { globalOptions } from './utils/ajax';\n/**\n * 为 Ajax 请求设置全局配置参数\n * @param options 键值对参数\n * @example\n```js\najaxSetup({\n  dataType: 'json',\n  method: 'POST',\n});\n```\n */\nfunction ajaxSetup(options) {\n    return extend(globalOptions, options);\n}\nexport default ajaxSetup;\n", "import $ from '../$';\nimport contains from '../functions/contains';\n$.contains = contains;\n", "const dataNS = '_mduiElementDataStorage';\nexport default dataNS;\n", "import { isObjectLike, isUndefined, toCamelCase } from '../utils';\nimport each from './each';\nimport dataNS from './utils/data';\n/**\n * 在元素上设置键值对数据\n * @param element\n * @param object\n */\nfunction setObjectToElement(element, object) {\n    // @ts-ignore\n    if (!element[dataNS]) {\n        // @ts-ignore\n        element[dataNS] = {};\n    }\n    each(object, (key, value) => {\n        // @ts-ignore\n        element[dataNS][toCamelCase(key)] = value;\n    });\n}\nfunction data(element, key, value) {\n    // 根据键值对设置值\n    // data(element, { 'key' : 'value' })\n    if (isObjectLike(key)) {\n        setObjectToElement(element, key);\n        return key;\n    }\n    // 根据 key、value 设置值\n    // data(element, 'key', 'value')\n    if (!isUndefined(value)) {\n        setObjectToElement(element, { [key]: value });\n        return value;\n    }\n    // 获取所有值\n    // data(element)\n    if (isUndefined(key)) {\n        // @ts-ignore\n        return element[dataNS] ? element[dataNS] : {};\n    }\n    // 从 dataNS 中获取指定值\n    // data(element, 'key')\n    key = toCamelCase(key);\n    // @ts-ignore\n    if (element[dataNS] && key in element[dataNS]) {\n        // @ts-ignore\n        return element[dataNS][key];\n    }\n    return undefined;\n}\nexport default data;\n", "import each from './each';\nfunction map(elements, callback) {\n    let value;\n    const ret = [];\n    each(elements, (i, element) => {\n        value = callback.call(window, element, i);\n        if (value != null) {\n            ret.push(value);\n        }\n    });\n    return [].concat(...ret);\n}\nexport default map;\n", "import each from '../functions/each';\nimport { isUndefined, isString, toCamelCase } from '../utils';\nimport dataNS from './utils/data';\n/**\n * 移除指定元素上存放的数据\n * @param element 存放数据的元素\n * @param name\n * 数据键名\n *\n * 若未指定键名，将移除元素上所有数据\n *\n * 多个键名可以用空格分隔，或者用数组表示多个键名\n  @example\n```js\n// 移除元素上键名为 name 的数据\nremoveData(document.body, 'name');\n```\n * @example\n```js\n// 移除元素上键名为 name1 和 name2 的数据\nremoveData(document.body, 'name1 name2');\n```\n * @example\n```js\n// 移除元素上键名为 name1 和 name2 的数据\nremoveData(document.body, ['name1', 'name2']);\n```\n * @example\n```js\n// 移除元素上所有数据\nremoveData(document.body);\n```\n */\nfunction removeData(element, name) {\n    // @ts-ignore\n    if (!element[dataNS]) {\n        return;\n    }\n    const remove = (nameItem) => {\n        nameItem = toCamelCase(nameItem);\n        // @ts-ignore\n        if (element[dataNS][nameItem]) {\n            // @ts-ignore\n            element[dataNS][nameItem] = null;\n            // @ts-ignore\n            delete element[dataNS][nameItem];\n        }\n    };\n    if (isUndefined(name)) {\n        // @ts-ignore\n        element[dataNS] = null;\n        // @ts-ignore\n        delete element[dataNS];\n        // @ts-ignore\n    }\n    else if (isString(name)) {\n        name\n            .split(' ')\n            .filter((nameItem) => nameItem)\n            .forEach((nameItem) => remove(nameItem));\n    }\n    else {\n        each(name, (_, nameItem) => remove(nameItem));\n    }\n}\nexport default removeData;\n", "import each from './each';\n/**\n * 过滤掉数组中的重复元素\n * @param arr 数组\n * @example\n```js\nunique([1, 2, 12, 3, 2, 1, 2, 1, 1]);\n// [1, 2, 12, 3]\n```\n */\nfunction unique(arr) {\n    const result = [];\n    each(arr, (_, val) => {\n        if (result.indexOf(val) === -1) {\n            result.push(val);\n        }\n    });\n    return result;\n}\nexport default unique;\n", "import $ from '../../$';\nimport unique from '../../functions/unique';\nimport { JQ } from '../../JQ';\nimport { isElement } from '../../utils';\nimport '../each';\nimport '../is';\nexport default function dir($elements, nameIndex, node, selector, filter) {\n    const ret = [];\n    let target;\n    $elements.each((_, element) => {\n        target = element[node];\n        // 不能包含最顶层的 document 元素\n        while (target && isElement(target)) {\n            // prevUntil, nextUntil, parentsUntil\n            if (nameIndex === 2) {\n                if (selector && $(target).is(selector)) {\n                    break;\n                }\n                if (!filter || $(target).is(filter)) {\n                    ret.push(target);\n                }\n            }\n            // prev, next, parent\n            else if (nameIndex === 0) {\n                if (!selector || $(target).is(selector)) {\n                    ret.push(target);\n                }\n                break;\n            }\n            // prevAll, nextAll, parents\n            else {\n                if (!selector || $(target).is(selector)) {\n                    ret.push(target);\n                }\n            }\n            // @ts-ignore\n            target = target[node];\n        }\n    });\n    return new JQ(unique(ret));\n}\n", "import $ from '../$';\nimport data from '../functions/data';\n$.data = data;\n", "import $ from '../$';\nimport each from '../functions/each';\n$.each = each;\n", "import $ from '../$';\nimport each from '../functions/each';\nimport extend from '../functions/extend';\n$.extend = function (...objectN) {\n    if (objectN.length === 1) {\n        each(objectN[0], (prop, value) => {\n            this[prop] = value;\n        });\n        return this;\n    }\n    return extend(objectN.shift(), objectN.shift(), ...objectN);\n};\n", "import $ from '../$';\nimport map from '../functions/map';\n$.map = map;\n", "import $ from '../$';\nimport merge from '../functions/merge';\n$.merge = merge;\n", "import $ from '../$';\nimport param from '../functions/param';\n$.param = param;\n", "import $ from '../$';\nimport removeData from '../functions/removeData';\n$.removeData = removeData;\n", "import $ from '../$';\nimport unique from '../functions/unique';\n$.unique = unique;\n", "import $ from '../$';\nimport merge from '../functions/merge';\nimport unique from '../functions/unique';\nimport { JQ } from '../JQ';\nimport './get';\n$.fn.add = function (selector) {\n    return new JQ(unique(merge(this.get(), $(selector).get())));\n};\n", "import $ from '../$';\nimport each from '../functions/each';\nimport { isElement, isFunction } from '../utils';\nimport './each';\neach(['add', 'remove', 'toggle'], (_, name) => {\n    $.fn[`${name}Class`] = function (className) {\n        if (name === 'remove' && !arguments.length) {\n            return this.each((_, element) => {\n                element.setAttribute('class', '');\n            });\n        }\n        return this.each((i, element) => {\n            if (!isElement(element)) {\n                return;\n            }\n            const classes = (isFunction(className)\n                ? className.call(element, i, element.getAttribute('class') || '')\n                : className)\n                .split(' ')\n                .filter((name) => name);\n            each(classes, (_, cls) => {\n                element.classList[name](cls);\n            });\n        });\n    };\n});\n", "import $ from '../$';\nimport each from '../functions/each';\nimport './each';\neach(['insertBefore', 'insertAfter'], (nameIndex, name) => {\n    $.fn[name] = function (target) {\n        const $element = nameIndex ? $(this.get().reverse()) : this; // 顺序和 jQuery 保持一致\n        const $target = $(target);\n        const result = [];\n        $target.each((index, target) => {\n            if (!target.parentNode) {\n                return;\n            }\n            $element.each((_, element) => {\n                const newItem = index\n                    ? element.cloneNode(true)\n                    : element;\n                const existingItem = nameIndex ? target.nextSibling : target;\n                result.push(newItem);\n                target.parentNode.insertBefore(newItem, existingItem);\n            });\n        });\n        return $(nameIndex ? result.reverse() : result);\n    };\n});\n", "import $ from '../$';\nimport each from '../functions/each';\nimport { getChildNodesArray, isFunction, isString, isElement } from '../utils';\nimport './each';\nimport './insertAfter';\nimport './insertBefore';\n/**\n * 是否不是 HTML 字符串（包裹在 <> 中）\n * @param target\n */\nfunction isPlainText(target) {\n    return (isString(target) && (target[0] !== '<' || target[target.length - 1] !== '>'));\n}\neach(['before', 'after'], (nameIndex, name) => {\n    $.fn[name] = function (...args) {\n        // after 方法，多个参数需要按参数顺序添加到元素后面，所以需要将参数顺序反向处理\n        if (nameIndex === 1) {\n            args = args.reverse();\n        }\n        return this.each((index, element) => {\n            const targets = isFunction(args[0])\n                ? [args[0].call(element, index, element.innerHTML)]\n                : args;\n            each(targets, (_, target) => {\n                let $target;\n                if (isPlainText(target)) {\n                    $target = $(getChildNodesArray(target, 'div'));\n                }\n                else if (index && isElement(target)) {\n                    $target = $(target.cloneNode(true));\n                }\n                else {\n                    $target = $(target);\n                }\n                $target[nameIndex ? 'insertAfter' : 'insertBefore'](element);\n            });\n        });\n    };\n});\n", "import $ from '../$';\nimport each from '../functions/each';\nimport { isFunction, isObjectLike, returnFalse } from '../utils';\nimport './each';\nimport { remove } from './utils/event';\n$.fn.off = function (types, selector, callback) {\n    // types 是对象\n    if (isObjectLike(types)) {\n        each(types, (type, fn) => {\n            // this.off('click', undefined, function () {})\n            // this.off('click', '.box', function () {})\n            this.off(type, selector, fn);\n        });\n        return this;\n    }\n    // selector 不存在\n    if (selector === false || isFunction(selector)) {\n        callback = selector;\n        selector = undefined;\n        // this.off('click', undefined, function () {})\n    }\n    // callback 传入 `false`，相当于 `return false`\n    if (callback === false) {\n        callback = returnFalse;\n    }\n    return this.each(function () {\n        remove(this, types, callback, selector);\n    });\n};\n", "import $ from '../$';\nimport each from '../functions/each';\nimport { isObjectLike, isString, returnFalse } from '../utils';\nimport './each';\nimport './off';\nimport { add } from './utils/event';\n$.fn.on = function (types, selector, data, callback, one) {\n    // types 可以是 type/func 对象\n    if (isObjectLike(types)) {\n        // (types-Object, selector, data)\n        if (!isString(selector)) {\n            // (types-Object, data)\n            data = data || selector;\n            selector = undefined;\n        }\n        each(types, (type, fn) => {\n            // selector 和 data 都可能是 undefined\n            // @ts-ignore\n            this.on(type, selector, data, fn, one);\n        });\n        return this;\n    }\n    if (data == null && callback == null) {\n        // (types, fn)\n        callback = selector;\n        data = selector = undefined;\n    }\n    else if (callback == null) {\n        if (isString(selector)) {\n            // (types, selector, fn)\n            callback = data;\n            data = undefined;\n        }\n        else {\n            // (types, data, fn)\n            callback = data;\n            data = selector;\n            selector = undefined;\n        }\n    }\n    if (callback === false) {\n        callback = returnFalse;\n    }\n    else if (!callback) {\n        return this;\n    }\n    // $().one()\n    if (one) {\n        // eslint-disable-next-line @typescript-eslint/no-this-alias\n        const _this = this;\n        const origCallback = callback;\n        callback = function (event) {\n            _this.off(event.type, selector, callback);\n            // eslint-disable-next-line prefer-rest-params\n            return origCallback.apply(this, arguments);\n        };\n    }\n    return this.each(function () {\n        add(this, types, callback, data, selector);\n    });\n};\n", "import $ from '../$';\nimport each from '../functions/each';\nimport { ajaxEvents } from '../functions/utils/ajax';\nimport './on';\neach(ajaxEvents, (name, eventName) => {\n    $.fn[name] = function (fn) {\n        return this.on(eventName, (e, params) => {\n            fn(e, params.xhr, params.options, params.data);\n        });\n    };\n});\n", "import $ from '../$';\nimport map from '../functions/map';\nimport { JQ } from '../JQ';\n$.fn.map = function (callback) {\n    return new JQ(map(this, (element, i) => callback.call(element, i, element)));\n};\n", "import $ from '../$';\nimport './map';\n$.fn.clone = function () {\n    return this.map(function () {\n        return this.cloneNode(true);\n    });\n};\n", "import $ from '../$';\nimport { isDocument, isFunction, isString, isWindow } from '../utils';\nimport './each';\n$.fn.is = function (selector) {\n    let isMatched = false;\n    if (isFunction(selector)) {\n        this.each((index, element) => {\n            if (selector.call(element, index, element)) {\n                isMatched = true;\n            }\n        });\n        return isMatched;\n    }\n    if (isString(selector)) {\n        this.each((_, element) => {\n            if (isDocument(element) || isWindow(element)) {\n                return;\n            }\n            // @ts-ignore\n            const matches = element.matches || element.msMatchesSelector;\n            if (matches.call(element, selector)) {\n                isMatched = true;\n            }\n        });\n        return isMatched;\n    }\n    const $compareWith = $(selector);\n    this.each((_, element) => {\n        $compareWith.each((_, compare) => {\n            if (element === compare) {\n                isMatched = true;\n            }\n        });\n    });\n    return isMatched;\n};\n", "import $ from '../$';\nimport './each';\nimport './is';\n$.fn.remove = function (selector) {\n    return this.each((_, element) => {\n        if (element.parentNode && (!selector || $(element).is(selector))) {\n            element.parentNode.removeChild(element);\n        }\n    });\n};\n", "import $ from '../$';\nimport each from '../functions/each';\nimport { isFunction, isString } from '../utils';\nimport './after';\nimport './before';\nimport './clone';\nimport './each';\nimport './map';\nimport './remove';\neach(['prepend', 'append'], (nameIndex, name) => {\n    $.fn[name] = function (...args) {\n        return this.each((index, element) => {\n            const childNodes = element.childNodes;\n            const childLength = childNodes.length;\n            const child = childLength\n                ? childNodes[nameIndex ? childLength - 1 : 0]\n                : document.createElement('div');\n            if (!childLength) {\n                element.appendChild(child);\n            }\n            let contents = isFunction(args[0])\n                ? [args[0].call(element, index, element.innerHTML)]\n                : args;\n            // 如果不是字符串，则仅第一个元素使用原始元素，其他的都克隆自第一个元素\n            if (index) {\n                contents = contents.map((content) => {\n                    return isString(content) ? content : $(content).clone();\n                });\n            }\n            $(child)[nameIndex ? 'after' : 'before'](...contents);\n            if (!childLength) {\n                element.removeChild(child);\n            }\n        });\n    };\n});\n", "import $ from '../$';\nimport each from '../functions/each';\nimport './insertAfter';\nimport './insertBefore';\nimport './map';\nimport './remove';\neach(['appendTo', 'prependTo'], (nameIndex, name) => {\n    $.fn[name] = function (target) {\n        const extraChilds = [];\n        const $target = $(target).map((_, element) => {\n            const childNodes = element.childNodes;\n            const childLength = childNodes.length;\n            if (childLength) {\n                return childNodes[nameIndex ? 0 : childLength - 1];\n            }\n            const child = document.createElement('div');\n            element.appendChild(child);\n            extraChilds.push(child);\n            return child;\n        });\n        const $result = this[nameIndex ? 'insertBefore' : 'insertAfter']($target);\n        $(extraChilds).remove();\n        return $result;\n    };\n});\n", "import $ from '../$';\nimport each from '../functions/each';\nimport { cssNumber, getStyle, isElement, isFunction, isNull, isNumber, isObjectLike, isUndefined, toCamelCase, } from '../utils';\nimport './each';\neach(['attr', 'prop', 'css'], (nameIndex, name) => {\n    function set(element, key, value) {\n        // 值为 undefined 时，不修改\n        if (isUndefined(value)) {\n            return;\n        }\n        switch (nameIndex) {\n            // attr\n            case 0:\n                if (isNull(value)) {\n                    element.removeAttribute(key);\n                }\n                else {\n                    element.setAttribute(key, value);\n                }\n                break;\n            // prop\n            case 1:\n                // @ts-ignore\n                element[key] = value;\n                break;\n            // css\n            default:\n                key = toCamelCase(key);\n                // @ts-ignore\n                element.style[key] = isNumber(value)\n                    ? `${value}${cssNumber.indexOf(key) > -1 ? '' : 'px'}`\n                    : value;\n                break;\n        }\n    }\n    function get(element, key) {\n        switch (nameIndex) {\n            // attr\n            case 0:\n                // 属性不存在时，原生 getAttribute 方法返回 null，而 jquery 返回 undefined。这里和 jquery 保持一致\n                const value = element.getAttribute(key);\n                return isNull(value) ? undefined : value;\n            // prop\n            case 1:\n                // @ts-ignore\n                return element[key];\n            // css\n            default:\n                return getStyle(element, key);\n        }\n    }\n    $.fn[name] = function (key, value) {\n        if (isObjectLike(key)) {\n            each(key, (k, v) => {\n                // @ts-ignore\n                this[name](k, v);\n            });\n            return this;\n        }\n        if (arguments.length === 1) {\n            const element = this[0];\n            return isElement(element) ? get(element, key) : undefined;\n        }\n        return this.each((i, element) => {\n            set(element, key, isFunction(value) ? value.call(element, i, get(element, key)) : value);\n        });\n    };\n});\n", "import $ from '../$';\nimport each from '../functions/each';\nimport unique from '../functions/unique';\nimport { JQ } from '../JQ';\nimport { isElement } from '../utils';\nimport './each';\nimport './is';\n$.fn.children = function (selector) {\n    const children = [];\n    this.each((_, element) => {\n        each(element.childNodes, (__, childNode) => {\n            if (!isElement(childNode)) {\n                return;\n            }\n            if (!selector || $(childNode).is(selector)) {\n                children.push(childNode);\n            }\n        });\n    });\n    return new JQ(unique(children));\n};\n", "import $ from '../$';\nimport { JQ } from '../JQ';\n$.fn.slice = function (...args) {\n    return new JQ([].slice.apply(this, args));\n};\n", "import $ from '../$';\nimport { JQ } from '../JQ';\nimport './slice';\n$.fn.eq = function (index) {\n    const ret = index === -1 ? this.slice(index) : this.slice(index, +index + 1);\n    return new JQ(ret);\n};\n", "import $ from '../$';\nimport each from '../functions/each';\nimport './get';\nimport dir from './utils/dir';\neach(['', 's', 'sUntil'], (nameIndex, name) => {\n    $.fn[`parent${name}`] = function (selector, filter) {\n        // parents、parentsUntil 需要把元素的顺序反向处理，以便和 jQuery 的结果一致\n        const $nodes = !nameIndex ? this : $(this.get().reverse());\n        return dir($nodes, nameIndex, 'parentNode', selector, filter);\n    };\n});\n", "import $ from '../$';\nimport { JQ } from '../JQ';\nimport './eq';\nimport './is';\nimport './parents';\n$.fn.closest = function (selector) {\n    if (this.is(selector)) {\n        return this;\n    }\n    const matched = [];\n    this.parents().each((_, element) => {\n        if ($(element).is(selector)) {\n            matched.push(element);\n            return false;\n        }\n    });\n    return new JQ(matched);\n};\n", "import $ from '../$';\nimport data from '../functions/data';\nimport { isObjectLike, isString, isUndefined, toCamelCase, toKebabCase, } from '../utils';\nimport './each';\nconst rbrace = /^(?:{[\\w\\W]*\\}|\\[[\\w\\W]*\\])$/;\n// 从 `data-*` 中获取的值，需要经过该函数转换\nfunction getData(value) {\n    if (value === 'true') {\n        return true;\n    }\n    if (value === 'false') {\n        return false;\n    }\n    if (value === 'null') {\n        return null;\n    }\n    if (value === +value + '') {\n        return +value;\n    }\n    if (rbrace.test(value)) {\n        return JSON.parse(value);\n    }\n    return value;\n}\n// 若 value 不存在，则从 `data-*` 中获取值\nfunction dataAttr(element, key, value) {\n    if (isUndefined(value) && element.nodeType === 1) {\n        const name = 'data-' + toKebabCase(key);\n        value = element.getAttribute(name);\n        if (isString(value)) {\n            try {\n                value = getData(value);\n            }\n            catch (e) { }\n        }\n        else {\n            value = undefined;\n        }\n    }\n    return value;\n}\n$.fn.data = function (key, value) {\n    // 获取所有值\n    if (isUndefined(key)) {\n        if (!this.length) {\n            return undefined;\n        }\n        const element = this[0];\n        const resultData = data(element);\n        // window, document 上不存在 `data-*` 属性\n        if (element.nodeType !== 1) {\n            return resultData;\n        }\n        // 从 `data-*` 中获取值\n        const attrs = element.attributes;\n        let i = attrs.length;\n        while (i--) {\n            if (attrs[i]) {\n                let name = attrs[i].name;\n                if (name.indexOf('data-') === 0) {\n                    name = toCamelCase(name.slice(5));\n                    resultData[name] = dataAttr(element, name, resultData[name]);\n                }\n            }\n        }\n        return resultData;\n    }\n    // 同时设置多个值\n    if (isObjectLike(key)) {\n        return this.each(function () {\n            data(this, key);\n        });\n    }\n    // value 传入了 undefined\n    if (arguments.length === 2 && isUndefined(value)) {\n        return this;\n    }\n    // 设置值\n    if (!isUndefined(value)) {\n        return this.each(function () {\n            data(this, key, value);\n        });\n    }\n    // 获取值\n    if (!this.length) {\n        return undefined;\n    }\n    return dataAttr(this[0], key, data(this[0], key));\n};\n", "import $ from '../$';\nimport each from '../functions/each';\nimport { isBoolean, isDocument, isFunction, isWindow, toElement, isBorderBox, getExtraWidth, getComputedStyleValue, isIE, } from '../utils';\nimport './css';\nimport './each';\n/**\n * 值上面的 padding、border、margin 处理\n * @param element\n * @param name\n * @param value\n * @param funcIndex\n * @param includeMargin\n * @param multiply\n */\nfunction handleExtraWidth(element, name, value, funcIndex, includeMargin, multiply) {\n    // 获取元素的 padding, border, margin 宽度（两侧宽度的和）\n    const getExtraWidthValue = (extra) => {\n        return (getExtraWidth(element, name.toLowerCase(), extra) *\n            multiply);\n    };\n    if (funcIndex === 2 && includeMargin) {\n        value += getExtraWidthValue('margin');\n    }\n    if (isBorderBox(element)) {\n        // IE 为 box-sizing: border-box 时，得到的值不含 border 和 padding，这里先修复\n        // 仅获取时需要处理，multiply === 1 为 get\n        if (isIE() && multiply === 1) {\n            value += getExtraWidthValue('border');\n            value += getExtraWidthValue('padding');\n        }\n        if (funcIndex === 0) {\n            value -= getExtraWidthValue('border');\n        }\n        if (funcIndex === 1) {\n            value -= getExtraWidthValue('border');\n            value -= getExtraWidthValue('padding');\n        }\n    }\n    else {\n        if (funcIndex === 0) {\n            value += getExtraWidthValue('padding');\n        }\n        if (funcIndex === 2) {\n            value += getExtraWidthValue('border');\n            value += getExtraWidthValue('padding');\n        }\n    }\n    return value;\n}\n/**\n * 获取元素的样式值\n * @param element\n * @param name\n * @param funcIndex 0: innerWidth, innerHeight; 1: width, height; 2: outerWidth, outerHeight\n * @param includeMargin\n */\nfunction get(element, name, funcIndex, includeMargin) {\n    const clientProp = `client${name}`;\n    const scrollProp = `scroll${name}`;\n    const offsetProp = `offset${name}`;\n    const innerProp = `inner${name}`;\n    // $(window).width()\n    if (isWindow(element)) {\n        // outerWidth, outerHeight 需要包含滚动条的宽度\n        return funcIndex === 2\n            ? element[innerProp]\n            : toElement(document)[clientProp];\n    }\n    // $(document).width()\n    if (isDocument(element)) {\n        const doc = toElement(element);\n        return Math.max(\n        // @ts-ignore\n        element.body[scrollProp], doc[scrollProp], \n        // @ts-ignore\n        element.body[offsetProp], doc[offsetProp], doc[clientProp]);\n    }\n    const value = parseFloat(getComputedStyleValue(element, name.toLowerCase()) || '0');\n    return handleExtraWidth(element, name, value, funcIndex, includeMargin, 1);\n}\n/**\n * 设置元素的样式值\n * @param element\n * @param elementIndex\n * @param name\n * @param funcIndex 0: innerWidth, innerHeight; 1: width, height; 2: outerWidth, outerHeight\n * @param includeMargin\n * @param value\n */\nfunction set(element, elementIndex, name, funcIndex, includeMargin, value) {\n    let computedValue = isFunction(value)\n        ? value.call(element, elementIndex, get(element, name, funcIndex, includeMargin))\n        : value;\n    if (computedValue == null) {\n        return;\n    }\n    const $element = $(element);\n    const dimension = name.toLowerCase();\n    // 特殊的值，不需要计算 padding、border、margin\n    if (['auto', 'inherit', ''].indexOf(computedValue) > -1) {\n        $element.css(dimension, computedValue);\n        return;\n    }\n    // 其他值保留原始单位。注意：如果不使用 px 作为单位，则算出的值一般是不准确的\n    const suffix = computedValue.toString().replace(/\\b[0-9.]*/, '');\n    const numerical = parseFloat(computedValue);\n    computedValue =\n        handleExtraWidth(element, name, numerical, funcIndex, includeMargin, -1) +\n            (suffix || 'px');\n    $element.css(dimension, computedValue);\n}\neach(['Width', 'Height'], (_, name) => {\n    each([`inner${name}`, name.toLowerCase(), `outer${name}`], (funcIndex, funcName) => {\n        $.fn[funcName] = function (margin, value) {\n            // 是否是赋值操作\n            const isSet = arguments.length && (funcIndex < 2 || !isBoolean(margin));\n            const includeMargin = margin === true || value === true;\n            // 获取第一个元素的值\n            if (!isSet) {\n                return this.length\n                    ? get(this[0], name, funcIndex, includeMargin)\n                    : undefined;\n            }\n            // 设置每个元素的值\n            return this.each((index, element) => set(element, index, name, funcIndex, includeMargin, margin));\n        };\n    });\n});\n", "import $ from '../$';\nimport './css';\nimport './eq';\nimport './offset';\nimport './offsetParent';\nfunction floatStyle($element, name) {\n    return parseFloat($element.css(name));\n}\n$.fn.position = function () {\n    if (!this.length) {\n        return undefined;\n    }\n    const $element = this.eq(0);\n    let currentOffset;\n    let parentOffset = {\n        left: 0,\n        top: 0,\n    };\n    if ($element.css('position') === 'fixed') {\n        currentOffset = $element[0].getBoundingClientRect();\n    }\n    else {\n        currentOffset = $element.offset();\n        const $offsetParent = $element.offsetParent();\n        parentOffset = $offsetParent.offset();\n        parentOffset.top += floatStyle($offsetParent, 'border-top-width');\n        parentOffset.left += floatStyle($offsetParent, 'border-left-width');\n    }\n    return {\n        top: currentOffset.top - parentOffset.top - floatStyle($element, 'margin-top'),\n        left: currentOffset.left -\n            parentOffset.left -\n            floatStyle($element, 'margin-left'),\n    };\n};\n", "import $ from '../$';\nimport extend from '../functions/extend';\nimport { isFunction } from '../utils';\nimport './css';\nimport './each';\nimport './position';\nfunction get(element) {\n    if (!element.getClientRects().length) {\n        return { top: 0, left: 0 };\n    }\n    const rect = element.getBoundingClientRect();\n    const win = element.ownerDocument.defaultView;\n    return {\n        top: rect.top + win.pageYOffset,\n        left: rect.left + win.pageXOffset,\n    };\n}\nfunction set(element, value, index) {\n    const $element = $(element);\n    const position = $element.css('position');\n    if (position === 'static') {\n        $element.css('position', 'relative');\n    }\n    const currentOffset = get(element);\n    const currentTopString = $element.css('top');\n    const currentLeftString = $element.css('left');\n    let currentTop;\n    let currentLeft;\n    const calculatePosition = (position === 'absolute' || position === 'fixed') &&\n        (currentTopString + currentLeftString).indexOf('auto') > -1;\n    if (calculatePosition) {\n        const currentPosition = $element.position();\n        currentTop = currentPosition.top;\n        currentLeft = currentPosition.left;\n    }\n    else {\n        currentTop = parseFloat(currentTopString);\n        currentLeft = parseFloat(currentLeftString);\n    }\n    const computedValue = isFunction(value)\n        ? value.call(element, index, extend({}, currentOffset))\n        : value;\n    $element.css({\n        top: computedValue.top != null\n            ? computedValue.top - currentOffset.top + currentTop\n            : undefined,\n        left: computedValue.left != null\n            ? computedValue.left - currentOffset.left + currentLeft\n            : undefined,\n    });\n}\n$.fn.offset = function (value) {\n    // 获取坐标\n    if (!arguments.length) {\n        if (!this.length) {\n            return undefined;\n        }\n        return get(this[0]);\n    }\n    // 设置坐标\n    return this.each(function (index) {\n        set(this, value, index);\n    });\n};\n", "import $ from '../$';\nimport './each';\n$.fn.empty = function () {\n    return this.each(function () {\n        this.innerHTML = '';\n    });\n};\n", "import $ from '../$';\nimport each from '../functions/each';\n$.fn.extend = function (obj) {\n    each(obj, (prop, value) => {\n        // 在 JQ 对象上扩展方法时，需要自己添加 typescript 的类型定义\n        $.fn[prop] = value;\n    });\n    return this;\n};\n", "import $ from '../$';\nimport { isFunction, isString } from '../utils';\nimport './is';\nimport './map';\n$.fn.filter = function (selector) {\n    if (isFunction(selector)) {\n        return this.map((index, element) => selector.call(element, index, element) ? element : undefined);\n    }\n    if (isString(selector)) {\n        return this.map((_, element) => $(element).is(selector) ? element : undefined);\n    }\n    const $selector = $(selector);\n    return this.map((_, element) => $selector.get().indexOf(element) > -1 ? element : undefined);\n};\n", "import $ from '../$';\nimport './eq';\n$.fn.first = function () {\n    return this.eq(0);\n};\n", "import $ from '../$';\nimport contains from '../functions/contains';\nimport { isString } from '../utils';\nimport './find';\n$.fn.has = function (selector) {\n    const $targets = isString(selector) ? this.find(selector) : $(selector);\n    const { length } = $targets;\n    return this.map(function () {\n        for (let i = 0; i < length; i += 1) {\n            if (contains(this, $targets[i])) {\n                return this;\n            }\n        }\n        return;\n    });\n};\n", "import $ from '../$';\n$.fn.hasClass = function (className) {\n    return this[0].classList.contains(className);\n};\n", "import $ from '../$';\nimport './each';\n$.fn.hide = function () {\n    return this.each(function () {\n        this.style.display = 'none';\n    });\n};\n", "import $ from '../$';\nimport each from '../functions/each';\nimport map from '../functions/map';\nimport { isElement, isFunction, isUndefined, toElement } from '../utils';\nimport './each';\nimport './is';\neach(['val', 'html', 'text'], (nameIndex, name) => {\n    const props = {\n        0: 'value',\n        1: 'innerHTML',\n        2: 'textContent',\n    };\n    const propName = props[nameIndex];\n    function get($elements) {\n        // text() 获取所有元素的文本\n        if (nameIndex === 2) {\n            // @ts-ignore\n            return map($elements, (element) => toElement(element)[propName]).join('');\n        }\n        // 空集合时，val() 和 html() 返回 undefined\n        if (!$elements.length) {\n            return undefined;\n        }\n        // val() 和 html() 仅获取第一个元素的内容\n        const firstElement = $elements[0];\n        // select multiple 返回数组\n        if (nameIndex === 0 && $(firstElement).is('select[multiple]')) {\n            return map($(firstElement).find('option:checked'), (element) => element.value);\n        }\n        // @ts-ignore\n        return firstElement[propName];\n    }\n    function set(element, value) {\n        // text() 和 html() 赋值为 undefined，则保持原内容不变\n        // val() 赋值为 undefined 则赋值为空\n        if (isUndefined(value)) {\n            if (nameIndex !== 0) {\n                return;\n            }\n            value = '';\n        }\n        if (nameIndex === 1 && isElement(value)) {\n            value = value.outerHTML;\n        }\n        // @ts-ignore\n        element[propName] = value;\n    }\n    $.fn[name] = function (value) {\n        // 获取值\n        if (!arguments.length) {\n            return get(this);\n        }\n        // 设置值\n        return this.each((i, element) => {\n            const computedValue = isFunction(value)\n                ? value.call(element, i, get($(element)))\n                : value;\n            // value 是数组，则选中数组中的元素，反选不在数组中的元素\n            if (nameIndex === 0 && Array.isArray(computedValue)) {\n                // select[multiple]\n                if ($(element).is('select[multiple]')) {\n                    map($(element).find('option'), (option) => (option.selected =\n                        computedValue.indexOf(option.value) >\n                            -1));\n                }\n                // 其他 checkbox, radio 等元素\n                else {\n                    element.checked =\n                        computedValue.indexOf(element.value) > -1;\n                }\n            }\n            else {\n                set(element, computedValue);\n            }\n        });\n    };\n});\n", "import $ from '../$';\nimport { isString } from '../utils';\nimport './children';\nimport './eq';\nimport './get';\nimport './parent';\n$.fn.index = function (selector) {\n    if (!arguments.length) {\n        return this.eq(0).parent().children().get().indexOf(this[0]);\n    }\n    if (isString(selector)) {\n        return $(selector).get().indexOf(this[0]);\n    }\n    return this.get().indexOf($(selector)[0]);\n};\n", "import $ from '../$';\nimport './eq';\n$.fn.last = function () {\n    return this.eq(-1);\n};\n", "import $ from '../$';\nimport each from '../functions/each';\nimport dir from './utils/dir';\neach(['', 'All', 'Until'], (nameIndex, name) => {\n    $.fn[`next${name}`] = function (selector, filter) {\n        return dir(this, nameIndex, 'nextElementSibling', selector, filter);\n    };\n});\n", "import $ from '../$';\nimport './filter';\nimport './map';\n$.fn.not = function (selector) {\n    const $excludes = this.filter(selector);\n    return this.map((_, element) => $excludes.index(element) > -1 ? undefined : element);\n};\n", "import $ from '../$';\nimport './css';\nimport './map';\n/**\n * 返回最近的用于定位的父元素\n */\n$.fn.offsetParent = function () {\n    return this.map(function () {\n        let offsetParent = this.offsetParent;\n        while (offsetParent && $(offsetParent).css('position') === 'static') {\n            offsetParent = offsetParent.offsetParent;\n        }\n        return offsetParent || document.documentElement;\n    });\n};\n", "import $ from '../$';\nimport './on';\n$.fn.one = function (types, selector, data, callback) {\n    // @ts-ignore\n    return this.on(types, selector, data, callback, true);\n};\n", "import $ from '../$';\nimport each from '../functions/each';\nimport './get';\nimport dir from './utils/dir';\neach(['', 'All', 'Until'], (nameIndex, name) => {\n    $.fn[`prev${name}`] = function (selector, filter) {\n        // prevAll、prevUntil 需要把元素的顺序倒序处理，以便和 jQuery 的结果一致\n        const $nodes = !nameIndex ? this : $(this.get().reverse());\n        return dir($nodes, nameIndex, 'previousElementSibling', selector, filter);\n    };\n});\n", "import $ from '../$';\nimport each from '../functions/each';\nimport './each';\n$.fn.removeAttr = function (attributeName) {\n    const names = attributeName.split(' ').filter((name) => name);\n    return this.each(function () {\n        each(names, (_, name) => {\n            this.removeAttribute(name);\n        });\n    });\n};\n", "import $ from '../$';\nimport removeData from '../functions/removeData';\nimport './each';\n$.fn.removeData = function (name) {\n    return this.each(function () {\n        removeData(this, name);\n    });\n};\n", "import $ from '../$';\nimport './each';\n$.fn.removeProp = function (name) {\n    return this.each(function () {\n        try {\n            // @ts-ignore\n            delete this[name];\n        }\n        catch (e) { }\n    });\n};\n", "import $ from '../$';\nimport './before';\nimport './clone';\nimport './each';\nimport './remove';\nimport { isFunction, isString } from '../utils';\n$.fn.replaceWith = function (newContent) {\n    this.each((index, element) => {\n        let content = newContent;\n        if (isFunction(content)) {\n            content = content.call(element, index, element.innerHTML);\n        }\n        else if (index && !isString(content)) {\n            content = $(content).clone();\n        }\n        $(element).before(content);\n    });\n    return this.remove();\n};\n", "import $ from '../$';\nimport './clone';\nimport './get';\nimport './map';\nimport './replaceWith';\n$.fn.replaceAll = function (target) {\n    return $(target).map((index, element) => {\n        $(element).replaceWith(index ? this.clone() : this);\n        return this.get();\n    });\n};\n", "import $ from '../$';\nimport './each';\nimport './val';\n/**\n * 将表单元素的值组合成键值对数组\n * @returns {Array}\n */\n$.fn.serializeArray = function () {\n    const result = [];\n    this.each((_, element) => {\n        const elements = element instanceof HTMLFormElement ? element.elements : [element];\n        $(elements).each((_, element) => {\n            const $element = $(element);\n            const type = element.type;\n            const nodeName = element.nodeName.toLowerCase();\n            if (nodeName !== 'fieldset' &&\n                element.name &&\n                !element.disabled &&\n                ['input', 'select', 'textarea', 'keygen'].indexOf(nodeName) > -1 &&\n                ['submit', 'button', 'image', 'reset', 'file'].indexOf(type) === -1 &&\n                (['radio', 'checkbox'].indexOf(type) === -1 ||\n                    element.checked)) {\n                const value = $element.val();\n                const valueArr = Array.isArray(value) ? value : [value];\n                valueArr.forEach((value) => {\n                    result.push({\n                        name: element.name,\n                        value,\n                    });\n                });\n            }\n        });\n    });\n    return result;\n};\n", "import $ from '../$';\nimport param from '../functions/param';\nimport './serializeArray';\n$.fn.serialize = function () {\n    return param(this.serializeArray());\n};\n", "import $ from '../$';\nimport { getStyle } from '../utils';\nimport './each';\nconst elementDisplay = {};\n/**\n * 获取元素的初始 display 值，用于 .show() 方法\n * @param nodeName\n */\nfunction defaultDisplay(nodeName) {\n    let element;\n    let display;\n    if (!elementDisplay[nodeName]) {\n        element = document.createElement(nodeName);\n        document.body.appendChild(element);\n        display = getStyle(element, 'display');\n        element.parentNode.removeChild(element);\n        if (display === 'none') {\n            display = 'block';\n        }\n        elementDisplay[nodeName] = display;\n    }\n    return elementDisplay[nodeName];\n}\n/**\n * 显示指定元素\n * @returns {JQ}\n */\n$.fn.show = function () {\n    return this.each(function () {\n        if (this.style.display === 'none') {\n            this.style.display = '';\n        }\n        if (getStyle(this, 'display') === 'none') {\n            this.style.display = defaultDisplay(this.nodeName);\n        }\n    });\n};\n", "import $ from '../$';\nimport './add';\nimport './nextAll';\nimport './prevAll';\n/**\n * 取得同辈元素的集合\n * @param selector {String=}\n * @returns {JQ}\n */\n$.fn.siblings = function (selector) {\n    return this.prevAll(selector).add(this.nextAll(selector));\n};\n", "import $ from '../$';\nimport { getStyle } from '../utils';\nimport './each';\nimport './hide';\nimport './show';\n/**\n * 切换元素的显示状态\n */\n$.fn.toggle = function () {\n    return this.each(function () {\n        getStyle(this, 'display') === 'none' ? $(this).show() : $(this).hide();\n    });\n};\n", "import $ from 'mdui.jq/es/$';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/each';\n\ndeclare module 'mdui.jq/es/JQ' {\n  interface JQ<T = HTMLElement> {\n    /**\n     * 强制重绘当前元素\n     *\n     * @example\n```js\n$('.box').reflow();\n```\n     */\n    reflow(): this;\n  }\n}\n\n$.fn.reflow = function (this: JQ): JQ {\n  return this.each(function () {\n    return this.clientLeft;\n  });\n};\n", "import $ from 'mdui.jq/es/$';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport { isNumber } from 'mdui.jq/es/utils';\nimport 'mdui.jq/es/methods/each';\n\ndeclare module 'mdui.jq/es/JQ' {\n  interface JQ<T = HTMLElement> {\n    /**\n     * 设置当前元素的 transition-duration 属性\n     * @param duration 可以是带单位的值；若不带单位，则自动添加 `ms` 作为单位\n     * @example\n```js\n$('.box').transition('300ms');\n$('.box').transition(300);\n```\n     */\n    transition(duration: string | number): this;\n  }\n}\n\n$.fn.transition = function (this: JQ, duration: string | number): JQ {\n  if (isNumber(duration)) {\n    duration = `${duration}ms`;\n  }\n\n  return this.each(function () {\n    this.style.webkitTransitionDuration = duration as string;\n    this.style.transitionDuration = duration as string;\n  });\n};\n", "import $ from 'mdui.jq/es/$';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport each from 'mdui.jq/es/functions/each';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/off';\n\ndeclare module 'mdui.jq/es/JQ' {\n  interface JQ<T = HTMLElement> {\n    /**\n     * 在当前元素上添加 transitionend 事件回调\n     * @param callback 回调函数的参数为 `transitionend` 事件对象；`this` 指向当前元素\n     * @example\n```js\n$('.box').transitionEnd(function() {\n  alert('.box 元素的 transitionend 事件已触发');\n});\n```\n     */\n    transitionEnd(callback: (this: T, e: Event) => void): this;\n  }\n}\n\n$.fn.transitionEnd = function (\n  this: JQ,\n  callback: (this: HTMLElement, e: Event) => void,\n): JQ {\n  // eslint-disable-next-line @typescript-eslint/no-this-alias\n  const that = this;\n  const events = ['webkitTransitionEnd', 'transitionend'];\n\n  function fireCallback(this: Element | Document | Window, e: Event): void {\n    if (e.target !== this) {\n      return;\n    }\n\n    // @ts-ignore\n    callback.call(this, e);\n\n    each(events, (_, event) => {\n      that.off(event, fireCallback);\n    });\n  }\n\n  each(events, (_, event) => {\n    that.on(event, fireCallback);\n  });\n\n  return this;\n};\n", "import $ from 'mdui.jq/es/$';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/each';\n\ndeclare module 'mdui.jq/es/JQ' {\n  interface JQ<T = HTMLElement> {\n    /**\n     * 设置当前元素的 transform-origin 属性\n     * @param transformOrigin\n     * @example\n```js\n$('.box').transformOrigin('top center');\n```\n     */\n    transformOrigin(transformOrigin: string): this;\n  }\n}\n\n$.fn.transformOrigin = function (this: JQ, transformOrigin: string): JQ {\n  return this.each(function () {\n    this.style.webkitTransformOrigin = transformOrigin;\n    this.style.transformOrigin = transformOrigin;\n  });\n};\n", "import $ from 'mdui.jq/es/$';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/each';\n\ndeclare module 'mdui.jq/es/JQ' {\n  interface JQ<T = HTMLElement> {\n    /**\n     * 设置当前元素的 transform 属性\n     * @param transform\n     * @example\n```js\n$('.box').transform('rotate(90deg)');\n```\n     */\n    transform(transform: string): this;\n  }\n}\n\n$.fn.transform = function (this: JQ, transform: string): JQ {\n  return this.each(function () {\n    this.style.webkitTransform = transform;\n    this.style.transform = transform;\n  });\n};\n", "import PlainObject from 'mdui.jq/es/interfaces/PlainObject';\nimport data from 'mdui.jq/es/functions/data';\n\ntype TYPE_API_INIT = (\n  this: HTMLElement,\n  i: number,\n  element: HTMLElement,\n) => void;\n\n/**\n * CSS 选择器和初始化函数组成的对象\n */\nconst entries: PlainObject<TYPE_API_INIT> = {};\n\n/**\n * 注册并执行初始化函数\n * @param selector CSS 选择器\n * @param apiInit 初始化函数\n * @param i 元素索引\n * @param element 元素\n */\nfunction mutation(\n  selector: string,\n  apiInit: TYPE_API_INIT,\n  i: number,\n  element: HTMLElement,\n): void {\n  let selectors = data(element, '_mdui_mutation');\n\n  if (!selectors) {\n    selectors = [];\n    data(element, '_mdui_mutation', selectors);\n  }\n\n  if (selectors.indexOf(selector) === -1) {\n    selectors.push(selector);\n    apiInit.call(element, i, element);\n  }\n}\n\nexport { TYPE_API_INIT, entries, mutation };\n", "import $ from 'mdui.jq/es/$';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport each from 'mdui.jq/es/functions/each';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/is';\nimport { entries, mutation } from '../../utils/mutation';\n\ndeclare module 'mdui.jq/es/JQ' {\n  interface JQ<T = HTMLElement> {\n    /**\n     * 执行在当前元素及其子元素内注册的初始化函数\n     */\n    mutation(): this;\n  }\n}\n\n$.fn.mutation = function (this: JQ): JQ {\n  return this.each((i, element) => {\n    const $this = $(element);\n\n    each(entries, (selector: string, apiInit) => {\n      if ($this.is(selector)) {\n        mutation(selector, apiInit, i, element);\n      }\n\n      $this.find(selector).each((i, element) => {\n        mutation(selector, apiInit, i, element);\n      });\n    });\n  });\n};\n", "import $ from 'mdui.jq/es/$';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport { isUndefined } from 'mdui.jq/es/utils';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/css';\nimport 'mdui.jq/es/methods/appendTo';\nimport 'mdui.jq/es/methods/addClass';\nimport '../methods/reflow';\n\ndeclare module 'mdui.jq/es/interfaces/JQStatic' {\n  interface JQStatic {\n    /**\n     * 创建并显示遮罩，返回遮罩层的 JQ 对象\n     * @param zIndex 遮罩层的 `z-index` 值，默认为 `2000`\n     * @example\n```js\n$.showOverlay();\n```\n     * @example\n```js\n$.showOverlay(3000);\n```\n     */\n    showOverlay(zIndex?: number): JQ;\n  }\n}\n\n$.showOverlay = function (zIndex?: number): JQ {\n  let $overlay = $('.mdui-overlay');\n\n  if ($overlay.length) {\n    $overlay.data('_overlay_is_deleted', false);\n\n    if (!isUndefined(zIndex)) {\n      $overlay.css('z-index', zIndex);\n    }\n  } else {\n    if (isUndefined(zIndex)) {\n      zIndex = 2000;\n    }\n\n    $overlay = $('<div class=\"mdui-overlay\">')\n      .appendTo(document.body)\n      .reflow()\n      .css('z-index', zIndex);\n  }\n\n  let level = $overlay.data('_overlay_level') || 0;\n\n  return $overlay.data('_overlay_level', ++level).addClass('mdui-overlay-show');\n};\n", "import $ from 'mdui.jq/es/$';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/removeClass';\nimport 'mdui.jq/es/methods/remove';\nimport '../methods/transitionEnd';\n\ndeclare module 'mdui.jq/es/interfaces/JQStatic' {\n  interface JQStatic {\n    /**\n     * 隐藏遮罩层\n     *\n     * 如果调用了多次 $.showOverlay() 来显示遮罩层，则也需要调用相同次数的 $.hideOverlay() 才能隐藏遮罩层。可以通过传入参数 true 来强制隐藏遮罩层。\n     * @param force 是否强制隐藏遮罩\n     * @example\n```js\n$.hideOverlay();\n```\n     * @example\n```js\n$.hideOverlay(true);\n```\n     */\n    hideOverlay(force?: boolean): void;\n  }\n}\n\n$.hideOverlay = function (force = false): void {\n  const $overlay = $('.mdui-overlay');\n\n  if (!$overlay.length) {\n    return;\n  }\n\n  let level = force ? 1 : $overlay.data('_overlay_level');\n\n  if (level > 1) {\n    $overlay.data('_overlay_level', --level);\n    return;\n  }\n\n  $overlay\n    .data('_overlay_level', 0)\n    .removeClass('mdui-overlay-show')\n    .data('_overlay_is_deleted', true)\n    .transitionEnd(() => {\n      if ($overlay.data('_overlay_is_deleted')) {\n        $overlay.remove();\n      }\n    });\n};\n", "import $ from 'mdui.jq/es/$';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/width';\n\ndeclare module 'mdui.jq/es/interfaces/JQStatic' {\n  interface JQStatic {\n    /**\n     * 锁定屏页面，禁止页面滚动\n     * @example\n```js\n$.lockScreen();\n```\n     */\n    lockScreen(): void;\n  }\n}\n\n$.lockScreen = function (): void {\n  const $body = $('body');\n\n  // 不直接把 body 设为 box-sizing: border-box，避免污染全局样式\n  const newBodyWidth = $body.width();\n  let level = $body.data('_lockscreen_level') || 0;\n\n  $body\n    .addClass('mdui-locked')\n    .width(newBodyWidth)\n    .data('_lockscreen_level', ++level);\n};\n", "import $ from 'mdui.jq/es/$';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/removeClass';\nimport 'mdui.jq/es/methods/width';\n\ndeclare module 'mdui.jq/es/interfaces/JQStatic' {\n  interface JQStatic {\n    /**\n     * 解除页面锁定\n     *\n     * 如果调用了多次 $.lockScreen() 来显示遮罩层，则也需要调用相同次数的 $.unlockScreen() 才能隐藏遮罩层。可以通过传入参数 true 来强制隐藏遮罩层。\n     * @param force 是否强制解除锁定\n     * @example\n```js\n$.unlockScreen();\n```\n     * @example\n```js\n$.unlockScreen(true);\n```\n     */\n    unlockScreen(force?: boolean): void;\n  }\n}\n\n$.unlockScreen = function (force = false): void {\n  const $body = $('body');\n  let level = force ? 1 : $body.data('_lockscreen_level');\n\n  if (level > 1) {\n    $body.data('_lockscreen_level', --level);\n    return;\n  }\n\n  $body.data('_lockscreen_level', 0).removeClass('mdui-locked').width('');\n};\n", "import $ from 'mdui.jq/es/$';\nimport { isNull } from 'mdui.jq/es/utils';\n\ndeclare module 'mdui.jq/es/interfaces/JQStatic' {\n  interface JQStatic {\n    /**\n     * 函数节流\n     * @param fn 执行的函数\n     * @param delay 最多多少毫秒执行一次\n     * @example\n```js\n$.throttle(function () {\n  console.log('这个函数最多 100ms 执行一次');\n}, 100)\n```\n     */\n    throttle(fn: () => void, delay: number): () => void;\n  }\n}\n\n$.throttle = function (fn: () => void, delay = 16): () => void {\n  let timer: any = null;\n\n  return function (this: any, ...args: any): void {\n    if (isNull(timer)) {\n      timer = setTimeout(() => {\n        fn.apply(this, args);\n        timer = null;\n      }, delay);\n    }\n  };\n};\n", "import $ from 'mdui.jq/es/$';\nimport { isUndefined } from 'mdui.jq/es/utils';\nimport PlainObject from 'mdui.jq/es/interfaces/PlainObject';\n\ndeclare module 'mdui.jq/es/interfaces/JQStatic' {\n  interface JQStatic {\n    /**\n     * 生成一个全局唯一的 ID\n     * @param name 当该参数值对应的 guid 不存在时，会生成一个新的 guid，并返回；当该参数对应的 guid 已存在，则直接返回已有 guid\n     * @example\n```js\n$.guid();\n```\n     * @example\n```js\n$.guid('test');\n```\n     */\n    guid(name?: string): string;\n  }\n}\n\nconst GUID: PlainObject<string> = {};\n\n$.guid = function (name?: string): string {\n  if (!isUndefined(name) && !isUndefined(GUID[name])) {\n    return GUID[name];\n  }\n\n  function s4(): string {\n    return Math.floor((1 + Math.random()) * 0x10000)\n      .toString(16)\n      .substring(1);\n  }\n\n  const guid =\n    '_' +\n    s4() +\n    s4() +\n    '-' +\n    s4() +\n    '-' +\n    s4() +\n    '-' +\n    s4() +\n    '-' +\n    s4() +\n    s4() +\n    s4();\n\n  if (!isUndefined(name)) {\n    GUID[name] = guid;\n  }\n\n  return guid;\n};\n", "import $ from 'mdui.jq/es/$';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport PlainObject from 'mdui.jq/es/interfaces/PlainObject';\nimport 'mdui.jq/es/methods/trigger';\n\n/**\n * 触发组件上的事件\n * @param eventName 事件名\n * @param componentName 组件名\n * @param target 在该元素上触发事件\n * @param instance 组件实例\n * @param parameters 事件参数\n */\nfunction componentEvent(\n  eventName: string,\n  componentName: string,\n  target: HTMLElement | HTMLElement[] | JQ,\n  instance?: any,\n  parameters?: PlainObject,\n): void {\n  if (!parameters) {\n    parameters = {};\n  }\n\n  // @ts-ignore\n  parameters.inst = instance;\n\n  const fullEventName = `${eventName}.mdui.${componentName}`;\n\n  // jQuery 事件\n  // @ts-ignore\n  if (typeof jQuery !== 'undefined') {\n    // @ts-ignore\n    jQuery(target).trigger(fullEventName, parameters);\n  }\n\n  const $target = $(target);\n\n  // mdui.jq 事件\n  $target.trigger(fullEventName, parameters);\n\n  // 原生事件，供使用 addEventListener 监听\n  type EventParams = {\n    detail?: any;\n    bubbles: boolean;\n    cancelable: boolean;\n  };\n\n  const eventParams: EventParams = {\n    bubbles: true,\n    cancelable: true,\n    detail: parameters,\n  };\n\n  const eventObject: CustomEvent = new CustomEvent(fullEventName, eventParams);\n\n  // @ts-ignore\n  eventObject._detail = parameters;\n\n  $target[0].dispatchEvent(eventObject);\n}\n\nexport { componentEvent };\n", "import $ from 'mdui.jq/es/$';\nimport 'mdui.jq/es/methods/each';\nimport mdui from '../mdui';\nimport '../jq_extends/methods/mutation';\nimport { isUndefined } from 'mdui.jq/es/utils';\nimport { TYPE_API_INIT, entries, mutation } from '../utils/mutation';\n\ndeclare module '../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 传入了两个参数时，注册并执行初始化函数\n     *\n     * 没有传入参数时，执行初始化\n     * @param selector CSS 选择器\n     * @param apiInit 初始化函数\n     * @example\n```js\nmdui.mutation();\n```\n     * @example\n```js\nmdui.mutation();\n```\n     */\n    mutation(selector?: string, apiInit?: TYPE_API_INIT): void;\n  }\n}\n\nmdui.mutation = function (selector?: string, apiInit?: TYPE_API_INIT): void {\n  if (isUndefined(selector) || isUndefined(apiInit)) {\n    $(document).mutation();\n    return;\n  }\n\n  entries[selector] = apiInit!;\n  $(selector).each((i, element) => mutation(selector, apiInit, i, element));\n};\n", "import $ from 'mdui.jq/es/$';\nimport extend from 'mdui.jq/es/functions/extend';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/removeClass';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport { isNumber } from 'mdui.jq/es/utils';\nimport mdui from '../../mdui';\nimport '../../jq_extends/methods/transitionEnd';\nimport { componentEvent } from '../../utils/componentEvent';\nimport { $window } from '../../utils/dom';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * Headroom 插件\n     *\n     * 请通过 `new mdui.Headroom()` 调用\n     */\n    Headroom: {\n      /**\n       * 实例化 Headroom 组件\n       * @param selector CSS 选择器、或 DOM 元素、或 JQ 对象\n       * @param options 配置参数\n       */\n      new (\n        selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n        options?: OPTIONS,\n      ): Headroom;\n    };\n  }\n}\n\ntype TOLERANCE = {\n  /**\n   * 滚动条向下滚动多少距离开始隐藏或显示元素\n   */\n  down: number;\n\n  /**\n   * 滚动条向上滚动多少距离开始隐藏或显示元素\n   */\n  up: number;\n};\n\ntype OPTIONS = {\n  /**\n   * 滚动条滚动多少距离开始隐藏或显示元素\n   */\n  tolerance?: TOLERANCE | number;\n\n  /**\n   * 在页面顶部多少距离内滚动不会隐藏元素\n   */\n  offset?: number;\n\n  /**\n   * 初始化时添加的类\n   */\n  initialClass?: string;\n\n  /**\n   * 元素固定时添加的类\n   */\n  pinnedClass?: string;\n\n  /**\n   * 元素隐藏时添加的类\n   */\n  unpinnedClass?: string;\n};\n\ntype STATE = 'pinning' | 'pinned' | 'unpinning' | 'unpinned';\ntype EVENT = 'pin' | 'pinned' | 'unpin' | 'unpinned';\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  tolerance: 5,\n  offset: 0,\n  initialClass: 'mdui-headroom',\n  pinnedClass: 'mdui-headroom-pinned-top',\n  unpinnedClass: 'mdui-headroom-unpinned-top',\n};\n\nclass Headroom {\n  /**\n   * headroom 元素的 JQ 对象\n   */\n  public $element: JQ;\n\n  /**\n   * 配置参数\n   */\n  public options: OPTIONS = extend({}, DEFAULT_OPTIONS);\n\n  /**\n   * 当前 headroom 的状态\n   */\n  private state: STATE = 'pinned';\n\n  /**\n   * 当前是否启用\n   */\n  private isEnable = false;\n\n  /**\n   * 上次滚动后，垂直方向的距离\n   */\n  private lastScrollY = 0;\n\n  /**\n   * AnimationFrame ID\n   */\n  private rafId = 0;\n\n  public constructor(\n    selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    options: OPTIONS = {},\n  ) {\n    this.$element = $(selector).first();\n\n    extend(this.options, options);\n\n    // tolerance 参数若为数值，转换为对象\n    const tolerance = this.options.tolerance;\n    if (isNumber(tolerance)) {\n      this.options.tolerance = {\n        down: tolerance,\n        up: tolerance,\n      };\n    }\n\n    this.enable();\n  }\n\n  /**\n   * 滚动时的处理\n   */\n  private onScroll(): void {\n    this.rafId = window.requestAnimationFrame(() => {\n      const currentScrollY = window.pageYOffset;\n      const direction = currentScrollY > this.lastScrollY ? 'down' : 'up';\n      const tolerance = (this.options.tolerance as TOLERANCE)[direction];\n      const scrolled = Math.abs(currentScrollY - this.lastScrollY);\n      const toleranceExceeded = scrolled >= tolerance;\n\n      if (\n        currentScrollY > this.lastScrollY &&\n        currentScrollY >= this.options.offset! &&\n        toleranceExceeded\n      ) {\n        this.unpin();\n      } else if (\n        (currentScrollY < this.lastScrollY && toleranceExceeded) ||\n        currentScrollY <= this.options.offset!\n      ) {\n        this.pin();\n      }\n\n      this.lastScrollY = currentScrollY;\n    });\n  }\n\n  /**\n   * 触发组件事件\n   * @param name\n   */\n  private triggerEvent(name: EVENT): void {\n    componentEvent(name, 'headroom', this.$element, this);\n  }\n\n  /**\n   * 动画结束的回调\n   */\n  private transitionEnd(): void {\n    if (this.state === 'pinning') {\n      this.state = 'pinned';\n      this.triggerEvent('pinned');\n    }\n\n    if (this.state === 'unpinning') {\n      this.state = 'unpinned';\n      this.triggerEvent('unpinned');\n    }\n  }\n\n  /**\n   * 使元素固定住\n   */\n  public pin(): void {\n    if (\n      this.state === 'pinning' ||\n      this.state === 'pinned' ||\n      !this.$element.hasClass(this.options.initialClass!)\n    ) {\n      return;\n    }\n\n    this.triggerEvent('pin');\n    this.state = 'pinning';\n    this.$element\n      .removeClass(this.options.unpinnedClass)\n      .addClass(this.options.pinnedClass!)\n      .transitionEnd(() => this.transitionEnd());\n  }\n\n  /**\n   * 使元素隐藏\n   */\n  public unpin(): void {\n    if (\n      this.state === 'unpinning' ||\n      this.state === 'unpinned' ||\n      !this.$element.hasClass(this.options.initialClass!)\n    ) {\n      return;\n    }\n\n    this.triggerEvent('unpin');\n    this.state = 'unpinning';\n    this.$element\n      .removeClass(this.options.pinnedClass)\n      .addClass(this.options.unpinnedClass!)\n      .transitionEnd(() => this.transitionEnd());\n  }\n\n  /**\n   * 启用 headroom 插件\n   */\n  public enable(): void {\n    if (this.isEnable) {\n      return;\n    }\n\n    this.isEnable = true;\n    this.state = 'pinned';\n    this.$element\n      .addClass(this.options.initialClass!)\n      .removeClass(this.options.pinnedClass)\n      .removeClass(this.options.unpinnedClass);\n    this.lastScrollY = window.pageYOffset;\n\n    $window.on('scroll', () => this.onScroll());\n  }\n\n  /**\n   * 禁用 headroom 插件\n   */\n  public disable(): void {\n    if (!this.isEnable) {\n      return;\n    }\n\n    this.isEnable = false;\n    this.$element\n      .removeClass(this.options.initialClass)\n      .removeClass(this.options.pinnedClass)\n      .removeClass(this.options.unpinnedClass);\n\n    $window.off('scroll', () => this.onScroll());\n    window.cancelAnimationFrame(this.rafId);\n  }\n\n  /**\n   * 获取当前状态。共包含四种状态：`pinning`、`pinned`、`unpinning`、`unpinned`\n   */\n  public getState(): STATE {\n    return this.state;\n  }\n}\n\nmdui.Headroom = Headroom;\n", "import $ from 'mdui.jq/es/$';\n\nconst $document = $(document);\nconst $window = $(window);\nconst $body = $('body');\n\nexport { $document, $window, $body };\n", "import $ from 'mdui.jq/es/$';\nimport 'mdui.jq/es/methods/attr';\nimport PlainObject from 'mdui.jq/es/interfaces/PlainObject';\n\n/**\n * 解析 DATA API 参数\n * @param element 元素\n * @param name 属性名\n */\nfunction parseOptions(element: HTMLElement, name: string): PlainObject {\n  const attr = $(element).attr(name);\n\n  if (!attr) {\n    return {};\n  }\n\n  return new Function(\n    '',\n    `var json = ${attr}; return JSON.parse(JSON.stringify(json));`,\n  )();\n}\n\nexport { parseOptions };\n", "import $ from 'mdui.jq/es/$';\nimport mdui from '../../mdui';\nimport '../../global/mutation';\nimport { parseOptions } from '../../utils/parseOptions';\nimport './index';\n\nconst customAttr = 'mdui-headroom';\n\n$(() => {\n  mdui.mutation(`[${customAttr}]`, function () {\n    new mdui.Headroom(this, parseOptions(this, customAttr));\n  });\n});\n", "import $ from 'mdui.jq/es/$';\nimport extend from 'mdui.jq/es/functions/extend';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/children';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/eq';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/height';\nimport 'mdui.jq/es/methods/is';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/parent';\nimport 'mdui.jq/es/methods/parents';\nimport 'mdui.jq/es/methods/removeClass';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport { isNumber } from 'mdui.jq/es/utils';\nimport '../../jq_extends/methods/reflow';\nimport '../../jq_extends/methods/transition';\nimport '../../jq_extends/methods/transitionEnd';\nimport { componentEvent } from '../../utils/componentEvent';\n\ntype OPTIONS = {\n  /**\n   * 是否启用手风琴效果\n   * 为 `true` 时，最多只能有一个面板项处于打开状态，打开一个面板项时会关闭其他面板项\n   * 为 `false` 时，可同时打开多个面板项\n   */\n  accordion?: boolean;\n};\n\ntype EVENT = 'open' | 'opened' | 'close' | 'closed';\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  accordion: false,\n};\n\nabstract class CollapseAbstract {\n  /**\n   * collapse 元素的 JQ 对象\n   */\n  public $element: JQ;\n\n  /**\n   * 配置参数\n   */\n  public options: OPTIONS = extend({}, DEFAULT_OPTIONS);\n\n  /**\n   * item 的 class 名\n   */\n  private classItem: string;\n\n  /**\n   * 打开状态的 item 的 class 名\n   */\n  private classItemOpen: string;\n\n  /**\n   * item-header 的 class 名\n   */\n  private classHeader: string;\n\n  /**\n   * item-body 的 class 名\n   */\n  private classBody: string;\n\n  /**\n   * 获取继承的组件名称\n   */\n  protected abstract getNamespace(): string;\n\n  public constructor(\n    selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    options: OPTIONS = {},\n  ) {\n    // CSS 类名\n    const classPrefix = `mdui-${this.getNamespace()}-item`;\n    this.classItem = classPrefix;\n    this.classItemOpen = `${classPrefix}-open`;\n    this.classHeader = `${classPrefix}-header`;\n    this.classBody = `${classPrefix}-body`;\n\n    this.$element = $(selector).first();\n\n    extend(this.options, options);\n\n    this.bindEvent();\n  }\n\n  /**\n   * 绑定事件\n   */\n  private bindEvent(): void {\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const that = this;\n\n    // 点击 header 时，打开/关闭 item\n    this.$element.on('click', `.${this.classHeader}`, function () {\n      const $header = $(this as HTMLElement);\n      const $item = $header.parent();\n      const $items = that.getItems();\n\n      $items.each((_, item) => {\n        if ($item.is(item)) {\n          that.toggle(item);\n        }\n      });\n    });\n\n    // 点击关闭按钮时，关闭 item\n    this.$element.on(\n      'click',\n      `[mdui-${this.getNamespace()}-item-close]`,\n      function () {\n        const $target = $(this as HTMLElement);\n        const $item = $target.parents(`.${that.classItem}`).first();\n\n        that.close($item);\n      },\n    );\n  }\n\n  /**\n   * 指定 item 是否处于打开状态\n   * @param $item\n   */\n  private isOpen($item: JQ): boolean {\n    return $item.hasClass(this.classItemOpen);\n  }\n\n  /**\n   * 获取所有 item\n   */\n  private getItems(): JQ {\n    return this.$element.children(`.${this.classItem}`);\n  }\n\n  /**\n   * 获取指定 item\n   * @param item\n   */\n  private getItem(\n    item: number | Selector | HTMLElement | ArrayLike<HTMLElement>,\n  ): JQ {\n    if (isNumber(item)) {\n      return this.getItems().eq(item);\n    }\n\n    return $(item).first();\n  }\n\n  /**\n   * 触发组件事件\n   * @param name 事件名\n   * @param $item 事件触发的目标 item\n   */\n  private triggerEvent(name: EVENT, $item: JQ): void {\n    componentEvent(name, this.getNamespace(), $item, this);\n  }\n\n  /**\n   * 动画结束回调\n   * @param $content body 元素\n   * @param $item item 元素\n   */\n  private transitionEnd($content: JQ, $item: JQ): void {\n    if (this.isOpen($item)) {\n      $content.transition(0).height('auto').reflow().transition('');\n\n      this.triggerEvent('opened', $item);\n    } else {\n      $content.height('');\n\n      this.triggerEvent('closed', $item);\n    }\n  }\n\n  /**\n   * 打开指定面板项\n   * @param item 面板项的索引号、或 CSS 选择器、或 DOM 元素、或 JQ 对象\n   */\n  public open(\n    item: number | Selector | HTMLElement | ArrayLike<HTMLElement>,\n  ): void {\n    const $item = this.getItem(item);\n\n    if (this.isOpen($item)) {\n      return;\n    }\n\n    // 关闭其他项\n    if (this.options.accordion) {\n      this.$element.children(`.${this.classItemOpen}`).each((_, element) => {\n        const $element = $(element);\n\n        if (!$element.is($item)) {\n          this.close($element);\n        }\n      });\n    }\n\n    const $content = $item.children(`.${this.classBody}`);\n\n    $content\n      .height($content[0].scrollHeight)\n      .transitionEnd(() => this.transitionEnd($content, $item));\n\n    this.triggerEvent('open', $item);\n\n    $item.addClass(this.classItemOpen);\n  }\n\n  /**\n   * 关闭指定面板项\n   * @param item 面板项的索引号、或 CSS 选择器、或 DOM 元素、或 JQ 对象\n   */\n  public close(\n    item: number | Selector | HTMLElement | ArrayLike<HTMLElement>,\n  ): void {\n    const $item = this.getItem(item);\n\n    if (!this.isOpen($item)) {\n      return;\n    }\n\n    const $content = $item.children(`.${this.classBody}`);\n\n    this.triggerEvent('close', $item);\n\n    $item.removeClass(this.classItemOpen);\n\n    $content\n      .transition(0)\n      .height($content[0].scrollHeight)\n      .reflow()\n      .transition('')\n      .height('')\n      .transitionEnd(() => this.transitionEnd($content, $item));\n  }\n\n  /**\n   * 切换指定面板项的打开状态\n   * @param item 面板项的索引号、或 CSS 选择器、或 DOM 元素、或 JQ 对象\n   */\n  public toggle(\n    item: number | Selector | HTMLElement | ArrayLike<HTMLElement>,\n  ): void {\n    const $item = this.getItem(item);\n\n    this.isOpen($item) ? this.close($item) : this.open($item);\n  }\n\n  /**\n   * 打开所有面板项\n   */\n  public openAll(): void {\n    this.getItems().each((_, element) => this.open(element));\n  }\n\n  /**\n   * 关闭所有面板项\n   */\n  public closeAll(): void {\n    this.getItems().each((_, element) => this.close(element));\n  }\n}\n\nexport { OPTIONS, CollapseAbstract };\n", "import Selector from 'mdui.jq/es/types/Selector';\nimport mdui from '../../mdui';\nimport { CollapseAbstract, OPTIONS } from './collapseAbstract';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 折叠内容块组件\n     *\n     * 请通过 `new mdui.Collapse()` 调用\n     */\n    Collapse: {\n      /**\n       * 实例化 Collapse 组件\n       * @param selector CSS 选择器或 DOM 元素\n       * @param options 配置参数\n       */\n      new (\n        selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n        options?: OPTIONS,\n      ): Collapse;\n    };\n  }\n}\n\nclass Collapse extends CollapseAbstract {\n  protected getNamespace(): string {\n    return 'collapse';\n  }\n}\n\nmdui.Collapse = Collapse;\n", "import $ from 'mdui.jq/es/$';\nimport mdui from '../../mdui';\nimport '../../global/mutation';\nimport { parseOptions } from '../../utils/parseOptions';\nimport './index';\n\nconst customAttr = 'mdui-collapse';\n\n$(() => {\n  mdui.mutation(`[${customAttr}]`, function () {\n    new mdui.Collapse(this, parseOptions(this, customAttr));\n  });\n});\n", "import Selector from 'mdui.jq/es/types/Selector';\nimport mdui from '../../mdui';\nimport { CollapseAbstract, OPTIONS } from '../collapse/collapseAbstract';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 可扩展面板组件\n     *\n     * 请通过 `new mdui.Panel()` 调用\n     */\n    Panel: {\n      /**\n       * 实例化 Panel 组件\n       * @param selector CSS 选择器或 DOM 元素\n       * @param options 配置参数\n       */\n      new (\n        selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n        options?: OPTIONS,\n      ): Panel;\n    };\n  }\n}\n\nclass Panel extends CollapseAbstract {\n  protected getNamespace(): string {\n    return 'panel';\n  }\n}\n\nmdui.Panel = Panel;\n", "import $ from 'mdui.jq/es/$';\nimport mdui from '../../mdui';\nimport '../../global/mutation';\nimport { parseOptions } from '../../utils/parseOptions';\nimport './index';\n\nconst customAttr = 'mdui-panel';\n\n$(() => {\n  mdui.mutation(`[${customAttr}]`, function () {\n    new mdui.Panel(this, parseOptions(this, customAttr));\n  });\n});\n", "import $ from 'mdui.jq/es/$';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/add';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/eq';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/prependTo';\nimport 'mdui.jq/es/methods/remove';\nimport 'mdui.jq/es/methods/removeClass';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport { isUndefined } from 'mdui.jq/es/utils';\nimport mdui from '../../mdui';\nimport '../../global/mutation';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 动态修改了表格后，需要调用该方法重新初始化表格。\n     *\n     * 若传入了参数，则只初始化该参数对应的表格。若没有传入参数，则重新初始化所有表格。\n     * @param selector CSS 选择器、或 DOM 元素、或 DOM 元素组成的数组、或 JQ 对象\n     */\n    updateTables(\n      selector?: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    ): void;\n  }\n}\n\nclass Table {\n  /**\n   * table 元素的 JQ 对象\n   */\n  public $element: JQ;\n\n  /**\n   * 表头 tr 元素\n   */\n  private $thRow: JQ = $();\n\n  /**\n   * 表格 body 中的 tr 元素\n   */\n  private $tdRows: JQ = $();\n\n  /**\n   * 表头的 checkbox 元素\n   */\n  private $thCheckbox: JQ<HTMLInputElement> = $();\n\n  /**\n   * 表格 body 中的 checkbox 元素\n   */\n  private $tdCheckboxs: JQ<HTMLInputElement> = $();\n\n  /**\n   * 表格行是否可选择\n   */\n  private selectable = false;\n\n  /**\n   * 已选中的行数\n   */\n  private selectedRow = 0;\n\n  public constructor(\n    selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n  ) {\n    this.$element = $(selector).first();\n    this.init();\n  }\n\n  /**\n   * 初始化表格\n   */\n  public init(): void {\n    this.$thRow = this.$element.find('thead tr');\n    this.$tdRows = this.$element.find('tbody tr');\n    this.selectable = this.$element.hasClass('mdui-table-selectable');\n\n    this.updateThCheckbox();\n    this.updateTdCheckbox();\n    this.updateNumericCol();\n  }\n\n  /**\n   * 生成 checkbox 的 HTML 结构\n   * @param tag 标签名\n   */\n  private createCheckboxHTML(tag: string): string {\n    return (\n      `<${tag} class=\"mdui-table-cell-checkbox\">` +\n      '<label class=\"mdui-checkbox\">' +\n      '<input type=\"checkbox\"/>' +\n      '<i class=\"mdui-checkbox-icon\"></i>' +\n      '</label>' +\n      `</${tag}>`\n    );\n  }\n\n  /**\n   * 更新表头 checkbox 的状态\n   */\n  private updateThCheckboxStatus(): void {\n    const checkbox = this.$thCheckbox[0];\n    const selectedRow = this.selectedRow;\n    const tdRowsLength = this.$tdRows.length;\n\n    checkbox.checked = selectedRow === tdRowsLength;\n    checkbox.indeterminate = !!selectedRow && selectedRow !== tdRowsLength;\n  }\n\n  /**\n   * 更新表格行的 checkbox\n   */\n  private updateTdCheckbox(): void {\n    const rowSelectedClass = 'mdui-table-row-selected';\n\n    this.$tdRows.each((_, row) => {\n      const $row = $(row);\n\n      // 移除旧的 checkbox\n      $row.find('.mdui-table-cell-checkbox').remove();\n\n      if (!this.selectable) {\n        return;\n      }\n\n      // 创建 DOM\n      const $checkbox = $(this.createCheckboxHTML('td'))\n        .prependTo($row)\n        .find('input[type=\"checkbox\"]') as JQ<HTMLInputElement>;\n\n      // 默认选中的行\n      if ($row.hasClass(rowSelectedClass)) {\n        $checkbox[0].checked = true;\n        this.selectedRow++;\n      }\n\n      this.updateThCheckboxStatus();\n\n      // 绑定事件\n      $checkbox.on('change', () => {\n        if ($checkbox[0].checked) {\n          $row.addClass(rowSelectedClass);\n          this.selectedRow++;\n        } else {\n          $row.removeClass(rowSelectedClass);\n          this.selectedRow--;\n        }\n\n        this.updateThCheckboxStatus();\n      });\n\n      this.$tdCheckboxs = this.$tdCheckboxs.add($checkbox);\n    });\n  }\n\n  /**\n   * 更新表头的 checkbox\n   */\n  private updateThCheckbox(): void {\n    // 移除旧的 checkbox\n    this.$thRow.find('.mdui-table-cell-checkbox').remove();\n\n    if (!this.selectable) {\n      return;\n    }\n\n    this.$thCheckbox = $(this.createCheckboxHTML('th'))\n      .prependTo(this.$thRow)\n      .find('input[type=\"checkbox\"]')\n      .on('change', () => {\n        const isCheckedAll = this.$thCheckbox[0].checked;\n        this.selectedRow = isCheckedAll ? this.$tdRows.length : 0;\n\n        this.$tdCheckboxs.each((_, checkbox) => {\n          checkbox.checked = isCheckedAll;\n        });\n\n        this.$tdRows.each((_, row) => {\n          isCheckedAll\n            ? $(row).addClass('mdui-table-row-selected')\n            : $(row).removeClass('mdui-table-row-selected');\n        });\n      }) as JQ<HTMLInputElement>;\n  }\n\n  /**\n   * 更新数值列\n   */\n  private updateNumericCol(): void {\n    const numericClass = 'mdui-table-col-numeric';\n\n    this.$thRow.find('th').each((i, th) => {\n      const isNumericCol = $(th).hasClass(numericClass);\n\n      this.$tdRows.each((_, row) => {\n        const $td = $(row).find('td').eq(i);\n\n        isNumericCol\n          ? $td.addClass(numericClass)\n          : $td.removeClass(numericClass);\n      });\n    });\n  }\n}\n\nconst dataName = '_mdui_table';\n\n$(() => {\n  mdui.mutation('.mdui-table', function () {\n    const $element = $(this);\n\n    if (!$element.data(dataName)) {\n      $element.data(dataName, new Table($element));\n    }\n  });\n});\n\nmdui.updateTables = function (\n  selector?: Selector | HTMLElement | ArrayLike<HTMLElement>,\n): void {\n  const $elements = isUndefined(selector) ? $('.mdui-table') : $(selector);\n\n  $elements.each((_, element) => {\n    const $element = $(element);\n    const instance = $element.data(dataName);\n\n    if (instance) {\n      instance.init();\n    } else {\n      $element.data(dataName, new Table($element));\n    }\n  });\n};\n", "/**\n * touch 事件后的 500ms 内禁用 mousedown 事件\n *\n * 不支持触控的屏幕上事件顺序为 mousedown -> mouseup -> click\n * 支持触控的屏幕上事件顺序为 touchstart -> touchend -> mousedown -> mouseup -> click\n *\n * 在每一个事件中都使用 TouchHandler.isAllow(event) 判断事件是否可执行\n * 在 touchstart 和 touchmove、touchend、touchcancel\n *\n * (function () {\n *   $document\n *     .on(start, function (e) {\n *       if (!isAllow(e)) {\n *         return;\n *       }\n *       register(e);\n *       console.log(e.type);\n *     })\n *     .on(move, function (e) {\n *       if (!isAllow(e)) {\n *         return;\n *       }\n *       console.log(e.type);\n *     })\n *     .on(end, function (e) {\n *       if (!isAllow(e)) {\n *         return;\n *       }\n *       console.log(e.type);\n *     })\n *     .on(unlock, register);\n * })();\n */\n\nconst startEvent = 'touchstart mousedown';\nconst moveEvent = 'touchmove mousemove';\nconst endEvent = 'touchend mouseup';\nconst cancelEvent = 'touchcancel mouseleave';\nconst unlockEvent = 'touchend touchmove touchcancel';\n\nlet touches = 0;\n\n/**\n * 该事件是否被允许，在执行事件前调用该方法判断事件是否可以执行\n * 若已触发 touch 事件，则阻止之后的鼠标事件\n * @param event\n */\nfunction isAllow(event: Event): boolean {\n  return !(\n    touches &&\n    [\n      'mousedown',\n      'mouseup',\n      'mousemove',\n      'click',\n      'mouseover',\n      'mouseout',\n      'mouseenter',\n      'mouseleave',\n    ].indexOf(event.type) > -1\n  );\n}\n\n/**\n * 在 touchstart 和 touchmove、touchend、touchcancel 事件中调用该方法注册事件\n * @param event\n */\nfunction register(event: Event): void {\n  if (event.type === 'touchstart') {\n    // 触发了 touch 事件\n    touches += 1;\n  } else if (\n    ['touchmove', 'touchend', 'touchcancel'].indexOf(event.type) > -1\n  ) {\n    // touch 事件结束 500ms 后解除对鼠标事件的阻止\n    setTimeout(function () {\n      if (touches) {\n        touches -= 1;\n      }\n    }, 500);\n  }\n}\n\nexport {\n  startEvent,\n  moveEvent,\n  endEvent,\n  cancelEvent,\n  unlockEvent,\n  isAllow,\n  register,\n};\n", "/**\n * Inspired by https://github.com/nolimits4web/Framework7/blob/master/src/js/fast-clicks.js\n * https://github.com/nolimits4web/Framework7/blob/master/LICENSE\n *\n * Inspired by https://github.com/fians/Waves\n */\n\nimport $ from 'mdui.jq/es/$';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/attr';\nimport 'mdui.jq/es/methods/children';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/innerHeight';\nimport 'mdui.jq/es/methods/innerWidth';\nimport 'mdui.jq/es/methods/off';\nimport 'mdui.jq/es/methods/offset';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/parents';\nimport 'mdui.jq/es/methods/prependTo';\nimport 'mdui.jq/es/methods/prop';\nimport 'mdui.jq/es/methods/remove';\nimport { isUndefined } from 'mdui.jq/es/utils';\nimport '../../jq_extends/methods/reflow';\nimport '../../jq_extends/methods/transform';\nimport '../../jq_extends/methods/transitionEnd';\nimport { $document } from '../../utils/dom';\nimport {\n  cancelEvent,\n  endEvent,\n  isAllow,\n  moveEvent,\n  register,\n  startEvent,\n  unlockEvent,\n} from '../../utils/touchHandler';\n\n/**\n * 显示涟漪动画\n * @param event\n * @param $ripple\n */\nfunction show(event: Event, $ripple: JQ): void {\n  // 鼠标右键不产生涟漪\n  if (event instanceof MouseEvent && event.button === 2) {\n    return;\n  }\n\n  // 点击位置坐标\n  const touchPosition =\n    typeof TouchEvent !== 'undefined' &&\n    event instanceof TouchEvent &&\n    event.touches.length\n      ? event.touches[0]\n      : (event as MouseEvent);\n\n  const touchStartX = touchPosition.pageX;\n  const touchStartY = touchPosition.pageY;\n\n  // 涟漪位置\n  const offset = $ripple.offset();\n  const height = $ripple.innerHeight();\n  const width = $ripple.innerWidth();\n  const center = {\n    x: touchStartX - offset.left,\n    y: touchStartY - offset.top,\n  };\n  const diameter = Math.max(\n    Math.pow(Math.pow(height, 2) + Math.pow(width, 2), 0.5),\n    48,\n  );\n\n  // 涟漪扩散动画\n  const translate =\n    `translate3d(${-center.x + width / 2}px,` +\n    `${-center.y + height / 2}px, 0) scale(1)`;\n\n  // 涟漪的 DOM 结构，并缓存动画效果\n  $(\n    `<div class=\"mdui-ripple-wave\" ` +\n      `style=\"width:${diameter}px;height:${diameter}px;` +\n      `margin-top:-${diameter / 2}px;margin-left:-${diameter / 2}px;` +\n      `left:${center.x}px;top:${center.y}px;\"></div>`,\n  )\n    .data('_ripple_wave_translate', translate)\n    .prependTo($ripple)\n    .reflow()\n    .transform(translate);\n}\n\n/**\n * 隐藏并移除涟漪\n * @param $wave\n */\nfunction removeRipple($wave: JQ): void {\n  if (!$wave.length || $wave.data('_ripple_wave_removed')) {\n    return;\n  }\n\n  $wave.data('_ripple_wave_removed', true);\n\n  let removeTimer = setTimeout(() => $wave.remove(), 400);\n  const translate = $wave.data('_ripple_wave_translate');\n\n  $wave\n    .addClass('mdui-ripple-wave-fill')\n    .transform(translate.replace('scale(1)', 'scale(1.01)'))\n    .transitionEnd(() => {\n      clearTimeout(removeTimer);\n\n      $wave\n        .addClass('mdui-ripple-wave-out')\n        .transform(translate.replace('scale(1)', 'scale(1.01)'));\n\n      removeTimer = setTimeout(() => $wave.remove(), 700);\n\n      setTimeout(() => {\n        $wave.transitionEnd(() => {\n          clearTimeout(removeTimer);\n          $wave.remove();\n        });\n      }, 0);\n    });\n}\n\n/**\n * 隐藏涟漪动画\n * @param this\n */\nfunction hide(this: any): void {\n  const $ripple = $(this as HTMLElement);\n\n  $ripple.children('.mdui-ripple-wave').each((_, wave) => {\n    removeRipple($(wave));\n  });\n\n  $ripple.off(`${moveEvent} ${endEvent} ${cancelEvent}`, hide);\n}\n\n/**\n * 显示涟漪，并绑定 touchend 等事件\n * @param event\n */\nfunction showRipple(event: Event): void {\n  if (!isAllow(event)) {\n    return;\n  }\n\n  register(event);\n\n  // Chrome 59 点击滚动条时，会在 document 上触发事件\n  if (event.target === document) {\n    return;\n  }\n\n  const $target = $(event.target as HTMLElement);\n\n  // 获取含 .mdui-ripple 类的元素\n  const $ripple = $target.hasClass('mdui-ripple')\n    ? $target\n    : $target.parents('.mdui-ripple').first();\n\n  if (!$ripple.length) {\n    return;\n  }\n\n  // 禁用状态的元素上不产生涟漪效果\n  if ($ripple.prop('disabled') || !isUndefined($ripple.attr('disabled'))) {\n    return;\n  }\n\n  if (event.type === 'touchstart') {\n    let hidden = false;\n\n    // touchstart 触发指定时间后开始涟漪动画，避免手指滑动时也触发涟漪\n    let timer = setTimeout(() => {\n      timer = 0;\n      show(event, $ripple);\n    }, 200);\n\n    const hideRipple = (): void => {\n      // 如果手指没有移动，且涟漪动画还没有开始，则开始涟漪动画\n      if (timer) {\n        clearTimeout(timer);\n        timer = 0;\n        show(event, $ripple);\n      }\n\n      if (!hidden) {\n        hidden = true;\n        hide.call($ripple);\n      }\n    };\n\n    // 手指移动后，移除涟漪动画\n    const touchMove = (): void => {\n      if (timer) {\n        clearTimeout(timer);\n        timer = 0;\n      }\n\n      hideRipple();\n    };\n\n    $ripple.on('touchmove', touchMove).on('touchend touchcancel', hideRipple);\n  } else {\n    show(event, $ripple);\n    $ripple.on(`${moveEvent} ${endEvent} ${cancelEvent}`, hide);\n  }\n}\n\n$(() => {\n  $document.on(startEvent, showRipple).on(unlockEvent, register);\n});\n", "import $ from 'mdui.jq/es/$';\nimport extend from 'mdui.jq/es/functions/extend';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/appendTo';\nimport 'mdui.jq/es/methods/attr';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/is';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/outerHeight';\nimport 'mdui.jq/es/methods/parent';\nimport 'mdui.jq/es/methods/parents';\nimport 'mdui.jq/es/methods/remove';\nimport 'mdui.jq/es/methods/removeClass';\nimport 'mdui.jq/es/methods/text';\nimport 'mdui.jq/es/methods/trigger';\nimport 'mdui.jq/es/methods/val';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport { isUndefined } from 'mdui.jq/es/utils';\nimport mdui from '../../mdui';\nimport '../../global/mutation';\nimport { $document } from '../../utils/dom';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 动态修改了文本框后，需要调用该方法重新初始化文本框。\n     *\n     * 若传入了参数，则只初始化该参数对应的文本框。若没有传入参数，则重新初始化所有文本框。\n     * @param selector CSS 选择器、或 DOM 元素、或 DOM 元素组成的数组、或 JQ 对象\n     */\n    updateTextFields(\n      selector?: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    ): void;\n  }\n}\n\ntype INPUT_EVENT_DATA = {\n  reInit?: boolean;\n  domLoadedEvent?: boolean;\n};\n\nconst defaultData: INPUT_EVENT_DATA = {\n  reInit: false,\n  domLoadedEvent: false,\n};\n\n/**\n * 输入框事件\n * @param event\n * @param data\n */\nfunction inputEvent(event: Event, data: INPUT_EVENT_DATA = {}): void {\n  data = extend({}, defaultData, data);\n\n  const input = event.target as HTMLInputElement;\n  const $input = $(input);\n  const eventType = event.type;\n  const value = $input.val() as string;\n\n  // 文本框类型\n  const inputType = $input.attr('type') || '';\n  if (\n    ['checkbox', 'button', 'submit', 'range', 'radio', 'image'].indexOf(\n      inputType,\n    ) > -1\n  ) {\n    return;\n  }\n\n  const $textfield = $input.parent('.mdui-textfield');\n\n  // 输入框是否聚焦\n  if (eventType === 'focus') {\n    $textfield.addClass('mdui-textfield-focus');\n  }\n\n  if (eventType === 'blur') {\n    $textfield.removeClass('mdui-textfield-focus');\n  }\n\n  // 输入框是否为空\n  if (eventType === 'blur' || eventType === 'input') {\n    value\n      ? $textfield.addClass('mdui-textfield-not-empty')\n      : $textfield.removeClass('mdui-textfield-not-empty');\n  }\n\n  // 输入框是否禁用\n  input.disabled\n    ? $textfield.addClass('mdui-textfield-disabled')\n    : $textfield.removeClass('mdui-textfield-disabled');\n\n  // 表单验证\n  if (\n    (eventType === 'input' || eventType === 'blur') &&\n    !data.domLoadedEvent &&\n    input.validity\n  ) {\n    input.validity.valid\n      ? $textfield.removeClass('mdui-textfield-invalid-html5')\n      : $textfield.addClass('mdui-textfield-invalid-html5');\n  }\n\n  // textarea 高度自动调整\n  if ($input.is('textarea')) {\n    // IE bug：textarea 的值仅为多个换行，不含其他内容时，textarea 的高度不准确\n    //         此时，在计算高度前，在值的开头加入一个空格，计算完后，移除空格\n    const inputValue = value;\n    let hasExtraSpace = false;\n\n    if (inputValue.replace(/[\\r\\n]/g, '') === '') {\n      $input.val(' ' + inputValue);\n      hasExtraSpace = true;\n    }\n\n    // 设置 textarea 高度\n    $input.outerHeight('');\n    const height = $input.outerHeight();\n    const scrollHeight = input.scrollHeight;\n\n    if (scrollHeight > height) {\n      $input.outerHeight(scrollHeight);\n    }\n\n    // 计算完，还原 textarea 的值\n    if (hasExtraSpace) {\n      $input.val(inputValue);\n    }\n  }\n\n  // 实时字数统计\n  if (data.reInit) {\n    $textfield.find('.mdui-textfield-counter').remove();\n  }\n\n  const maxLength = $input.attr('maxlength');\n  if (maxLength) {\n    if (data.reInit || data.domLoadedEvent) {\n      $(\n        '<div class=\"mdui-textfield-counter\">' +\n          `<span class=\"mdui-textfield-counter-inputed\"></span> / ${maxLength}` +\n          '</div>',\n      ).appendTo($textfield);\n    }\n\n    $textfield\n      .find('.mdui-textfield-counter-inputed')\n      .text(value.length.toString());\n  }\n\n  // 含 帮助文本、错误提示、字数统计 时，增加文本框底部内边距\n  if (\n    $textfield.find('.mdui-textfield-helper').length ||\n    $textfield.find('.mdui-textfield-error').length ||\n    maxLength\n  ) {\n    $textfield.addClass('mdui-textfield-has-bottom');\n  }\n}\n\n$(() => {\n  // 绑定事件\n  $document.on(\n    'input focus blur',\n    '.mdui-textfield-input',\n    { useCapture: true },\n    inputEvent,\n  );\n\n  // 可展开文本框展开\n  $document.on(\n    'click',\n    '.mdui-textfield-expandable .mdui-textfield-icon',\n    function () {\n      $(this as HTMLElement)\n        .parents('.mdui-textfield')\n        .addClass('mdui-textfield-expanded')\n        .find('.mdui-textfield-input')[0]\n        .focus();\n    },\n  );\n\n  // 可展开文本框关闭\n  $document.on(\n    'click',\n    '.mdui-textfield-expanded .mdui-textfield-close',\n    function () {\n      $(this)\n        .parents('.mdui-textfield')\n        .removeClass('mdui-textfield-expanded')\n        .find('.mdui-textfield-input')\n        .val('');\n    },\n  );\n\n  /**\n   * 初始化文本框\n   */\n  mdui.mutation('.mdui-textfield', function () {\n    $(this).find('.mdui-textfield-input').trigger('input', {\n      domLoadedEvent: true,\n    });\n  });\n});\n\nmdui.updateTextFields = function (\n  selector?: Selector | HTMLElement | ArrayLike<HTMLElement>,\n): void {\n  const $elements = isUndefined(selector) ? $('.mdui-textfield') : $(selector);\n\n  $elements.each((_, element) => {\n    $(element).find('.mdui-textfield-input').trigger('input', {\n      reInit: true,\n    });\n  });\n};\n", "import $ from 'mdui.jq/es/$';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/append';\nimport 'mdui.jq/es/methods/attr';\nimport 'mdui.jq/es/methods/css';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/empty';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/parent';\nimport 'mdui.jq/es/methods/remove';\nimport 'mdui.jq/es/methods/removeClass';\nimport 'mdui.jq/es/methods/text';\nimport 'mdui.jq/es/methods/val';\nimport 'mdui.jq/es/methods/width';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport { isUndefined } from 'mdui.jq/es/utils';\nimport mdui from '../../mdui';\nimport { $document } from '../../utils/dom';\nimport {\n  endEvent,\n  isAllow,\n  register,\n  startEvent,\n  unlockEvent,\n} from '../../utils/touchHandler';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 动态修改了滑块后，需要调用该方法重新初始化滑块\n     *\n     * 若传入了参数，则只初始化该参数对应的滑块。若没有传入参数，则重新初始化所有滑块。\n     * @param selector CSS 选择器、或 DOM 元素、或 DOM 元素组成的数组、或 JQ 对象\n     */\n    updateSliders(\n      selector?: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    ): void;\n  }\n}\n\n/**\n * 滑块的值改变后修改滑块样式\n * @param $slider\n */\nfunction updateValueStyle($slider: JQ): void {\n  const data = $slider.data();\n\n  const $track = data._slider_$track;\n  const $fill = data._slider_$fill;\n  const $thumb = data._slider_$thumb;\n  const $input = data._slider_$input;\n  const min = data._slider_min;\n  const max = data._slider_max;\n  const isDisabled = data._slider_disabled;\n  const isDiscrete = data._slider_discrete;\n  const $thumbText = data._slider_$thumbText;\n  const value = $input.val();\n  const percent = ((value - min) / (max - min)) * 100;\n\n  $fill.width(`${percent}%`);\n  $track.width(`${100 - percent}%`);\n\n  if (isDisabled) {\n    $fill.css('padding-right', '6px');\n    $track.css('padding-left', '6px');\n  }\n\n  $thumb.css('left', `${percent}%`);\n\n  if (isDiscrete) {\n    $thumbText.text(value);\n  }\n\n  percent === 0\n    ? $slider.addClass('mdui-slider-zero')\n    : $slider.removeClass('mdui-slider-zero');\n}\n\n/**\n * 重新初始化滑块\n * @param $slider\n */\nfunction reInit($slider: JQ): void {\n  const $track = $('<div class=\"mdui-slider-track\"></div>');\n  const $fill = $('<div class=\"mdui-slider-fill\"></div>');\n  const $thumb = $('<div class=\"mdui-slider-thumb\"></div>');\n  const $input = $slider.find('input[type=\"range\"]') as JQ<HTMLInputElement>;\n  const isDisabled = $input[0].disabled;\n  const isDiscrete = $slider.hasClass('mdui-slider-discrete');\n\n  // 禁用状态\n  isDisabled\n    ? $slider.addClass('mdui-slider-disabled')\n    : $slider.removeClass('mdui-slider-disabled');\n\n  // 重新填充 HTML\n  $slider.find('.mdui-slider-track').remove();\n  $slider.find('.mdui-slider-fill').remove();\n  $slider.find('.mdui-slider-thumb').remove();\n  $slider.append($track).append($fill).append($thumb);\n\n  // 间续型滑块\n  let $thumbText = $();\n  if (isDiscrete) {\n    $thumbText = $('<span></span>');\n    $thumb.empty().append($thumbText);\n  }\n\n  $slider.data('_slider_$track', $track);\n  $slider.data('_slider_$fill', $fill);\n  $slider.data('_slider_$thumb', $thumb);\n  $slider.data('_slider_$input', $input);\n  $slider.data('_slider_min', $input.attr('min'));\n  $slider.data('_slider_max', $input.attr('max'));\n  $slider.data('_slider_disabled', isDisabled);\n  $slider.data('_slider_discrete', isDiscrete);\n  $slider.data('_slider_$thumbText', $thumbText);\n\n  // 设置默认值\n  updateValueStyle($slider);\n}\n\nconst rangeSelector = '.mdui-slider input[type=\"range\"]';\n\n$(() => {\n  // 滑块滑动事件\n  $document.on('input change', rangeSelector, function () {\n    const $slider = $(this).parent() as JQ<HTMLElement>;\n\n    updateValueStyle($slider);\n  });\n\n  // 开始触摸滑块事件\n  $document.on(startEvent, rangeSelector, function (event: Event) {\n    if (!isAllow(event)) {\n      return;\n    }\n\n    register(event);\n\n    if ((this as HTMLInputElement).disabled) {\n      return;\n    }\n\n    const $slider = $(this).parent() as JQ<HTMLElement>;\n\n    $slider.addClass('mdui-slider-focus');\n  });\n\n  // 结束触摸滑块事件\n  $document.on(endEvent, rangeSelector, function (event: Event) {\n    if (!isAllow(event)) {\n      return;\n    }\n\n    if ((this as HTMLInputElement).disabled) {\n      return;\n    }\n\n    const $slider = $(this).parent() as JQ<HTMLElement>;\n\n    $slider.removeClass('mdui-slider-focus');\n  });\n\n  $document.on(unlockEvent, rangeSelector, register);\n\n  /**\n   * 初始化滑块\n   */\n  mdui.mutation('.mdui-slider', function () {\n    reInit($(this));\n  });\n});\n\nmdui.updateSliders = function (\n  selector?: Selector | HTMLElement | ArrayLike<HTMLElement>,\n): void {\n  const $elements = isUndefined(selector) ? $('.mdui-slider') : $(selector);\n\n  $elements.each((_, element) => {\n    reInit($(element));\n  });\n};\n", "import $ from 'mdui.jq/es/$';\nimport extend from 'mdui.jq/es/functions/extend';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/css';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/last';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/parents';\nimport 'mdui.jq/es/methods/removeClass';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport mdui from '../../mdui';\nimport '../../jq_extends/methods/transitionEnd';\nimport { componentEvent } from '../../utils/componentEvent';\nimport { $document } from '../../utils/dom';\nimport { startEvent } from '../../utils/touchHandler';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 浮动操作按钮组件\n     *\n     * 请通过 `new mdui.Fab()` 调用\n     */\n    Fab: {\n      /**\n       * 实例化 Fab 组件\n       * @param selector CSS 选择器、或 DOM 元素、或 JQ 对象\n       * @param options 配置参数\n       */\n      new (\n        selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n        options?: OPTIONS,\n      ): Fab;\n    };\n  }\n}\n\ntype OPTIONS = {\n  /**\n   * 触发方式。`hover`: 鼠标悬浮触发；`click`: 点击触发\n   *\n   * 默认为 `hover`\n   */\n  trigger?: 'click' | 'hover';\n};\n\ntype STATE = 'opening' | 'opened' | 'closing' | 'closed';\ntype EVENT = 'open' | 'opened' | 'close' | 'closed';\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  trigger: 'hover',\n};\n\nclass Fab {\n  /**\n   * Fab 元素的 JQ 对象\n   */\n  public $element: JQ;\n\n  /**\n   * 配置参数\n   */\n  public options: OPTIONS = extend({}, DEFAULT_OPTIONS);\n\n  /**\n   * 当前 fab 的状态\n   */\n  private state: STATE = 'closed';\n\n  /**\n   * 按钮元素\n   */\n  private $btn: JQ;\n\n  /**\n   * 拨号菜单元素\n   */\n  private $dial: JQ;\n\n  /**\n   * 拨号菜单内的按钮\n   */\n  private $dialBtns: JQ;\n\n  public constructor(\n    selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    options: OPTIONS = {},\n  ) {\n    this.$element = $(selector).first();\n\n    extend(this.options, options);\n\n    this.$btn = this.$element.find('.mdui-fab');\n    this.$dial = this.$element.find('.mdui-fab-dial');\n    this.$dialBtns = this.$dial.find('.mdui-fab');\n\n    if (this.options.trigger === 'hover') {\n      this.$btn.on('touchstart mouseenter', () => this.open());\n      this.$element.on('mouseleave', () => this.close());\n    }\n\n    if (this.options.trigger === 'click') {\n      this.$btn.on(startEvent, () => this.open());\n    }\n\n    // 触摸屏幕其他地方关闭快速拨号\n    $document.on(startEvent, (event) => {\n      if ($(event.target as HTMLElement).parents('.mdui-fab-wrapper').length) {\n        return;\n      }\n\n      this.close();\n    });\n  }\n\n  /**\n   * 触发组件事件\n   * @param name\n   */\n  private triggerEvent(name: EVENT): void {\n    componentEvent(name, 'fab', this.$element, this);\n  }\n\n  /**\n   * 当前是否为打开状态\n   */\n  private isOpen(): boolean {\n    return this.state === 'opening' || this.state === 'opened';\n  }\n\n  /**\n   * 打开快速拨号菜单\n   */\n  public open(): void {\n    if (this.isOpen()) {\n      return;\n    }\n\n    // 为菜单中的按钮添加不同的 transition-delay\n    this.$dialBtns.each((index, btn) => {\n      const delay = `${15 * (this.$dialBtns.length - index)}ms`;\n\n      btn.style.transitionDelay = delay;\n      btn.style.webkitTransitionDelay = delay;\n    });\n\n    this.$dial.css('height', 'auto').addClass('mdui-fab-dial-show');\n\n    // 如果按钮中存在 .mdui-fab-opened 的图标，则进行图标切换\n    if (this.$btn.find('.mdui-fab-opened').length) {\n      this.$btn.addClass('mdui-fab-opened');\n    }\n\n    this.state = 'opening';\n    this.triggerEvent('open');\n\n    // 打开顺序为从下到上逐个打开，最上面的打开后才表示动画完成\n    this.$dialBtns.first().transitionEnd(() => {\n      if (this.$btn.hasClass('mdui-fab-opened')) {\n        this.state = 'opened';\n        this.triggerEvent('opened');\n      }\n    });\n  }\n\n  /**\n   * 关闭快速拨号菜单\n   */\n  public close(): void {\n    if (!this.isOpen()) {\n      return;\n    }\n\n    // 为菜单中的按钮添加不同的 transition-delay\n    this.$dialBtns.each((index, btn) => {\n      const delay = `${15 * index}ms`;\n\n      btn.style.transitionDelay = delay;\n      btn.style.webkitTransitionDelay = delay;\n    });\n\n    this.$dial.removeClass('mdui-fab-dial-show');\n    this.$btn.removeClass('mdui-fab-opened');\n    this.state = 'closing';\n    this.triggerEvent('close');\n\n    // 从上往下依次关闭，最后一个关闭后才表示动画完成\n    this.$dialBtns.last().transitionEnd(() => {\n      if (this.$btn.hasClass('mdui-fab-opened')) {\n        return;\n      }\n\n      this.state = 'closed';\n      this.triggerEvent('closed');\n      this.$dial.css('height', 0);\n    });\n  }\n\n  /**\n   * 切换快速拨号菜单的打开状态\n   */\n  public toggle(): void {\n    this.isOpen() ? this.close() : this.open();\n  }\n\n  /**\n   * 以动画的形式显示整个浮动操作按钮\n   */\n  public show(): void {\n    this.$element.removeClass('mdui-fab-hide');\n  }\n\n  /**\n   * 以动画的形式隐藏整个浮动操作按钮\n   */\n  public hide(): void {\n    this.$element.addClass('mdui-fab-hide');\n  }\n\n  /**\n   * 返回当前快速拨号菜单的打开状态。共包含四种状态：`opening`、`opened`、`closing`、`closed`\n   */\n  public getState(): STATE {\n    return this.state;\n  }\n}\n\nmdui.Fab = Fab;\n", "import $ from 'mdui.jq/es/$';\nimport mdui from '../../mdui';\nimport { $document } from '../../utils/dom';\nimport { parseOptions } from '../../utils/parseOptions';\nimport './index';\n\nconst customAttr = 'mdui-fab';\n\n$(() => {\n  // mouseenter 不冒泡，无法进行事件委托，这里用 mouseover 代替。\n  // 不管是 click 、 mouseover 还是 touchstart ，都先初始化。\n\n  $document.on(\n    'touchstart mousedown mouseover',\n    `[${customAttr}]`,\n    function () {\n      new mdui.Fab(\n        this as HTMLElement,\n        parseOptions(this as HTMLElement, customAttr),\n      );\n    },\n  );\n});\n", "/**\n * 最终生成的元素结构为：\n *  <select class=\"mdui-select\" mdui-select=\"{position: 'top'}\" style=\"display: none;\"> // $native\n *    <option value=\"1\">State 1</option>\n *    <option value=\"2\">State 2</option>\n *    <option value=\"3\" disabled=\"\">State 3</option>\n *  </select>\n *  <div class=\"mdui-select mdui-select-position-top\" style=\"\" id=\"88dec0e4-d4a2-c6d0-0e7f-1ba4501e0553\"> // $element\n *    <span class=\"mdui-select-selected\">State 1</span> // $selected\n *    <div class=\"mdui-select-menu\" style=\"transform-origin: center 100% 0px;\"> // $menu\n *      <div class=\"mdui-select-menu-item mdui-ripple\" selected=\"\">State 1</div> // $items\n *      <div class=\"mdui-select-menu-item mdui-ripple\">State 2</div>\n *      <div class=\"mdui-select-menu-item mdui-ripple\" disabled=\"\">State 3</div>\n *    </div>\n *  </div>\n */\n\nimport $ from 'mdui.jq/es/$';\nimport contains from 'mdui.jq/es/functions/contains';\nimport extend from 'mdui.jq/es/functions/extend';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/add';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/after';\nimport 'mdui.jq/es/methods/append';\nimport 'mdui.jq/es/methods/appendTo';\nimport 'mdui.jq/es/methods/attr';\nimport 'mdui.jq/es/methods/css';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/height';\nimport 'mdui.jq/es/methods/hide';\nimport 'mdui.jq/es/methods/index';\nimport 'mdui.jq/es/methods/innerWidth';\nimport 'mdui.jq/es/methods/is';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/remove';\nimport 'mdui.jq/es/methods/removeAttr';\nimport 'mdui.jq/es/methods/removeClass';\nimport 'mdui.jq/es/methods/show';\nimport 'mdui.jq/es/methods/text';\nimport 'mdui.jq/es/methods/trigger';\nimport 'mdui.jq/es/methods/val';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport mdui from '../../mdui';\nimport '../../jq_extends/methods/transitionEnd';\nimport '../../jq_extends/static/guid';\nimport { componentEvent } from '../../utils/componentEvent';\nimport { $document, $window } from '../../utils/dom';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 下拉选择组件\n     *\n     * 请通过 `new mdui.Select()` 调用\n     */\n    Select: {\n      /**\n       * 实例化 Select 组件\n       * @param selector CSS 选择器、或 DOM 元素、或 JQ 对象\n       * @param options 配置参数\n       */\n      new (\n        selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n        options?: OPTIONS,\n      ): Select;\n    };\n  }\n}\n\ntype OPTIONS = {\n  /**\n   * 下拉框位置：`auto`、`top`、`bottom`\n   */\n  position?: 'auto' | 'top' | 'bottom';\n\n  /**\n   * 菜单与窗口上下边框至少保持多少间距\n   */\n  gutter?: number;\n};\n\ntype STATE = 'closing' | 'closed' | 'opening' | 'opened';\ntype EVENT = 'open' | 'opened' | 'close' | 'closed';\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  position: 'auto',\n  gutter: 16,\n};\n\nclass Select {\n  /**\n   * 原生 `<select>` 元素的 JQ 对象\n   */\n  public $native: JQ<HTMLSelectElement>;\n\n  /**\n   * 生成的 `<div class=\"mdui-select\">` 元素的 JQ 对象\n   */\n  public $element: JQ = $();\n\n  /**\n   * 配置参数\n   */\n  public options: OPTIONS = extend({}, DEFAULT_OPTIONS);\n\n  /**\n   * select 的 size 属性的值，根据该值设置 select 的高度\n   */\n  private size = 0;\n\n  /**\n   * 占位元素，显示已选中菜单项的文本\n   */\n  private $selected: JQ = $();\n\n  /**\n   * 菜单项的外层元素的 JQ 对象\n   */\n  private $menu: JQ = $();\n\n  /**\n   * 菜单项数组的 JQ 对象\n   */\n  private $items: JQ = $();\n\n  /**\n   * 当前选中的菜单项的索引号\n   */\n  private selectedIndex = 0;\n\n  /**\n   * 当前选中菜单项的文本\n   */\n  private selectedText = '';\n\n  /**\n   * 当前选中菜单项的值\n   */\n  private selectedValue = '';\n\n  /**\n   * 唯一 ID\n   */\n  private uniqueID: string;\n\n  /**\n   * 当前 select 的状态\n   */\n  private state: STATE = 'closed';\n\n  public constructor(\n    selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    options: OPTIONS = {},\n  ) {\n    this.$native = $(selector).first() as JQ<HTMLSelectElement>;\n    this.$native.hide();\n\n    extend(this.options, options);\n\n    // 为当前 select 生成唯一 ID\n    this.uniqueID = $.guid();\n\n    // 生成 select\n    this.handleUpdate();\n\n    // 点击 select 外面区域关闭\n    $document.on('click touchstart', (event: Event) => {\n      const $target = $(event.target as HTMLElement);\n\n      if (\n        this.isOpen() &&\n        !$target.is(this.$element) &&\n        !contains(this.$element[0], $target[0])\n      ) {\n        this.close();\n      }\n    });\n  }\n\n  /**\n   * 调整菜单位置\n   */\n  private readjustMenu(): void {\n    const windowHeight = $window.height();\n\n    // mdui-select 高度\n    const elementHeight = this.$element.height();\n\n    // 菜单项高度\n    const $itemFirst = this.$items.first();\n    const itemHeight = $itemFirst.height();\n    const itemMargin = parseInt($itemFirst.css('margin-top'));\n\n    // 菜单高度\n    const menuWidth = this.$element.innerWidth() + 0.01; // 必须比真实宽度多一点，不然会出现省略号\n    let menuHeight = itemHeight * this.size + itemMargin * 2;\n\n    // mdui-select 在窗口中的位置\n    const elementTop = this.$element[0].getBoundingClientRect().top;\n\n    let transformOriginY: string;\n    let menuMarginTop: number;\n\n    if (this.options.position === 'bottom') {\n      menuMarginTop = elementHeight;\n      transformOriginY = '0px';\n    } else if (this.options.position === 'top') {\n      menuMarginTop = -menuHeight - 1;\n      transformOriginY = '100%';\n    } else {\n      // 菜单高度不能超过窗口高度\n      const menuMaxHeight = windowHeight - this.options.gutter! * 2;\n      if (menuHeight > menuMaxHeight) {\n        menuHeight = menuMaxHeight;\n      }\n\n      // 菜单的 margin-top\n      menuMarginTop = -(\n        itemMargin +\n        this.selectedIndex * itemHeight +\n        (itemHeight - elementHeight) / 2\n      );\n\n      const menuMaxMarginTop = -(\n        itemMargin +\n        (this.size - 1) * itemHeight +\n        (itemHeight - elementHeight) / 2\n      );\n      if (menuMarginTop < menuMaxMarginTop) {\n        menuMarginTop = menuMaxMarginTop;\n      }\n\n      // 菜单不能超出窗口\n      const menuTop = elementTop + menuMarginTop;\n      if (menuTop < this.options.gutter!) {\n        // 不能超出窗口上方\n        menuMarginTop = -(elementTop - this.options.gutter!);\n      } else if (menuTop + menuHeight + this.options.gutter! > windowHeight) {\n        // 不能超出窗口下方\n        menuMarginTop = -(\n          elementTop +\n          menuHeight +\n          this.options.gutter! -\n          windowHeight\n        );\n      }\n\n      // transform 的 Y 轴坐标\n      transformOriginY = `${\n        this.selectedIndex * itemHeight + itemHeight / 2 + itemMargin\n      }px`;\n    }\n\n    // 设置样式\n    this.$element.innerWidth(menuWidth);\n    this.$menu\n      .innerWidth(menuWidth)\n      .height(menuHeight)\n      .css({\n        'margin-top': menuMarginTop + 'px',\n        'transform-origin': 'center ' + transformOriginY + ' 0',\n      });\n  }\n\n  /**\n   * select 是否为打开状态\n   */\n  private isOpen(): boolean {\n    return this.state === 'opening' || this.state === 'opened';\n  }\n\n  /**\n   * 对原生 select 组件进行了修改后，需要调用该方法\n   */\n  public handleUpdate(): void {\n    if (this.isOpen()) {\n      this.close();\n    }\n\n    this.selectedValue = this.$native.val() as string;\n\n    // 保存菜单项数据的数组\n    type typeItemsData = {\n      value: string;\n      text: string;\n      disabled: boolean;\n      selected: boolean;\n      index: number;\n    };\n    const itemsData: typeItemsData[] = [];\n    this.$items = $();\n\n    // 生成 HTML\n    this.$native.find('option').each((index, option) => {\n      const text = option.textContent || '';\n      const value = option.value;\n      const disabled = option.disabled;\n      const selected = this.selectedValue === value;\n\n      itemsData.push({\n        value,\n        text,\n        disabled,\n        selected,\n        index,\n      });\n\n      if (selected) {\n        this.selectedText = text;\n        this.selectedIndex = index;\n      }\n\n      this.$items = this.$items.add(\n        '<div class=\"mdui-select-menu-item mdui-ripple\"' +\n          (disabled ? ' disabled' : '') +\n          (selected ? ' selected' : '') +\n          `>${text}</div>`,\n      );\n    });\n\n    this.$selected = $(\n      `<span class=\"mdui-select-selected\">${this.selectedText}</span>`,\n    );\n\n    this.$element = $(\n      `<div class=\"mdui-select mdui-select-position-${this.options.position}\" ` +\n        `style=\"${this.$native.attr('style')}\" ` +\n        `id=\"${this.uniqueID}\"></div>`,\n    )\n      .show()\n      .append(this.$selected);\n\n    this.$menu = $('<div class=\"mdui-select-menu\"></div>')\n      .appendTo(this.$element)\n      .append(this.$items);\n\n    $(`#${this.uniqueID}`).remove();\n    this.$native.after(this.$element);\n\n    // 根据 select 的 size 属性设置高度\n    this.size = parseInt(this.$native.attr('size') || '0');\n\n    if (this.size <= 0) {\n      this.size = this.$items.length;\n\n      if (this.size > 8) {\n        this.size = 8;\n      }\n    }\n\n    // 点击选项时关闭下拉菜单\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const that = this;\n    this.$items.on('click', function () {\n      if (that.state === 'closing') {\n        return;\n      }\n\n      const $item = $(this);\n      const index = $item.index();\n      const data = itemsData[index];\n\n      if (data.disabled) {\n        return;\n      }\n\n      that.$selected.text(data.text);\n      that.$native.val(data.value);\n      that.$items.removeAttr('selected');\n      $item.attr('selected', '');\n      that.selectedIndex = data.index;\n      that.selectedValue = data.value;\n      that.selectedText = data.text;\n      that.$native.trigger('change');\n      that.close();\n    });\n\n    // 点击 $element 时打开下拉菜单\n    this.$element.on('click', (event: Event) => {\n      const $target = $(event.target as HTMLElement);\n\n      // 在菜单上点击时不打开\n      if (\n        $target.is('.mdui-select-menu') ||\n        $target.is('.mdui-select-menu-item')\n      ) {\n        return;\n      }\n\n      this.toggle();\n    });\n  }\n\n  /**\n   * 动画结束的回调\n   */\n  private transitionEnd(): void {\n    this.$element.removeClass('mdui-select-closing');\n\n    if (this.state === 'opening') {\n      this.state = 'opened';\n      this.triggerEvent('opened');\n      this.$menu.css('overflow-y', 'auto');\n    }\n\n    if (this.state === 'closing') {\n      this.state = 'closed';\n      this.triggerEvent('closed');\n\n      // 恢复样式\n      this.$element.innerWidth('');\n      this.$menu.css({\n        'margin-top': '',\n        height: '',\n        width: '',\n      });\n    }\n  }\n\n  /**\n   * 触发组件事件\n   * @param name\n   */\n  private triggerEvent(name: EVENT): void {\n    componentEvent(name, 'select', this.$native, this);\n  }\n\n  /**\n   * 切换下拉菜单的打开状态\n   */\n  public toggle(): void {\n    this.isOpen() ? this.close() : this.open();\n  }\n\n  /**\n   * 打开下拉菜单\n   */\n  public open(): void {\n    if (this.isOpen()) {\n      return;\n    }\n\n    this.state = 'opening';\n    this.triggerEvent('open');\n    this.readjustMenu();\n    this.$element.addClass('mdui-select-open');\n    this.$menu.transitionEnd(() => this.transitionEnd());\n  }\n\n  /**\n   * 关闭下拉菜单\n   */\n  public close(): void {\n    if (!this.isOpen()) {\n      return;\n    }\n\n    this.state = 'closing';\n    this.triggerEvent('close');\n    this.$menu.css('overflow-y', '');\n    this.$element\n      .removeClass('mdui-select-open')\n      .addClass('mdui-select-closing');\n    this.$menu.transitionEnd(() => this.transitionEnd());\n  }\n\n  /**\n   * 获取当前菜单的状态。共包含四种状态：`opening`、`opened`、`closing`、`closed`\n   */\n  public getState(): STATE {\n    return this.state;\n  }\n}\n\nmdui.Select = Select;\n", "import $ from 'mdui.jq/es/$';\nimport mdui from '../../mdui';\nimport '../../global/mutation';\nimport { parseOptions } from '../../utils/parseOptions';\nimport './index';\n\nconst customAttr = 'mdui-select';\n\n$(() => {\n  mdui.mutation(`[${customAttr}]`, function () {\n    new mdui.Select(this, parseOptions(this, customAttr));\n  });\n});\n", "import $ from 'mdui.jq/es/$';\nimport mdui from '../../mdui';\nimport '../../global/mutation';\nimport '../headroom';\n\n$(() => {\n  // 滚动时隐藏应用栏\n  mdui.mutation('.mdui-appbar-scroll-hide', function () {\n    new mdui.Headroom(this);\n  });\n\n  // 滚动时只隐藏应用栏中的工具栏\n  mdui.mutation('.mdui-appbar-scroll-toolbar-hide', function () {\n    new mdui.Headroom(this, {\n      pinnedClass: 'mdui-headroom-pinned-toolbar',\n      unpinnedClass: 'mdui-headroom-unpinned-toolbar',\n    });\n  });\n});\n", "import $ from 'mdui.jq/es/$';\nimport extend from 'mdui.jq/es/functions/extend';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/appendTo';\nimport 'mdui.jq/es/methods/attr';\nimport 'mdui.jq/es/methods/children';\nimport 'mdui.jq/es/methods/css';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/eq';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/get';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/hide';\nimport 'mdui.jq/es/methods/index';\nimport 'mdui.jq/es/methods/innerWidth';\nimport 'mdui.jq/es/methods/offset';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/removeClass';\nimport 'mdui.jq/es/methods/show';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport { isNumber } from 'mdui.jq/es/utils';\nimport mdui from '../../mdui';\nimport '../../jq_extends/static/throttle';\nimport { componentEvent } from '../../utils/componentEvent';\nimport { $window } from '../../utils/dom';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * Tab 选项卡组件\n     *\n     * 请通过 `new mdui.Tab()` 调用\n     */\n    Tab: {\n      /**\n       * 实例化 Tab 组件\n       * @param selector CSS 选择器、或 DOM 元素、或 JQ 对象\n       * @param options 配置参数\n       */\n      new (\n        selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n        options?: OPTIONS,\n      ): Tab;\n    };\n  }\n}\n\ntype OPTIONS = {\n  /**\n   * 切换选项卡的触发方式。`click`: 点击切换；`hover`: 鼠标悬浮切换\n   */\n  trigger?: 'click' | 'hover';\n\n  /**\n   * 是否启用循环切换，若为 `true`，则最后一个选项激活时调用 `next` 方法将回到第一个选项，第一个选项激活时调用 `prev` 方法将回到最后一个选项。\n   */\n  loop?: boolean;\n};\n\ntype EVENT = 'change' | 'show';\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  trigger: 'click',\n  loop: false,\n};\n\nclass Tab {\n  /**\n   * tab 元素的 JQ 对象\n   */\n  public $element: JQ;\n\n  /**\n   * 配置参数\n   */\n  public options: OPTIONS = extend({}, DEFAULT_OPTIONS);\n\n  /**\n   * 当前激活的 tab 的索引号。为 -1 时表示没有激活的选项卡，或不存在选项卡\n   */\n  public activeIndex = -1;\n\n  /**\n   * 选项数组 JQ 对象\n   */\n  private $tabs: JQ;\n\n  /**\n   * 激活状态的 tab 底部的指示符\n   */\n  private $indicator: JQ;\n\n  public constructor(\n    selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    options: OPTIONS = {},\n  ) {\n    this.$element = $(selector).first();\n\n    extend(this.options, options);\n\n    this.$tabs = this.$element.children('a');\n    this.$indicator = $('<div class=\"mdui-tab-indicator\"></div>').appendTo(\n      this.$element,\n    );\n\n    // 根据 url hash 获取默认激活的选项卡\n    const hash = window.location.hash;\n    if (hash) {\n      this.$tabs.each((index, tab) => {\n        if ($(tab).attr('href') === hash) {\n          this.activeIndex = index;\n          return false;\n        }\n\n        return true;\n      });\n    }\n\n    // 含 .mdui-tab-active 的元素默认激活\n    if (this.activeIndex === -1) {\n      this.$tabs.each((index, tab) => {\n        if ($(tab).hasClass('mdui-tab-active')) {\n          this.activeIndex = index;\n          return false;\n        }\n\n        return true;\n      });\n    }\n\n    // 存在选项卡时，默认激活第一个选项卡\n    if (this.$tabs.length && this.activeIndex === -1) {\n      this.activeIndex = 0;\n    }\n\n    // 设置激活状态选项卡\n    this.setActive();\n\n    // 监听窗口大小变化事件，调整指示器位置\n    $window.on(\n      'resize',\n      $.throttle(() => this.setIndicatorPosition(), 100),\n    );\n\n    // 监听点击选项卡事件\n    this.$tabs.each((_, tab) => {\n      this.bindTabEvent(tab);\n    });\n  }\n\n  /**\n   * 指定选项卡是否已禁用\n   * @param $tab\n   */\n  private isDisabled($tab: JQ): boolean {\n    return $tab.attr('disabled') !== undefined;\n  }\n\n  /**\n   * 绑定在 Tab 上点击或悬浮的事件\n   * @param tab\n   */\n  private bindTabEvent(tab: HTMLElement): void {\n    const $tab = $(tab);\n\n    // 点击或鼠标移入触发的事件\n    const clickEvent = (): void | false => {\n      // 禁用状态的选项卡无法选中\n      if (this.isDisabled($tab)) {\n        return false;\n      }\n\n      this.activeIndex = this.$tabs.index(tab);\n      this.setActive();\n    };\n\n    // 无论 trigger 是 click 还是 hover，都会响应 click 事件\n    $tab.on('click', clickEvent);\n\n    // trigger 为 hover 时，额外响应 mouseenter 事件\n    if (this.options.trigger === 'hover') {\n      $tab.on('mouseenter', clickEvent);\n    }\n\n    // 阻止链接的默认点击动作\n    $tab.on('click', (): void | false => {\n      if (($tab.attr('href') || '').indexOf('#') === 0) {\n        return false;\n      }\n    });\n  }\n\n  /**\n   * 触发组件事件\n   * @param name\n   * @param $element\n   * @param parameters\n   */\n  private triggerEvent(name: EVENT, $element: JQ, parameters = {}): void {\n    componentEvent(name, 'tab', $element, this, parameters);\n  }\n\n  /**\n   * 设置激活状态的选项卡\n   */\n  private setActive(): void {\n    this.$tabs.each((index, tab) => {\n      const $tab = $(tab);\n      const targetId = $tab.attr('href') || '';\n\n      // 设置选项卡激活状态\n      if (index === this.activeIndex && !this.isDisabled($tab)) {\n        if (!$tab.hasClass('mdui-tab-active')) {\n          this.triggerEvent('change', this.$element, {\n            index: this.activeIndex,\n            id: targetId.substr(1),\n          });\n          this.triggerEvent('show', $tab);\n\n          $tab.addClass('mdui-tab-active');\n        }\n\n        $(targetId).show();\n        this.setIndicatorPosition();\n      } else {\n        $tab.removeClass('mdui-tab-active');\n        $(targetId).hide();\n      }\n    });\n  }\n\n  /**\n   * 设置选项卡指示器的位置\n   */\n  private setIndicatorPosition(): void {\n    // 选项卡数量为 0 时，不显示指示器\n    if (this.activeIndex === -1) {\n      this.$indicator.css({\n        left: 0,\n        width: 0,\n      });\n\n      return;\n    }\n\n    const $activeTab = this.$tabs.eq(this.activeIndex);\n\n    if (this.isDisabled($activeTab)) {\n      return;\n    }\n\n    const activeTabOffset = $activeTab.offset();\n\n    this.$indicator.css({\n      left: `${\n        activeTabOffset.left +\n        this.$element[0].scrollLeft -\n        this.$element[0].getBoundingClientRect().left\n      }px`,\n      width: `${$activeTab.innerWidth()}px`,\n    });\n  }\n\n  /**\n   * 切换到下一个选项卡\n   */\n  public next(): void {\n    if (this.activeIndex === -1) {\n      return;\n    }\n\n    if (this.$tabs.length > this.activeIndex + 1) {\n      this.activeIndex++;\n    } else if (this.options.loop) {\n      this.activeIndex = 0;\n    }\n\n    this.setActive();\n  }\n\n  /**\n   * 切换到上一个选项卡\n   */\n  public prev(): void {\n    if (this.activeIndex === -1) {\n      return;\n    }\n\n    if (this.activeIndex > 0) {\n      this.activeIndex--;\n    } else if (this.options.loop) {\n      this.activeIndex = this.$tabs.length - 1;\n    }\n\n    this.setActive();\n  }\n\n  /**\n   * 显示指定索引号、或指定id的选项卡\n   * @param index 索引号、或id\n   */\n  public show(index: number | string): void {\n    if (this.activeIndex === -1) {\n      return;\n    }\n\n    if (isNumber(index)) {\n      this.activeIndex = index;\n    } else {\n      this.$tabs.each((i, tab): void | false => {\n        if (tab.id === index) {\n          this.activeIndex === i;\n          return false;\n        }\n      });\n    }\n\n    this.setActive();\n  }\n\n  /**\n   * 在父元素的宽度变化时，需要调用该方法重新调整指示器位置\n   * 在添加或删除选项卡时，需要调用该方法\n   */\n  public handleUpdate(): void {\n    const $oldTabs = this.$tabs; // 旧的 tabs JQ对象\n    const $newTabs = this.$element.children('a'); // 新的 tabs JQ对象\n    const oldTabsElement = $oldTabs.get(); // 旧的 tabs 元素数组\n    const newTabsElement = $newTabs.get(); // 新的 tabs 元素数组\n\n    if (!$newTabs.length) {\n      this.activeIndex = -1;\n      this.$tabs = $newTabs;\n      this.setIndicatorPosition();\n\n      return;\n    }\n\n    // 重新遍历选项卡，找出新增的选项卡\n    $newTabs.each((index, tab) => {\n      // 有新增的选项卡\n      if (oldTabsElement.indexOf(tab) < 0) {\n        this.bindTabEvent(tab);\n\n        if (this.activeIndex === -1) {\n          this.activeIndex = 0;\n        } else if (index <= this.activeIndex) {\n          this.activeIndex++;\n        }\n      }\n    });\n\n    // 找出被移除的选项卡\n    $oldTabs.each((index, tab) => {\n      // 有被移除的选项卡\n      if (newTabsElement.indexOf(tab) < 0) {\n        if (index < this.activeIndex) {\n          this.activeIndex--;\n        } else if (index === this.activeIndex) {\n          this.activeIndex = 0;\n        }\n      }\n    });\n\n    this.$tabs = $newTabs;\n\n    this.setActive();\n  }\n}\n\nmdui.Tab = Tab;\n", "import $ from 'mdui.jq/es/$';\nimport mdui from '../../mdui';\nimport '../../global/mutation';\nimport { parseOptions } from '../../utils/parseOptions';\nimport './index';\n\nconst customAttr = 'mdui-tab';\n\n$(() => {\n  mdui.mutation(`[${customAttr}]`, function () {\n    new mdui.Tab(this, parseOptions(this, customAttr));\n  });\n});\n", "/**\n * 在桌面设备上默认显示抽屉栏，不显示遮罩层\n * 在手机和平板设备上默认不显示抽屉栏，始终显示遮罩层，且覆盖导航栏\n */\n\nimport $ from 'mdui.jq/es/$';\nimport extend from 'mdui.jq/es/functions/extend';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/off';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/one';\nimport 'mdui.jq/es/methods/removeClass';\nimport 'mdui.jq/es/methods/width';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport mdui from '../../mdui';\nimport '../../jq_extends/methods/transitionEnd';\nimport '../../jq_extends/static/hideOverlay';\nimport '../../jq_extends/static/lockScreen';\nimport '../../jq_extends/static/showOverlay';\nimport '../../jq_extends/static/throttle';\nimport '../../jq_extends/static/unlockScreen';\nimport { componentEvent } from '../../utils/componentEvent';\nimport { $window } from '../../utils/dom';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * Drawer 组件\n     *\n     * 请通过 `new mdui.Drawer()` 调用\n     */\n    Drawer: {\n      /**\n       * 实例化 Drawer 组件\n       * @param selector CSS 选择器、或 DOM 元素、或 JQ 对象\n       * @param options 配置参数\n       */\n      new (\n        selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n        options?: OPTIONS,\n      ): Drawer;\n    };\n  }\n}\n\ntype OPTIONS = {\n  /**\n   * 打开抽屉栏时是否显示遮罩层。该参数只对中等屏幕及以上的设备有效，在超小屏和小屏设备上始终会显示遮罩层。\n   */\n  overlay?: boolean;\n\n  /**\n   * 是否启用滑动手势。\n   */\n  swipe?: boolean;\n};\n\ntype STATE = 'opening' | 'opened' | 'closing' | 'closed';\ntype EVENT = 'open' | 'opened' | 'close' | 'closed';\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  overlay: false,\n  swipe: false,\n};\n\nclass Drawer {\n  /**\n   * drawer 元素的 JQ 对象\n   */\n  public $element: JQ;\n\n  /**\n   * 配置参数\n   */\n  public options: OPTIONS = extend({}, DEFAULT_OPTIONS);\n\n  /**\n   * 当前是否显示着遮罩层\n   */\n  private overlay = false;\n\n  /**\n   * 抽屉栏的位置\n   */\n  private position: 'left' | 'right';\n\n  /**\n   * 当前抽屉栏状态\n   */\n  private state: STATE;\n\n  public constructor(\n    selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    options: OPTIONS = {},\n  ) {\n    this.$element = $(selector).first();\n\n    extend(this.options, options);\n\n    this.position = this.$element.hasClass('mdui-drawer-right')\n      ? 'right'\n      : 'left';\n\n    if (this.$element.hasClass('mdui-drawer-close')) {\n      this.state = 'closed';\n    } else if (this.$element.hasClass('mdui-drawer-open')) {\n      this.state = 'opened';\n    } else if (this.isDesktop()) {\n      this.state = 'opened';\n    } else {\n      this.state = 'closed';\n    }\n\n    // 浏览器窗口大小调整时\n    $window.on(\n      'resize',\n      $.throttle(() => {\n        if (this.isDesktop()) {\n          // 由手机平板切换到桌面时\n          // 如果显示着遮罩，则隐藏遮罩\n          if (this.overlay && !this.options.overlay) {\n            $.hideOverlay();\n            this.overlay = false;\n            $.unlockScreen();\n          }\n\n          // 没有强制关闭，则状态为打开状态\n          if (!this.$element.hasClass('mdui-drawer-close')) {\n            this.state = 'opened';\n          }\n        } else if (!this.overlay && this.state === 'opened') {\n          // 由桌面切换到手机平板时。如果抽屉栏是打开着的且没有遮罩层，则关闭抽屉栏\n          if (this.$element.hasClass('mdui-drawer-open')) {\n            $.showOverlay();\n            this.overlay = true;\n            $.lockScreen();\n\n            $('.mdui-overlay').one('click', () => this.close());\n          } else {\n            this.state = 'closed';\n          }\n        }\n      }, 100),\n    );\n\n    // 绑定关闭按钮事件\n    this.$element.find('[mdui-drawer-close]').each((_, close) => {\n      $(close).on('click', () => this.close());\n    });\n\n    this.swipeSupport();\n  }\n\n  /**\n   * 是否是桌面设备\n   */\n  private isDesktop(): boolean {\n    return $window.width() >= 1024;\n  }\n\n  /**\n   * 滑动手势支持\n   */\n  private swipeSupport(): void {\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const that = this;\n\n    // 抽屉栏滑动手势控制\n    let openNavEventHandler: (event: Event) => void;\n    let touchStartX: number;\n    let touchStartY: number;\n    let swipeStartX: number;\n    let swiping: null | 'opening' | 'closing' = null;\n    let maybeSwiping = false;\n    const $body = $('body');\n\n    // 手势触发的范围\n    const swipeAreaWidth = 24;\n\n    function setPosition(translateX: number): void {\n      const rtlTranslateMultiplier = that.position === 'right' ? -1 : 1;\n      const transformCSS = `translate(${\n        -1 * rtlTranslateMultiplier * translateX\n      }px, 0) !important;`;\n      const transitionCSS = 'initial !important;';\n\n      that.$element.css(\n        'cssText',\n        `transform: ${transformCSS}; transition: ${transitionCSS};`,\n      );\n    }\n\n    function cleanPosition(): void {\n      that.$element[0].style.transform = '';\n      that.$element[0].style.webkitTransform = '';\n      that.$element[0].style.transition = '';\n      that.$element[0].style.webkitTransition = '';\n    }\n\n    function getMaxTranslateX(): number {\n      return that.$element.width() + 10;\n    }\n\n    function getTranslateX(currentX: number): number {\n      return Math.min(\n        Math.max(\n          swiping === 'closing'\n            ? swipeStartX - currentX\n            : getMaxTranslateX() + swipeStartX - currentX,\n          0,\n        ),\n        getMaxTranslateX(),\n      );\n    }\n\n    function onBodyTouchEnd(event?: Event): void {\n      if (swiping) {\n        let touchX = (event as TouchEvent).changedTouches[0].pageX;\n        if (that.position === 'right') {\n          touchX = $body.width() - touchX;\n        }\n\n        const translateRatio = getTranslateX(touchX) / getMaxTranslateX();\n\n        maybeSwiping = false;\n        const swipingState = swiping;\n        swiping = null;\n\n        if (swipingState === 'opening') {\n          if (translateRatio < 0.92) {\n            cleanPosition();\n            that.open();\n          } else {\n            cleanPosition();\n          }\n        } else {\n          if (translateRatio > 0.08) {\n            cleanPosition();\n            that.close();\n          } else {\n            cleanPosition();\n          }\n        }\n\n        $.unlockScreen();\n      } else {\n        maybeSwiping = false;\n      }\n\n      $body.off({\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        touchmove: onBodyTouchMove,\n        touchend: onBodyTouchEnd,\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        touchcancel: onBodyTouchMove,\n      });\n    }\n\n    function onBodyTouchMove(event: Event): void {\n      let touchX = (event as TouchEvent).touches[0].pageX;\n      if (that.position === 'right') {\n        touchX = $body.width() - touchX;\n      }\n\n      const touchY = (event as TouchEvent).touches[0].pageY;\n\n      if (swiping) {\n        setPosition(getTranslateX(touchX));\n      } else if (maybeSwiping) {\n        const dXAbs = Math.abs(touchX - touchStartX);\n        const dYAbs = Math.abs(touchY - touchStartY);\n        const threshold = 8;\n\n        if (dXAbs > threshold && dYAbs <= threshold) {\n          swipeStartX = touchX;\n          swiping = that.state === 'opened' ? 'closing' : 'opening';\n          $.lockScreen();\n          setPosition(getTranslateX(touchX));\n        } else if (dXAbs <= threshold && dYAbs > threshold) {\n          onBodyTouchEnd();\n        }\n      }\n    }\n\n    function onBodyTouchStart(event: Event): void {\n      touchStartX = (event as TouchEvent).touches[0].pageX;\n      if (that.position === 'right') {\n        touchStartX = $body.width() - touchStartX;\n      }\n\n      touchStartY = (event as TouchEvent).touches[0].pageY;\n\n      if (that.state !== 'opened') {\n        if (\n          touchStartX > swipeAreaWidth ||\n          openNavEventHandler !== onBodyTouchStart\n        ) {\n          return;\n        }\n      }\n\n      maybeSwiping = true;\n\n      $body.on({\n        touchmove: onBodyTouchMove,\n        touchend: onBodyTouchEnd,\n        touchcancel: onBodyTouchMove,\n      });\n    }\n\n    function enableSwipeHandling(): void {\n      if (!openNavEventHandler) {\n        $body.on('touchstart', onBodyTouchStart);\n        openNavEventHandler = onBodyTouchStart;\n      }\n    }\n\n    if (this.options.swipe) {\n      enableSwipeHandling();\n    }\n  }\n\n  /**\n   * 触发组件事件\n   * @param name\n   */\n  private triggerEvent(name: EVENT): void {\n    componentEvent(name, 'drawer', this.$element, this);\n  }\n\n  /**\n   * 动画结束回调\n   */\n  private transitionEnd(): void {\n    if (this.$element.hasClass('mdui-drawer-open')) {\n      this.state = 'opened';\n      this.triggerEvent('opened');\n    } else {\n      this.state = 'closed';\n      this.triggerEvent('closed');\n    }\n  }\n\n  /**\n   * 是否处于打开状态\n   */\n  private isOpen(): boolean {\n    return this.state === 'opening' || this.state === 'opened';\n  }\n\n  /**\n   * 打开抽屉栏\n   */\n  public open(): void {\n    if (this.isOpen()) {\n      return;\n    }\n\n    this.state = 'opening';\n    this.triggerEvent('open');\n\n    if (!this.options.overlay) {\n      $('body').addClass(`mdui-drawer-body-${this.position}`);\n    }\n\n    this.$element\n      .removeClass('mdui-drawer-close')\n      .addClass('mdui-drawer-open')\n      .transitionEnd(() => this.transitionEnd());\n\n    if (!this.isDesktop() || this.options.overlay) {\n      this.overlay = true;\n      $.showOverlay().one('click', () => this.close());\n      $.lockScreen();\n    }\n  }\n\n  /**\n   * 关闭抽屉栏\n   */\n  public close(): void {\n    if (!this.isOpen()) {\n      return;\n    }\n\n    this.state = 'closing';\n    this.triggerEvent('close');\n\n    if (!this.options.overlay) {\n      $('body').removeClass(`mdui-drawer-body-${this.position}`);\n    }\n\n    this.$element\n      .addClass('mdui-drawer-close')\n      .removeClass('mdui-drawer-open')\n      .transitionEnd(() => this.transitionEnd());\n\n    if (this.overlay) {\n      $.hideOverlay();\n      this.overlay = false;\n      $.unlockScreen();\n    }\n  }\n\n  /**\n   * 切换抽屉栏打开/关闭状态\n   */\n  public toggle(): void {\n    this.isOpen() ? this.close() : this.open();\n  }\n\n  /**\n   * 返回当前抽屉栏的状态。共包含四种状态：`opening`、`opened`、`closing`、`closed`\n   */\n  public getState(): STATE {\n    return this.state;\n  }\n}\n\nmdui.Drawer = Drawer;\n", "import $ from 'mdui.jq/es/$';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/on';\nimport mdui from '../../mdui';\nimport '../../global/mutation';\nimport { parseOptions } from '../../utils/parseOptions';\nimport './index';\n\nconst customAttr = 'mdui-drawer';\n\ntype OPTIONS = {\n  target: string;\n  overlay?: boolean;\n  swipe?: boolean;\n};\n\n$(() => {\n  mdui.mutation(`[${customAttr}]`, function () {\n    const $element = $(this);\n    const options = parseOptions(this, customAttr) as OPTIONS;\n    const selector = options.target;\n    // @ts-ignore\n    delete options.target;\n\n    const $drawer = $(selector).first();\n    const instance = new mdui.Drawer($drawer, options);\n\n    $element.on('click', () => instance.toggle());\n  });\n});\n", "import { isUndefined } from 'mdui.jq/es/utils';\nimport PlainObject from 'mdui.jq/es/interfaces/PlainObject';\n\ntype Func = () => any;\n\nconst container: PlainObject<Func[]> = {};\n\n/**\n * 根据队列名，获取队列中所有函数\n * @param name 队列名\n */\nfunction queue(name: string): Func[];\n\n/**\n * 写入队列\n * @param name 队列名\n * @param func 函数\n */\nfunction queue(name: string, func: Func): void;\n\nfunction queue(name: string, func?: Func): void | Func[] {\n  if (isUndefined(container[name])) {\n    container[name] = [];\n  }\n\n  if (isUndefined(func)) {\n    return container[name];\n  }\n\n  container[name].push(func);\n}\n\n/**\n * 从队列中移除第一个函数，并执行该函数\n * @param name 队列满\n */\nfunction dequeue(name: string): void {\n  if (isUndefined(container[name])) {\n    return;\n  }\n\n  if (!container[name].length) {\n    return;\n  }\n\n  const func = container[name].shift()!;\n\n  func();\n}\n\nexport { queue, dequeue };\n", "import $ from 'mdui.jq/es/$';\nimport contains from 'mdui.jq/es/functions/contains';\nimport extend from 'mdui.jq/es/functions/extend';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/append';\nimport 'mdui.jq/es/methods/children';\nimport 'mdui.jq/es/methods/css';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/height';\nimport 'mdui.jq/es/methods/hide';\nimport 'mdui.jq/es/methods/innerHeight';\nimport 'mdui.jq/es/methods/off';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/remove';\nimport 'mdui.jq/es/methods/removeClass';\nimport 'mdui.jq/es/methods/show';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport '../../jq_extends/methods/transitionEnd';\nimport '../../jq_extends/static/hideOverlay';\nimport '../../jq_extends/static/lockScreen';\nimport '../../jq_extends/static/showOverlay';\nimport '../../jq_extends/static/throttle';\nimport '../../jq_extends/static/unlockScreen';\nimport { componentEvent } from '../../utils/componentEvent';\nimport { $window } from '../../utils/dom';\nimport { dequeue, queue } from '../../utils/queue';\n\ntype OPTIONS = {\n  /**\n   * 打开对话框时是否添加 url hash，若为 `true`，则打开对话框后可用过浏览器的后退按钮或 Android 的返回键关闭对话框。\n   */\n  history?: boolean;\n\n  /**\n   * 打开对话框时是否显示遮罩。\n   */\n  overlay?: boolean;\n\n  /**\n   * 是否模态化对话框。为 `false` 时点击对话框外面的区域时关闭对话框，否则不关闭。\n   */\n  modal?: boolean;\n\n  /**\n   * 按下 Esc 键时是否关闭对话框。\n   */\n  closeOnEsc?: boolean;\n\n  /**\n   * 按下取消按钮时是否关闭对话框。\n   */\n  closeOnCancel?: boolean;\n\n  /**\n   * 按下确认按钮时是否关闭对话框。\n   */\n  closeOnConfirm?: boolean;\n\n  /**\n   * 关闭对话框后是否自动销毁对话框。\n   */\n  destroyOnClosed?: boolean;\n};\n\ntype STATE = 'opening' | 'opened' | 'closing' | 'closed';\ntype EVENT = 'open' | 'opened' | 'close' | 'closed' | 'cancel' | 'confirm';\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  history: true,\n  overlay: true,\n  modal: false,\n  closeOnEsc: true,\n  closeOnCancel: true,\n  closeOnConfirm: true,\n  destroyOnClosed: false,\n};\n\n/**\n * 当前显示的对话框实例\n */\nlet currentInst: null | Dialog = null;\n\n/**\n * 队列名\n */\nconst queueName = '_mdui_dialog';\n\n/**\n * 窗口是否已锁定\n */\nlet isLockScreen = false;\n\n/**\n * 遮罩层元素\n */\nlet $overlay: null | JQ;\n\nclass Dialog {\n  /**\n   * dialog 元素的 JQ 对象\n   */\n  public $element: JQ;\n\n  /**\n   * 配置参数\n   */\n  public options: OPTIONS = extend({}, DEFAULT_OPTIONS);\n\n  /**\n   * 当前 dialog 的状态\n   */\n  public state: STATE = 'closed';\n\n  /**\n   * dialog 元素是否是动态添加的\n   */\n  private append = false;\n\n  public constructor(\n    selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    options: OPTIONS = {},\n  ) {\n    this.$element = $(selector).first();\n\n    // 如果对话框元素没有在当前文档中，则需要添加\n    if (!contains(document.body, this.$element[0])) {\n      this.append = true;\n      $('body').append(this.$element);\n    }\n\n    extend(this.options, options);\n\n    // 绑定取消按钮事件\n    this.$element.find('[mdui-dialog-cancel]').each((_, cancel) => {\n      $(cancel).on('click', () => {\n        this.triggerEvent('cancel');\n\n        if (this.options.closeOnCancel) {\n          this.close();\n        }\n      });\n    });\n\n    // 绑定确认按钮事件\n    this.$element.find('[mdui-dialog-confirm]').each((_, confirm) => {\n      $(confirm).on('click', () => {\n        this.triggerEvent('confirm');\n\n        if (this.options.closeOnConfirm) {\n          this.close();\n        }\n      });\n    });\n\n    // 绑定关闭按钮事件\n    this.$element.find('[mdui-dialog-close]').each((_, close) => {\n      $(close).on('click', () => this.close());\n    });\n  }\n\n  /**\n   * 触发组件事件\n   * @param name\n   */\n  private triggerEvent(name: EVENT): void {\n    componentEvent(name, 'dialog', this.$element, this);\n  }\n\n  /**\n   * 窗口宽度变化，或对话框内容变化时，调整对话框位置和对话框内的滚动条\n   */\n  private readjust(): void {\n    if (!currentInst) {\n      return;\n    }\n\n    const $element = currentInst.$element;\n    const $title = $element.children('.mdui-dialog-title');\n    const $content = $element.children('.mdui-dialog-content');\n    const $actions = $element.children('.mdui-dialog-actions');\n\n    // 调整 dialog 的 top 和 height 值\n    $element.height('');\n    $content.height('');\n\n    const elementHeight = $element.height();\n    $element.css({\n      top: `${($window.height() - elementHeight) / 2}px`,\n      height: `${elementHeight}px`,\n    });\n\n    // 调整 mdui-dialog-content 的高度\n    $content.innerHeight(\n      elementHeight -\n        ($title.innerHeight() || 0) -\n        ($actions.innerHeight() || 0),\n    );\n  }\n\n  /**\n   * hashchange 事件触发时关闭对话框\n   */\n  private hashchangeEvent(): void {\n    if (window.location.hash.substring(1).indexOf('mdui-dialog') < 0) {\n      currentInst!.close(true);\n    }\n  }\n\n  /**\n   * 点击遮罩层关闭对话框\n   * @param event\n   */\n  private overlayClick(event: Event): void {\n    if (\n      $(event.target as HTMLElement).hasClass('mdui-overlay') &&\n      currentInst\n    ) {\n      currentInst.close();\n    }\n  }\n\n  /**\n   * 动画结束回调\n   */\n  private transitionEnd(): void {\n    if (this.$element.hasClass('mdui-dialog-open')) {\n      this.state = 'opened';\n      this.triggerEvent('opened');\n    } else {\n      this.state = 'closed';\n      this.triggerEvent('closed');\n      this.$element.hide();\n\n      // 所有对话框都关闭，且当前没有打开的对话框时，解锁屏幕\n      if (!queue(queueName).length && !currentInst && isLockScreen) {\n        $.unlockScreen();\n        isLockScreen = false;\n      }\n\n      $window.off('resize', $.throttle(this.readjust, 100));\n\n      if (this.options.destroyOnClosed) {\n        this.destroy();\n      }\n    }\n  }\n\n  /**\n   * 打开指定对话框\n   */\n  private doOpen(): void {\n    currentInst = this;\n\n    if (!isLockScreen) {\n      $.lockScreen();\n      isLockScreen = true;\n    }\n\n    this.$element.show();\n    this.readjust();\n\n    $window.on('resize', $.throttle(this.readjust, 100));\n\n    // 打开消息框\n    this.state = 'opening';\n    this.triggerEvent('open');\n    this.$element\n      .addClass('mdui-dialog-open')\n      .transitionEnd(() => this.transitionEnd());\n\n    // 不存在遮罩层元素时，添加遮罩层\n    if (!$overlay) {\n      $overlay = $.showOverlay(5100);\n    }\n\n    // 点击遮罩层时是否关闭对话框\n    if (this.options.modal) {\n      $overlay.off('click', this.overlayClick);\n    } else {\n      $overlay.on('click', this.overlayClick);\n    }\n\n    // 是否显示遮罩层，不显示时，把遮罩层背景透明\n    $overlay.css('opacity', this.options.overlay ? '' : 0);\n\n    if (this.options.history) {\n      // 如果 hash 中原来就有 mdui-dialog，先删除，避免后退历史纪录后仍然有 mdui-dialog 导致无法关闭\n      // 包括 mdui-dialog 和 &mdui-dialog 和 ?mdui-dialog\n      let hash = window.location.hash.substring(1);\n      if (hash.indexOf('mdui-dialog') > -1) {\n        hash = hash.replace(/[&?]?mdui-dialog/g, '');\n      }\n\n      // 后退按钮关闭对话框\n      if (hash) {\n        window.location.hash = `${hash}${\n          hash.indexOf('?') > -1 ? '&' : '?'\n        }mdui-dialog`;\n      } else {\n        window.location.hash = 'mdui-dialog';\n      }\n\n      $window.on('hashchange', this.hashchangeEvent);\n    }\n  }\n\n  /**\n   * 当前对话框是否为打开状态\n   */\n  private isOpen(): boolean {\n    return this.state === 'opening' || this.state === 'opened';\n  }\n\n  /**\n   * 打开对话框\n   */\n  public open(): void {\n    if (this.isOpen()) {\n      return;\n    }\n\n    // 如果当前有正在打开或已经打开的对话框,或队列不为空，则先加入队列，等旧对话框开始关闭时再打开\n    if (\n      (currentInst &&\n        (currentInst.state === 'opening' || currentInst.state === 'opened')) ||\n      queue(queueName).length\n    ) {\n      queue(queueName, () => this.doOpen());\n\n      return;\n    }\n\n    this.doOpen();\n  }\n\n  /**\n   * 关闭对话框\n   */\n  public close(historyBack = false): void {\n    // historyBack 是否需要后退历史纪录，默认为 `false`。该参数仅内部使用\n    // 为 `false` 时是通过 js 关闭，需要后退一个历史记录\n    // 为 `true` 时是通过后退按钮关闭，不需要后退历史记录\n\n    // setTimeout 的作用是：\n    // 当同时关闭一个对话框，并打开另一个对话框时，使打开对话框的操作先执行，以使需要打开的对话框先加入队列\n    setTimeout(() => {\n      if (!this.isOpen()) {\n        return;\n      }\n\n      currentInst = null;\n\n      this.state = 'closing';\n      this.triggerEvent('close');\n\n      // 所有对话框都关闭，且当前没有打开的对话框时，隐藏遮罩\n      if (!queue(queueName).length && $overlay) {\n        $.hideOverlay();\n        $overlay = null;\n\n        // 若仍存在遮罩，恢复遮罩的 z-index\n        $('.mdui-overlay').css('z-index', 2000);\n      }\n\n      this.$element\n        .removeClass('mdui-dialog-open')\n        .transitionEnd(() => this.transitionEnd());\n\n      if (this.options.history && !queue(queueName).length) {\n        if (!historyBack) {\n          window.history.back();\n        }\n\n        $window.off('hashchange', this.hashchangeEvent);\n      }\n\n      // 关闭旧对话框，打开新对话框。\n      // 加一点延迟，仅仅为了视觉效果更好。不加延时也不影响功能\n      setTimeout(() => {\n        dequeue(queueName);\n      }, 100);\n    });\n  }\n\n  /**\n   * 切换对话框打开/关闭状态\n   */\n  public toggle(): void {\n    this.isOpen() ? this.close() : this.open();\n  }\n\n  /**\n   * 获取对话框状态。共包含四种状态：`opening`、`opened`、`closing`、`closed`\n   */\n  public getState(): STATE {\n    return this.state;\n  }\n\n  /**\n   * 销毁对话框\n   */\n  public destroy(): void {\n    if (this.append) {\n      this.$element.remove();\n    }\n\n    if (!queue(queueName).length && !currentInst) {\n      if ($overlay) {\n        $.hideOverlay();\n        $overlay = null;\n      }\n\n      if (isLockScreen) {\n        $.unlockScreen();\n        isLockScreen = false;\n      }\n    }\n  }\n\n  /**\n   * 对话框内容变化时，需要调用该方法来调整对话框位置和滚动条高度\n   */\n  public handleUpdate(): void {\n    this.readjust();\n  }\n}\n\nexport { currentInst, OPTIONS, Dialog };\n", "import Selector from 'mdui.jq/es/types/Selector';\nimport mdui from '../../mdui';\nimport 'mdui.jq/es/methods/on';\nimport { $document } from '../../utils/dom';\nimport { currentInst, OPTIONS, Dialog } from './class';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * Dialog 组件\n     *\n     * 请通过 `new mdui.Dialog()` 调用\n     */\n    Dialog: {\n      /**\n       * 实例化 Dialog 组件\n       * @param selector CSS 选择器、或 DOM 元素、或 JQ 对象\n       * @param options 配置参数\n       */\n      new (\n        selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n        options?: OPTIONS,\n      ): Dialog;\n    };\n  }\n}\n\n// esc 按下时关闭对话框\n$document.on('keydown', (event: Event) => {\n  if (\n    currentInst &&\n    currentInst.options.closeOnEsc &&\n    currentInst.state === 'opened' &&\n    (event as KeyboardEvent).keyCode === 27\n  ) {\n    currentInst.close();\n  }\n});\n\nmdui.Dialog = Dialog;\n", "import $ from 'mdui.jq/es/$';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/on';\nimport mdui from '../../mdui';\nimport { $document } from '../../utils/dom';\nimport { parseOptions } from '../../utils/parseOptions';\nimport './index';\n\nconst customAttr = 'mdui-dialog';\nconst dataName = '_mdui_dialog';\n\ntype OPTIONS = {\n  target: string;\n  history?: boolean;\n  overlay?: boolean;\n  modal?: boolean;\n  closeOnEsc?: boolean;\n  closeOnCancel?: boolean;\n  closeOnConfirm?: boolean;\n  destroyOnClosed?: boolean;\n};\n\n$(() => {\n  $document.on('click', `[${customAttr}]`, function () {\n    const options = parseOptions(this as HTMLElement, customAttr) as OPTIONS;\n    const selector = options.target;\n    // @ts-ignore\n    delete options.target;\n\n    const $dialog = $(selector).first();\n    let instance = $dialog.data(dataName);\n\n    if (!instance) {\n      instance = new mdui.Dialog($dialog, options);\n      $dialog.data(dataName, instance);\n    }\n\n    instance.open();\n  });\n});\n", "import $ from 'mdui.jq/es/$';\nimport each from 'mdui.jq/es/functions/each';\nimport extend from 'mdui.jq/es/functions/extend';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/on';\nimport mdui from '../../mdui';\nimport { Dialog } from './class';\nimport './index';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 打开一个对话框，标题、内容、按钮等都可以自定义\n     * @param options 配置参数\n     */\n    dialog(options: OPTIONS): Dialog;\n  }\n}\n\ntype BUTTON = {\n  /**\n   * 按钮文本\n   */\n  text?: string;\n\n  /**\n   * 按钮文本是否加粗，默认为 `false`\n   */\n  bold?: boolean;\n\n  /**\n   * 点击按钮后是否关闭对话框，默认为 `true`\n   */\n  close?: boolean;\n\n  /**\n   * 点击按钮的回调函数，参数为对话框的实例\n   */\n  onClick?: (dialog: Dialog) => void;\n};\n\ntype OPTIONS = {\n  /**\n   * 对话框的标题\n   */\n  title?: string;\n\n  /**\n   * 对话框的内容\n   */\n  content?: string;\n\n  /**\n   * 按钮数组，每个按钮都是一个带按钮参数的对象\n   */\n  buttons?: BUTTON[];\n\n  /**\n   * 按钮是否垂直排列，默认为 `false`\n   */\n  stackedButtons?: boolean;\n\n  /**\n   * 添加到 `.mdui-dialog` 上的 CSS 类\n   */\n  cssClass?: string;\n\n  /**\n   * 是否监听 `hashchange` 事件，为 `true` 时可以通过 Android 的返回键或浏览器后退按钮关闭对话框，默认为 `true`\n   */\n  history?: boolean;\n\n  /**\n   * 打开对话框后是否显示遮罩层，默认为 `true`\n   */\n  overlay?: boolean;\n\n  /**\n   * 是否模态化对话框。为 `false` 时点击对话框外面的区域时关闭对话框，否则不关闭\n   */\n  modal?: boolean;\n\n  /**\n   * 按下 Esc 键时是否关闭对话框，默认为 `true`\n   */\n  closeOnEsc?: boolean;\n\n  /**\n   * 关闭对话框后是否自动销毁对话框，默认为 `true`\n   */\n  destroyOnClosed?: boolean;\n\n  /**\n   * 打开动画开始时的回调，参数为对话框实例\n   */\n  onOpen?: (dialog: Dialog) => void;\n\n  /**\n   * 打开动画结束时的回调，参数为对话框实例\n   */\n  onOpened?: (dialog: Dialog) => void;\n\n  /**\n   * 关闭动画开始时的回调，参数为对话框实例\n   */\n  onClose?: (dialog: Dialog) => void;\n\n  /**\n   * 关闭动画结束时的回调，参数为对话框实例\n   */\n  onClosed?: (dialog: Dialog) => void;\n};\n\nconst DEFAULT_BUTTON: BUTTON = {\n  text: '',\n  bold: false,\n  close: true,\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onClick: () => {},\n};\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  title: '',\n  content: '',\n  buttons: [],\n  stackedButtons: false,\n  cssClass: '',\n  history: true,\n  overlay: true,\n  modal: false,\n  closeOnEsc: true,\n  destroyOnClosed: true,\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onOpen: () => {},\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onOpened: () => {},\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onClose: () => {},\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onClosed: () => {},\n};\n\nmdui.dialog = function (options: OPTIONS): Dialog {\n  // 合并配置参数\n  options = extend({}, DEFAULT_OPTIONS, options);\n\n  each(options.buttons!, (i, button) => {\n    options.buttons![i] = extend({}, DEFAULT_BUTTON, button);\n  });\n\n  // 按钮的 HTML\n  let buttonsHTML = '';\n  if (options.buttons?.length) {\n    buttonsHTML = `<div class=\"mdui-dialog-actions${\n      options.stackedButtons ? ' mdui-dialog-actions-stacked' : ''\n    }\">`;\n\n    each(options.buttons, (_, button) => {\n      buttonsHTML +=\n        '<a href=\"javascript:void(0)\" ' +\n        `class=\"mdui-btn mdui-ripple mdui-text-color-primary ${\n          button.bold ? 'mdui-btn-bold' : ''\n        }\">${button.text}</a>`;\n    });\n\n    buttonsHTML += '</div>';\n  }\n\n  // Dialog 的 HTML\n  const HTML =\n    `<div class=\"mdui-dialog ${options.cssClass}\">` +\n    (options.title\n      ? `<div class=\"mdui-dialog-title\">${options.title}</div>`\n      : '') +\n    (options.content\n      ? `<div class=\"mdui-dialog-content\">${options.content}</div>`\n      : '') +\n    buttonsHTML +\n    '</div>';\n\n  // 实例化 Dialog\n  const instance = new mdui.Dialog(HTML, {\n    history: options.history,\n    overlay: options.overlay,\n    modal: options.modal,\n    closeOnEsc: options.closeOnEsc,\n    destroyOnClosed: options.destroyOnClosed,\n  });\n\n  // 绑定按钮事件\n  if (options.buttons?.length) {\n    instance.$element\n      .find('.mdui-dialog-actions .mdui-btn')\n      .each((index, button) => {\n        $(button).on('click', () => {\n          options.buttons![index].onClick!(instance);\n\n          if (options.buttons![index].close) {\n            instance.close();\n          }\n        });\n      });\n  }\n\n  // 绑定打开关闭事件\n  instance.$element\n    .on('open.mdui.dialog', () => {\n      options.onOpen!(instance);\n    })\n    .on('opened.mdui.dialog', () => {\n      options.onOpened!(instance);\n    })\n    .on('close.mdui.dialog', () => {\n      options.onClose!(instance);\n    })\n    .on('closed.mdui.dialog', () => {\n      options.onClosed!(instance);\n    });\n\n  instance.open();\n\n  return instance;\n};\n", "import extend from 'mdui.jq/es/functions/extend';\nimport { isFunction, isUndefined } from 'mdui.jq/es/utils';\nimport mdui from '../../mdui';\nimport { Dialog } from './class';\nimport './dialog';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 打开一个警告框，可以包含标题、内容和一个确认按钮\n     * @param text 警告框内容\n     * @param title 警告框标题\n     * @param onConfirm 点击确认按钮的回调函数，参数为对话框实例\n     * @param options 配置参数\n     */\n    alert(\n      text: string,\n      title: string,\n      onConfirm?: (dialog: Dialog) => void,\n      options?: OPTIONS,\n    ): Dialog;\n\n    /**\n     * 打开一个警告框，可以包含内容，和一个确认按钮\n     * @param text 警告框内容\n     * @param onConfirm 点击确认按钮的回调函数，参数为对话框实例\n     * @param options 配置参数\n     */\n    alert(\n      text: string,\n      onConfirm?: (dialog: Dialog) => void,\n      options?: OPTIONS,\n    ): Dialog;\n  }\n}\n\ntype OPTIONS = {\n  /**\n   * 确认按钮的文本\n   */\n  confirmText?: string;\n\n  /**\n   * 是否监听 hashchange 事件，为 `true` 时可以通过 Android 的返回键或浏览器后退按钮关闭对话框，默认为 `true`\n   */\n  history?: boolean;\n\n  /**\n   * 是否模态化对话框。为 `false` 时点击对话框外面的区域时关闭对话框，否则不关闭，默认为 `false`\n   */\n  modal?: boolean;\n\n  /**\n   * 按下 Esc 键时是否关闭对话框，默认为 `true`\n   */\n  closeOnEsc?: boolean;\n\n  /**\n   * 是否在按下确认按钮时是否关闭对话框\n   */\n  closeOnConfirm?: boolean;\n};\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  confirmText: 'ok',\n  history: true,\n  modal: false,\n  closeOnEsc: true,\n  closeOnConfirm: true,\n};\n\nmdui.alert = function (\n  text: string,\n  title?: any,\n  onConfirm?: any,\n  options?: any,\n): Dialog {\n  if (isFunction(title)) {\n    options = onConfirm;\n    onConfirm = title;\n    title = '';\n  }\n\n  if (isUndefined(onConfirm)) {\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    onConfirm = (): void => {};\n  }\n\n  if (isUndefined(options)) {\n    options = {};\n  }\n\n  options = extend({}, DEFAULT_OPTIONS, options);\n\n  return mdui.dialog({\n    title: title,\n    content: text,\n    buttons: [\n      {\n        text: options.confirmText,\n        bold: false,\n        close: options.closeOnConfirm,\n        onClick: onConfirm,\n      },\n    ],\n    cssClass: 'mdui-dialog-alert',\n    history: options.history,\n    modal: options.modal,\n    closeOnEsc: options.closeOnEsc,\n  });\n};\n", "import extend from 'mdui.jq/es/functions/extend';\nimport { isFunction, isUndefined } from 'mdui.jq/es/utils';\nimport mdui from '../../mdui';\nimport { Dialog } from './class';\nimport './dialog';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 打开一个确认框，可以包含标题、内容、一个确认按钮和一个取消按钮\n     * @param text 确认框内容\n     * @param title 确认框标题\n     * @param onConfirm 点击确认按钮的回调函数，参数为对话框实例\n     * @param onCancel 点击取消按钮的回调函数，参数为对话框实例\n     * @param options 配置参数\n     */\n    confirm(\n      text: string,\n      title: string,\n      onConfirm?: (dialog: Dialog) => void,\n      onCancel?: (dialog: Dialog) => void,\n      options?: OPTIONS,\n    ): Dialog;\n\n    /**\n     * 打开一个确认框，可以包含内容、一个确认按钮和一个取消按钮\n     * @param text 确认框内容\n     * @param onConfirm 点击确认按钮的回调函数，参数为对话框实例\n     * @param onCancel 点击取消按钮的回调函数，参数为对话框实例\n     * @param options 配置参数\n     */\n    confirm(\n      text: string,\n      onConfirm?: (dialog: Dialog) => void,\n      onCancel?: (dialog: Dialog) => void,\n      options?: OPTIONS,\n    ): Dialog;\n  }\n}\n\ntype OPTIONS = {\n  /**\n   * 确认按钮的文本\n   */\n  confirmText?: string;\n\n  /**\n   * 取消按钮的文本\n   */\n  cancelText?: string;\n\n  /**\n   * 是否监听 hashchange 事件，为 `true` 时可以通过 Android 的返回键或浏览器后退按钮关闭对话框，默认为 `true`\n   */\n  history?: boolean;\n\n  /**\n   * 是否模态化对话框。为 `false` 时点击对话框外面的区域时关闭对话框，否则不关闭，默认为 `false`\n   */\n  modal?: boolean;\n\n  /**\n   * 按下 Esc 键时是否关闭对话框，默认为 `true`\n   */\n  closeOnEsc?: boolean;\n\n  /**\n   * 是否在按下取消按钮时是否关闭对话框\n   */\n  closeOnCancel?: boolean;\n\n  /**\n   * 是否在按下确认按钮时是否关闭对话框\n   */\n  closeOnConfirm?: boolean;\n};\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  confirmText: 'ok',\n  cancelText: 'cancel',\n  history: true,\n  modal: false,\n  closeOnEsc: true,\n  closeOnCancel: true,\n  closeOnConfirm: true,\n};\n\nmdui.confirm = function (\n  text: string,\n  title?: any,\n  onConfirm?: any,\n  onCancel?: any,\n  options?: any,\n): Dialog {\n  if (isFunction(title)) {\n    options = onCancel;\n    onCancel = onConfirm;\n    onConfirm = title;\n    title = '';\n  }\n\n  if (isUndefined(onConfirm)) {\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    onConfirm = (): void => {};\n  }\n\n  if (isUndefined(onCancel)) {\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    onCancel = (): void => {};\n  }\n\n  if (isUndefined(options)) {\n    options = {};\n  }\n\n  options = extend({}, DEFAULT_OPTIONS, options);\n\n  return mdui.dialog({\n    title: title,\n    content: text,\n    buttons: [\n      {\n        text: options.cancelText,\n        bold: false,\n        close: options.closeOnCancel,\n        onClick: onCancel,\n      },\n      {\n        text: options.confirmText,\n        bold: false,\n        close: options.closeOnConfirm,\n        onClick: onConfirm,\n      },\n    ],\n    cssClass: 'mdui-dialog-confirm',\n    history: options.history,\n    modal: options.modal,\n    closeOnEsc: options.closeOnEsc,\n  });\n};\n", "import extend from 'mdui.jq/es/functions/extend';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/val';\nimport { isFunction, isUndefined } from 'mdui.jq/es/utils';\nimport mdui from '../../mdui';\nimport '../textfield';\nimport { Dialog } from './class';\nimport './dialog';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 打开一个提示用户输入的对话框，可以包含标题、文本框标签、文本框、一个确认按钮和一个取消按钮\n     * @param label 文本框浮动标签的文本\n     * @param title 标题\n     * @param onConfirm 点击确认按钮的回调。含两个参数，分别为文本框的值和对话框实例\n     * @param onCancel 点击取消按钮的回调。含两个参数，分别为文本框的值和对话框实例\n     * @param options 配置参数\n     */\n    prompt(\n      label: string,\n      title: string,\n      onConfirm?: (value: string, dialog: Dialog) => void,\n      onCancel?: (value: string, dialog: Dialog) => void,\n      options?: OPTIONS,\n    ): Dialog;\n\n    /**\n     * 打开一个提示用户输入的对话框，可以包含文本框标签、文本框、一个确认按钮和一个取消按钮\n     * @param label 文本框浮动标签的文本\n     * @param onConfirm 点击确认按钮的回调。含两个参数，分别为文本框的值和对话框实例\n     * @param onCancel 点击取消按钮的回调，含两个参数，分别为文本框的值和对话框实例\n     * @param options 配置参数\n     */\n    prompt(\n      label: string,\n      onConfirm?: (value: string, dialog: Dialog) => void,\n      onCancel?: (value: string, dialog: Dialog) => void,\n      options?: OPTIONS,\n    ): Dialog;\n  }\n}\n\ntype OPTIONS = {\n  /**\n   * 确认按钮的文本\n   */\n  confirmText?: string;\n\n  /**\n   * 取消按钮的文本\n   */\n  cancelText?: string;\n\n  /**\n   * 是否监听 hashchange 事件，为 `true` 时可以通过 Android 的返回键或浏览器后退按钮关闭对话框，默认为 `true`\n   */\n  history?: boolean;\n\n  /**\n   * 是否模态化对话框。为 `false` 时点击对话框外面的区域时关闭对话框，否则不关闭，默认为 `false`\n   */\n  modal?: boolean;\n\n  /**\n   * 是否在按下 Esc 键时是否关闭对话框，默认为 `true`\n   */\n  closeOnEsc?: boolean;\n\n  /**\n   * 是否在按下取消按钮时是否关闭对话框\n   */\n  closeOnCancel?: boolean;\n\n  /**\n   * 是否在按下确认按钮时是否关闭对话框\n   */\n  closeOnConfirm?: boolean;\n\n  /**\n   * 是否在按下 Enter 键时触发 `onConfirm` 回调函数，默认为 `false`\n   */\n  confirmOnEnter?: boolean;\n\n  /**\n   * 文本框的类型。`text`: 单行文本框； `textarea`: 多行文本框\n   */\n  type?: 'text' | 'textarea';\n\n  /**\n   * 最大输入字符数量，为 0 时表示不限制\n   */\n  maxlength?: number;\n\n  /**\n   * 文本框的默认值\n   */\n  defaultValue?: string;\n};\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  confirmText: 'ok',\n  cancelText: 'cancel',\n  history: true,\n  modal: false,\n  closeOnEsc: true,\n  closeOnCancel: true,\n  closeOnConfirm: true,\n  type: 'text',\n  maxlength: 0,\n  defaultValue: '',\n  confirmOnEnter: false,\n};\n\nmdui.prompt = function (\n  label: string,\n  title?: any,\n  onConfirm?: any,\n  onCancel?: any,\n  options?: any,\n): Dialog {\n  if (isFunction(title)) {\n    options = onCancel;\n    onCancel = onConfirm;\n    onConfirm = title;\n    title = '';\n  }\n\n  if (isUndefined(onConfirm)) {\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    onConfirm = (): void => {};\n  }\n\n  if (isUndefined(onCancel)) {\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    onCancel = (): void => {};\n  }\n\n  if (isUndefined(options)) {\n    options = {};\n  }\n\n  options = extend({}, DEFAULT_OPTIONS, options);\n\n  const content =\n    '<div class=\"mdui-textfield\">' +\n    (label ? `<label class=\"mdui-textfield-label\">${label}</label>` : '') +\n    (options.type === 'text'\n      ? `<input class=\"mdui-textfield-input\" type=\"text\" value=\"${\n          options.defaultValue\n        }\" ${\n          options.maxlength ? 'maxlength=\"' + options.maxlength + '\"' : ''\n        }/>`\n      : '') +\n    (options.type === 'textarea'\n      ? `<textarea class=\"mdui-textfield-input\" ${\n          options.maxlength ? 'maxlength=\"' + options.maxlength + '\"' : ''\n        }>${options.defaultValue}</textarea>`\n      : '') +\n    '</div>';\n\n  const onCancelClick = (dialog: Dialog): void => {\n    const value = dialog.$element.find('.mdui-textfield-input').val();\n    onCancel(value, dialog);\n  };\n\n  const onConfirmClick = (dialog: Dialog): void => {\n    const value = dialog.$element.find('.mdui-textfield-input').val();\n    onConfirm(value, dialog);\n  };\n\n  return mdui.dialog({\n    title,\n    content,\n    buttons: [\n      {\n        text: options.cancelText,\n        bold: false,\n        close: options.closeOnCancel,\n        onClick: onCancelClick,\n      },\n      {\n        text: options.confirmText,\n        bold: false,\n        close: options.closeOnConfirm,\n        onClick: onConfirmClick,\n      },\n    ],\n    cssClass: 'mdui-dialog-prompt',\n    history: options.history,\n    modal: options.modal,\n    closeOnEsc: options.closeOnEsc,\n    onOpen: (dialog) => {\n      // 初始化输入框\n      const $input = dialog.$element.find('.mdui-textfield-input');\n      mdui.updateTextFields($input);\n\n      // 聚焦到输入框\n      $input[0].focus();\n\n      // 捕捉文本框回车键，在单行文本框的情况下触发回调\n      if (options.type !== 'textarea' && options.confirmOnEnter === true) {\n        $input.on('keydown', (event) => {\n          if ((event as KeyboardEvent).keyCode === 13) {\n            const value = dialog.$element.find('.mdui-textfield-input').val();\n            onConfirm(value, dialog);\n\n            if (options.closeOnConfirm) {\n              dialog.close();\n            }\n\n            return false;\n          }\n\n          return;\n        });\n      }\n\n      // 如果是多行输入框，监听输入框的 input 事件，更新对话框高度\n      if (options.type === 'textarea') {\n        $input.on('input', () => dialog.handleUpdate());\n      }\n\n      // 有字符数限制时，加载完文本框后 DOM 会变化，需要更新对话框高度\n      if (options.maxlength) {\n        dialog.handleUpdate();\n      }\n    },\n  });\n};\n", "import $ from 'mdui.jq/es/$';\nimport extend from 'mdui.jq/es/functions/extend';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/appendTo';\nimport 'mdui.jq/es/methods/attr';\nimport 'mdui.jq/es/methods/css';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/height';\nimport 'mdui.jq/es/methods/html';\nimport 'mdui.jq/es/methods/offset';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/removeClass';\nimport 'mdui.jq/es/methods/width';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport mdui from '../../mdui';\nimport '../../jq_extends/methods/transformOrigin';\nimport '../../jq_extends/methods/transitionEnd';\nimport '../../jq_extends/static/guid';\nimport { componentEvent } from '../../utils/componentEvent';\nimport { $window } from '../../utils/dom';\nimport { isAllow, register, unlockEvent } from '../../utils/touchHandler';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * Tooltip 组件\n     *\n     * 请通过 `new mdui.Tooltip()` 调用\n     */\n    Tooltip: {\n      /**\n       * 实例化 Tooltip 组件\n       * @param selector CSS 选择器、或 DOM 元素、或 JQ 对象\n       * @param options 配置参数\n       */\n      new (\n        selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n        options?: OPTIONS,\n      ): Tooltip;\n    };\n  }\n}\n\ntype POSITION = 'auto' | 'bottom' | 'top' | 'left' | 'right';\n\ntype OPTIONS = {\n  /**\n   * Tooltip 的位置。取值范围包括 `auto`、`bottom`、`top`、`left`、`right`。\n   * 为 `auto` 时，会自动判断位置。默认在下方。优先级为 `bottom` > `top` > `left` > `right`。\n   * 默认为 `auto`\n   */\n  position?: POSITION;\n\n  /**\n   * 延时触发，单位毫秒。默认为 `0`，即没有延时。\n   */\n  delay?: number;\n\n  /**\n   * Tooltip 的内容\n   */\n  content?: string;\n};\n\ntype STATE = 'opening' | 'opened' | 'closing' | 'closed';\ntype EVENT = 'open' | 'opened' | 'close' | 'closed';\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  position: 'auto',\n  delay: 0,\n  content: '',\n};\n\nclass Tooltip {\n  /**\n   * 触发 tooltip 元素的 JQ 对象\n   */\n  public $target: JQ;\n\n  /**\n   * tooltip 元素的 JQ 对象\n   */\n  public $element: JQ;\n\n  /**\n   * 配置参数\n   */\n  public options: OPTIONS = extend({}, DEFAULT_OPTIONS);\n\n  /**\n   * 当前 tooltip 的状态\n   */\n  private state: STATE = 'closed';\n\n  /**\n   * setTimeout 的返回值\n   */\n  private timeoutId: any = null;\n\n  public constructor(\n    selector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    options: OPTIONS = {},\n  ) {\n    this.$target = $(selector).first();\n\n    extend(this.options, options);\n\n    // 创建 Tooltip HTML\n    this.$element = $(\n      `<div class=\"mdui-tooltip\" id=\"${$.guid()}\">${\n        this.options.content\n      }</div>`,\n    ).appendTo(document.body);\n\n    // 绑定事件。元素处于 disabled 状态时无法触发鼠标事件，为了统一，把 touch 事件也禁用\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const that = this;\n    this.$target\n      .on('touchstart mouseenter', function (event) {\n        if (that.isDisabled(this as HTMLElement)) {\n          return;\n        }\n\n        if (!isAllow(event)) {\n          return;\n        }\n\n        register(event);\n\n        that.open();\n      })\n      .on('touchend mouseleave', function (event) {\n        if (that.isDisabled(this as HTMLElement)) {\n          return;\n        }\n\n        if (!isAllow(event)) {\n          return;\n        }\n\n        that.close();\n      })\n      .on(unlockEvent, function (event) {\n        if (that.isDisabled(this as HTMLElement)) {\n          return;\n        }\n\n        register(event);\n      });\n  }\n\n  /**\n   * 元素是否已禁用\n   * @param element\n   */\n  private isDisabled(element: HTMLElement): boolean {\n    return (\n      (element as HTMLInputElement).disabled ||\n      $(element).attr('disabled') !== undefined\n    );\n  }\n\n  /**\n   * 是否是桌面设备\n   */\n  private isDesktop(): boolean {\n    return $window.width() > 1024;\n  }\n\n  /**\n   * 设置 Tooltip 的位置\n   */\n  private setPosition(): void {\n    let marginLeft: number;\n    let marginTop: number;\n\n    // 触发的元素\n    const targetProps = this.$target[0].getBoundingClientRect();\n\n    // 触发的元素和 Tooltip 之间的距离\n    const targetMargin = this.isDesktop() ? 14 : 24;\n\n    // Tooltip 的宽度和高度\n    const tooltipWidth = this.$element[0].offsetWidth;\n    const tooltipHeight = this.$element[0].offsetHeight;\n\n    // Tooltip 的方向\n    let position: POSITION = this.options.position!;\n\n    // 自动判断位置，加 2px，使 Tooltip 距离窗口边框至少有 2px 的间距\n    if (position === 'auto') {\n      if (\n        targetProps.top +\n          targetProps.height +\n          targetMargin +\n          tooltipHeight +\n          2 <\n        $window.height()\n      ) {\n        position = 'bottom';\n      } else if (targetMargin + tooltipHeight + 2 < targetProps.top) {\n        position = 'top';\n      } else if (targetMargin + tooltipWidth + 2 < targetProps.left) {\n        position = 'left';\n      } else if (\n        targetProps.width + targetMargin + tooltipWidth + 2 <\n        $window.width() - targetProps.left\n      ) {\n        position = 'right';\n      } else {\n        position = 'bottom';\n      }\n    }\n\n    // 设置位置\n    switch (position) {\n      case 'bottom':\n        marginLeft = -1 * (tooltipWidth / 2);\n        marginTop = targetProps.height / 2 + targetMargin;\n        this.$element.transformOrigin('top center');\n        break;\n\n      case 'top':\n        marginLeft = -1 * (tooltipWidth / 2);\n        marginTop =\n          -1 * (tooltipHeight + targetProps.height / 2 + targetMargin);\n        this.$element.transformOrigin('bottom center');\n        break;\n\n      case 'left':\n        marginLeft = -1 * (tooltipWidth + targetProps.width / 2 + targetMargin);\n        marginTop = -1 * (tooltipHeight / 2);\n        this.$element.transformOrigin('center right');\n        break;\n\n      case 'right':\n        marginLeft = targetProps.width / 2 + targetMargin;\n        marginTop = -1 * (tooltipHeight / 2);\n        this.$element.transformOrigin('center left');\n        break;\n    }\n\n    const targetOffset = this.$target.offset();\n\n    this.$element.css({\n      top: `${targetOffset.top + targetProps.height / 2}px`,\n      left: `${targetOffset.left + targetProps.width / 2}px`,\n      'margin-left': `${marginLeft}px`,\n      'margin-top': `${marginTop}px`,\n    });\n  }\n\n  /**\n   * 触发组件事件\n   * @param name\n   */\n  private triggerEvent(name: EVENT): void {\n    componentEvent(name, 'tooltip', this.$target, this);\n  }\n\n  /**\n   * 动画结束回调\n   */\n  private transitionEnd(): void {\n    if (this.$element.hasClass('mdui-tooltip-open')) {\n      this.state = 'opened';\n      this.triggerEvent('opened');\n    } else {\n      this.state = 'closed';\n      this.triggerEvent('closed');\n    }\n  }\n\n  /**\n   * 当前 tooltip 是否为打开状态\n   */\n  private isOpen(): boolean {\n    return this.state === 'opening' || this.state === 'opened';\n  }\n\n  /**\n   * 执行打开 tooltip\n   */\n  private doOpen(): void {\n    this.state = 'opening';\n    this.triggerEvent('open');\n\n    this.$element\n      .addClass('mdui-tooltip-open')\n      .transitionEnd(() => this.transitionEnd());\n  }\n\n  /**\n   * 打开 Tooltip\n   * @param options 允许每次打开时设置不同的参数\n   */\n  public open(options?: OPTIONS): void {\n    if (this.isOpen()) {\n      return;\n    }\n\n    const oldOptions = extend({}, this.options);\n\n    if (options) {\n      extend(this.options, options);\n    }\n\n    // tooltip 的内容有更新\n    if (oldOptions.content !== this.options.content) {\n      this.$element.html(this.options.content);\n    }\n\n    this.setPosition();\n\n    if (this.options.delay) {\n      this.timeoutId = setTimeout(() => this.doOpen(), this.options.delay);\n    } else {\n      this.timeoutId = null;\n      this.doOpen();\n    }\n  }\n\n  /**\n   * 关闭 Tooltip\n   */\n  public close(): void {\n    if (this.timeoutId) {\n      clearTimeout(this.timeoutId);\n      this.timeoutId = null;\n    }\n\n    if (!this.isOpen()) {\n      return;\n    }\n\n    this.state = 'closing';\n    this.triggerEvent('close');\n\n    this.$element\n      .removeClass('mdui-tooltip-open')\n      .transitionEnd(() => this.transitionEnd());\n  }\n\n  /**\n   * 切换 Tooltip 的打开状态\n   */\n  public toggle(): void {\n    this.isOpen() ? this.close() : this.open();\n  }\n\n  /**\n   * 获取 Tooltip 状态。共包含四种状态：`opening`、`opened`、`closing`、`closed`\n   */\n  public getState(): STATE {\n    return this.state;\n  }\n}\n\nmdui.Tooltip = Tooltip;\n", "import $ from 'mdui.jq/es/$';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/on';\nimport mdui from '../../mdui';\nimport { $document } from '../../utils/dom';\nimport { parseOptions } from '../../utils/parseOptions';\nimport './index';\n\nconst customAttr = 'mdui-tooltip';\nconst dataName = '_mdui_tooltip';\n\n$(() => {\n  // mouseenter 不能冒泡，所以这里用 mouseover 代替\n  $document.on('touchstart mouseover', `[${customAttr}]`, function () {\n    const $target = $(this);\n    let instance = $target.data(dataName);\n\n    if (!instance) {\n      instance = new mdui.Tooltip(\n        this as HTMLElement,\n        parseOptions(this as HTMLElement, customAttr),\n      );\n      $target.data(dataName, instance);\n    }\n  });\n});\n", "import $ from 'mdui.jq/es/$';\nimport extend from 'mdui.jq/es/functions/extend';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/appendTo';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/off';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/parents';\nimport 'mdui.jq/es/methods/remove';\nimport { isString } from 'mdui.jq/es/utils';\nimport mdui from '../../mdui';\nimport '../../jq_extends/methods/reflow';\nimport '../../jq_extends/methods/transform';\nimport '../../jq_extends/methods/transitionEnd';\nimport { $document } from '../../utils/dom';\nimport { dequeue, queue } from '../../utils/queue';\nimport { startEvent } from '../../utils/touchHandler';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 打开一个 Snackbar\n     * @param message Snackbar 的文本\n     * @param options 配置参数\n     */\n    snackbar(message: string, options?: OPTIONS): Snackbar;\n\n    /**\n     * 打开一个 Snackbar\n     * @param options 配置参数\n     */\n    snackbar(options: OPTIONS): Snackbar;\n  }\n}\n\ntype OPTIONS = {\n  /**\n   * Snackbar 的文本。通过 `mdui.snackbar(options)` 调用时，该参数不能为空\n   */\n  message?: string;\n\n  /**\n   * 在用户没有操作时多长时间自动隐藏，单位（毫秒）。为 `0` 时表示不自动关闭，默认为 `4000`\n   */\n  timeout?: number;\n\n  /**\n   * Snackbar 的位置，默认为 `bottom`。\n   * 取值范围包括：\n   *   `bottom`: 下方\n   *   `top`: 上方\n   *   `left-top`: 左上角\n   *   `left-bottom`: 左下角\n   *   `right-top`: 右上角\n   *   `right-bottom`: 右下角\n   */\n  position?:\n    | 'bottom'\n    | 'top'\n    | 'left-top'\n    | 'left-bottom'\n    | 'right-top'\n    | 'right-bottom';\n\n  /**\n   * 按钮的文本\n   */\n  buttonText?: string;\n\n  /**\n   * 按钮的文本颜色，可以是颜色名或颜色值，如 `red`、`#ffffff`、`rgba(255, 255, 255, 0.3)` 等。默认为 `#90CAF9`\n   */\n  buttonColor?: string;\n\n  /**\n   * 点击按钮时是否关闭 Snackbar，默认为 `true`\n   */\n  closeOnButtonClick?: boolean;\n\n  /**\n   * 点击或触摸 Snackbar 以外的区域时是否关闭 Snackbar，默认为 `true`\n   */\n  closeOnOutsideClick?: boolean;\n\n  /**\n   * 在 Snackbar 上点击的回调函数，参数为 Snackbar 的实例\n   */\n  onClick?: (snackbar: Snackbar) => void;\n\n  /**\n   * 点击 Snackbar 上的按钮时的回调函数，参数为 Snackbar 的实例\n   */\n  onButtonClick?: (snackbar: Snackbar) => void;\n\n  /**\n   * Snackbar 开始打开时的回调函数，参数为 Snackbar 的实例\n   */\n  onOpen?: (snackbar: Snackbar) => void;\n\n  /**\n   * Snackbar 打开后的回调函数，参数为 Snackbar 的实例\n   */\n  onOpened?: (snackbar: Snackbar) => void;\n\n  /**\n   * Snackbar 开始关闭时的回调函数，参数为 Snackbar 的实例\n   */\n  onClose?: (snackbar: Snackbar) => void;\n\n  /**\n   * Snackbar 关闭后的回调函数，参数为 Snackbar 的实例\n   */\n  onClosed?: (snackbar: Snackbar) => void;\n};\n\ntype STATE = 'opening' | 'opened' | 'closing' | 'closed';\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  message: '',\n  timeout: 4000,\n  position: 'bottom',\n  buttonText: '',\n  buttonColor: '',\n  closeOnButtonClick: true,\n  closeOnOutsideClick: true,\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onClick: () => {},\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onButtonClick: () => {},\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onOpen: () => {},\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onOpened: () => {},\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onClose: () => {},\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onClosed: () => {},\n};\n\n/**\n * 当前打开着的 Snackbar\n */\nlet currentInst: null | Snackbar = null;\n\n/**\n * 队列名\n */\nconst queueName = '_mdui_snackbar';\n\nclass Snackbar {\n  /**\n   * Snackbar 元素\n   */\n  public $element: JQ;\n  /**\n   * 配置参数\n   */\n  public options: OPTIONS = extend({}, DEFAULT_OPTIONS);\n\n  /**\n   * 当前 Snackbar 的状态\n   */\n  private state: STATE = 'closed';\n\n  /**\n   * setTimeout 的 ID\n   */\n  private timeoutId: any = null;\n\n  public constructor(options: OPTIONS) {\n    extend(this.options, options);\n\n    // 按钮颜色\n    let buttonColorStyle = '';\n    let buttonColorClass = '';\n\n    if (\n      this.options.buttonColor!.indexOf('#') === 0 ||\n      this.options.buttonColor!.indexOf('rgb') === 0\n    ) {\n      buttonColorStyle = `style=\"color:${this.options.buttonColor}\"`;\n    } else if (this.options.buttonColor !== '') {\n      buttonColorClass = `mdui-text-color-${this.options.buttonColor}`;\n    }\n\n    // 添加 HTML\n    this.$element = $(\n      '<div class=\"mdui-snackbar\">' +\n        `<div class=\"mdui-snackbar-text\">${this.options.message}</div>` +\n        (this.options.buttonText\n          ? `<a href=\"javascript:void(0)\" class=\"mdui-snackbar-action mdui-btn mdui-ripple mdui-ripple-white ${buttonColorClass}\" ${buttonColorStyle}>${this.options.buttonText}</a>`\n          : '') +\n        '</div>',\n    ).appendTo(document.body);\n\n    // 设置位置\n    this.setPosition('close');\n\n    this.$element.reflow().addClass(`mdui-snackbar-${this.options.position}`);\n  }\n\n  /**\n   * 点击 Snackbar 外面的区域关闭\n   * @param event\n   */\n  private closeOnOutsideClick(event: Event): void {\n    const $target = $(event.target as HTMLElement);\n\n    if (\n      !$target.hasClass('mdui-snackbar') &&\n      !$target.parents('.mdui-snackbar').length\n    ) {\n      currentInst!.close();\n    }\n  }\n\n  /**\n   * 设置 Snackbar 的位置\n   * @param state\n   */\n  private setPosition(state: 'open' | 'close'): void {\n    const snackbarHeight = this.$element[0].clientHeight;\n    const position = this.options.position;\n\n    let translateX;\n    let translateY;\n\n    // translateX\n    if (position === 'bottom' || position === 'top') {\n      translateX = '-50%';\n    } else {\n      translateX = '0';\n    }\n\n    // translateY\n    if (state === 'open') {\n      translateY = '0';\n    } else {\n      if (position === 'bottom') {\n        translateY = snackbarHeight;\n      }\n\n      if (position === 'top') {\n        translateY = -snackbarHeight;\n      }\n\n      if (position === 'left-top' || position === 'right-top') {\n        translateY = -snackbarHeight - 24;\n      }\n\n      if (position === 'left-bottom' || position === 'right-bottom') {\n        translateY = snackbarHeight + 24;\n      }\n    }\n\n    this.$element.transform(`translate(${translateX},${translateY}px`);\n  }\n\n  /**\n   * 打开 Snackbar\n   */\n  public open(): void {\n    if (this.state === 'opening' || this.state === 'opened') {\n      return;\n    }\n\n    // 如果当前有正在显示的 Snackbar，则先加入队列，等旧 Snackbar 关闭后再打开\n    if (currentInst) {\n      queue(queueName, () => this.open());\n      return;\n    }\n\n    currentInst = this;\n\n    // 开始打开\n    this.state = 'opening';\n    this.options.onOpen!(this);\n\n    this.setPosition('open');\n\n    this.$element.transitionEnd(() => {\n      if (this.state !== 'opening') {\n        return;\n      }\n\n      this.state = 'opened';\n      this.options.onOpened!(this);\n\n      // 有按钮时绑定事件\n      if (this.options.buttonText) {\n        this.$element.find('.mdui-snackbar-action').on('click', () => {\n          this.options.onButtonClick!(this);\n          if (this.options.closeOnButtonClick) {\n            this.close();\n          }\n        });\n      }\n\n      // 点击 snackbar 的事件\n      this.$element.on('click', (event) => {\n        if (!$(event.target as HTMLElement).hasClass('mdui-snackbar-action')) {\n          this.options.onClick!(this);\n        }\n      });\n\n      // 点击 Snackbar 外面的区域关闭\n      if (this.options.closeOnOutsideClick) {\n        $document.on(startEvent, this.closeOnOutsideClick);\n      }\n\n      // 超时后自动关闭\n      if (this.options.timeout) {\n        this.timeoutId = setTimeout(() => this.close(), this.options.timeout);\n      }\n    });\n  }\n\n  /**\n   * 关闭 Snackbar\n   */\n  public close(): void {\n    if (this.state === 'closing' || this.state === 'closed') {\n      return;\n    }\n\n    if (this.timeoutId) {\n      clearTimeout(this.timeoutId);\n    }\n\n    if (this.options.closeOnOutsideClick) {\n      $document.off(startEvent, this.closeOnOutsideClick);\n    }\n\n    this.state = 'closing';\n    this.options.onClose!(this);\n\n    this.setPosition('close');\n\n    this.$element.transitionEnd(() => {\n      if (this.state !== 'closing') {\n        return;\n      }\n\n      currentInst = null;\n      this.state = 'closed';\n      this.options.onClosed!(this);\n      this.$element.remove();\n      dequeue(queueName);\n    });\n  }\n}\n\nmdui.snackbar = function (message: any, options: any = {}): Snackbar {\n  if (isString(message)) {\n    options.message = message;\n  } else {\n    options = message;\n  }\n\n  const instance = new Snackbar(options);\n\n  instance.open();\n\n  return instance;\n};\n", "import $ from 'mdui.jq/es/$';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/html';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport { isUndefined } from 'mdui.jq/es/utils';\nimport mdui from '../../mdui';\nimport '../../global/mutation';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * 如果需要修改已有的圆形进度条组件，需要调用该方法来重新初始化组件。\n     *\n     * 若传入了参数，则只重新初始化该参数对应的圆形进度条。若没有传入参数，则重新初始化所有圆形进度条。\n     * @param selector CSS 选择器、或 DOM 元素、或 DOM 元素组成的数组、或 JQ 对象\n     */\n    updateSpinners(\n      selector?: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    ): void;\n  }\n}\n\n/**\n * layer 的 HTML 结构\n * @param index\n */\nfunction layerHTML(index: number | false = false): string {\n  return (\n    `<div class=\"mdui-spinner-layer ${\n      index ? `mdui-spinner-layer-${index}` : ''\n    }\">` +\n    '<div class=\"mdui-spinner-circle-clipper mdui-spinner-left\">' +\n    '<div class=\"mdui-spinner-circle\"></div>' +\n    '</div>' +\n    '<div class=\"mdui-spinner-gap-patch\">' +\n    '<div class=\"mdui-spinner-circle\"></div>' +\n    '</div>' +\n    '<div class=\"mdui-spinner-circle-clipper mdui-spinner-right\">' +\n    '<div class=\"mdui-spinner-circle\"></div>' +\n    '</div>' +\n    '</div>'\n  );\n}\n\n/**\n * 填充 HTML\n * @param spinner\n */\nfunction fillHTML(spinner: HTMLElement): void {\n  const $spinner = $(spinner);\n\n  const layer = $spinner.hasClass('mdui-spinner-colorful')\n    ? layerHTML(1) + layerHTML(2) + layerHTML(3) + layerHTML(4)\n    : layerHTML();\n\n  $spinner.html(layer);\n}\n\n$(() => {\n  // 页面加载完后自动填充 HTML 结构\n  mdui.mutation('.mdui-spinner', function () {\n    fillHTML(this);\n  });\n});\n\nmdui.updateSpinners = function (\n  selector?: Selector | HTMLElement | ArrayLike<HTMLElement>,\n): void {\n  const $elements = isUndefined(selector) ? $('.mdui-spinner') : $(selector);\n\n  $elements.each(function () {\n    fillHTML(this);\n  });\n};\n", "import $ from 'mdui.jq/es/$';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/children';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/is';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/parent';\nimport 'mdui.jq/es/methods/removeClass';\nimport mdui from '../../mdui';\nimport '../../global/mutation';\nimport { componentEvent } from '../../utils/componentEvent';\nimport { $document } from '../../utils/dom';\nimport '../headroom';\n\n$(() => {\n  // 切换导航项\n  $document.on('click', '.mdui-bottom-nav>a', function () {\n    const $item = $(this as HTMLElement);\n    const $bottomNav = $item.parent();\n\n    $bottomNav.children('a').each((index, item) => {\n      const isThis = $item.is(item);\n\n      if (isThis) {\n        componentEvent('change', 'bottomNav', $bottomNav[0], undefined, {\n          index,\n        });\n      }\n\n      isThis\n        ? $(item).addClass('mdui-bottom-nav-active')\n        : $(item).removeClass('mdui-bottom-nav-active');\n    });\n  });\n\n  // 滚动时隐藏 mdui-bottom-nav-scroll-hide\n  mdui.mutation('.mdui-bottom-nav-scroll-hide', function () {\n    new mdui.Headroom(this, {\n      pinnedClass: 'mdui-headroom-pinned-down',\n      unpinnedClass: 'mdui-headroom-unpinned-down',\n    });\n  });\n});\n", "import $ from 'mdui.jq/es/$';\nimport contains from 'mdui.jq/es/functions/contains';\nimport extend from 'mdui.jq/es/functions/extend';\nimport { JQ } from 'mdui.jq/es/JQ';\nimport 'mdui.jq/es/methods/addClass';\nimport 'mdui.jq/es/methods/attr';\nimport 'mdui.jq/es/methods/children';\nimport 'mdui.jq/es/methods/css';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/each';\nimport 'mdui.jq/es/methods/find';\nimport 'mdui.jq/es/methods/first';\nimport 'mdui.jq/es/methods/hasClass';\nimport 'mdui.jq/es/methods/height';\nimport 'mdui.jq/es/methods/is';\nimport 'mdui.jq/es/methods/on';\nimport 'mdui.jq/es/methods/parent';\nimport 'mdui.jq/es/methods/parents';\nimport 'mdui.jq/es/methods/removeClass';\nimport 'mdui.jq/es/methods/width';\nimport Selector from 'mdui.jq/es/types/Selector';\nimport mdui from '../../mdui';\nimport '../../jq_extends/methods/transformOrigin';\nimport '../../jq_extends/methods/transitionEnd';\nimport '../../jq_extends/static/throttle';\nimport { componentEvent } from '../../utils/componentEvent';\nimport { $document, $window } from '../../utils/dom';\n\ndeclare module '../../interfaces/MduiStatic' {\n  interface MduiStatic {\n    /**\n     * Menu 组件\n     *\n     * 请通过 `new mdui.Menu()` 调用\n     */\n    Menu: {\n      /**\n       * 实例化 Menu 组件\n       * @param anchorSelector 触发菜单的元素的 CSS 选择器、或 DOM 元素、或 JQ 对象\n       * @param menuSelector 菜单的 CSS 选择器、或 DOM 元素、或 JQ 对象\n       * @param options 配置参数\n       */\n      new (\n        anchorSelector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n        menuSelector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n        options?: OPTIONS,\n      ): Menu;\n    };\n  }\n}\n\ntype OPTIONS = {\n  /**\n   * 菜单相对于触发它的元素的位置，默认为 `auto`。\n   * 取值范围包括：\n   *   `top`: 菜单在触发它的元素的上方\n   *   `bottom`: 菜单在触发它的元素的下方\n   *   `center`: 菜单在窗口中垂直居中\n   *   `auto`: 自动判断位置。优先级为：`bottom` > `top` > `center`\n   */\n  position?: 'auto' | 'top' | 'bottom' | 'center';\n\n  /**\n   * 菜单与触发它的元素的对其方式，默认为 `auto`。\n   * 取值范围包括：\n   *   `left`: 菜单与触发它的元素左对齐\n   *   `right`: 菜单与触发它的元素右对齐\n   *   `center`: 菜单在窗口中水平居中\n   *   `auto`: 自动判断位置：优先级为：`left` > `right` > `center`\n   */\n  align?: 'auto' | 'left' | 'right' | 'center';\n\n  /**\n   * 菜单与窗口边框至少保持多少间距，单位为 px，默认为 `16`\n   */\n  gutter?: number;\n\n  /**\n   * 菜单的定位方式，默认为 `false`。\n   * 为 `true` 时，菜单使用 fixed 定位。在页面滚动时，菜单将保持在窗口固定位置，不随滚动条滚动。\n   * 为 `false` 时，菜单使用 absolute 定位。在页面滚动时，菜单将随着页面一起滚动。\n   */\n  fixed?: boolean;\n\n  /**\n   * 菜单是否覆盖在触发它的元素的上面，默认为 `auto`\n   * 为 `true` 时，使菜单覆盖在触发它的元素的上面\n   * 为 `false` 时，使菜单不覆盖触发它的元素\n   * 为 `auto` 时，简单菜单覆盖触发它的元素。级联菜单不覆盖触发它的元素\n   */\n  covered?: boolean | 'auto';\n\n  /**\n   * 子菜单的触发方式，默认为 `hover`\n   * 为 `click` 时，点击时触发子菜单\n   * 为 `hover` 时，鼠标悬浮时触发子菜单\n   */\n  subMenuTrigger?: 'click' | 'hover';\n\n  /**\n   * 子菜单的触发延迟时间（单位：毫秒），只有在 `subMenuTrigger: hover` 时，这个参数才有效，默认为 `200`\n   */\n  subMenuDelay?: number;\n};\n\ntype EVENT = 'open' | 'opened' | 'close' | 'closed';\ntype STATE = 'opening' | 'opened' | 'closing' | 'closed';\n\nconst DEFAULT_OPTIONS: OPTIONS = {\n  position: 'auto',\n  align: 'auto',\n  gutter: 16,\n  fixed: false,\n  covered: 'auto',\n  subMenuTrigger: 'hover',\n  subMenuDelay: 200,\n};\n\nclass Menu {\n  /**\n   * 触发菜单的元素的 JQ 对象\n   */\n  public $anchor: JQ;\n\n  /**\n   * 菜单元素的 JQ 对象\n   */\n  public $element: JQ;\n\n  /**\n   * 配置参数\n   */\n  public options: OPTIONS = extend({}, DEFAULT_OPTIONS);\n\n  /**\n   * 当前菜单状态\n   */\n  private state: STATE = 'closed';\n\n  /**\n   * 是否是级联菜单\n   */\n  private isCascade: boolean;\n\n  /**\n   * 菜单是否覆盖在触发它的元素的上面\n   */\n  private isCovered: boolean;\n\n  public constructor(\n    anchorSelector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    menuSelector: Selector | HTMLElement | ArrayLike<HTMLElement>,\n    options: OPTIONS = {},\n  ) {\n    this.$anchor = $(anchorSelector).first();\n    this.$element = $(menuSelector).first();\n\n    // 触发菜单的元素 和 菜单必须是同级的元素，否则菜单可能不能定位\n    if (!this.$anchor.parent().is(this.$element.parent())) {\n      throw new Error('anchorSelector and menuSelector must be siblings');\n    }\n\n    extend(this.options, options);\n\n    // 是否是级联菜单\n    this.isCascade = this.$element.hasClass('mdui-menu-cascade');\n\n    // covered 参数处理\n    this.isCovered =\n      this.options.covered === 'auto' ? !this.isCascade : this.options.covered!;\n\n    // 点击触发菜单切换\n    this.$anchor.on('click', () => this.toggle());\n\n    // 点击菜单外面区域关闭菜单\n    $document.on('click touchstart', (event: Event) => {\n      const $target = $(event.target as HTMLElement);\n\n      if (\n        this.isOpen() &&\n        !$target.is(this.$element) &&\n        !contains(this.$element[0], $target[0]) &&\n        !$target.is(this.$anchor) &&\n        !contains(this.$anchor[0], $target[0])\n      ) {\n        this.close();\n      }\n    });\n\n    // 点击不含子菜单的菜单条目关闭菜单\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const that = this;\n    $document.on('click', '.mdui-menu-item', function () {\n      const $item = $(this);\n\n      if (\n        !$item.find('.mdui-menu').length &&\n        $item.attr('disabled') === undefined\n      ) {\n        that.close();\n      }\n    });\n\n    // 绑定点击或鼠标移入含子菜单的条目的事件\n    this.bindSubMenuEvent();\n\n    // 窗口大小变化时，重新调整菜单位置\n    $window.on(\n      'resize',\n      $.throttle(() => this.readjust(), 100),\n    );\n  }\n\n  /**\n   * 是否为打开状态\n   */\n  private isOpen(): boolean {\n    return this.state === 'opening' || this.state === 'opened';\n  }\n\n  /**\n   * 触发组件事件\n   * @param name\n   */\n  private triggerEvent(name: EVENT): void {\n    componentEvent(name, 'menu', this.$element, this);\n  }\n\n  /**\n   * 调整主菜单位置\n   */\n  private readjust(): void {\n    let menuLeft;\n    let menuTop;\n\n    // 菜单位置和方向\n    let position: 'bottom' | 'top' | 'center';\n    let align: 'left' | 'right' | 'center';\n\n    // window 窗口的宽度和高度\n    const windowHeight = $window.height();\n    const windowWidth = $window.width();\n\n    // 配置参数\n    const gutter = this.options.gutter!;\n    const isCovered = this.isCovered;\n    const isFixed = this.options.fixed;\n\n    // 动画方向参数\n    let transformOriginX;\n    let transformOriginY;\n\n    // 菜单的原始宽度和高度\n    const menuWidth = this.$element.width();\n    const menuHeight = this.$element.height();\n\n    // 触发菜单的元素在窗口中的位置\n    const anchorRect = this.$anchor[0].getBoundingClientRect();\n    const anchorTop = anchorRect.top;\n    const anchorLeft = anchorRect.left;\n    const anchorHeight = anchorRect.height;\n    const anchorWidth = anchorRect.width;\n    const anchorBottom = windowHeight - anchorTop - anchorHeight;\n    const anchorRight = windowWidth - anchorLeft - anchorWidth;\n\n    // 触发元素相对其拥有定位属性的父元素的位置\n    const anchorOffsetTop = this.$anchor[0].offsetTop;\n    const anchorOffsetLeft = this.$anchor[0].offsetLeft;\n\n    // 自动判断菜单位置\n    if (this.options.position === 'auto') {\n      if (anchorBottom + (isCovered ? anchorHeight : 0) > menuHeight + gutter) {\n        // 判断下方是否放得下菜单\n        position = 'bottom';\n      } else if (\n        anchorTop + (isCovered ? anchorHeight : 0) >\n        menuHeight + gutter\n      ) {\n        // 判断上方是否放得下菜单\n        position = 'top';\n      } else {\n        // 上下都放不下，居中显示\n        position = 'center';\n      }\n    } else {\n      position = this.options.position!;\n    }\n\n    // 自动判断菜单对齐方式\n    if (this.options.align === 'auto') {\n      if (anchorRight + anchorWidth > menuWidth + gutter) {\n        // 判断右侧是否放得下菜单\n        align = 'left';\n      } else if (anchorLeft + anchorWidth > menuWidth + gutter) {\n        // 判断左侧是否放得下菜单\n        align = 'right';\n      } else {\n        // 左右都放不下，居中显示\n        align = 'center';\n      }\n    } else {\n      align = this.options.align!;\n    }\n\n    // 设置菜单位置\n    if (position === 'bottom') {\n      transformOriginY = '0';\n      menuTop =\n        (isCovered ? 0 : anchorHeight) +\n        (isFixed ? anchorTop : anchorOffsetTop);\n    } else if (position === 'top') {\n      transformOriginY = '100%';\n      menuTop =\n        (isCovered ? anchorHeight : 0) +\n        (isFixed ? anchorTop - menuHeight : anchorOffsetTop - menuHeight);\n    } else {\n      transformOriginY = '50%';\n\n      // =====================在窗口中居中\n      // 显示的菜单的高度，简单菜单高度不超过窗口高度，若超过了则在菜单内部显示滚动条\n      // 级联菜单内部不允许出现滚动条\n      let menuHeightTemp = menuHeight;\n\n      // 简单菜单比窗口高时，限制菜单高度\n      if (!this.isCascade) {\n        if (menuHeight + gutter * 2 > windowHeight) {\n          menuHeightTemp = windowHeight - gutter * 2;\n          this.$element.height(menuHeightTemp);\n        }\n      }\n\n      menuTop =\n        (windowHeight - menuHeightTemp) / 2 +\n        (isFixed ? 0 : anchorOffsetTop - anchorTop);\n    }\n\n    this.$element.css('top', `${menuTop}px`);\n\n    // 设置菜单对齐方式\n    if (align === 'left') {\n      transformOriginX = '0';\n      menuLeft = isFixed ? anchorLeft : anchorOffsetLeft;\n    } else if (align === 'right') {\n      transformOriginX = '100%';\n      menuLeft = isFixed\n        ? anchorLeft + anchorWidth - menuWidth\n        : anchorOffsetLeft + anchorWidth - menuWidth;\n    } else {\n      transformOriginX = '50%';\n\n      //=======================在窗口中居中\n      // 显示的菜单的宽度，菜单宽度不能超过窗口宽度\n      let menuWidthTemp = menuWidth;\n\n      // 菜单比窗口宽，限制菜单宽度\n      if (menuWidth + gutter * 2 > windowWidth) {\n        menuWidthTemp = windowWidth - gutter * 2;\n        this.$element.width(menuWidthTemp);\n      }\n\n      menuLeft =\n        (windowWidth - menuWidthTemp) / 2 +\n        (isFixed ? 0 : anchorOffsetLeft - anchorLeft);\n    }\n\n    this.$element.css('left', `${menuLeft}px`);\n\n    // 设置菜单动画方向\n    this.$element.transformOrigin(`${transformOriginX} ${transformOriginY}`);\n  }\n\n  /**\n   * 调整子菜单的位置\n   * @param $submenu\n   */\n  private readjustSubmenu($submenu: JQ): void {\n    const $item = $submenu.parent('.mdui-menu-item');\n\n    let submenuTop;\n    let submenuLeft;\n\n    // 子菜单位置和方向\n    let position: 'top' | 'bottom';\n    let align: 'left' | 'right';\n\n    // window 窗口的宽度和高度\n    const windowHeight = $window.height();\n    const windowWidth = $window.width();\n\n    // 动画方向参数\n    let transformOriginX;\n    let transformOriginY;\n\n    // 子菜单的原始宽度和高度\n    const submenuWidth = $submenu.width();\n    const submenuHeight = $submenu.height();\n\n    // 触发子菜单的菜单项的宽度高度\n    const itemRect = $item[0].getBoundingClientRect();\n    const itemWidth = itemRect.width;\n    const itemHeight = itemRect.height;\n    const itemLeft = itemRect.left;\n    const itemTop = itemRect.top;\n\n    // 判断菜单上下位置\n    if (windowHeight - itemTop > submenuHeight) {\n      // 判断下方是否放得下菜单\n      position = 'bottom';\n    } else if (itemTop + itemHeight > submenuHeight) {\n      // 判断上方是否放得下菜单\n      position = 'top';\n    } else {\n      // 默认放在下方\n      position = 'bottom';\n    }\n\n    // 判断菜单左右位置\n    if (windowWidth - itemLeft - itemWidth > submenuWidth) {\n      // 判断右侧是否放得下菜单\n      align = 'left';\n    } else if (itemLeft > submenuWidth) {\n      // 判断左侧是否放得下菜单\n      align = 'right';\n    } else {\n      // 默认放在右侧\n      align = 'left';\n    }\n\n    // 设置菜单位置\n    if (position === 'bottom') {\n      transformOriginY = '0';\n      submenuTop = '0';\n    } else if (position === 'top') {\n      transformOriginY = '100%';\n      submenuTop = -submenuHeight + itemHeight;\n    }\n\n    $submenu.css('top', `${submenuTop}px`);\n\n    // 设置菜单对齐方式\n    if (align === 'left') {\n      transformOriginX = '0';\n      submenuLeft = itemWidth;\n    } else if (align === 'right') {\n      transformOriginX = '100%';\n      submenuLeft = -submenuWidth;\n    }\n\n    $submenu.css('left', `${submenuLeft}px`);\n\n    // 设置菜单动画方向\n    $submenu.transformOrigin(`${transformOriginX} ${transformOriginY}`);\n  }\n\n  /**\n   * 打开子菜单\n   * @param $submenu\n   */\n  private openSubMenu($submenu: JQ): void {\n    this.readjustSubmenu($submenu);\n\n    $submenu\n      .addClass('mdui-menu-open')\n      .parent('.mdui-menu-item')\n      .addClass('mdui-menu-item-active');\n  }\n\n  /**\n   * 关闭子菜单，及其嵌套的子菜单\n   * @param $submenu\n   */\n  private closeSubMenu($submenu: JQ): void {\n    // 关闭子菜单\n    $submenu\n      .removeClass('mdui-menu-open')\n      .addClass('mdui-menu-closing')\n      .transitionEnd(() => $submenu.removeClass('mdui-menu-closing'))\n\n      // 移除激活状态的样式\n      .parent('.mdui-menu-item')\n      .removeClass('mdui-menu-item-active');\n\n    // 循环关闭嵌套的子菜单\n    $submenu.find('.mdui-menu').each((_, menu) => {\n      const $subSubmenu = $(menu);\n\n      $subSubmenu\n        .removeClass('mdui-menu-open')\n        .addClass('mdui-menu-closing')\n        .transitionEnd(() => $subSubmenu.removeClass('mdui-menu-closing'))\n        .parent('.mdui-menu-item')\n        .removeClass('mdui-menu-item-active');\n    });\n  }\n\n  /**\n   * 切换子菜单状态\n   * @param $submenu\n   */\n  private toggleSubMenu($submenu: JQ): void {\n    $submenu.hasClass('mdui-menu-open')\n      ? this.closeSubMenu($submenu)\n      : this.openSubMenu($submenu);\n  }\n\n  /**\n   * 绑定子菜单事件\n   */\n  private bindSubMenuEvent(): void {\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const that = this;\n\n    // 点击打开子菜单\n    this.$element.on('click', '.mdui-menu-item', function (event) {\n      const $item = $(this as HTMLElement);\n      const $target = $(event.target as HTMLElement);\n\n      // 禁用状态菜单不操作\n      if ($item.attr('disabled') !== undefined) {\n        return;\n      }\n\n      // 没有点击在子菜单的菜单项上时，不操作（点在了子菜单的空白区域、或分隔线上）\n      if ($target.is('.mdui-menu') || $target.is('.mdui-divider')) {\n        return;\n      }\n\n      // 阻止冒泡，点击菜单项时只在最后一级的 mdui-menu-item 上生效，不向上冒泡\n      if (!$target.parents('.mdui-menu-item').first().is($item)) {\n        return;\n      }\n\n      // 当前菜单的子菜单\n      const $submenu = $item.children('.mdui-menu');\n\n      // 先关闭除当前子菜单外的所有同级子菜单\n      $item\n        .parent('.mdui-menu')\n        .children('.mdui-menu-item')\n        .each((_, item) => {\n          const $tmpSubmenu = $(item).children('.mdui-menu');\n\n          if (\n            $tmpSubmenu.length &&\n            (!$submenu.length || !$tmpSubmenu.is($submenu))\n          ) {\n            that.closeSubMenu($tmpSubmenu);\n          }\n        });\n\n      // 切换当前子菜单\n      if ($submenu.length) {\n        that.toggleSubMenu($submenu);\n      }\n    });\n\n    if (this.options.subMenuTrigger === 'hover') {\n      // 临时存储 setTimeout 对象\n      let timeout: any = null;\n      let timeoutOpen: any = null;\n\n      this.$element.on('mouseover mouseout', '.mdui-menu-item', function (\n        event,\n      ) {\n        const $item = $(this as HTMLElement);\n        const eventType = event.type;\n        const $relatedTarget = $(\n          (event as MouseEvent).relatedTarget as HTMLElement,\n        );\n\n        // 禁用状态的菜单不操作\n        if ($item.attr('disabled') !== undefined) {\n          return;\n        }\n\n        // 用 mouseover 模拟 mouseenter\n        if (eventType === 'mouseover') {\n          if (\n            !$item.is($relatedTarget) &&\n            contains($item[0], $relatedTarget[0])\n          ) {\n            return;\n          }\n        }\n\n        // 用 mouseout 模拟 mouseleave\n        else if (eventType === 'mouseout') {\n          if (\n            $item.is($relatedTarget) ||\n            contains($item[0], $relatedTarget[0])\n          ) {\n            return;\n          }\n        }\n\n        // 当前菜单项下的子菜单，未必存在\n        const $submenu = $item.children('.mdui-menu');\n\n        // 鼠标移入菜单项时，显示菜单项下的子菜单\n        if (eventType === 'mouseover') {\n          if ($submenu.length) {\n            // 当前子菜单准备打开时，如果当前子菜单正准备着关闭，不用再关闭了\n            const tmpClose = $submenu.data('timeoutClose.mdui.menu');\n            if (tmpClose) {\n              clearTimeout(tmpClose);\n            }\n\n            // 如果当前子菜单已经打开，不操作\n            if ($submenu.hasClass('mdui-menu-open')) {\n              return;\n            }\n\n            // 当前子菜单准备打开时，其他准备打开的子菜单不用再打开了\n            clearTimeout(timeoutOpen);\n\n            // 准备打开当前子菜单\n            timeout = timeoutOpen = setTimeout(\n              () => that.openSubMenu($submenu),\n              that.options.subMenuDelay,\n            );\n\n            $submenu.data('timeoutOpen.mdui.menu', timeout);\n          }\n        }\n\n        // 鼠标移出菜单项时，关闭菜单项下的子菜单\n        else if (eventType === 'mouseout') {\n          if ($submenu.length) {\n            // 鼠标移出菜单项时，如果当前菜单项下的子菜单正准备打开，不用再打开了\n            const tmpOpen = $submenu.data('timeoutOpen.mdui.menu');\n            if (tmpOpen) {\n              clearTimeout(tmpOpen);\n            }\n\n            // 准备关闭当前子菜单\n            timeout = setTimeout(\n              () => that.closeSubMenu($submenu),\n              that.options.subMenuDelay,\n            );\n\n            $submenu.data('timeoutClose.mdui.menu', timeout);\n          }\n        }\n      });\n    }\n  }\n\n  /**\n   * 动画结束回调\n   */\n  private transitionEnd(): void {\n    this.$element.removeClass('mdui-menu-closing');\n\n    if (this.state === 'opening') {\n      this.state = 'opened';\n      this.triggerEvent('opened');\n    }\n\n    if (this.state === 'closing') {\n      this.state = 'closed';\n      this.triggerEvent('closed');\n\n      // 关闭后，恢复菜单样式到默认状态，并恢复 fixed 定位\n      this.$element.css({\n        top: '',\n        left: '',\n        width: '',\n        position: 'fixed',\n      });\n    }\n  }\n\n  /**\n   * 切换菜单状态\n   */\n  public toggle(): void {\n    this.isOpen() ? this.close() : this.open();\n  }\n\n  /**\n   * 打开菜单\n   */\n  public open(): void {\n    if (this.isOpen()) {\n      return;\n    }\n\n    this.state = 'opening';\n    this.triggerEvent('open');\n\n    this.readjust();\n\n    this.$element\n      // 菜单隐藏状态使用使用 fixed 定位。\n      .css('position', this.options.fixed ? 'fixed' : 'absolute')\n      .addClass('mdui-menu-open')\n      .transitionEnd(() => this.transitionEnd());\n  }\n\n  /**\n   * 关闭菜单\n   */\n  public close(): void {\n    if (!this.isOpen()) {\n      return;\n    }\n\n    this.state = 'closing';\n    this.triggerEvent('close');\n\n    // 菜单开始关闭时，关闭所有子菜单\n    this.$element.find('.mdui-menu').each((_, submenu) => {\n      this.closeSubMenu($(submenu));\n    });\n\n    this.$element\n      .removeClass('mdui-menu-open')\n      .addClass('mdui-menu-closing')\n      .transitionEnd(() => this.transitionEnd());\n  }\n}\n\nmdui.Menu = Menu;\n", "import $ from 'mdui.jq/es/$';\nimport 'mdui.jq/es/methods/data';\nimport 'mdui.jq/es/methods/on';\nimport mdui from '../../mdui';\nimport { $document } from '../../utils/dom';\nimport { parseOptions } from '../../utils/parseOptions';\nimport './index';\n\nconst customAttr = 'mdui-menu';\nconst dataName = '_mdui_menu';\n\ntype OPTIONS = {\n  target: string;\n  position?: 'auto' | 'top' | 'bottom' | 'center';\n  align?: 'auto' | 'left' | 'right' | 'center';\n  gutter?: number;\n  fixed?: boolean;\n  covered?: boolean | 'auto';\n  subMenuTrigger?: 'click' | 'hover';\n  subMenuDelay?: number;\n};\n\n$(() => {\n  $document.on('click', `[${customAttr}]`, function () {\n    const $this = $(this as HTMLElement);\n    let instance = $this.data(dataName);\n\n    if (!instance) {\n      const options = parseOptions(this as HTMLElement, customAttr) as OPTIONS;\n      const menuSelector = options.target;\n      // @ts-ignore\n      delete options.target;\n\n      instance = new mdui.Menu($this, menuSelector, options);\n      $this.data(dataName, instance);\n\n      instance.toggle();\n    }\n  });\n});\n"], "names": ["t", "e", "bubbles", "cancelable", "detail", "n", "document", "createEvent", "initCustomEvent", "finallyConstructor", "callback", "constructor", "this", "then", "value", "resolve", "reason", "reject", "allSettled", "arr", "length", "TypeError", "args", "Array", "prototype", "slice", "call", "remaining", "res", "i", "val", "status", "MouseEvent", "initMouseEvent", "window", "screenX", "screenY", "clientX", "clientY", "ctrl<PERSON>ey", "altKey", "shift<PERSON>ey", "metaKey", "button", "relatedTarget", "Event", "CustomEvent", "setTimeoutFunc", "setTimeout", "isArray", "x", "Boolean", "noop", "Promise", "fn", "_state", "_handled", "_value", "undefined", "_deferreds", "doResolve", "handle", "self", "deferred", "_immediateFn", "cb", "onFulfilled", "onRejected", "ret", "promise", "push", "newValue", "finale", "thisArg", "apply", "arguments", "_unhandledRejectionFn", "len", "Handler", "done", "ex", "prom", "promiseFinally", "all", "race", "setImmediate", "err", "console", "warn", "globalNS", "global", "Error", "isFunction", "target", "isString", "isNumber", "isUndefined", "isNull", "isWindow", "Window", "isDocument", "Document", "isElement", "Element", "isArrayLike", "isObjectLike", "toElement", "documentElement", "toCamelCase", "string", "replace", "_", "letter", "toUpperCase", "toKebabCase", "replacer", "toLowerCase", "getComputedStyleValue", "element", "name", "getComputedStyle", "getPropertyValue", "isBorderBox", "getExtraWidth", "direction", "extra", "const", "position", "reduce", "prev", "index", "let", "prop", "parseFloat", "getStyle", "valueNumber", "getBoundingClientRect", "getChildNodesArray", "parent", "tempParent", "createElement", "innerHTML", "childNodes", "returnFalse", "cssNumber", "each", "keys", "Object", "JQ", "item", "$", "selector", "test", "readyState", "body", "addEventListener", "html", "trim", "toCreate", "li", "tr", "td", "th", "tbody", "option", "childTag", "parentTag", "indexOf", "match", "querySelectorAll", "getElementById", "Node", "addClass", "mdui", "contains", "container", "merge", "first", "second", "get", "find", "foundElements", "handlers", "mduiElementId", "getElementId", "key", "parse", "type", "parts", "split", "ns", "sort", "join", "matcher<PERSON><PERSON>", "RegExp", "remove", "types", "func", "removeEvent", "handler", "handlersInElement", "id", "removeEventListener", "proxy", "for<PERSON>ach", "event", "filter", "extend", "object1", "objectN", "unshift", "object", "param", "obj", "destructure", "keyTmp", "v", "encodeURIComponent", "trigger", "extraParameters", "eventObject", "eventParams", "isMouseEvent", "_detail", "_ns", "dispatchEvent", "globalOptions", "ajaxEvents", "ajaxStart", "ajaxSuccess", "ajaxError", "ajaxComplete", "isQueryStringData", "method", "append<PERSON>uery", "url", "query", "ajax", "options", "defaults", "textStatus", "isCanceled", "mergedOptions", "data", "processData", "async", "cache", "username", "password", "headers", "xhrFields", "statusCode", "dataType", "contentType", "timeout", "location", "toString", "params", "result1", "result2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Blob", "FormData", "Date", "now", "xhrTimeout", "xhr", "XMLHttpRequest", "open", "setRequestHeader", "$2", "host", "onload", "clearTimeout", "responseData", "isHttpStatusSuccess", "JSON", "responseText", "responseType", "response", "onerror", "statusText", "<PERSON>ab<PERSON>", "abort", "send", "ajaxSetup", "dataNS", "setObjectToElement", "map", "elements", "concat", "removeData", "nameItem", "unique", "result", "dir", "$elements", "nameIndex", "node", "is", "shift", "add", "className", "getAttribute", "cls", "classList", "setAttribute", "$element", "reverse", "$target", "parentNode", "newItem", "cloneNode", "existingItem", "nextS<PERSON>ling", "insertBefore", "off", "on", "one", "_this", "origCallback", "elementId", "useCapture", "proxyFn", "callFn", "elem", "preventDefault", "stopPropagation", "_data", "eventName", "clone", "isMatched", "matches", "msMatchesSelector", "$compareWith", "compare", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "child", "append<PERSON><PERSON><PERSON>", "contents", "content", "extraChilds", "$result", "k", "removeAttribute", "style", "set", "children", "__", "childNode", "eq", "closest", "matched", "parents", "r<PERSON>ce", "dataAttr", "nodeType", "handleExtraWidth", "funcIndex", "<PERSON><PERSON><PERSON><PERSON>", "multiply", "getExtraWidthValue", "documentMode", "clientProp", "scrollProp", "offsetProp", "innerProp", "doc", "Math", "max", "floatStyle", "css", "getClientRects", "top", "left", "rect", "win", "ownerDocument", "defaultView", "pageYOffset", "pageXOffset", "resultData", "attrs", "attributes", "empty", "$selector", "has", "$targets", "hasClass", "funcName", "margin", "isSet", "elementIndex", "computedValue", "dimension", "suffix", "hide", "display", "propName", "0", "1", "2", "firstElement", "selected", "checked", "outerHTML", "last", "not", "$excludes", "offsetParent", "currentOffset", "parentOffset", "offset", "$offsetParent", "currentTop", "currentLeft", "currentTopString", "currentLeftString", "currentPosition", "removeAttr", "attributeName", "names", "removeProp", "replaceWith", "newContent", "before", "replaceAll", "serializeArray", "HTMLFormElement", "nodeName", "disabled", "serialize", "elementDisplay", "show", "siblings", "prevAll", "nextAll", "toggle", "reflow", "clientLeft", "transition", "duration", "webkitTransitionDuration", "transitionDuration", "transitionEnd", "that", "events", "fireCallback", "transform<PERSON><PERSON>in", "webkitTransformOrigin", "transform", "webkitTransform", "entries", "mutation", "apiInit", "selectors", "$this", "showOverlay", "zIndex", "$overlay", "appendTo", "level", "hideOverlay", "force", "removeClass", "lockScreen", "$body", "newBody<PERSON>idth", "width", "unlockScreen", "throttle", "delay", "timer", "GUID", "componentEvent", "componentName", "instance", "parameters", "inst", "fullEventName", "j<PERSON><PERSON><PERSON>", "guid", "s4", "floor", "random", "substring", "Headroom", "DEFAULT_OPTIONS", "tolerance", "down", "up", "enable", "$document", "$window", "initialClass", "pinnedClass", "unpinnedClass", "parseOptions", "attr", "Function", "onScroll", "rafId", "requestAnimationFrame", "currentScrollY", "lastScrollY", "toleranceExceeded", "abs", "unpin", "pin", "triggerEvent", "state", "isEnable", "disable", "cancelAnimationFrame", "getState", "customAttr", "CollapseAbstract", "classPrefix", "getNamespace", "classItem", "classItemOpen", "classHeader", "classBody", "bindEvent", "accordion", "$item", "getItems", "close", "isOpen", "getItem", "$content", "height", "scrollHeight", "openAll", "closeAll", "Collapse", "Panel", "Table", "init", "$thRow", "$tdRows", "selectable", "updateThCheckbox", "updateTdCheckbox", "updateNumericCol", "createCheckboxHTML", "tag", "updateThCheckboxStatus", "checkbox", "$thCheckbox", "selectedRow", "tdRowsLength", "indeterminate", "rowSelectedClass", "row", "$row", "$checkbox", "prependTo", "$tdCheckboxs", "isCheckedAll", "numericClass", "isNumericCol", "$td", "dataName", "updateTables", "startEvent", "moveEvent", "endEvent", "cancelEvent", "unlockEvent", "touches", "isAllow", "register", "$ripple", "touchPosition", "TouchEvent", "touchStartX", "pageX", "touchStartY", "pageY", "innerHeight", "innerWidth", "center", "diameter", "pow", "translate", "wave", "$wave", "removeTimer", "removeRipple", "showRipple", "hidden", "hideRipple", "defaultData", "reInit", "domLoadedEvent", "inputEvent", "input", "$input", "eventType", "inputType", "$textfield", "validity", "valid", "inputValue", "hasExtraSpace", "outerHeight", "max<PERSON><PERSON><PERSON>", "text", "updateValueStyle", "$slider", "$track", "_slider_$track", "$fill", "_slider_$fill", "$thumb", "_slider_$thumb", "_slider_$input", "min", "_slider_min", "_slider_max", "isDisabled", "_slider_disabled", "isDiscrete", "_slider_discrete", "$thumbText", "_slider_$thumbText", "percent", "append", "focus", "updateTextFields", "rangeSelector", "updateSliders", "Fab", "$btn", "$dial", "$dialBtns", "btn", "transitionDelay", "webkitTransitionDelay", "Select", "$native", "uniqueID", "handleUpdate", "gutter", "readjustMenu", "transformOriginY", "menuMarginTop", "windowHeight", "elementHeight", "$itemFirst", "$items", "itemHeight", "itemMargin", "parseInt", "menuWidth", "menuHeight", "size", "elementTop", "menuMaxHeight", "selectedIndex", "menuMaxMarginTop", "menuTop", "$menu", "margin-top", "transform-origin", "selected<PERSON><PERSON><PERSON>", "itemsData", "textContent", "selectedText", "$selected", "after", "Tab", "$tabs", "$indicator", "hash", "tab", "activeIndex", "setActive", "setIndicatorPosition", "bindTabEvent", "loop", "$tab", "clickEvent", "targetId", "substr", "$activeTab", "activeTabOffset", "scrollLeft", "next", "$oldTabs", "$newTabs", "oldTabsElement", "newTabsElement", "Drawer", "isDesktop", "overlay", "swipeSupport", "swipe", "openNavEventHandler", "swipeStartX", "swiping", "maybeSwiping", "swipe<PERSON><PERSON><PERSON><PERSON><PERSON>", "setPosition", "translateX", "transformCSS", "cleanPosition", "webkitTransition", "getMaxTranslateX", "getTranslateX", "currentX", "onBodyTouchEnd", "touchX", "changedTouches", "translateRatio", "swipingState", "touchmove", "onBodyTouchMove", "touchend", "touchcancel", "touchY", "dXAbs", "dYAbs", "onBodyTouchStart", "$drawer", "queue", "dequeue", "Dialog", "cancel", "closeOnCancel", "confirm", "closeOnConfirm", "history", "modal", "closeOnEsc", "destroyOnClosed", "currentInst", "queueName", "isLockScreen", "readjust", "$title", "$actions", "hashchangeEvent", "overlayClick", "destroy", "doOpen", "historyBack", "back", "keyCode", "$dialog", "DEFAULT_BUTTON", "bold", "onClick", "title", "buttons", "stackedButtons", "cssClass", "onOpen", "onOpened", "onClose", "onClosed", "confirmText", "dialog", "buttonsHTML", "HTML", "cancelText", "alert", "onConfirm", "onCancel", "maxlength", "defaultValue", "confirmOnEnter", "prompt", "label", "<PERSON><PERSON><PERSON>", "marginLeft", "marginTop", "targetProps", "<PERSON><PERSON><PERSON><PERSON>", "tooltipWidth", "offsetWidth", "tooltipHeight", "offsetHeight", "targetOffset", "margin-left", "oldOptions", "timeoutId", "Snackbar", "buttonColorStyle", "buttonColorClass", "buttonColor", "buttonText", "message", "closeOnButtonClick", "closeOnOutsideClick", "onButtonClick", "layerHTML", "fillHTML", "spinner", "$spinner", "layer", "translateY", "snackbarHeight", "clientHeight", "snackbar", "$bottomNav", "isThis", "<PERSON><PERSON>", "anchorSelector", "menuSelector", "$anchor", "isCascade", "isCovered", "covered", "bindSubMenuEvent", "align", "fixed", "updateSpinners", "subMenuTrigger", "subMenuDelay", "menuLeft", "transformOriginX", "windowWidth", "isFixed", "anchorRect", "anchorTop", "anchorLeft", "anchorHeight", "anchorWidth", "anchorBottom", "anchorRight", "anchorOffsetTop", "offsetTop", "anchorOffsetLeft", "offsetLeft", "menuHeightTemp", "menuWidthTemp", "readjustSubmenu", "$submenu", "submenuTop", "submenuLeft", "submenuWidth", "submenuHeight", "itemRect", "itemWidth", "itemLeft", "itemTop", "openSubMenu", "closeSubMenu", "menu", "$subSubmenu", "toggleSubMenu", "$tmpSubmenu", "timeoutOpen", "$relatedTarget", "tmpClose", "tmpOpen", "submenu"], "mappings": ";;;;;oOAAY,SAASA,EAAEA,EAAEC,GAAGA,EAAEA,GAAG,CAACC,SAAQ,EAAGC,YAAW,EAAGC,YAAO,GAAQ,IAAIC,EAAEC,SAASC,YAAY,eAAe,OAAOF,EAAEG,gBAAgBR,EAAEC,EAAEC,QAAQD,EAAEE,WAAWF,EAAEG,QAAQC,ECGhL,SAASI,EAAmBC,GAC1B,IAAIC,EAAcC,KAAKD,YACvB,OAAOC,KAAKC,KACV,SAASC,GAEP,OAAOH,EAAYI,QAAQL,KAAYG,KAAK,WAC1C,OAAOC,KAGX,SAASE,GAEP,OAAOL,EAAYI,QAAQL,KAAYG,KAAK,WAE1C,OAAOF,EAAYM,OAAOD,OChBlC,SAASE,EAAWC,GAElB,OAAO,IADCP,KACK,SAASG,EAASE,GAC7B,IAAME,QAA6B,IAAfA,EAAIC,OACtB,OAAOH,EACL,IAAII,iBACKF,EACL,IACAA,EACA,mEAIR,IAAIG,EAAOC,MAAMC,UAAUC,MAAMC,KAAKP,GACtC,GAAoB,IAAhBG,EAAKF,OAAc,OAAOL,EAAQ,IACtC,IAAIY,EAAYL,EAAKF,OAErB,SAASQ,EAAIC,EAAGC,GACd,GAAIA,IAAuB,iBAARA,GAAmC,mBAARA,GAAqB,CACjE,IAAIjB,EAAOiB,EAAIjB,KACf,GAAoB,mBAATA,EAaT,YAZAA,EAAKa,KACHI,EACA,SAASA,GACPF,EAAIC,EAAGC,IAET,SAAS7B,GACPqB,EAAKO,GAAK,CAAEE,OAAQ,WAAYf,OAAQf,GACpB,KAAd0B,GACJZ,EAAQO,KAOlBA,EAAKO,GAAK,CAAEE,OAAQ,YAAajB,MAAOgB,GACpB,KAAdH,GACJZ,EAAQO,GAIZ,IAAK,IAAIO,EAAI,EAAGA,EAAIP,EAAKF,OAAQS,IAC/BD,EAAIC,EAAGP,EAAKO,OC3CjB,WAAW,IAAI,OAAO,IAAIG,WAAW,QAAQ,MAAM/B,IAAU,SAAFA,EAAWA,EAAED,GAAGA,EAAEA,GAAG,CAACE,SAAQ,EAAGC,YAAW,GAAI,IAAIE,EAAEC,SAASC,YAAY,cAAc,OAAOF,EAAE4B,eAAehC,EAAED,EAAEE,QAAQF,EAAEG,WAAW+B,OAAO,EAAElC,EAAEmC,SAAS,EAAEnC,EAAEoC,SAAS,EAAEpC,EAAEqC,SAAS,EAAErC,EAAEsC,SAAS,EAAEtC,EAAEuC,UAAS,EAAGvC,EAAEwC,SAAQ,EAAGxC,EAAEyC,WAAU,EAAGzC,EAAE0C,UAAS,EAAG1C,EAAE2C,QAAQ,EAAE3C,EAAE4C,eAAe,MAAMvC,EAAGJ,EAAEuB,UAAUqB,MAAMrB,UAAUU,OAAOF,WAAW/B,EAA9Y,GHAiL,mBAAmBiC,OAAOY,cAAc9C,EAAEwB,UAAUU,OAAOW,MAAMrB,UAAUU,OAAOY,YAAY9C,GIKhR,IAAI+C,EAAiBC,WAErB,SAASC,EAAQC,GACf,OAAOC,QAAQD,QAAyB,IAAbA,EAAE9B,QAG/B,SAASgC,KAaT,SAASC,EAAQC,GACf,KAAM1C,gBAAgByC,GACpB,MAAM,IAAIhC,UAAU,wCACtB,GAAkB,mBAAPiC,EAAmB,MAAM,IAAIjC,UAAU,kBAElDT,KAAK2C,OAAS,EAEd3C,KAAK4C,UAAW,EAEhB5C,KAAK6C,YAASC,EAEd9C,KAAK+C,WAAa,GAElBC,EAAUN,EAAI1C,MAGhB,SAASiD,EAAOC,EAAMC,GACpB,KAAuB,IAAhBD,EAAKP,QACVO,EAAOA,EAAKL,OAEM,IAAhBK,EAAKP,QAITO,EAAKN,UAAW,EAChBH,EAAQW,aAAa,WACnB,IAAIC,EAAqB,IAAhBH,EAAKP,OAAeQ,EAASG,YAAcH,EAASI,WAC7D,GAAW,OAAPF,EAAJ,CAIA,IAAIG,EACJ,IACEA,EAAMH,EAAGH,EAAKL,QACd,MAAOxD,GAEP,YADAgB,EAAO8C,EAASM,QAASpE,GAG3Bc,EAAQgD,EAASM,QAASD,QAVP,IAAhBN,EAAKP,OAAexC,EAAUE,GAAQ8C,EAASM,QAASP,EAAKL,WAPhEK,EAAKH,WAAWW,KAAKP,GAqBzB,SAAShD,EAAQ+C,EAAMS,GACrB,IAEE,GAAIA,IAAaT,EACf,MAAM,IAAIzC,UAAU,6CACtB,GACEkD,IACqB,iBAAbA,GAA6C,mBAAbA,GACxC,CACA,IAAI1D,EAAO0D,EAAS1D,KACpB,GAAI0D,aAAoBlB,EAItB,OAHAS,EAAKP,OAAS,EACdO,EAAKL,OAASc,OACdC,EAAOV,GAEF,GAAoB,mBAATjD,EAEhB,YADA+C,GApEMN,EAoESzC,EApEL4D,EAoEWF,EAnEpB,WACLjB,EAAGoB,MAAMD,EAASE,aAkEkBb,GAIpCA,EAAKP,OAAS,EACdO,EAAKL,OAASc,EACdC,EAAOV,GACP,MAAO7D,GACPgB,EAAO6C,EAAM7D,GA5EjB,IAAcqD,EAAImB,EAgFlB,SAASxD,EAAO6C,EAAMS,GACpBT,EAAKP,OAAS,EACdO,EAAKL,OAASc,EACdC,EAAOV,GAGT,SAASU,EAAOV,GACM,IAAhBA,EAAKP,QAA2C,IAA3BO,EAAKH,WAAWvC,QACvCiC,EAAQW,aAAa,WACdF,EAAKN,UACRH,EAAQuB,sBAAsBd,EAAKL,UAKzC,IAAK,IAAI5B,EAAI,EAAGgD,EAAMf,EAAKH,WAAWvC,OAAQS,EAAIgD,EAAKhD,IACrDgC,EAAOC,EAAMA,EAAKH,WAAW9B,IAE/BiC,EAAKH,WAAa,KAMpB,SAASmB,EAAQZ,EAAaC,EAAYE,GACxCzD,KAAKsD,YAAqC,mBAAhBA,EAA6BA,EAAc,KACrEtD,KAAKuD,WAAmC,mBAAfA,EAA4BA,EAAa,KAClEvD,KAAKyD,QAAUA,EASjB,SAAST,EAAUN,EAAIQ,GACrB,IAAIiB,GAAO,EACX,IACEzB,EACE,SAASxC,GACHiE,IACJA,GAAO,EACPhE,EAAQ+C,EAAMhD,KAEhB,SAASE,GACH+D,IACJA,GAAO,EACP9D,EAAO6C,EAAM9C,MAGjB,MAAOgE,GACP,GAAID,EAAM,OACVA,GAAO,EACP9D,EAAO6C,EAAMkB,MAITxD,UAAiB,MAAI,SAAS2C,GACpC,OAAOvD,KAAKC,KAAK,KAAMsD,MAGjB3C,UAAUX,KAAO,SAASqD,EAAaC,GAE7C,IAAIc,EAAO,IAAIrE,KAAKD,YAAYyC,GAGhC,OADAS,EAAOjD,KAAM,IAAIkE,EAAQZ,EAAaC,EAAYc,IAC3CA,KAGDzD,UAAmB,QAAI0D,IAEvBC,IAAM,SAAShE,GACrB,OAAO,IAAIkC,EAAQ,SAAStC,EAASE,GACnC,IAAKgC,EAAQ9B,GACX,OAAOF,EAAO,IAAII,UAAU,iCAG9B,IAAIC,EAAOC,MAAMC,UAAUC,MAAMC,KAAKP,GACtC,GAAoB,IAAhBG,EAAKF,OAAc,OAAOL,EAAQ,IACtC,IAAIY,EAAYL,EAAKF,OAErB,SAASQ,EAAIC,EAAGC,GACd,IACE,GAAIA,IAAuB,iBAARA,GAAmC,mBAARA,GAAqB,CACjE,IAAIjB,EAAOiB,EAAIjB,KACf,GAAoB,mBAATA,EAQT,YAPAA,EAAKa,KACHI,EACA,SAASA,GACPF,EAAIC,EAAGC,IAETb,GAKNK,EAAKO,GAAKC,EACU,KAAdH,GACJZ,EAAQO,GAEV,MAAO0D,GACP/D,EAAO+D,IAIX,IAAK,IAAInD,EAAI,EAAGA,EAAIP,EAAKF,OAAQS,IAC/BD,EAAIC,EAAGP,EAAKO,SAKVX,WAAaA,IAEbH,QAAU,SAASD,GACzB,OAAIA,GAA0B,iBAAVA,GAAsBA,EAAMH,cAAgB0C,EACvDvC,EAGF,IAAIuC,EAAQ,SAAStC,GAC1BA,EAAQD,QAIJG,OAAS,SAASH,GACxB,OAAO,IAAIuC,EAAQ,SAAStC,EAASE,GACnCA,EAAOH,QAIHsE,KAAO,SAASjE,GACtB,OAAO,IAAIkC,EAAQ,SAAStC,EAASE,GACnC,IAAKgC,EAAQ9B,GACX,OAAOF,EAAO,IAAII,UAAU,kCAG9B,IAAK,IAAIQ,EAAI,EAAGgD,EAAM1D,EAAIC,OAAQS,EAAIgD,EAAKhD,IACzCwB,EAAQtC,QAAQI,EAAIU,IAAIhB,KAAKE,EAASE,QAMpC+C,aAEmB,mBAAjBqB,aACN,SAAS/B,GAEP+B,aAAa/B,IAEjB,SAASA,GACPP,EAAeO,EAAI,MAGfsB,sBAAwB,SAA+BU,GACtC,oBAAZC,SAA2BA,SACpCA,QAAQC,KAAK,wCAAyCF,ICrP1D,IAAIG,EAAW,WAIb,GAAoB,oBAAT3B,KACT,OAAOA,KAET,GAAsB,oBAAX5B,OACT,OAAOA,OAET,GAAsB,oBAAXwD,OACT,OAAOA,OAET,MAAM,IAAIC,MAAM,kCAbH,GCFf,SAASC,EAAWC,GAChB,MAAyB,mBAAXA,EAElB,SAASC,EAASD,GACd,MAAyB,iBAAXA,EAElB,SAASE,EAASF,GACd,MAAyB,iBAAXA,EAKlB,SAASG,EAAYH,GACjB,YAAyB,IAAXA,EAElB,SAASI,EAAOJ,GACZ,OAAkB,OAAXA,EAEX,SAASK,EAASL,GACd,OAAOA,aAAkBM,OAE7B,SAASC,EAAWP,GAChB,OAAOA,aAAkBQ,SAE7B,SAASC,EAAUT,GACf,OAAOA,aAAkBU,QAY7B,SAASC,EAAYX,GACjB,OAAID,EAAWC,KAAWK,EAASL,IAG5BE,EAASF,EAAOzE,QAE3B,SAASqF,EAAaZ,GAClB,MAAyB,iBAAXA,GAAkC,OAAXA,EAEzC,SAASa,EAAUb,GACf,OAAOO,EAAWP,GAAUA,EAAOc,gBAAkBd,EAMzD,SAASe,EAAYC,GACjB,OAAOA,EACFC,QAAQ,QAAS,OACjBA,QAAQ,qBAAcC,EAAGC,UAAWA,EAAOC,gBAMpD,SAASC,EAAYL,GACjB,OAAOA,EAAOC,QAAQ,kBAAWK,SAAa,IAAMA,EAASC,gBAOjE,SAASC,EAAsBC,EAASC,GACpC,OAAOrF,OAAOsF,iBAAiBF,GAASG,iBAAiBP,EAAYK,IAMzE,SAASG,EAAYJ,GACjB,MAAwD,eAAjDD,EAAsBC,EAAS,cAQ1C,SAASK,EAAcL,EAASM,EAAWC,GACvCC,IAAMC,EAAyB,UAAdH,EAAwB,CAAC,OAAQ,SAAW,CAAC,MAAO,UACrE,MAAO,CAAC,EAAG,GAAGI,gBAAQC,EAAMlB,EAAGmB,GAC3BC,IAAIC,EAAOP,EAAQE,EAASG,GAI5B,MAHc,WAAVL,IACAO,GAAQ,SAELH,EAAOI,WAAWhB,EAAsBC,EAASc,IAAS,MAClE,GAOP,SAASE,EAAShB,EAASC,GAEvB,GAAa,UAATA,GAA6B,WAATA,EASxB,OAAOF,EAAsBC,EAASC,GARlCO,IAAMS,EAAcjB,EAAQkB,wBAAwBjB,GACpD,OAAIG,EAAYJ,GACFiB,OAEJA,EACNZ,EAAcL,EAASC,EAAM,UAC7BI,EAAcL,EAASC,EAAM,gBASzC,SAASkB,EAAmB5C,EAAQ6C,GAChCZ,IAAMa,EAAarI,SAASsI,cAAcF,GAE1C,OADAC,EAAWE,UAAYhD,EAChB,GAAGpE,MAAMC,KAAKiH,EAAWG,YAKpC,SAASC,IACL,OAAO,ED1GwB,mBAAxBtD,EAAkB,QAC3BA,EAAkB,QAAIpC,EACZoC,EAASpC,QAAQ7B,UAAmB,QAEpCiE,EAASpC,QAAQnC,aAC3BuE,EAASpC,QAAQnC,WAAaA,GAF9BuE,EAASpC,QAAQ7B,UAAmB,QAAI0D,EC4G1C4C,IAAMkB,EAAY,CACd,0BACA,cACA,cACA,WACA,aACA,aACA,WACA,aACA,gBACA,kBACA,UACA,aACA,eACA,aACA,UACA,QACA,UACA,SACA,SACA,QC3JJ,SAASC,EAAKpD,EAAQnF,GAClB,GAAI8F,EAAYX,IACZ,IAAKsC,IAAItG,EAAI,EAAGA,EAAIgE,EAAOzE,OAAQS,GAAK,EACpC,IAA+C,IAA3CnB,EAASgB,KAAKmE,EAAOhE,GAAIA,EAAGgE,EAAOhE,IACnC,OAAOgE,OAMf,IADAiC,IAAMoB,EAAOC,OAAOD,KAAKrD,GAChBhE,EAAI,EAAGA,EAAIqH,EAAK9H,OAAQS,GAAK,EAClC,IAAiE,IAA7DnB,EAASgB,KAAKmE,EAAOqD,EAAKrH,IAAKqH,EAAKrH,GAAIgE,EAAOqD,EAAKrH,KACpD,OAAOgE,EAInB,OAAOA,ECZP,SADSuD,EACGjI,cAER,OADAP,KAAKQ,OAAS,EACTD,IAGL8H,EAAK9H,WAAMU,EAAGwH,GAEVzI,EAAKiB,GAAKwH,IAEdzI,KAAKQ,OAASD,EAAIC,QACXR,KCkDfkH,IA7DUwB,EA6DJA,IA7DIA,EAAI,SAAUC,GAChB,IAAKA,EACD,OAAO,IAAIH,EAGf,GAAIG,aAAoBH,EACpB,OAAOG,EAGX,GAAI3D,EAAW2D,GAQX,MAPI,8BAA8BC,KAAKlJ,SAASmJ,aAC5CnJ,SAASoJ,KACTH,EAAS7H,KAAKpB,SAAUgJ,GAGxBhJ,SAASqJ,iBAAiB,qCAA0BJ,EAAS7H,KAAKpB,SAAUgJ,KAAI,GAE7E,IAAIF,EAAG,CAAC9I,WAGnB,GAAIwF,EAASyD,GAAW,CACpBzB,IAAM8B,EAAOL,EAASM,OAEtB,GAAgB,MAAZD,EAAK,IAAwC,MAA1BA,EAAKA,EAAKxI,OAAS,GAAY,CAClD+G,IAAI2B,EAAW,MAgBf,OAPAb,EARa,CACTc,GAAI,KACJC,GAAI,QACJC,GAAI,KACJC,GAAI,KACJC,MAAO,QACPC,OAAQ,mBAEAC,EAAUC,GAClB,GAAqC,IAAjCV,EAAKW,YAAYF,GAEjB,OADAP,EAAWQ,GACJ,IAIR,IAAIlB,EAAGX,EAAmBmB,EAAME,IAI3C,KADqC,MAAhBP,EAAS,KAAeA,EAASiB,MAAM,aAExD,OAAO,IAAIpB,EAAG9I,SAASmK,iBAAiBlB,IAE5CzB,IAAMR,EAAUhH,SAASoK,eAAenB,EAAS9H,MAAM,IACvD,OAAI6F,EACO,IAAI8B,EAAG,CAAC9B,IAEZ,IAAI8B,EAEf,OAAI5C,EAAY+C,IAAqBA,aH1BhBoB,KG6Bd,IAAIvB,EAAG,CAACG,IAFJ,IAAIH,EAAGG,KAIpBjG,GAAK8F,EAAG5H,UACH8H,GC1DXtG,6BAAiBsG,EAAE,QAAQsB,SAAS,qBAE9BC,EAAO,CACXvB,EAAGA,GCIL,SAASwB,EAASC,EAAWD,GACzB,OAAOC,IAAcD,GAAYpE,EAAUqE,GAAWD,SAASA,GCFnE,SAASE,EAAMC,EAAOC,GAIlB,OAHAjC,EAAKiC,WAASnE,EAAGjG,GACbmK,EAAM3G,KAAKxD,KAERmK,ECbX3B,EAAEhG,GAAG2F,KAAO,SAAUvI,GAClB,OAAOuI,EAAKrI,KAAMF,ICFtB4I,EAAEhG,GAAG6H,IAAM,SAAUjD,GACjB,YAAiBxE,IAAVwE,EACD,GAAGzG,MAAMC,KAAKd,MACdA,KAAc,GAATsH,EAAaA,EAAQA,EAAQtH,KAAKQ,SCCjDkI,EAAEhG,GAAG8H,KAAO,SAAU7B,GAClBzB,IAAMuD,EAAgB,GAItB,OAHAzK,KAAKqI,cAAMlC,EAAGO,GACV0D,EAAMK,EAAe/B,EAAEhC,EAAQmD,iBAAiBlB,IAAW4B,SAExD,IAAI/B,EAAGiC,ICLlBvD,IAAMwD,EAAW,GAEbC,EAAgB,EAIpB,SAASC,EAAalE,GAClBQ,IAAM2D,EAAM,eAOZ,OALKnE,EAAQmE,KAETnE,EAAQmE,KAASF,GAGdjE,EAAQmE,GAKnB,SAASC,EAAMC,GACX7D,IAAM8D,EAAQD,EAAKE,MAAM,KACzB,MAAO,CACHF,KAAMC,EAAM,GACZE,GAAIF,EAAMnK,MAAM,GAAGsK,OAAOC,KAAK,MAMvC,SAASC,EAAWH,GAChB,OAAO,IAAII,OAAO,UAAYJ,EAAGhF,QAAQ,IAAK,SAAW,WA8F7D,SAASqF,EAAO7E,EAAS8E,EAAOC,EAAM9C,YAE5B+C,EAAeC,UACVC,EAAkBD,EAAQE,IACjCnF,EAAQoF,oBAAoBH,EAAQZ,KAAMY,EAAQI,OAAO,GAH7D7E,IAAM0E,EAAoBlB,EAASE,EAAalE,KAAa,GAKxD8E,EAIDA,EAAMP,MAAM,KAAKe,iBAASjB,GA/FlC,IAAqBrE,EAAe+E,EAAM9C,EAChCsD,EA+FMlB,IAhGKrE,EAiGOA,EAjGQ+E,EAiGOA,EAjGD9C,EAiGOA,EAhGvCsD,EAAQnB,EAgGmBC,IA/FzBL,EAASE,EAAalE,KAAa,IAAIwF,gBAAQP,UAAYA,KAC7DM,EAAMlB,MAAQY,EAAQZ,OAASkB,EAAMlB,SACrCkB,EAAMf,IAAMG,EAAWY,EAAMf,IAAItC,KAAK+C,EAAQT,QAC9CO,GAAQb,EAAae,EAAQF,QAAUb,EAAaa,OACpD9C,GAAYgD,EAAQhD,WAAaA,MA2FgBqD,iBAASL,UAAYD,EAAYC,OALpFC,EAAkBI,iBAASL,UAAYD,EAAYC,KCtI3D,SAASQ,EAAOlH,EAAQmH,gEASpB,OARAC,EAAQC,QAAQF,GAChB/D,EAAKgE,WAAUlG,EAAGoG,GACdlE,EAAKkE,WAAS/E,EAAMtH,GACXkF,EAAYlF,KACb+E,EAAOuC,GAAQtH,OAIpB+E,ECoBX,SAASuH,EAAMC,GACX,IAAK5G,EAAa4G,KAAS9L,MAAM0B,QAAQoK,GACrC,MAAO,GAEXvF,IAAMxG,EAAO,GACb,SAASgM,EAAY7B,EAAK3K,GACtBqH,IAAIoF,EACA9G,EAAa3F,GACbmI,EAAKnI,WAAQe,EAAG2L,GAERD,EADAhM,MAAM0B,QAAQnC,KAAW2F,EAAa+G,GAC7B,GAGA3L,EAEbyL,EAAe7B,MAAO8B,MAAWC,MAKjCD,EADS,MAATzM,GAA2B,KAAVA,EACR,IAGA,IAAI2M,mBAAmB3M,GAEpCQ,EAAKgD,KAAKmJ,mBAAmBhC,GAAO8B,IAW5C,OARIhM,MAAM0B,QAAQoK,GACdpE,EAAKoE,EAAK,WACNC,EAAY1M,KAAK2G,KAAM3G,KAAKE,SAIhCmI,EAAKoE,EAAKC,GAEPhM,EAAK0K,KAAK,KChErB1C,EAAEhG,GAAGoK,QAAU,SAAU/B,EAAMgC,GAC3B7F,IACI8F,EADEf,EAAQnB,EAAMC,GAEdkC,EAAc,CAChB3N,SAAS,EACTC,YAAY,GAEV2N,GAAqF,EAAtE,CAAC,QAAS,YAAa,UAAW,aAAavD,QAAQsC,EAAMlB,MAalF,OAVIiC,EAFAE,EAEc,IAAI9L,WAAW6K,EAAMlB,KAAMkC,IAGzCA,EAAYzN,OAASuN,EACP,IAAI7K,YAAY+J,EAAMlB,KAAMkC,KAGlCE,QAAUJ,EAEtBC,EAAYI,IAAMnB,EAAMf,GACjBlL,KAAKqI,KAAK,WACbrI,KAAKqN,cAAcL,MCvB3B9F,IAAMoG,EAAgB,GAEhBC,EAAa,CACfC,UAAW,kBACXC,YAAa,oBACbC,UAAW,kBACXC,aAAc,sBCIlB,SAASC,EAAkBC,GACvB,OAA0C,GAAnC,CAAC,MAAO,QAAQlE,QAAQkE,GAOnC,SAASC,EAAYC,EAAKC,GACtB,OAAUD,MAAOC,GAAQ9H,QAAQ,YAAa,KClBlDwC,EAAEuF,KDyEF,SAAcC,GAEV3G,IAnDkB2G,EAEZC,EAyHEC,EAxEJC,GAAa,EAEXpB,EAAc,GAEdqB,GAvDYJ,EAuDiBA,EArD7BC,EAAW,CACbJ,IAAK,GACLF,OAAQ,MACRU,KAAM,GACNC,aAAa,EACbC,OAAO,EACPC,OAAO,EACPC,SAAU,GACVC,SAAU,GACVC,QAAS,GACTC,UAAW,GACXC,WAAY,GACZC,SAAU,OACVC,YAAa,oCACbC,QAAS,EACTpK,QAAQ,GAGZuD,EAAKiF,WAAgBzC,EAAK3K,GACJ,CACd,aACA,UACA,QACA,WACA,cAGUyJ,QAAQkB,GAAO,IAAMzF,EAAYlF,KAC3CiO,EAAStD,GAAO3K,KAGjBiM,EAAO,GAAIgC,EAAUD,IAuBxBH,EAAMO,EAAcP,KAAOzM,OAAO6N,SAASC,WACzCvB,EAASS,EAAcT,OAAOxH,cAChCkI,EAAOD,EAAcC,KACnBC,EAAcF,EAAcE,YAC5BC,EAAQH,EAAcG,MACtBC,EAAQJ,EAAcI,MACtBC,EAAWL,EAAcK,SACzBC,EAAWN,EAAcM,SACzBC,EAAUP,EAAcO,QACxBC,EAAYR,EAAcQ,UAC1BC,EAAaT,EAAcS,WAC3BC,EAAWV,EAAcU,SACzBC,EAAcX,EAAcW,YAC5BC,EAAUZ,EAAcY,QACxBpK,EAASwJ,EAAcxJ,OAyB7B,SAASgI,EAAQb,EAAOoD,EAAQvP,WAMxBwP,EACAC,uDALAzK,GACA4D,EAAEhJ,UAAUoN,QAAQb,EAAOoD,GAK3BvP,IAEIA,KAAYwN,IAEZgC,EAAUhC,EAAcxN,WAAaY,IAGrC4N,EAAcxO,KAEdyP,EAAUjB,EAAcxO,WAAaY,IAGxB,eAAbZ,IACa,IAAZwP,IAAiC,IAAZC,IACtBlB,GAAa,IAkJzB,OA9LIE,IACCX,EAAkBC,KAAWW,GAC7BtJ,EAASqJ,IACRA,aAAgBiB,aAChBjB,aAAgBkB,MAChBlB,aAAgB9I,UAChB8I,aAAgBmB,WAClBnB,EAAO/B,EAAM+B,IAGbA,GAAQX,EAAkBC,KAE1BE,EAAMD,EAAYC,EAAKQ,GACvBA,EAAO,MAsCA,IAAI9L,iBAAStC,EAASE,GAErBuN,EAAkBC,KAAYa,IAC9BX,EAAMD,EAAYC,OAAU4B,KAAKC,QAGrC1I,IAiCI2I,EAjCEC,EAAM,IAAIC,eAChBD,EAAIE,KAAKnC,EAAQE,EAAKU,EAAOE,EAAUC,IACnCK,GACCV,IAASX,EAAkBC,KAA2B,IAAhBoB,IACvCa,EAAIG,iBAAiB,eAAgBhB,GAGxB,SAAbD,GACAc,EAAIG,iBAAiB,SAAU,qCAG/BpB,GACAxG,EAAKwG,WAAUhE,EAAK3K,GAEXkF,EAAYlF,IACb4P,EAAIG,iBAAiBpF,EAAK3K,EAAQ,MAK1B,yBAAyB0I,KAAKmF,IAC9CzC,OAAO4E,KAAO5O,OAAO6N,SAASgB,MAE9BL,EAAIG,iBAAiB,mBAAoB,kBAEzCnB,GACAzG,EAAKyG,WAAYjE,EAAK3K,GAElB4P,EAAIjF,GAAO3K,IAGnB+M,EAAY6C,IAAMA,EAClB7C,EAAYiB,QAAUI,EAEtBwB,EAAIM,OAAS,WACLP,GACAQ,aAAaR,GAGjB3I,IAGIoJ,EAHEC,EAAqC,KAAdT,EAAI3O,QAAiB2O,EAAI3O,OAAS,KAC5C,MAAf2O,EAAI3O,QACW,IAAf2O,EAAI3O,OAER,GAAIoP,EAUA,GARInC,EADe,MAAf0B,EAAI3O,QAA6B,SAAX0M,EACT,YAEO,MAAfiC,EAAI3O,OACI,cAGA,UAEA,SAAb6N,EAAqB,CACrB,IACIsB,EACe,SAAXzC,OAAoB/K,EAAY0N,KAAK1F,MAAMgF,EAAIW,cACnDxD,EAAYsB,KAAO+B,EAEvB,MAAO5L,GAEHoI,EAAQS,EAAWG,UAAWT,EAAa,QAAS6C,EADpD1B,EAAa,eAEb/N,EAAO,IAAI0E,MAAMqJ,IAEF,gBAAfA,IACAtB,EAAQS,EAAWE,YAAaR,EAAa,UAAWqD,EAAclC,EAAY0B,GAClF3P,EAAQmQ,SAIZA,EACe,SAAXzC,OACM/K,EACqB,SAArBgN,EAAIY,cAAgD,KAArBZ,EAAIY,aAC/BZ,EAAIW,aACJX,EAAIa,SAClB1D,EAAYsB,KAAO+B,EACnBxD,EAAQS,EAAWE,YAAaR,EAAa,UAAWqD,EAAclC,EAAY0B,GAClF3P,EAAQmQ,QAKZxD,EAAQS,EAAWG,UAAWT,EAD9BmB,EAAa,QACuC0B,EAAK1B,GACzD/N,EAAO,IAAI0E,MAAMqJ,IAGrB/F,EAAK,CAACiF,EAAcyB,WAAYA,YAAc5I,EAAGsF,GACzCA,GAAQA,EAAKqE,EAAI3O,UACboP,EACA9E,EAAKqE,EAAI3O,QAAQmP,EAAclC,EAAY0B,GAG3CrE,EAAKqE,EAAI3O,QAAQ2O,EAAK1B,MAIlCtB,EAAQS,EAAWI,aAAcV,EAAa,WAAY6C,EAAK1B,IAEnE0B,EAAIc,QAAU,WACNf,GACAQ,aAAaR,GAEjB/C,EAAQS,EAAWG,UAAWT,EAAa,QAAS6C,EAAKA,EAAIe,YAC7D/D,EAAQS,EAAWI,aAAcV,EAAa,WAAY6C,EAAK,SAC/DzP,EAAO,IAAI0E,MAAM+K,EAAIe,cAEzBf,EAAIgB,QAAU,WACVvJ,IAAIsJ,EAAa,QACbhB,IACAgB,EAAa,UACbR,aAAaR,IAEjB/C,EAAQS,EAAWG,UAAWT,EAAa,QAAS6C,EAAKe,GACzD/D,EAAQS,EAAWI,aAAcV,EAAa,WAAY6C,EAAKe,GAC/DxQ,EAAO,IAAI0E,MAAM8L,KAGrB/D,EAAQS,EAAWC,UAAWP,EAAa,aAAc6C,GACrDzB,EACAhO,EAAO,IAAI0E,MAAM,YAIP,EAAVmK,IACAW,EAAazN,sBACT0N,EAAIiB,SACL7B,IAGPY,EAAIkB,KAAKzC,OE5RrB7F,EAAEuI,UCWF,SAAmB/C,GACf,OAAO/B,EAAOmB,EAAeY,ICZjCxF,EAAEwB,SAAWA,ECFbhD,IAAMgK,EAAS,0BCQf,SAASC,GAAmBzK,EAAS6F,GAE5B7F,EAAQwK,KAETxK,EAAQwK,GAAU,IAEtB7I,EAAKkE,WAAS1B,EAAK3K,GAEfwG,EAAQwK,GAAQlL,EAAY6E,IAAQ3K,IAG5C,SAASqO,GAAK7H,EAASmE,EAAK3K,SAGxB,OAAI2F,EAAagF,IACbsG,GAAmBzK,EAASmE,GACrBA,GAINzF,EAAYlF,GAMbkF,EAAYyF,GAELnE,EAAQwK,GAAUxK,EAAQwK,GAAU,IAI/CrG,EAAM7E,EAAY6E,GAEdnE,EAAQwK,IAAWrG,KAAOnE,EAAQwK,GAE3BxK,EAAQwK,GAAQrG,QAF3B,IAbIsG,GAAmBzK,MAAS,IAAGmE,GAAM3K,MAC9BA,GC7Bf,SAASkR,GAAIC,EAAUvR,SACfI,EACEsD,EAAM,GAOZ,OANA6E,EAAKgJ,WAAWpQ,EAAGyF,GAEF,OADbxG,EAAQJ,EAASgB,KAAKQ,OAAQoF,EAASzF,KAEnCuC,EAAIE,KAAKxD,QAGV,IAAGoR,eAAU9N,GCuBxB,SAAS+N,GAAW7K,EAASC,GAEzB,GAAKD,EAAQwK,GAAb,CAGAhK,IAAMqE,WAAUiG,GACZA,EAAWxL,EAAYwL,GAEnB9K,EAAQwK,GAAQM,KAEhB9K,EAAQwK,GAAQM,GAAY,YAErB9K,EAAQwK,GAAQM,KAG3BpM,EAAYuB,IAEZD,EAAQwK,GAAU,YAEXxK,EAAQwK,IAGVhM,EAASyB,GACdA,EACKsE,MAAM,KACNiB,gBAAQsF,UAAaA,IACrBxF,iBAASwF,UAAajG,EAAOiG,KAGlCnJ,EAAK1B,WAAOR,EAAGqL,UAAajG,EAAOiG,MCpD3C,SAASC,GAAOlR,GACZ2G,IAAMwK,EAAS,GAMf,OALArJ,EAAK9H,WAAM4F,EAAGjF,IACmB,IAAzBwQ,EAAO/H,QAAQzI,IACfwQ,EAAOhO,KAAKxC,KAGbwQ,ECXI,SAASC,GAAIC,EAAWC,EAAWC,EAAMnJ,EAAUuD,GAC9DhF,IACIjC,EADEzB,EAAM,GAgCZ,OA9BAoO,EAAUvJ,cAAMlC,EAAGO,GAGf,IAFAzB,EAASyB,EAAQoL,GAEV7M,GAAUS,EAAUT,IAAS,CAEhC,GAAkB,IAAd4M,EAAiB,CACjB,GAAIlJ,GAAYD,EAAEzD,GAAQ8M,GAAGpJ,GACzB,MAECuD,IAAUxD,EAAEzD,GAAQ8M,GAAG7F,IACxB1I,EAAIE,KAAKuB,OAIZ,CAAA,GAAkB,IAAd4M,EAAiB,CACjBlJ,IAAYD,EAAEzD,GAAQ8M,GAAGpJ,IAC1BnF,EAAIE,KAAKuB,GAEb,MAIK0D,IAAYD,EAAEzD,GAAQ8M,GAAGpJ,IAC1BnF,EAAIE,KAAKuB,GAIjBA,EAASA,EAAO6M,MAGjB,IAAItJ,EAAGiJ,GAAOjO,ICrCzBkF,EAAE6F,KAAOA,GCAT7F,EAAEL,KAAOA,ECCTK,EAAEyD,OAAS,yEACP,OAAuB,IAAnBE,EAAQ7L,QACR6H,EAAKgE,EAAQ,YAAK7E,EAAMtH,GACpBF,EAAKwH,GAAQtH,IAEVF,MAEJmM,gBAAOE,EAAQ2F,QAAS3F,EAAQ2F,gBAAY3F,KCRvD3D,EAAE0I,IAAMA,GCAR1I,EAAE0B,MAAQA,ECAV1B,EAAE8D,MAAQA,ECAV9D,EAAE6I,WAAaA,GCAf7I,EAAE+I,OAASA,GCGX/I,EAAEhG,GAAGuP,IAAM,SAAUtJ,GACjB,OAAO,IAAIH,EAAGiJ,GAAOrH,EAAMpK,KAAKuK,MAAO7B,EAAEC,GAAU4B,UCFvDlC,EAAK,CAAC,MAAO,SAAU,mBAAYlC,EAAGQ,GAClC+B,EAAEhG,GAAMiE,WAAe,SAAUuL,GAC7B,MAAa,WAATvL,GAAsB5C,UAAUvD,OAK7BR,KAAKqI,cAAMpH,EAAGyF,GACZhB,EAAUgB,IAQf2B,GALiBrD,EAAWkN,GACtBA,EAAUpR,KAAK4F,EAASzF,EAAGyF,EAAQyL,aAAa,UAAY,IAC5DD,GACDjH,MAAM,KACNiB,gBAAQvF,UAASA,aACPR,EAAGiM,GACd1L,EAAQ2L,UAAU1L,GAAMyL,OAdrBpS,KAAKqI,cAAMlC,EAAGO,GACjBA,EAAQ4L,aAAa,QAAS,SCL9CjK,EAAK,CAAC,eAAgB,wBAAiBwJ,EAAWlL,GAC9C+B,EAAEhG,GAAGiE,GAAQ,SAAU1B,GACnBiC,IAAMqL,EAAWV,EAAYnJ,EAAE1I,KAAKuK,MAAMiI,WAAaxS,KACjDyS,EAAU/J,EAAEzD,GACZyM,EAAS,GAcf,OAbAe,EAAQpK,cAAMf,EAAOrC,GACZA,EAAOyN,YAGZH,EAASlK,cAAMlC,EAAGO,GACdQ,IAAMyL,EAAUrL,EACVZ,EAAQkM,WAAU,GAClBlM,EACAmM,EAAehB,EAAY5M,EAAO6N,YAAc7N,EACtDyM,EAAOhO,KAAKiP,GACZ1N,EAAOyN,WAAWK,aAAaJ,EAASE,OAGzCnK,EAAEmJ,EAAYH,EAAOc,UAAYd,MCRhDrJ,EAAK,CAAC,SAAU,kBAAWwJ,EAAWlL,GAClC+B,EAAEhG,GAAGiE,GAAQ,kEAKT,OAHkB,IAAdkL,IACAnR,EAAOA,EAAK8R,WAETxS,KAAKqI,cAAMf,EAAOZ,GAIrB2B,EAHgBrD,EAAWtE,EAAK,IAC1B,CAACA,EAAK,GAAGI,KAAK4F,EAASY,EAAOZ,EAAQuB,YACtCvH,WACSyF,EAAGlB,GACdsC,IAdKtC,IACTC,EADSD,EAeWA,IAde,MAAdA,EAAO,IAA4C,MAA9BA,EAAOA,EAAOzE,OAAS,GAiBpD8G,GAAS5B,EAAUT,GACdyD,EAAEzD,EAAO2N,WAAU,IAGnBlK,EAAEzD,GANFyD,EAAEb,EAAmB5C,EAAQ,SAQnC4M,EAAY,cAAgB,gBAAgBnL,UC7BpEgC,EAAEhG,GAAGsQ,IAAM,SAAUxH,EAAO7C,EAAU7I,cAElC,OAAI+F,EAAa2F,IACbnD,EAAKmD,WAAQT,EAAMrI,GAGf1C,EAAKgT,IAAIjI,EAAMpC,EAAUjG,KAEtB1C,QAGM,IAAb2I,IAAsB3D,EAAW2D,KACjC7I,EAAW6I,EACXA,OAAW7F,IAIE,IAAbhD,IACAA,EAAWqI,GAERnI,KAAKqI,KAAK,WACbkD,EAAOvL,KAAMwL,EAAO1L,EAAU6I,OCpBtCD,EAAEhG,GAAGuQ,GAAK,SAAUzH,EAAO7C,EAAU4F,EAAMzO,EAAUoT,cAEjD,GAAIrN,EAAa2F,GAYb,OAVKtG,EAASyD,KAEV4F,EAAOA,GAAQ5F,EACfA,OAAW7F,GAEfuF,EAAKmD,WAAQT,EAAMrI,GAGf1C,EAAKiT,GAAGlI,EAAMpC,EAAU4F,EAAM7L,EAAIwQ,KAE/BlT,KAoBX,GAlBY,MAARuO,GAA4B,MAAZzO,GAEhBA,EAAW6I,EACX4F,EAAO5F,OAAW7F,GAED,MAAZhD,IACDoF,EAASyD,IAET7I,EAAWyO,EACXA,OAAOzL,IAIPhD,EAAWyO,EACXA,EAAO5F,EACPA,OAAW7F,KAGF,IAAbhD,EACAA,EAAWqI,OAEV,IAAKrI,EACN,OAAOE,KAGX,GAAIkT,EAAK,CAELhM,IAAMiM,EAAQnT,KACRoT,EAAetT,EACrBA,EAAW,SAAUmM,GAGjB,OAFAkH,EAAMH,IAAI/G,EAAMlB,KAAMpC,EAAU7I,GAEzBsT,EAAatP,MAAM9D,KAAM+D,YAGxC,OAAO/D,KAAKqI,KAAK,Y7BGrB,SAAa3B,EAAS8E,EAAOC,EAAM8C,EAAM5F,GACrCzB,IAAMmM,EAAYzI,EAAalE,GAC1BgE,EAAS2I,KACV3I,EAAS2I,GAAa,IAG1B9L,IAAI+L,GAAa,EACbzN,EAAa0I,IAASA,EAAK+E,aAC3BA,GAAa,GAEjB9H,EAAMP,MAAM,KAAKe,iBAASjB,GACtB,GAAKA,EAAL,CAGA7D,IAAM+E,EAAQnB,EAAMC,GAoCdY,EAAU,CACZZ,KAAMkB,EAAMlB,KACZG,GAAIe,EAAMf,QACVO,WACA9C,EACAkD,GAAInB,EAAS2I,GAAW7S,OACxBuL,MAAOwH,GAEX7I,EAAS2I,GAAW3P,KAAKiI,GACzBjF,EAAQqC,iBAAiB4C,EAAQZ,KAAMwI,EAASD,GA5ChD,SAASE,EAAOnU,EAAGoU,IAKA,IAHAhI,EAAK3H,MAAM2P,OAEZ3Q,IAAdzD,EAAE8N,QAAwB,CAAC9N,GAAK,CAACA,GAAGiS,OAAOjS,EAAE8N,YAEzC9N,EAAEqU,iBACFrU,EAAEsU,mBAGV,SAASJ,EAAQlU,GAETA,EAAE+N,MAAQ/B,EAAWhM,EAAE+N,KAAKxE,KAAKqD,EAAMf,MAI3C7L,EAAEuU,MAAQrF,EACN5F,EAEAD,EAAEhC,GACG8D,KAAK7B,GACL4B,MACAiI,UACAxG,iBAASyH,GACNA,IAASpU,EAAE4F,SACXiF,EAASuJ,EAAMpU,EAAE4F,SACjBuO,EAAOnU,EAAGoU,KAMlBD,EAAOnU,EAAGqH,O6BjDlBuL,CAAIjS,KAAMwL,EAAO1L,EAAUyO,EAAM5F,MCtDzCN,EAAKkF,WAAa5G,EAAMkN,GACpBnL,EAAEhG,GAAGiE,GAAQ,SAAUjE,GACnB,OAAO1C,KAAKiT,GAAGY,WAAYxU,EAAGgQ,GAC1B3M,EAAGrD,EAAGgQ,EAAOS,IAAKT,EAAOnB,QAASmB,EAAOd,WCJrD7F,EAAEhG,GAAG0O,IAAM,SAAUtR,GACjB,OAAO,IAAI0I,EAAG4I,GAAIpR,cAAO0G,EAASzF,UAAMnB,EAASgB,KAAK4F,EAASzF,EAAGyF,OCFtEgC,EAAEhG,GAAGoR,MAAQ,WACT,OAAO9T,KAAKoR,IAAI,WACZ,OAAOpR,KAAK4S,WAAU,MCD9BlK,EAAEhG,GAAGqP,GAAK,SAAUpJ,GAChBpB,IAAIwM,GAAY,EAChB,GAAI/O,EAAW2D,GAMX,OALA3I,KAAKqI,cAAMf,EAAOZ,GACViC,EAAS7H,KAAK4F,EAASY,EAAOZ,KAC9BqN,GAAY,KAGbA,EAEX,GAAI7O,EAASyD,GAWT,OAVA3I,KAAKqI,cAAMlC,EAAGO,GACNlB,EAAWkB,IAAYpB,EAASoB,KAIpBA,EAAQsN,SAAWtN,EAAQuN,mBAC/BnT,KAAK4F,EAASiC,KACtBoL,GAAY,KAGbA,EAEX7M,IAAMgN,EAAexL,EAAEC,GAQvB,OAPA3I,KAAKqI,cAAMlC,EAAGO,GACVwN,EAAa7L,cAAMlC,EAAGgO,GACdzN,IAAYyN,IACZJ,GAAY,OAIjBA,GC/BXrL,EAAEhG,GAAG6I,OAAS,SAAU5C,GACpB,OAAO3I,KAAKqI,cAAMlC,EAAGO,IACbA,EAAQgM,YAAgB/J,IAAYD,EAAEhC,GAASqL,GAAGpJ,IAClDjC,EAAQgM,WAAW0B,YAAY1N,MCG3C2B,EAAK,CAAC,UAAW,mBAAYwJ,EAAWlL,GACpC+B,EAAEhG,GAAGiE,GAAQ,kEACT,OAAO3G,KAAKqI,cAAMf,EAAOZ,SACfwB,EAAaxB,EAAQwB,WACrBmM,EAAcnM,EAAW1H,OACzB8T,EAAQD,EACRnM,EAAW2J,EAAYwC,EAAc,EAAI,GACzC3U,SAASsI,cAAc,OACxBqM,GACD3N,EAAQ6N,YAAYD,GAExB/M,IAAIiN,EAAWxP,EAAWtE,EAAK,IACzB,CAACA,EAAK,GAAGI,KAAK4F,EAASY,EAAOZ,EAAQuB,YACtCvH,EAEF4G,IACAkN,EAAWA,EAASpD,aAAKqD,GACrB,OAAOvP,EAASuP,GAAWA,EAAU/L,EAAE+L,GAASX,cAGxDpL,EAAE4L,IAAOzC,EAAY,QAAU,kBAAa2C,GACvCH,GACD3N,EAAQ0N,YAAYE,QCzBpCjM,EAAK,CAAC,WAAY,sBAAewJ,EAAWlL,GACxC+B,EAAEhG,GAAGiE,GAAQ,SAAU1B,GACnBiC,IAAMwN,EAAc,GACdjC,EAAU/J,EAAEzD,GAAQmM,aAAKjL,EAAGO,GAC9BQ,IAAMgB,EAAaxB,EAAQwB,WACrBmM,EAAcnM,EAAW1H,OAC/B,GAAI6T,EACA,OAAOnM,EAAW2J,EAAY,EAAIwC,EAAc,GAEpDnN,IAAMoN,EAAQ5U,SAASsI,cAAc,OAGrC,OAFAtB,EAAQ6N,YAAYD,GACpBI,EAAYhR,KAAK4Q,GACVA,IAELK,EAAU3U,KAAK6R,EAAY,eAAiB,eAAeY,GAEjE,OADA/J,EAAEgM,GAAanJ,SACRoJ,KClBftM,EAAK,CAAC,OAAQ,OAAQ,gBAASwJ,EAAWlL,GA+BtC,SAAS4D,EAAI7D,EAASmE,GAClB,OAAQgH,GAEJ,KAAK,EAED3K,IAAMhH,EAAQwG,EAAQyL,aAAatH,GACnC,OAAOxF,EAAOnF,QAAS4C,EAAY5C,EAEvC,KAAK,EAED,OAAOwG,EAAQmE,GAEnB,QACI,OAAOnD,EAAShB,EAASmE,IAGrCnC,EAAEhG,GAAGiE,GAAQ,SAAUkE,EAAK3K,cACxB,GAAI2F,EAAagF,GAKb,OAJAxC,EAAKwC,WAAM+J,EAAGhI,GAEV5M,EAAK2G,GAAMiO,EAAGhI,KAEX5M,KAEX,GAAyB,IAArB+D,UAAUvD,OAId,OAAOR,KAAKqI,cAAMpH,EAAGyF,IA1DzB,SAAaA,EAASmE,EAAK3K,GAEvB,IAAIkF,EAAYlF,GAGhB,OAAQ2R,GAEJ,KAAK,EACGxM,EAAOnF,GACPwG,EAAQmO,gBAAgBhK,GAGxBnE,EAAQ4L,aAAazH,EAAK3K,GAE9B,MAEJ,KAAK,EAEDwG,EAAQmE,GAAO3K,EACf,MAEJ,QACI2K,EAAM7E,EAAY6E,GAElBnE,EAAQoO,MAAMjK,GAAO1F,EAASjF,GACrBA,IAAkC,EAA1BkI,EAAUuB,QAAQkB,GAAY,GAAK,MAC9C3K,GAiCV6U,CAAIrO,EAASmE,EAAK7F,EAAW9E,GAASA,EAAMY,KAAK4F,EAASzF,EAAGsJ,EAAI7D,EAASmE,IAAQ3K,KAJlFgH,IAAMR,EAAU1G,KAAK,GACrB,OAAO0F,EAAUgB,GAAW6D,EAAI7D,EAASmE,QAAO/H,KCtD5D4F,EAAEhG,GAAGsS,SAAW,SAAUrM,GACtBzB,IAAM8N,EAAW,GAWjB,OAVAhV,KAAKqI,cAAMlC,EAAGO,GACV2B,EAAK3B,EAAQwB,oBAAa+M,EAAIC,GACrBxP,EAAUwP,KAGVvM,IAAYD,EAAEwM,GAAWnD,GAAGpJ,IAC7BqM,EAAStR,KAAKwR,QAInB,IAAI1M,EAAGiJ,GAAOuD,KCjBzBtM,EAAEhG,GAAG7B,MAAQ,kEACT,OAAO,IAAI2H,EAAG,GAAG3H,MAAMiD,MAAM9D,KAAMU,KCAvCgI,EAAEhG,GAAGyS,GAAK,SAAU7N,GAChBJ,IAAM1D,GAAiB,IAAX8D,EAAetH,KAAKa,MAAMyG,GAAStH,KAAKa,MAAMyG,GAAQA,EAAQ,GAC1E,OAAO,IAAIkB,EAAGhF,ICDlB6E,EAAK,CAAC,GAAI,IAAK,mBAAYwJ,EAAWlL,GAClC+B,EAAEhG,YAAYiE,GAAU,SAAUgC,EAAUuD,GAGxC,OAAOyF,GADSE,EAAmBnJ,EAAE1I,KAAKuK,MAAMiI,WAApBxS,KACT6R,EAAW,aAAclJ,EAAUuD,MCH9DxD,EAAEhG,GAAG0S,QAAU,SAAUzM,GACrB,GAAI3I,KAAK+R,GAAGpJ,GACR,OAAO3I,KAEXkH,IAAMmO,EAAU,GAOhB,OANArV,KAAKsV,UAAUjN,cAAMlC,EAAGO,GACpB,GAAIgC,EAAEhC,GAASqL,GAAGpJ,GAEd,OADA0M,EAAQ3R,KAAKgD,IACN,IAGR,IAAI8B,EAAG6M,ICZlBnO,IAAMqO,GAAS,+BAqBf,SAASC,GAAS9O,EAASmE,EAAK3K,GAC5B,GAAIkF,EAAYlF,IAA+B,IAArBwG,EAAQ+O,SAAgB,CAC9CvO,IAAMP,EAAO,QAAUL,EAAYuE,GAEnC,GAAI3F,EADJhF,EAAQwG,EAAQyL,aAAaxL,IAEzB,IACIzG,EAxBE,UADDA,EAyBeA,IArBd,UAAVA,IAGU,SAAVA,EACO,KAEPA,KAAWA,EAAQ,IACXA,EAERqV,GAAO3M,KAAK1I,GACLsQ,KAAK1F,MAAM5K,GAEfA,GAWC,MAAOb,SAGPa,OAAQ4C,EA9BpB,IAAiB5C,EAiCb,OAAOA,ECzBX,SAASwV,GAAiBhP,EAASC,EAAMzG,EAAOyV,EAAWC,EAAeC,YAEhEC,EAAsB7O,GACxB,OAAQF,EAAcL,EAASC,EAAKH,cAAeS,GAC/C4O,EA6BR,OA3BkB,IAAdF,GAAmBC,IACnB1V,GAAS4V,EAAmB,WAE5BhP,EAAYJ,ItDePpF,OAAO5B,SAASqW,csDZM,IAAbF,IACV3V,GAAS4V,EAAmB,UAC5B5V,GAAS4V,EAAmB,YAEd,IAAdH,IACAzV,GAAS4V,EAAmB,WAEd,IAAdH,IACAzV,GAAS4V,EAAmB,UAC5B5V,GAAS4V,EAAmB,cAId,IAAdH,IACAzV,GAAS4V,EAAmB,YAEd,IAAdH,IACAzV,GAAS4V,EAAmB,UAC5B5V,GAAS4V,EAAmB,aAG7B5V,EASX,SAASqK,GAAI7D,EAASC,EAAMgP,EAAWC,GACnC1O,IAAM8O,EAAa,SAASrP,EACtBsP,EAAa,SAAStP,EACtBuP,EAAa,SAASvP,EACtBwP,EAAY,QAAQxP,EAE1B,GAAIrB,EAASoB,GAET,OAAqB,IAAdiP,EACDjP,EAAQyP,GACRrQ,EAAUpG,UAAUsW,GAG9B,GAAIxQ,EAAWkB,GAAU,CACrBQ,IAAMkP,EAAMtQ,EAAUY,GACtB,OAAO2P,KAAKC,IAEZ5P,EAAQoC,KAAKmN,GAAaG,EAAIH,GAE9BvP,EAAQoC,KAAKoN,GAAaE,EAAIF,GAAaE,EAAIJ,IAEnD9O,IAAMhH,EAAQuH,WAAWhB,EAAsBC,EAASC,EAAKH,gBAAkB,KAC/E,OAAOkP,GAAiBhP,EAASC,EAAMzG,EAAOyV,EAAWC,EAAe,GCzE5E,SAASW,GAAWhE,EAAU5L,GAC1B,OAAOc,WAAW8K,EAASiE,IAAI7P,ICAnC,SAAS4D,GAAI7D,GACT,IAAKA,EAAQ+P,iBAAiBjW,OAC1B,MAAO,CAAEkW,IAAK,EAAGC,KAAM,GAE3BzP,IAAM0P,EAAOlQ,EAAQkB,wBACfiP,EAAMnQ,EAAQoQ,cAAcC,YAClC,MAAO,CACHL,IAAKE,EAAKF,IAAMG,EAAIG,YACpBL,KAAMC,EAAKD,KAAOE,EAAII,aH2B9BvO,EAAEhG,GAAG6L,KAAO,SAAU1D,EAAK3K,GAEvB,GAAIkF,EAAYyF,GAAM,CAClB,IAAK7K,KAAKQ,OACN,OAEJ0G,IAAMR,EAAU1G,KAAK,GACfkX,EAAa3I,GAAK7H,GAExB,GAAyB,IAArBA,EAAQ+O,SACR,OAAOyB,EAKX,IAFAhQ,IAAMiQ,EAAQzQ,EAAQ0Q,WAClBnW,EAAIkW,EAAM3W,OACPS,KACH,GAAIkW,EAAMlW,GAAI,CACVsG,IAAIZ,EAAOwQ,EAAMlW,GAAG0F,KACU,IAA1BA,EAAKgD,QAAQ,WAEbuN,EADAvQ,EAAOX,EAAYW,EAAK9F,MAAM,KACX2U,GAAS9O,EAASC,EAAMuQ,EAAWvQ,KAIlE,OAAOuQ,EAGX,OAAIrR,EAAagF,GACN7K,KAAKqI,KAAK,WACbkG,GAAKvO,KAAM6K,KAIM,IAArB9G,UAAUvD,QAAgB4E,EAAYlF,GAC/BF,KAGNoF,EAAYlF,GAMZF,KAAKQ,OAGHgV,GAASxV,KAAK,GAAI6K,EAAK0D,GAAKvO,KAAK,GAAI6K,SAH5C,EALW7K,KAAKqI,KAAK,WACbkG,GAAKvO,KAAM6K,EAAK3K,MI9E5BwI,EAAEhG,GAAG2U,MAAQ,WACT,OAAOrX,KAAKqI,KAAK,WACbrI,KAAKiI,UAAY,MCFzBS,EAAEhG,GAAGyJ,OAAS,SAAUM,GAKpB,OAJApE,EAAKoE,WAAMjF,EAAMtH,GAEbwI,EAAEhG,GAAG8E,GAAQtH,IAEVF,MCHX0I,EAAEhG,GAAGwJ,OAAS,SAAUvD,GACpB,GAAI3D,EAAW2D,GACX,OAAO3I,KAAKoR,aAAK9J,EAAOZ,UAAYiC,EAAS7H,KAAK4F,EAASY,EAAOZ,GAAWA,OAAU5D,IAE3F,GAAIoC,EAASyD,GACT,OAAO3I,KAAKoR,aAAKjL,EAAGO,UAAYgC,EAAEhC,GAASqL,GAAGpJ,GAAYjC,OAAU5D,IAExEoE,IAAMoQ,EAAY5O,EAAEC,GACpB,OAAO3I,KAAKoR,aAAKjL,EAAGO,UAAgD,EAApC4Q,EAAU/M,MAAMZ,QAAQjD,GAAgBA,OAAU5D,KCVtF4F,EAAEhG,GAAG2H,MAAQ,WACT,OAAOrK,KAAKmV,GAAG,ICCnBzM,EAAEhG,GAAG6U,IAAM,SAAU5O,GACjBzB,IAAMsQ,EAAWtS,EAASyD,GAAY3I,KAAKwK,KAAK7B,GAAYD,EAAEC,cAE9D,OAAO3I,KAAKoR,IAAI,WACZ,IAAK7J,IAAItG,EAAI,EAAGA,EAAIT,EAAQS,GAAK,EAC7B,GAAIiJ,EAASlK,KAAMwX,EAASvW,IACxB,OAAOjB,QCTvB0I,EAAEhG,GAAG+U,SAAW,SAAUvF,GACtB,OAAOlS,KAAK,GAAGqS,UAAUnI,SAASgI,IR6GtC7J,EAAK,CAAC,QAAS,mBAAYlC,EAAGQ,GAC1B0B,EAAK,SAAS1B,EAAQA,EAAKH,sBAAuBG,YAAUgP,EAAW+B,GACnEhP,EAAEhG,GAAGgV,GAAY,SAAUC,EAAQzX,GAE/BgH,IAAM0Q,EAAQ7T,UAAUvD,SAAWmV,EAAY,KtDtG9B,kBsDsG8CgC,IACzD/B,GAA2B,IAAX+B,IAA6B,IAAVzX,EAEzC,OAAK0X,EAME5X,KAAKqI,cAAMf,EAAOZ,UAnCrC,SAAaA,EAASmR,EAAclR,EAAMgP,EAAWC,EAAe1V,GAChEqH,IAAIuQ,EAAgB9S,EAAW9E,GACzBA,EAAMY,KAAK4F,EAASmR,EAActN,GAAI7D,EAASC,EAAMgP,EAAWC,IAChE1V,EACN,GAAqB,MAAjB4X,EAAJ,CAGA5Q,IAAMqL,EAAW7J,EAAEhC,GACbqR,EAAYpR,EAAKH,cAEvB,IAAsD,EAAlD,CAAC,OAAQ,UAAW,IAAImD,QAAQmO,GAChCvF,EAASiE,IAAIuB,EAAWD,OAD5B,CAKA5Q,IAAM8Q,EAASF,EAAc1I,WAAWlJ,QAAQ,YAAa,IAE7D4R,EACIpC,GAAiBhP,EAASC,EAFZc,WAAWqQ,GAEkBnC,EAAWC,GAAgB,IACjEoC,GAAU,MACnBzF,EAASiE,IAAIuB,EAAWD,KAeqB/C,CAAIrO,EAASY,EAAOX,EAAMgP,EAAWC,EAAe+B,KAL9E3X,KAAKQ,OACN+J,GAAIvK,KAAK,GAAI2G,EAAMgP,EAAWC,QAC9B9S,OSvHtB4F,EAAEhG,GAAGuV,KAAO,WACR,OAAOjY,KAAKqI,KAAK,WACbrI,KAAK8U,MAAMoD,QAAU,UCE7B7P,EAAK,CAAC,MAAO,OAAQ,iBAAUwJ,EAAWlL,GACtCO,IAKMiR,EALQ,CACVC,EAAG,QACHC,EAAG,YACHC,EAAG,eAEgBzG,GACvB,SAAStH,EAAIqH,GAET,GAAkB,IAAdC,EAEA,OAAOT,GAAIQ,WAAYlL,UAAYZ,EAAUY,GAASyR,KAAW/M,KAAK,IAG1E,GAAKwG,EAAUpR,OAAf,CAIA0G,IAAMqR,EAAe3G,EAAU,GAE/B,OAAkB,IAAdC,GAAmBnJ,EAAE6P,GAAcxG,GAAG,oBAC/BX,GAAI1I,EAAE6P,GAAc/N,KAAK,2BAAoB9D,UAAYA,EAAQxG,QAGrEqY,EAAaJ,IAiBxBzP,EAAEhG,GAAGiE,GAAQ,SAAUzG,GAEnB,OAAK6D,UAAUvD,OAIRR,KAAKqI,cAAMpH,EAAGyF,GACjBQ,IAAM4Q,EAAgB9S,EAAW9E,GAC3BA,EAAMY,KAAK4F,EAASzF,EAAGsJ,EAAI7B,EAAEhC,KAC7BxG,EAEY,IAAd2R,GAAmBlR,MAAM0B,QAAQyV,GAE7BpP,EAAEhC,GAASqL,GAAG,oBACdX,GAAI1I,EAAEhC,GAAS8D,KAAK,mBAAYhB,UAAYA,EAAOgP,UAE1C,EADLV,EAAcnO,QAAQH,EAAOtJ,SAKjCwG,EAAQ+R,SACoC,EAAxCX,EAAcnO,QAAQjD,EAAQxG,OApClD,SAAawG,EAASxG,GAGlB,GAAIkF,EAAYlF,GAAQ,CACpB,GAAkB,IAAd2R,EACA,OAEJ3R,EAAQ,GAEM,IAAd2R,GAAmBnM,EAAUxF,KAC7BA,EAAQA,EAAMwY,WAGlBhS,EAAQyR,GAAYjY,EA2BZ6U,CAAIrO,EAASoR,KAtBVvN,EAAIvK,SC5CvB0I,EAAEhG,GAAG4E,MAAQ,SAAUqB,GACnB,OAAK5E,UAAUvD,OAGX0E,EAASyD,GACFD,EAAEC,GAAU4B,MAAMZ,QAAQ3J,KAAK,IAEnCA,KAAKuK,MAAMZ,QAAQjB,EAAEC,GAAU,IAL3B3I,KAAKmV,GAAG,GAAGrN,SAASkN,WAAWzK,MAAMZ,QAAQ3J,KAAK,KCNjE0I,EAAEhG,GAAGiW,KAAO,WACR,OAAO3Y,KAAKmV,IAAI,ICApB9M,EAAK,CAAC,GAAI,MAAO,kBAAWwJ,EAAWlL,GACnC+B,EAAEhG,UAAUiE,GAAU,SAAUgC,EAAUuD,GACtC,OAAOyF,GAAI3R,KAAM6R,EAAW,qBAAsBlJ,EAAUuD,MCFpExD,EAAEhG,GAAGkW,IAAM,SAAUjQ,GACjBzB,IAAM2R,EAAY7Y,KAAKkM,OAAOvD,GAC9B,OAAO3I,KAAKoR,aAAKjL,EAAGO,UAAwC,EAA5BmS,EAAUvR,MAAMZ,QAAgB5D,EAAY4D,KCChFgC,EAAEhG,GAAGoW,aAAe,WAChB,OAAO9Y,KAAKoR,IAAI,WAEZ,IADA7J,IAAIuR,EAAe9Y,KAAK8Y,aACjBA,GAAoD,WAApCpQ,EAAEoQ,GAActC,IAAI,aACvCsC,EAAeA,EAAaA,aAEhC,OAAOA,GAAgBpZ,SAASqG,mBdJxC2C,EAAEhG,GAAGyE,SAAW,WACZ,GAAKnH,KAAKQ,OAAV,CAGA0G,IACI6R,EADExG,EAAWvS,KAAKmV,GAAG,GAErB6D,EAAe,CACfrC,KAAM,EACND,IAAK,GAET,GAAiC,UAA7BnE,EAASiE,IAAI,YACbuC,EAAgBxG,EAAS,GAAG3K,4BAE3B,CACDmR,EAAgBxG,EAAS0G,SACzB/R,IAAMgS,EAAgB3G,EAASuG,gBAC/BE,EAAeE,EAAcD,UAChBvC,KAAOH,GAAW2C,EAAe,oBAC9CF,EAAarC,MAAQJ,GAAW2C,EAAe,qBAEnD,MAAO,CACHxC,IAAKqC,EAAcrC,IAAMsC,EAAatC,IAAMH,GAAWhE,EAAU,cACjEoE,KAAMoC,EAAcpC,KAChBqC,EAAarC,KACbJ,GAAWhE,EAAU,kBCmBjC7J,EAAEhG,GAAGuW,OAAS,SAAU/Y,GAEpB,OAAK6D,UAAUvD,OAORR,KAAKqI,KAAK,SAAUf,IA3C/B,SAAaZ,EAASxG,EAAOoH,GACzBJ,IAAMqL,EAAW7J,EAAEhC,GACbS,EAAWoL,EAASiE,IAAI,YACb,WAAbrP,GACAoL,EAASiE,IAAI,WAAY,YAE7BtP,IAGIiS,EACAC,EAJEL,EAAgBxO,GAAI7D,GACpB2S,EAAmB9G,EAASiE,IAAI,OAChC8C,EAAoB/G,EAASiE,IAAI,QAKvC,IAFwC,aAAbrP,GAAwC,UAAbA,KACQ,GAAzDkS,EAAmBC,GAAmB3P,QAAQ,QAC5B,CACnBzC,IAAMqS,EAAkBhH,EAASpL,WACjCgS,EAAaI,EAAgB7C,IAC7B0C,EAAcG,EAAgB5C,UAG9BwC,EAAa1R,WAAW4R,GACxBD,EAAc3R,WAAW6R,GAE7BpS,IAAM4Q,EAAgB9S,EAAW9E,GAC3BA,EAAMY,KAAK4F,EAASY,EAAO6E,EAAO,GAAI4M,IACtC7Y,EACNqS,EAASiE,IAAI,CACTE,IAA0B,MAArBoB,EAAcpB,IACboB,EAAcpB,IAAMqC,EAAcrC,IAAMyC,OACxCrW,EACN6T,KAA4B,MAAtBmB,EAAcnB,KACdmB,EAAcnB,KAAOoC,EAAcpC,KAAOyC,OAC1CtW,IAaNiS,CAAI/U,KAAME,EAAOoH,KAPZtH,KAAKQ,OAGH+J,GAAIvK,KAAK,SAHhB,GcpDR0I,EAAEhG,GAAGwQ,IAAM,SAAU1H,EAAO7C,EAAU4F,EAAMzO,GAExC,OAAOE,KAAKiT,GAAGzH,EAAO7C,EAAU4F,EAAMzO,GAAU,ICApDuI,EAAK,CAAC,GAAI,MAAO,kBAAWwJ,EAAWlL,GACnC+B,EAAEhG,UAAUiE,GAAU,SAAUgC,EAAUuD,GAGtC,OAAOyF,GADSE,EAAmBnJ,EAAE1I,KAAKuK,MAAMiI,WAApBxS,KACT6R,EAAW,yBAA0BlJ,EAAUuD,MCL1ExD,EAAEhG,GAAG8W,WAAa,SAAUC,GACxBvS,IAAMwS,EAAQD,EAAcxO,MAAM,KAAKiB,gBAAQvF,UAASA,IACxD,OAAO3G,KAAKqI,KAAK,sBACbA,EAAKqR,WAAQvT,EAAGQ,GACZ3G,EAAK6U,gBAAgBlO,QCJjC+B,EAAEhG,GAAG6O,WAAa,SAAU5K,GACxB,OAAO3G,KAAKqI,KAAK,WACbkJ,GAAWvR,KAAM2G,MCHzB+B,EAAEhG,GAAGiX,WAAa,SAAUhT,GACxB,OAAO3G,KAAKqI,KAAK,WACb,WAEWrI,KAAK2G,GAEhB,MAAOtH,QCFfqJ,EAAEhG,GAAGkX,YAAc,SAAUC,GAWzB,OAVA7Z,KAAKqI,cAAMf,EAAOZ,GACda,IAAIkN,EAAUoF,EACV7U,EAAWyP,GACXA,EAAUA,EAAQ3T,KAAK4F,EAASY,EAAOZ,EAAQuB,WAE1CX,IAAUpC,EAASuP,KACxBA,EAAU/L,EAAE+L,GAASX,SAEzBpL,EAAEhC,GAASoT,OAAOrF,KAEfzU,KAAKuL,UCZhB7C,EAAEhG,GAAGqX,WAAa,SAAU9U,cACxB,OAAOyD,EAAEzD,GAAQmM,aAAK9J,EAAOZ,GAEzB,OADAgC,EAAEhC,GAASkT,YAAYtS,EAAQtH,EAAK8T,QAAU9T,GACvCA,EAAKuK,SCDpB7B,EAAEhG,GAAGsX,eAAiB,WAClB9S,IAAMwK,EAAS,GAyBf,OAxBA1R,KAAKqI,cAAMlC,EAAGO,GACVQ,IAAMmK,EAAW3K,aAAmBuT,gBAAkBvT,EAAQ2K,SAAW,CAAC3K,GAC1EgC,EAAE2I,GAAUhJ,cAAMlC,EAAGO,GACjBQ,IAAMqL,EAAW7J,EAAEhC,GACbqE,EAAOrE,EAAQqE,KACfmP,EAAWxT,EAAQwT,SAAS1T,cAClC,GAAiB,aAAb0T,GACAxT,EAAQC,OACPD,EAAQyT,WACsD,EAA/D,CAAC,QAAS,SAAU,WAAY,UAAUxQ,QAAQuQ,KACgB,IAAlE,CAAC,SAAU,SAAU,QAAS,QAAS,QAAQvQ,QAAQoB,MACb,IAAzC,CAAC,QAAS,YAAYpB,QAAQoB,IAC3BrE,EAAQ+R,SAAU,CACtBvR,IAAMhH,EAAQqS,EAASrR,OACNP,MAAM0B,QAAQnC,GAASA,EAAQ,CAACA,IACxC8L,iBAAS9L,GACdwR,EAAOhO,KAAK,CACRiD,KAAMD,EAAQC,WACdzG,WAMbwR,GC9BXhJ,EAAEhG,GAAG0X,UAAY,WACb,OAAO5N,EAAMxM,KAAKga,mBCDtB9S,IAAMmT,GAAiB,GAwBvB3R,EAAEhG,GAAG4X,KAAO,WACR,OAAOta,KAAKqI,KAAK,WApBrB,IAAwB6R,EAChBxT,EACAwR,EAmB2B,SAAvBlY,KAAK8U,MAAMoD,UACXlY,KAAK8U,MAAMoD,QAAU,IAES,SAA9BxQ,EAAS1H,KAAM,aACfA,KAAK8U,MAAMoD,SAzBCgC,EAyBwBla,KAAKka,SAtB5CG,GAAeH,KAChBxT,EAAUhH,SAASsI,cAAckS,GACjCxa,SAASoJ,KAAKyL,YAAY7N,GAC1BwR,EAAUxQ,EAAShB,EAAS,WAC5BA,EAAQgM,WAAW0B,YAAY1N,GACf,SAAZwR,IACAA,EAAU,SAEdmC,GAAeH,GAAYhC,GAExBmC,GAAeH,QCZ1BxR,EAAEhG,GAAG6X,SAAW,SAAU5R,GACtB,OAAO3I,KAAKwa,QAAQ7R,GAAUsJ,IAAIjS,KAAKya,QAAQ9R,KCFnDD,EAAEhG,GAAGgY,OAAS,WACV,OAAO1a,KAAKqI,KAAK,WACiB,SAA9BX,EAAS1H,KAAM,WAAwB0I,EAAE1I,MAAMsa,OAAS5R,EAAE1I,MAAMiY,UCQxEvP,EAAEhG,GAAGiY,OAAS,WACZ,OAAO3a,KAAKqI,KAAK,WACf,OAAOrI,KAAK4a,cCAhBlS,EAAEhG,GAAGmY,WAAa,SAAoBC,GAKpC,OAJI3V,EAAS2V,KACXA,SAGK9a,KAAKqI,KAAK,WACfrI,KAAK8U,MAAMiG,yBAA2BD,EACtC9a,KAAK8U,MAAMkG,mBAAqBF,KCLpCpS,EAAEhG,GAAGuY,cAAgB,SAEnBnb,GAGAoH,IAAMgU,EAAOlb,KACPmb,EAAS,CAAC,sBAAuB,iBAEvC,SAASC,EAAgD/b,GACnDA,EAAE4F,SAAWjF,OAKjBF,EAASgB,KAAKd,KAAMX,GAEpBgJ,EAAK8S,WAAShV,EAAG8F,GACfiP,EAAKlI,IAAI/G,EAAOmP,MAQpB,OAJA/S,EAAK8S,WAAShV,EAAG8F,GACfiP,EAAKjI,GAAGhH,EAAOmP,KAGVpb,MC7BT0I,EAAEhG,GAAG2Y,gBAAkB,SAAoBA,GACzC,OAAOrb,KAAKqI,KAAK,WACfrI,KAAK8U,MAAMwG,sBAAwBD,EACnCrb,KAAK8U,MAAMuG,gBAAkBA,KCHjC3S,EAAEhG,GAAG6Y,UAAY,SAAoBA,GACnC,OAAOvb,KAAKqI,KAAK,WACfrI,KAAK8U,MAAM0G,gBAAkBD,EAC7Bvb,KAAK8U,MAAMyG,UAAYA,KCT3BrU,IAAMuU,GAAsC,GAS5C,SAASC,GACP/S,EACAgT,EACA1a,EACAyF,GAEAa,IAAIqU,EAAYrN,GAAK7H,EAAS,kBAEzBkV,GAEHrN,GAAK7H,EAAS,iBADdkV,EAAY,KAIuB,IAAjCA,EAAUjS,QAAQhB,KACpBiT,EAAUlY,KAAKiF,GACfgT,EAAQ7a,KAAK4F,EAASzF,EAAGyF,ICnB7BgC,EAAEhG,GAAGgZ,SAAW,WACd,OAAO1b,KAAKqI,cAAMpH,EAAGyF,GACnBQ,IAAM2U,EAAQnT,EAAEhC,GAEhB2B,EAAKoT,YAAU9S,EAAkBgT,GAC3BE,EAAM9J,GAAGpJ,IACX+S,GAAS/S,EAAUgT,EAAS1a,EAAGyF,GAGjCmV,EAAMrR,KAAK7B,GAAUN,cAAMpH,EAAGyF,GAC5BgV,GAAS/S,EAAUgT,EAAS1a,EAAGyF,UCAvCgC,EAAEoT,YAAc,SAAUC,GACxBxU,IAAIyU,EAAWtT,EAAE,iBAEbsT,EAASxb,QACXwb,EAASzN,KAAK,uBAAuB,GAEhCnJ,EAAY2W,IACfC,EAASxF,IAAI,UAAWuF,KAGtB3W,EAAY2W,KACdA,EAAS,KAGXC,EAAWtT,EAAE,8BACVuT,SAASvc,SAASoJ,MAClB6R,SACAnE,IAAI,UAAWuF,IAGpBxU,IAAI2U,EAAQF,EAASzN,KAAK,mBAAqB,EAE/C,OAAOyN,EAASzN,KAAK,mBAAoB2N,GAAOlS,SAAS,sBCvB3DtB,EAAEyT,YAAc,SAAUC,mBAAQ,GAChClV,IAAM8U,EAAWtT,EAAE,iBAEnB,GAAKsT,EAASxb,OAAd,CAIA+G,IAAI2U,EAAQE,EAAQ,EAAIJ,EAASzN,KAAK,kBAE1B,EAAR2N,EACFF,EAASzN,KAAK,mBAAoB2N,GAIpCF,EACGzN,KAAK,iBAAkB,GACvB8N,YAAY,qBACZ9N,KAAK,uBAAuB,GAC5B0M,yBACKe,EAASzN,KAAK,wBAChByN,EAASzQ,aC5BjB7C,EAAE4T,WAAa,WACbpV,IAAMqV,EAAQ7T,EAAE,QAGV8T,EAAeD,EAAME,QACvBP,EAAQK,EAAMhO,KAAK,sBAAwB,EAE/CgO,EACGvS,SAAS,eACTyS,MAAMD,GACNjO,KAAK,sBAAuB2N,ICHjCxT,EAAEgU,aAAe,SAAUN,mBAAQ,GACjClV,IAAMqV,EAAQ7T,EAAE,QACZwT,EAAQE,EAAQ,EAAIG,EAAMhO,KAAK,qBAEvB,EAAR2N,EACFK,EAAMhO,KAAK,sBAAuB2N,GAIpCK,EAAMhO,KAAK,oBAAqB,GAAG8N,YAAY,eAAeI,MAAM,KCdtE/T,EAAEiU,SAAW,SAAUja,EAAgBka,kBAAQ,IAC7CrV,IAAIsV,EAAa,KAEjB,OAAO,yEACDxX,EAAOwX,KACTA,EAAQza,sBACNM,EAAGoB,MAAM9D,EAAMU,GACfmc,EAAQ,MACPD,MCNT1V,IAAM4V,GAA4B,GCTlC,SAASC,GACPlJ,EACAmJ,EACA/X,EACAgY,EACAC,IAGEA,EADGA,GACU,IAIJC,KAAOF,EAElB/V,IAAMkW,EAAmBvJ,WAAkBmJ,EAIrB,oBAAXK,QAETA,OAAOpY,GAAQ6H,QAAQsQ,EAAeF,GAGxChW,IAAMuL,EAAU/J,EAAEzD,GAGlBwN,EAAQ3F,QAAQsQ,EAAeF,GAS/BhW,IAMM8F,EAA2B,IAAI9K,YAAYkb,EANhB,CAC/B9d,SAAS,EACTC,YAAY,EACZC,OAAQ0d,IAMVlQ,EAAYG,QAAU+P,EAEtBzK,EAAQ,GAAGpF,cAAcL,GDnC3BtE,EAAE4U,KAAO,SAAU3W,GACjB,IAAKvB,EAAYuB,KAAUvB,EAAY0X,GAAKnW,IAC1C,OAAOmW,GAAKnW,GAGd,SAAS4W,IACP,OAAOlH,KAAKmH,MAA4B,OAArB,EAAInH,KAAKoH,WACzBrO,SAAS,IACTsO,UAAU,GAGfxW,IAAMoW,EACJ,IACAC,IACAA,IACA,IACAA,IACA,IACAA,IACA,IACAA,IACA,IACAA,IACAA,IACAA,IAMF,OAJKnY,EAAYuB,KACfmW,GAAKnW,GAAQ2W,GAGRA,GE1BTrT,EAAKyR,SAAW,SAAU/S,EAAmBgT,GACvCvW,EAAYuD,IAAavD,EAAYuW,GACvCjT,EAAEhJ,UAAUgc,YAIdD,GAAQ9S,GAAYgT,EACpBjT,EAAEC,GAAUN,cAAMpH,EAAGyF,UAAYgV,GAAS/S,EAAUgT,EAAS1a,EAAGyF,OCmFhE,SA/BIiX,GAgCFhV,EACAuF,kBAAmB,IAxBdlO,aAAmBmM,EAAO,GAAIyR,IAK7B5d,WAAe,SAKfA,eAAW,EAKXA,iBAAc,EAKdA,WAAQ,EAMdA,KAAKuS,SAAW7J,EAAEC,GAAU0B,QAE5B8B,EAAOnM,KAAKkO,QAASA,GAGrBhH,IAAM2W,EAAY7d,KAAKkO,QAAQ2P,UAC3B1Y,EAAS0Y,KACX7d,KAAKkO,QAAQ2P,UAAY,CACvBC,KAAMD,EACNE,GAAIF,IAIR7d,KAAKge,SCrIT9W,IAAM+W,GAAYvV,EAAEhJ,UACdwe,GAAUxV,EAAEpH,QD4EZsc,IC3EQlV,EAAE,QD2EiB,CAC/BmV,UAAW,EACX5E,OAAQ,EACRkF,aAAc,gBACdC,YAAa,2BACbC,cAAe,+BE3EjB,SAASC,GAAa5X,EAAsBC,GAC1CO,IAAMqX,EAAO7V,EAAEhC,GAAS6X,KAAK5X,GAE7B,OAAK4X,EAIE,IAAIC,SACT,iBACcD,+CAFT,GAHE,gBFgIDE,+BACNze,KAAK0e,MAAQpd,OAAOqd,iCAClBzX,IAAM0X,EAAiBtd,OAAO0V,YACxBhQ,EAAY4X,EAAiB5e,EAAK6e,YAAc,OAAS,KAGzDC,EAFa9e,EAAKkO,QAAQ2P,UAAwB7W,IACvCqP,KAAK0I,IAAIH,EAAiB5e,EAAK6e,aAI9CD,EAAiB5e,EAAK6e,aACtBD,GAAkB5e,EAAKkO,QAAQ+K,QAC/B6F,EAEA9e,EAAKgf,SAEJJ,EAAiB5e,EAAK6e,aAAeC,GACtCF,GAAkB5e,EAAKkO,QAAQ+K,SAE/BjZ,EAAKif,MAGPjf,EAAK6e,YAAcD,kBAQfM,sBAAavY,GACnBoW,GAAepW,EAAM,WAAY3G,KAAKuS,SAAUvS,oBAM1Cib,yBACa,YAAfjb,KAAKmf,QACPnf,KAAKmf,MAAQ,SACbnf,KAAKkf,aAAa,WAGD,cAAflf,KAAKmf,QACPnf,KAAKmf,MAAQ,WACbnf,KAAKkf,aAAa,2BAOfD,0BAEY,YAAfjf,KAAKmf,OACU,WAAfnf,KAAKmf,OACJnf,KAAKuS,SAASkF,SAASzX,KAAKkO,QAAQiQ,gBAKvCne,KAAKkf,aAAa,OAClBlf,KAAKmf,MAAQ,UACbnf,KAAKuS,SACF8J,YAAYrc,KAAKkO,QAAQmQ,eACzBrU,SAAShK,KAAKkO,QAAQkQ,aACtBnD,gCAAoBjb,EAAKib,iCAMvB+D,4BAEY,cAAfhf,KAAKmf,OACU,aAAfnf,KAAKmf,OACJnf,KAAKuS,SAASkF,SAASzX,KAAKkO,QAAQiQ,gBAKvCne,KAAKkf,aAAa,SAClBlf,KAAKmf,MAAQ,YACbnf,KAAKuS,SACF8J,YAAYrc,KAAKkO,QAAQkQ,aACzBpU,SAAShK,KAAKkO,QAAQmQ,eACtBpD,gCAAoBjb,EAAKib,iCAMvB+C,6BACDhe,KAAKof,WAITpf,KAAKof,UAAW,EAChBpf,KAAKmf,MAAQ,SACbnf,KAAKuS,SACFvI,SAAShK,KAAKkO,QAAQiQ,cACtB9B,YAAYrc,KAAKkO,QAAQkQ,aACzB/B,YAAYrc,KAAKkO,QAAQmQ,eAC5Bre,KAAK6e,YAAcvd,OAAO0V,YAE1BkH,GAAQjL,GAAG,2BAAgBjT,EAAKye,4BAM3BY,8BACArf,KAAKof,WAIVpf,KAAKof,UAAW,EAChBpf,KAAKuS,SACF8J,YAAYrc,KAAKkO,QAAQiQ,cACzB9B,YAAYrc,KAAKkO,QAAQkQ,aACzB/B,YAAYrc,KAAKkO,QAAQmQ,eAE5BH,GAAQlL,IAAI,2BAAgBhT,EAAKye,aACjCnd,OAAOge,qBAAqBtf,KAAK0e,sBAM5Ba,oBACL,OAAOvf,KAAKmf,OAIhBlV,EAAK0T,SAAWA,GG5QhBzW,IAAMsY,GAAa,gBAEnB9W,aACEuB,EAAKyR,aAAa8D,OAAe,WAC/B,IAAIvV,EAAK0T,SAAS3d,KAAMse,GAAate,KAAMwf,SC+D7C,SApCaC,GAqCX9W,EACAuF,kBAAmB,IA7BdlO,aAAmBmM,EAAO,GAAIyR,IAgCnC1W,IAAMwY,EAAc,QAAQ1f,KAAK2f,uBACjC3f,KAAK4f,UAAYF,EACjB1f,KAAK6f,cAAmBH,UACxB1f,KAAK8f,YAAiBJ,YACtB1f,KAAK+f,UAAeL,UAEpB1f,KAAKuS,SAAW7J,EAAEC,GAAU0B,QAE5B8B,EAAOnM,KAAKkO,QAASA,GAErBlO,KAAKggB,YAvDT9Y,IAAM0W,GAA2B,CAC/BqC,WAAW,gBA4DHD,qBAEN9Y,IAAMgU,EAAOlb,KAGbA,KAAKuS,SAASU,GAAG,YAAajT,iBAAoB,WAChDkH,IACMgZ,EADUxX,EAAE1I,MACI8H,SACPoT,EAAKiF,WAEb9X,cAAMlC,EAAGsC,GACVyX,EAAMnO,GAAGtJ,IACXyS,EAAKR,OAAOjS,OAMlBzI,KAAKuS,SAASU,GACZ,iBACSjT,KAAK2f,8BACd,WACEzY,IACMgZ,EADUxX,EAAE1I,MACIsV,YAAY4F,aAAkB7Q,QAEpD6Q,EAAKkF,MAAMF,mBASTG,gBAAOH,GACb,OAAOA,EAAMzI,SAASzX,KAAK6f,6BAMrBM,oBACN,OAAOngB,KAAKuS,SAASyC,aAAahV,8BAO5BsgB,iBACN7X,GAEA,OAAItD,EAASsD,GACJzI,KAAKmgB,WAAWhL,GAAG1M,GAGrBC,EAAED,GAAM4B,sBAQT6U,sBAAavY,EAAauZ,GAChCnD,GAAepW,EAAM3G,KAAK2f,eAAgBO,EAAOlgB,oBAQ3Cib,uBAAcsF,EAAcL,GAC9BlgB,KAAKqgB,OAAOH,IACdK,EAAS1F,WAAW,GAAG2F,OAAO,QAAQ7F,SAASE,WAAW,IAE1D7a,KAAKkf,aAAa,SAAUgB,KAE5BK,EAASC,OAAO,IAEhBxgB,KAAKkf,aAAa,SAAUgB,kBAQzBlQ,cACLvH,cAEMyX,EAAQlgB,KAAKsgB,QAAQ7X,GAE3B,IAAIzI,KAAKqgB,OAAOH,GAAhB,CAKIlgB,KAAKkO,QAAQ+R,WACfjgB,KAAKuS,SAASyC,aAAahV,oBAAsBqI,cAAMlC,EAAGO,GACxDQ,IAAMqL,EAAW7J,EAAEhC,GAEd6L,EAASR,GAAGmO,IACflgB,EAAKogB,MAAM7N,KAKjBrL,IAAMqZ,EAAWL,EAAMlL,aAAahV,gBAEpCugB,EACGC,OAAOD,EAAS,GAAGE,cACnBxF,gCAAoBjb,EAAKib,cAAcsF,EAAUL,KAEpDlgB,KAAKkf,aAAa,OAAQgB,GAE1BA,EAAMlW,SAAShK,KAAK6f,8BAOfO,eACL3X,cAEMyX,EAAQlgB,KAAKsgB,QAAQ7X,GAE3B,GAAKzI,KAAKqgB,OAAOH,GAAjB,CAIAhZ,IAAMqZ,EAAWL,EAAMlL,aAAahV,gBAEpCA,KAAKkf,aAAa,QAASgB,GAE3BA,EAAM7D,YAAYrc,KAAK6f,eAEvBU,EACG1F,WAAW,GACX2F,OAAOD,EAAS,GAAGE,cACnB9F,SACAE,WAAW,IACX2F,OAAO,IACPvF,gCAAoBjb,EAAKib,cAAcsF,EAAUL,oBAO/CxF,gBACLjS,GAEAvB,IAAMgZ,EAAQlgB,KAAKsgB,QAAQ7X,GAE3BzI,KAAKqgB,OAAOH,GAASlgB,KAAKogB,MAAMF,GAASlgB,KAAKgQ,KAAKkQ,iBAM9CQ,8BACL1gB,KAAKmgB,WAAW9X,cAAMlC,EAAGO,UAAY1G,EAAKgQ,KAAKtJ,mBAM1Cia,+BACL3gB,KAAKmgB,WAAW9X,cAAMlC,EAAGO,UAAY1G,EAAKogB,MAAM1Z,MChPpD,IAAMka,oJACMjB,wBACR,MAAO,eAFYF,IAMvBxV,EAAK2W,SAAWA,GCzBhB1Z,IAAMsY,GAAa,gBAEnB9W,aACEuB,EAAKyR,aAAa8D,OAAe,WAC/B,IAAIvV,EAAK2W,SAAS5gB,KAAMse,GAAate,KAAMwf,SCe/C,IAAMqB,oJACMlB,wBACR,MAAO,YAFSF,IAMpBxV,EAAK4W,MAAQA,GCzBb3Z,IAAMsY,GAAa,aAEnB9W,aACEuB,EAAKyR,aAAa8D,OAAe,WAC/B,IAAIvV,EAAK4W,MAAM7gB,KAAMse,GAAate,KAAMwf,SC2D1C,SApCIsB,GAqCFnY,GA5BM3I,YAAa0I,IAKb1I,aAAc0I,IAKd1I,iBAAoC0I,IAKpC1I,kBAAqC0I,IAKrC1I,iBAAa,EAKbA,iBAAc,EAKpBA,KAAKuS,SAAW7J,EAAEC,GAAU0B,QAC5BrK,KAAK+gB,oBAMAA,gBACL/gB,KAAKghB,OAAShhB,KAAKuS,SAAS/H,KAAK,YACjCxK,KAAKihB,QAAUjhB,KAAKuS,SAAS/H,KAAK,YAClCxK,KAAKkhB,WAAalhB,KAAKuS,SAASkF,SAAS,yBAEzCzX,KAAKmhB,mBACLnhB,KAAKohB,mBACLphB,KAAKqhB,iCAOCC,4BAAmBC,GACzB,MACE,IAAIA,wIAKCA,oBAODC,kCACNta,IAAMua,EAAWzhB,KAAK0hB,YAAY,GAC5BC,EAAc3hB,KAAK2hB,YACnBC,EAAe5hB,KAAKihB,QAAQzgB,OAElCihB,EAAShJ,QAAUkJ,IAAgBC,EACnCH,EAASI,gBAAkBF,GAAeA,IAAgBC,gBAMpDR,uCACAU,EAAmB,0BAEzB9hB,KAAKihB,QAAQ5Y,cAAMlC,EAAG4b,GACpB7a,IAAM8a,EAAOtZ,EAAEqZ,GAKf,GAFAC,EAAKxX,KAAK,6BAA6Be,SAElCvL,EAAKkhB,WAAV,CAKAha,IAAM+a,EAAYvZ,EAAE1I,EAAKshB,mBAAmB,OACzCY,UAAUF,GACVxX,KAAK,0BAGJwX,EAAKvK,SAASqK,KAChBG,EAAU,GAAGxJ,SAAU,EACvBzY,EAAK2hB,eAGP3hB,EAAKwhB,yBAGLS,EAAUhP,GAAG,oBACPgP,EAAU,GAAGxJ,SACfuJ,EAAKhY,SAAS8X,GACd9hB,EAAK2hB,gBAELK,EAAK3F,YAAYyF,GACjB9hB,EAAK2hB,eAGP3hB,EAAKwhB,2BAGPxhB,EAAKmiB,aAAeniB,EAAKmiB,aAAalQ,IAAIgQ,oBAOtCd,uCAENnhB,KAAKghB,OAAOxW,KAAK,6BAA6Be,SAEzCvL,KAAKkhB,aAIVlhB,KAAK0hB,YAAchZ,EAAE1I,KAAKshB,mBAAmB,OAC1CY,UAAUliB,KAAKghB,QACfxW,KAAK,0BACLyI,GAAG,oBACF/L,IAAMkb,EAAepiB,EAAK0hB,YAAY,GAAGjJ,QACzCzY,EAAK2hB,YAAcS,EAAepiB,EAAKihB,QAAQzgB,OAAS,EAExDR,EAAKmiB,aAAa9Z,cAAMlC,EAAGsb,GACzBA,EAAShJ,QAAU2J,IAGrBpiB,EAAKihB,QAAQ5Y,cAAMlC,EAAG4b,GACpBK,EACI1Z,EAAEqZ,GAAK/X,SAAS,2BAChBtB,EAAEqZ,GAAK1F,YAAY,8CAQvBgF,uCACAgB,EAAe,yBAErBriB,KAAKghB,OAAOxW,KAAK,MAAMnC,cAAMpH,EAAGqI,GAC9BpC,IAAMob,EAAe5Z,EAAEY,GAAImO,SAAS4K,GAEpCriB,EAAKihB,QAAQ5Y,cAAMlC,EAAG4b,GACpB7a,IAAMqb,EAAM7Z,EAAEqZ,GAAKvX,KAAK,MAAM2K,GAAGlU,GAEjCqhB,EACIC,EAAIvY,SAASqY,GACbE,EAAIlG,YAAYgG,QAM5Bnb,IAAMsb,GAAW,cAEjB9Z,aACEuB,EAAKyR,SAAS,cAAe,WAC3BxU,IAAMqL,EAAW7J,EAAE1I,MAEduS,EAAShE,KAAKiU,KACjBjQ,EAAShE,KAAKiU,GAAU,IAAI1B,GAAMvO,QAKxCtI,EAAKwY,aAAe,SAClB9Z,IAEkBvD,EAAYuD,GAAYD,EAAE,eAAiBA,EAAEC,IAErDN,cAAMlC,EAAGO,GACjBQ,IAAMqL,EAAW7J,EAAEhC,GACbuW,EAAW1K,EAAShE,KAAKiU,IAE3BvF,EACFA,EAAS8D,OAETxO,EAAShE,KAAKiU,GAAU,IAAI1B,GAAMvO,OC1MxCrL,IAAMwb,GAAa,uBACbC,GAAY,sBACZC,GAAW,mBACXC,GAAc,yBACdC,GAAc,iCAEhBC,GAAU,EAOd,SAASC,GAAQ/W,GACf,QACE8W,KAUyB,EATzB,CACE,YACA,UACA,YACA,QACA,YACA,WACA,aACA,cACApZ,QAAQsC,EAAMlB,OAQpB,SAASkY,GAAShX,GACG,eAAfA,EAAMlB,KAERgY,IAAW,GAEqD,EAAhE,CAAC,YAAa,WAAY,eAAepZ,QAAQsC,EAAMlB,OAGvD3I,WAAW,WACL2gB,MACFA,IAED,KClCP,SAASzI,GAAKrO,EAAciX,GAE1B,KAAIjX,aAAiB7K,YAA+B,IAAjB6K,EAAMlK,QAAzC,CAKAmF,IAAMic,EACkB,oBAAfC,YACPnX,aAAiBmX,YACjBnX,EAAM8W,QAAQviB,OACVyL,EAAM8W,QAAQ,GACb9W,EAEDoX,EAAcF,EAAcG,MAC5BC,EAAcJ,EAAcK,MAG5BvK,EAASiK,EAAQjK,SACjBuH,EAAS0C,EAAQO,cACjBhH,EAAQyG,EAAQQ,aAChBC,EACDN,EAAcpK,EAAOtC,KADpBgN,EAEDJ,EAActK,EAAOvC,IAEpBkN,EAAWvN,KAAKC,IACpBD,KAAKwN,IAAIxN,KAAKwN,IAAIrD,EAAQ,GAAKnK,KAAKwN,IAAIpH,EAAO,GAAI,IACnD,IAIIqH,EACJ,gBAA2BrH,EAAQ,EAAnBkH,UACDnD,EAAS,EAApBmD,qBAGNjb,EACE,8CACkBkb,eAAqBA,oBACtBA,EAAW,qBAAoBA,EAAW,0CAG1DrV,KAAK,yBAA0BuV,GAC/B5B,UAAUgB,GACVvI,SACAY,UAAUuI,IA0Cf,SAAS7L,KACP/Q,IAAMgc,EAAUxa,EAAE1I,MAElBkjB,EAAQlO,SAAS,qBAAqB3M,cAAMlC,EAAG4d,IAtCjD,SAAsBC,GACpB,GAAKA,EAAMxjB,SAAUwjB,EAAMzV,KAAK,wBAAhC,CAIAyV,EAAMzV,KAAK,wBAAwB,GAEnChH,IAAI0c,EAAc7hB,6BAAiB4hB,EAAMzY,UAAU,KAC7CuY,EAAYE,EAAMzV,KAAK,0BAE7ByV,EACGha,SAAS,yBACTuR,UAAUuI,EAAU5d,QAAQ,WAAY,gBACxC+U,yBACC5K,aAAa4T,GAEbD,EACGha,SAAS,wBACTuR,UAAUuI,EAAU5d,QAAQ,WAAY,gBAE3C+d,EAAc7hB,6BAAiB4hB,EAAMzY,UAAU,KAE/CnJ,sBACE4hB,EAAM/I,yBACJ5K,aAAa4T,GACbD,EAAMzY,YAEP,MAYL2Y,CAAaxb,EAAEqb,MAGjBb,EAAQlQ,IAAO2P,OAAaC,OAAYC,GAAe5K,IAOzD,SAASkM,GAAWlY,GAClB,GAAK+W,GAAQ/W,KAIbgX,GAAShX,GAGLA,EAAMhH,SAAWvF,UAArB,CAIAwH,IAAMuL,EAAU/J,EAAEuD,EAAMhH,QAGlBie,EAAUzQ,EAAQgF,SAAS,eAC7BhF,EACAA,EAAQ6C,QAAQ,gBAAgBjL,QAEpC,GAAK6Y,EAAQ1iB,SAKT0iB,EAAQ1b,KAAK,aAAgBpC,EAAY8d,EAAQ3E,KAAK,aAI1D,GAAmB,eAAftS,EAAMlB,KAAuB,CAC/BxD,IAAI6c,GAAS,EAGTvH,EAAQza,sBACVya,EAAQ,EACRvC,GAAKrO,EAAOiX,IACX,KAEGmB,aAEAxH,IACFxM,aAAawM,GACbA,EAAQ,EACRvC,GAAKrO,EAAOiX,IAGTkB,IACHA,GAAS,EACTnM,GAAKnX,KAAKoiB,KAcdA,EAAQjQ,GAAG,uBARL4J,IACFxM,aAAawM,GACbA,EAAQ,GAGVwH,MAGiCpR,GAAG,uBAAwBoR,QAE9D/J,GAAKrO,EAAOiX,GACZA,EAAQjQ,GAAM0P,OAAaC,OAAYC,GAAe5K,KAI1DvP,aACEuV,GAAUhL,GAAGyP,GAAYyB,IAAYlR,GAAG6P,GAAaG,MC7KvD/b,IAAMod,GAAgC,CACpCC,QAAQ,EACRC,gBAAgB,GAQlB,SAASC,GAAWxY,EAAcsC,kBAAyB,IACzDA,EAAOpC,EAAO,GAAImY,GAAa/V,GAE/BrH,IAAMwd,EAAQzY,EAAMhH,OACd0f,EAASjc,EAAEgc,GACXE,EAAY3Y,EAAMlB,KAClB7K,EAAQykB,EAAOzjB,MAGf2jB,EAAYF,EAAOpG,KAAK,SAAW,GACzC,MAGO,EAFL,CAAC,WAAY,SAAU,SAAU,QAAS,QAAS,SAAS5U,QAC1Dkb,IAFJ,CAQA3d,IAAM4d,EAAaH,EAAO7c,OAAO,mBAmCjC,GAhCkB,UAAd8c,GACFE,EAAW9a,SAAS,wBAGJ,SAAd4a,GACFE,EAAWzI,YAAY,wBAIP,SAAduI,GAAsC,UAAdA,IAC1B1kB,EACI4kB,EAAW9a,SAAS,4BACpB8a,EAAWzI,YAAY,6BAI7BqI,EAAMvK,SACF2K,EAAW9a,SAAS,2BACpB8a,EAAWzI,YAAY,2BAIV,UAAduI,GAAuC,SAAdA,GACzBrW,EAAKiW,iBACNE,EAAMK,WAENL,EAAMK,SAASC,MACXF,EAAWzI,YAAY,gCACvByI,EAAW9a,SAAS,iCAItB2a,EAAO5S,GAAG,YAAa,CAGzB7K,IAAM+d,EAAa/kB,EACfglB,GAAgB,EAEsB,KAAtCD,EAAW/e,QAAQ,UAAW,MAChCye,EAAOzjB,IAAI,IAAM+jB,GACjBC,GAAgB,GAIlBP,EAAOQ,YAAY,IACnBje,IAAMsZ,EAASmE,EAAOQ,cAChB1E,EAAeiE,EAAMjE,aAERD,EAAfC,GACFkE,EAAOQ,YAAY1E,GAIjByE,GACFP,EAAOzjB,IAAI+jB,GAKX1W,EAAKgW,QACPO,EAAWta,KAAK,2BAA2Be,SAG7CrE,IAAMke,EAAYT,EAAOpG,KAAK,aAC1B6G,KACE7W,EAAKgW,QAAUhW,EAAKiW,iBACtB9b,EACE,8FAC4D0c,EAC1D,UACFnJ,SAAS6I,GAGbA,EACGta,KAAK,mCACL6a,KAAKnlB,EAAMM,OAAO4O,cAKrB0V,EAAWta,KAAK,0BAA0BhK,QAC1CskB,EAAWta,KAAK,yBAAyBhK,QACzC4kB,IAEAN,EAAW9a,SAAS,8BC7GxB,SAASsb,GAAiBC,GACxBre,IAAMqH,EAAOgX,EAAQhX,OAEfiX,EAASjX,EAAKkX,eACdC,EAAQnX,EAAKoX,cACbC,EAASrX,EAAKsX,eACdlB,EAASpW,EAAKuX,eACdC,EAAMxX,EAAKyX,YACX1P,EAAM/H,EAAK0X,YACXC,EAAa3X,EAAK4X,iBAClBC,EAAa7X,EAAK8X,iBAClBC,EAAa/X,EAAKgY,mBAClBrmB,EAAQykB,EAAOzjB,MACfslB,GAAYtmB,EAAQ6lB,IAAQzP,EAAMyP,GAAQ,IAEhDL,EAAMjJ,MAAS+J,OACfhB,EAAO/I,MAAS,IAAM+J,OAElBN,IACFR,EAAMlP,IAAI,gBAAiB,OAC3BgP,EAAOhP,IAAI,eAAgB,QAG7BoP,EAAOpP,IAAI,OAAWgQ,OAElBJ,GACFE,EAAWjB,KAAKnlB,GAGN,GAAZsmB,EACIjB,EAAQvb,SAAS,oBACjBub,EAAQlJ,YAAY,oBAO1B,SAASkI,GAAOgB,GACdre,IAAMse,EAAS9c,EAAE,yCACXgd,EAAQhd,EAAE,wCACVkd,EAASld,EAAE,yCACXic,EAASY,EAAQ/a,KAAK,uBACtB0b,EAAavB,EAAO,GAAGxK,SACvBiM,EAAab,EAAQ9N,SAAS,wBAGpCyO,EACIX,EAAQvb,SAAS,wBACjBub,EAAQlJ,YAAY,wBAGxBkJ,EAAQ/a,KAAK,sBAAsBe,SACnCga,EAAQ/a,KAAK,qBAAqBe,SAClCga,EAAQ/a,KAAK,sBAAsBe,SACnCga,EAAQkB,OAAOjB,GAAQiB,OAAOf,GAAOe,OAAOb,GAG5Cre,IAAI+e,EAAa5d,IACb0d,IACFE,EAAa5d,EAAE,iBACfkd,EAAOvO,QAAQoP,OAAOH,IAGxBf,EAAQhX,KAAK,iBAAkBiX,GAC/BD,EAAQhX,KAAK,gBAAiBmX,GAC9BH,EAAQhX,KAAK,iBAAkBqX,GAC/BL,EAAQhX,KAAK,iBAAkBoW,GAC/BY,EAAQhX,KAAK,cAAeoW,EAAOpG,KAAK,QACxCgH,EAAQhX,KAAK,cAAeoW,EAAOpG,KAAK,QACxCgH,EAAQhX,KAAK,mBAAoB2X,GACjCX,EAAQhX,KAAK,mBAAoB6X,GACjCb,EAAQhX,KAAK,qBAAsB+X,GAGnChB,GAAiBC,GDsCnB7c,aAEEuV,GAAUhL,GACR,mBACA,wBACA,CAAEK,YAAY,GACdmR,IAIFxG,GAAUhL,GACR,QACA,kDACA,WACEvK,EAAE1I,MACCsV,QAAQ,mBACRtL,SAAS,2BACTQ,KAAK,yBAAyB,GAC9Bkc,UAKPzI,GAAUhL,GACR,QACA,iDACA,WACEvK,EAAE1I,MACCsV,QAAQ,mBACR+G,YAAY,2BACZ7R,KAAK,yBACLtJ,IAAI,MAOX+I,EAAKyR,SAAS,kBAAmB,WAC/BhT,EAAE1I,MAAMwK,KAAK,yBAAyBsC,QAAQ,QAAS,CACrD0X,gBAAgB,QAKtBva,EAAK0c,iBAAmB,SACtBhe,IAEkBvD,EAAYuD,GAAYD,EAAE,mBAAqBA,EAAEC,IAEzDN,cAAMlC,EAAGO,GACjBgC,EAAEhC,GAAS8D,KAAK,yBAAyBsC,QAAQ,QAAS,CACxDyX,QAAQ,OCvFdrd,IAAM0f,GAAgB,mCAEtBle,aAEEuV,GAAUhL,GAAG,eAAgB2T,GAAe,WAG1CtB,GAFgB5c,EAAE1I,MAAM8H,YAM1BmW,GAAUhL,GAAGyP,GAAYkE,GAAe,SAAU3a,GAC3C+W,GAAQ/W,KAIbgX,GAAShX,GAEJjM,KAA0Bma,UAIfzR,EAAE1I,MAAM8H,SAEhBkC,SAAS,wBAInBiU,GAAUhL,GAAG2P,GAAUgE,GAAe,SAAU3a,GACzC+W,GAAQ/W,KAIRjM,KAA0Bma,UAIfzR,EAAE1I,MAAM8H,SAEhBuU,YAAY,wBAGtB4B,GAAUhL,GAAG6P,GAAa8D,GAAe3D,IAKzChZ,EAAKyR,SAAS,eAAgB,WAC5B6I,GAAO7b,EAAE1I,WAIbiK,EAAK4c,cAAgB,SACnBle,IAEkBvD,EAAYuD,GAAYD,EAAE,gBAAkBA,EAAEC,IAEtDN,cAAMlC,EAAGO,GACjB6d,GAAO7b,EAAEhC,OChGX,SA/BIogB,GAgCFne,EACAuF,6BAAmB,IAxBdlO,aAAmBmM,EAAO,GAAIyR,IAK7B5d,WAAe,SAqBrBA,KAAKuS,SAAW7J,EAAEC,GAAU0B,QAE5B8B,EAAOnM,KAAKkO,QAASA,GAErBlO,KAAK+mB,KAAO/mB,KAAKuS,SAAS/H,KAAK,aAC/BxK,KAAKgnB,MAAQhnB,KAAKuS,SAAS/H,KAAK,kBAChCxK,KAAKinB,UAAYjnB,KAAKgnB,MAAMxc,KAAK,aAEJ,UAAzBxK,KAAKkO,QAAQpB,UACf9M,KAAK+mB,KAAK9T,GAAG,0CAA+BjT,EAAKgQ,SACjDhQ,KAAKuS,SAASU,GAAG,+BAAoBjT,EAAKogB,WAGf,UAAzBpgB,KAAKkO,QAAQpB,SACf9M,KAAK+mB,KAAK9T,GAAGyP,qBAAkB1iB,EAAKgQ,SAItCiO,GAAUhL,GAAGyP,YAAazW,GACpBvD,EAAEuD,EAAMhH,QAAuBqQ,QAAQ,qBAAqB9U,QAIhER,EAAKogB,UA9DXlZ,IAAM0W,GAA2B,CAC/B9Q,QAAS,sBAqEDoS,sBAAavY,GACnBoW,GAAepW,EAAM,MAAO3G,KAAKuS,SAAUvS,oBAMrCqgB,kBACN,MAAsB,YAAfrgB,KAAKmf,OAAsC,WAAfnf,KAAKmf,oBAMnCnP,2BACDhQ,KAAKqgB,WAKTrgB,KAAKinB,UAAU5e,cAAMf,EAAO4f,GAC1BhgB,IAAM0V,EAAW,IAAM5c,EAAKinB,UAAUzmB,OAAS8G,QAE/C4f,EAAIpS,MAAMqS,gBAAkBvK,EAC5BsK,EAAIpS,MAAMsS,sBAAwBxK,IAGpC5c,KAAKgnB,MAAMxQ,IAAI,SAAU,QAAQxM,SAAS,sBAGtChK,KAAK+mB,KAAKvc,KAAK,oBAAoBhK,QACrCR,KAAK+mB,KAAK/c,SAAS,mBAGrBhK,KAAKmf,MAAQ,UACbnf,KAAKkf,aAAa,QAGlBlf,KAAKinB,UAAU5c,QAAQ4Q,yBACjBjb,EAAK+mB,KAAKtP,SAAS,qBACrBzX,EAAKmf,MAAQ,SACbnf,EAAKkf,aAAa,4BAQjBkB,4BACApgB,KAAKqgB,WAKVrgB,KAAKinB,UAAU5e,cAAMf,EAAO4f,GAC1BhgB,IAAM0V,EAAW,GAAKtV,OAEtB4f,EAAIpS,MAAMqS,gBAAkBvK,EAC5BsK,EAAIpS,MAAMsS,sBAAwBxK,IAGpC5c,KAAKgnB,MAAM3K,YAAY,sBACvBrc,KAAK+mB,KAAK1K,YAAY,mBACtBrc,KAAKmf,MAAQ,UACbnf,KAAKkf,aAAa,SAGlBlf,KAAKinB,UAAUtO,OAAOsC,yBAChBjb,EAAK+mB,KAAKtP,SAAS,qBAIvBzX,EAAKmf,MAAQ,SACbnf,EAAKkf,aAAa,UAClBlf,EAAKgnB,MAAMxQ,IAAI,SAAU,qBAOtBkE,kBACL1a,KAAKqgB,SAAWrgB,KAAKogB,QAAUpgB,KAAKgQ,qBAM/BsK,gBACLta,KAAKuS,SAAS8J,YAAY,+BAMrBpE,gBACLjY,KAAKuS,SAASvI,SAAS,+BAMlBuV,oBACL,OAAOvf,KAAKmf,OAIhBlV,EAAK6c,IAAMA,GCjOX5f,IAAMsY,GAAa,WAEnB9W,aAIEuV,GAAUhL,GACR,qCACIuM,OACJ,WACE,IAAIvV,EAAK6c,IACP9mB,KACAse,GAAate,KAAqBwf,SCuIxC,SA7DI6H,GA8DF1e,EACAuF,6BAAmB,IAtDdlO,cAAe0I,IAKf1I,aAAmBmM,EAAO,GAAIyR,IAK7B5d,UAAO,EAKPA,eAAgB0I,IAKhB1I,WAAY0I,IAKZ1I,YAAa0I,IAKb1I,mBAAgB,EAKhBA,kBAAe,GAKfA,mBAAgB,GAUhBA,WAAe,SAMrBA,KAAKsnB,QAAU5e,EAAEC,GAAU0B,QAC3BrK,KAAKsnB,QAAQrP,OAEb9L,EAAOnM,KAAKkO,QAASA,GAGrBlO,KAAKunB,SAAW7e,EAAE4U,OAGlBtd,KAAKwnB,eAGLvJ,GAAUhL,GAAG,4BAAqBhH,GAChC/E,IAAMuL,EAAU/J,EAAEuD,EAAMhH,SAGtBjF,EAAKqgB,UACJ5N,EAAQV,GAAG/R,EAAKuS,WAChBrI,EAASlK,EAAKuS,SAAS,GAAIE,EAAQ,KAEpCzS,EAAKogB,UA1FblZ,IAAM0W,GAA2B,CAC/BzW,SAAU,OACVsgB,OAAQ,iBAgGAC,wBACNxgB,IAiBIygB,EACAC,EAlBEC,EAAe3J,GAAQsC,SAGvBsH,EAAgB9nB,KAAKuS,SAASiO,SAG9BuH,EAAa/nB,KAAKgoB,OAAO3d,QACzB4d,EAAaF,EAAWvH,SACxB0H,EAAaC,SAASJ,EAAWvR,IAAI,eAGrC4R,EAAYpoB,KAAKuS,SAASmR,aAAe,IAC3C2E,EAAaJ,EAAajoB,KAAKsoB,KAAoB,EAAbJ,EAGpCK,EAAavoB,KAAKuS,SAAS,GAAG3K,wBAAwB8O,IAK5D,GAA8B,WAA1B1W,KAAKkO,QAAQ/G,SACfygB,EAAgBE,EAChBH,EAAmB,WACd,GAA8B,QAA1B3nB,KAAKkO,QAAQ/G,SACtBygB,GAAiBS,EAAa,EAC9BV,EAAmB,WACd,CAELzgB,IAAMshB,EAAgBX,EAAsC,EAAvB7nB,KAAKkO,QAAQuZ,OACjCe,EAAbH,IACFA,EAAaG,GAIfZ,IACEM,EACAloB,KAAKyoB,cAAgBR,GACpBA,EAAaH,GAAiB,GAGjC5gB,IAAMwhB,IACJR,GACCloB,KAAKsoB,KAAO,GAAKL,GACjBA,EAAaH,GAAiB,GAE7BF,EAAgBc,IAClBd,EAAgBc,GAIlBxhB,IAAMyhB,EAAUJ,EAAaX,EACzBe,EAAU3oB,KAAKkO,QAAQuZ,OAEzBG,IAAkBW,EAAavoB,KAAKkO,QAAQuZ,QACnCkB,EAAUN,EAAaroB,KAAKkO,QAAQuZ,OAAUI,IAEvDD,IACEW,EACAF,EACAroB,KAAKkO,QAAQuZ,OACbI,IAKJF,EACE3nB,KAAKyoB,cAAgBR,EAAaA,EAAa,EAAIC,OAKvDloB,KAAKuS,SAASmR,WAAW0E,GACzBpoB,KAAK4oB,MACFlF,WAAW0E,GACX5H,OAAO6H,GACP7R,IAAI,CACHqS,aAAcjB,EAAgB,KAC9BkB,mBAAoB,UAAYnB,EAAmB,qBAOjDtH,kBACN,MAAsB,YAAfrgB,KAAKmf,OAAsC,WAAfnf,KAAKmf,oBAMnCqI,mCACDxnB,KAAKqgB,UACPrgB,KAAKogB,QAGPpgB,KAAK+oB,cAAgB/oB,KAAKsnB,QAAQpmB,MAUlCgG,IAAM8hB,EAA6B,GACnChpB,KAAKgoB,OAAStf,IAGd1I,KAAKsnB,QAAQ9c,KAAK,UAAUnC,cAAMf,EAAOkC,GACvCtC,IAAMme,EAAO7b,EAAOyf,aAAe,GAC7B/oB,EAAQsJ,EAAOtJ,MACfia,EAAW3Q,EAAO2Q,SAClB3B,EAAWxY,EAAK+oB,gBAAkB7oB,EAExC8oB,EAAUtlB,KAAK,OACbxD,OACAmlB,WACAlL,WACA3B,QACAlR,IAGEkR,IACFxY,EAAKkpB,aAAe7D,EACpBrlB,EAAKyoB,cAAgBnhB,GAGvBtH,EAAKgoB,OAAShoB,EAAKgoB,OAAO/V,IACxB,kDACGkI,EAAW,YAAc,KACzB3B,EAAW,YAAc,IAC1B,IAAI6M,cAIVrlB,KAAKmpB,UAAYzgB,wCACuB1I,6BAGxCA,KAAKuS,SAAW7J,EACd,gDAAgD1I,KAAKkO,6BACzClO,KAAKsnB,QAAQ/I,KAAK,kBACrBve,0BAERsa,OACAmM,OAAOzmB,KAAKmpB,WAEfnpB,KAAK4oB,MAAQlgB,EAAE,wCACZuT,SAASjc,KAAKuS,UACdkU,OAAOzmB,KAAKgoB,QAEftf,MAAM1I,eAAiBuL,SACvBvL,KAAKsnB,QAAQ8B,MAAMppB,KAAKuS,UAGxBvS,KAAKsoB,KAAOH,SAASnoB,KAAKsnB,QAAQ/I,KAAK,SAAW,KAE9Cve,KAAKsoB,MAAQ,IACftoB,KAAKsoB,KAAOtoB,KAAKgoB,OAAOxnB,OAER,EAAZR,KAAKsoB,OACPtoB,KAAKsoB,KAAO,IAMhBphB,IAAMgU,EAAOlb,KACbA,KAAKgoB,OAAO/U,GAAG,QAAS,WACtB,GAAmB,YAAfiI,EAAKiE,MAAT,CAIAjY,IAAMgZ,EAAQxX,EAAE1I,MACVsH,EAAQ4Y,EAAM5Y,QACdiH,EAAOya,EAAU1hB,GAEnBiH,EAAK4L,WAITe,EAAKiO,UAAU9D,KAAK9W,EAAK8W,MACzBnK,EAAKoM,QAAQpmB,IAAIqN,EAAKrO,OACtBgb,EAAK8M,OAAOxO,WAAW,YACvB0G,EAAM3B,KAAK,WAAY,IACvBrD,EAAKuN,cAAgBla,EAAKjH,MAC1B4T,EAAK6N,cAAgBxa,EAAKrO,MAC1Bgb,EAAKgO,aAAe3a,EAAK8W,KACzBnK,EAAKoM,QAAQxa,QAAQ,UACrBoO,EAAKkF,YAIPpgB,KAAKuS,SAASU,GAAG,iBAAUhH,GACzB/E,IAAMuL,EAAU/J,EAAEuD,EAAMhH,QAItBwN,EAAQV,GAAG,sBACXU,EAAQV,GAAG,2BAKb/R,EAAK0a,yBAODO,yBACNjb,KAAKuS,SAAS8J,YAAY,uBAEP,YAAfrc,KAAKmf,QACPnf,KAAKmf,MAAQ,SACbnf,KAAKkf,aAAa,UAClBlf,KAAK4oB,MAAMpS,IAAI,aAAc,SAGZ,YAAfxW,KAAKmf,QACPnf,KAAKmf,MAAQ,SACbnf,KAAKkf,aAAa,UAGlBlf,KAAKuS,SAASmR,WAAW,IACzB1jB,KAAK4oB,MAAMpS,IAAI,CACbqS,aAAc,GACdrI,OAAQ,GACR/D,MAAO,oBASLyC,sBAAavY,GACnBoW,GAAepW,EAAM,SAAU3G,KAAKsnB,QAAStnB,oBAMxC0a,kBACL1a,KAAKqgB,SAAWrgB,KAAKogB,QAAUpgB,KAAKgQ,qBAM/BA,2BACDhQ,KAAKqgB,WAITrgB,KAAKmf,MAAQ,UACbnf,KAAKkf,aAAa,QAClBlf,KAAK0nB,eACL1nB,KAAKuS,SAASvI,SAAS,oBACvBhK,KAAK4oB,MAAM3N,gCAAoBjb,EAAKib,iCAM/BmF,4BACApgB,KAAKqgB,WAIVrgB,KAAKmf,MAAQ,UACbnf,KAAKkf,aAAa,SAClBlf,KAAK4oB,MAAMpS,IAAI,aAAc,IAC7BxW,KAAKuS,SACF8J,YAAY,oBACZrS,SAAS,uBACZhK,KAAK4oB,MAAM3N,gCAAoBjb,EAAKib,iCAM/BsE,oBACL,OAAOvf,KAAKmf,OAIhBlV,EAAKod,OAASA,GCvddngB,IAAMsY,GAAa,cAEnB9W,aACEuB,EAAKyR,aAAa8D,OAAe,WAC/B,IAAIvV,EAAKod,OAAOrnB,KAAMse,GAAate,KAAMwf,SCL7C9W,aAEEuB,EAAKyR,SAAS,2BAA4B,WACxC,IAAIzR,EAAK0T,SAAS3d,QAIpBiK,EAAKyR,SAAS,mCAAoC,WAChD,IAAIzR,EAAK0T,SAAS3d,KAAM,CACtBoe,YAAa,+BACbC,cAAe,uCC8EnB,SA1BIgL,GA2BF1gB,EACAuF,6BAAmB,IAnBdlO,aAAmBmM,EAAO,GAAIyR,IAK9B5d,kBAAe,EAgBpBA,KAAKuS,SAAW7J,EAAEC,GAAU0B,QAE5B8B,EAAOnM,KAAKkO,QAASA,GAErBlO,KAAKspB,MAAQtpB,KAAKuS,SAASyC,SAAS,KACpChV,KAAKupB,WAAa7gB,EAAE,0CAA0CuT,SAC5Djc,KAAKuS,UAIPrL,IAAMsiB,EAAOloB,OAAO6N,SAASqa,KACzBA,GACFxpB,KAAKspB,MAAMjhB,cAAMf,EAAOmiB,GACtB,OAAI/gB,EAAE+gB,GAAKlL,KAAK,UAAYiL,IAC1BxpB,EAAK0pB,YAAcpiB,GACZ,MAQa,IAAtBtH,KAAK0pB,aACP1pB,KAAKspB,MAAMjhB,cAAMf,EAAOmiB,GACtB,OAAI/gB,EAAE+gB,GAAKhS,SAAS,qBAClBzX,EAAK0pB,YAAcpiB,GACZ,KAQTtH,KAAKspB,MAAM9oB,SAAgC,IAAtBR,KAAK0pB,cAC5B1pB,KAAK0pB,YAAc,GAIrB1pB,KAAK2pB,YAGLzL,GAAQjL,GACN,SACAvK,EAAEiU,2BAAe3c,EAAK4pB,wBAAwB,MAIhD5pB,KAAKspB,MAAMjhB,cAAMlC,EAAGsjB,GAClBzpB,EAAK6pB,aAAaJ,KArFxBviB,IAAM0W,GAA2B,CAC/B9Q,QAAS,QACTgd,MAAM,gBA2FE5D,oBAAW6D,GACjB,YAAiCjnB,IAA1BinB,EAAKxL,KAAK,0BAOXsL,sBAAaJ,YAIbO,IAEJ,GAAIhqB,EAAKkmB,WAAW6D,GAClB,OAAO,EAGT/pB,EAAK0pB,YAAc1pB,EAAKspB,MAAMhiB,MAAMmiB,GACpCzpB,EAAK2pB,uBAVDI,EAAOrhB,EAAE+gB,GAcfM,EAAK9W,GAAG,QAAS+W,GAGY,UAAzBhqB,KAAKkO,QAAQpB,SACfid,EAAK9W,GAAG,aAAc+W,GAIxBD,EAAK9W,GAAG,mBACN,GAA+C,KAA1C8W,EAAKxL,KAAK,SAAW,IAAI5U,QAAQ,KACpC,OAAO,kBAWLuV,sBAAavY,EAAa4L,EAAc2K,kBAAa,IAC3DH,GAAepW,EAAM,MAAO4L,EAAUvS,KAAMkd,iBAMtCyM,gCACN3pB,KAAKspB,MAAMjhB,cAAMf,EAAOmiB,GACtBviB,IAAM6iB,EAAOrhB,EAAE+gB,GACTQ,EAAWF,EAAKxL,KAAK,SAAW,GAGlCjX,IAAUtH,EAAK0pB,aAAgB1pB,EAAKkmB,WAAW6D,IAcjDA,EAAK1N,YAAY,mBACjB3T,EAAEuhB,GAAUhS,SAdP8R,EAAKtS,SAAS,qBACjBzX,EAAKkf,aAAa,SAAUlf,EAAKuS,SAAU,CACzCjL,MAAOtH,EAAK0pB,YACZ7d,GAAIoe,EAASC,OAAO,KAEtBlqB,EAAKkf,aAAa,OAAQ6K,GAE1BA,EAAK/f,SAAS,oBAGhBtB,EAAEuhB,GAAU3P,OACZta,EAAK4pB,wCAWHA,gCAEN,IAA0B,IAAtB5pB,KAAK0pB,YAAT,CASAxiB,IAAMijB,EAAanqB,KAAKspB,MAAMnU,GAAGnV,KAAK0pB,aAEtC,IAAI1pB,KAAKkmB,WAAWiE,GAApB,CAIAjjB,IAAMkjB,EAAkBD,EAAWlR,SAEnCjZ,KAAKupB,WAAW/S,IAAI,CAClBG,KACEyT,EAAgBzT,KAChB3W,KAAKuS,SAAS,GAAG8X,WACjBrqB,KAAKuS,SAAS,GAAG3K,wBAAwB+O,UAE3C8F,MAAU0N,EAAWzG,0BAtBrB1jB,KAAKupB,WAAW/S,IAAI,CAClBG,KAAM,EACN8F,MAAO,kBA2BN6N,iBACqB,IAAtBtqB,KAAK0pB,cAIL1pB,KAAKspB,MAAM9oB,OAASR,KAAK0pB,YAAc,EACzC1pB,KAAK0pB,cACI1pB,KAAKkO,QAAQ4b,OACtB9pB,KAAK0pB,YAAc,GAGrB1pB,KAAK2pB,2BAMAtiB,iBACqB,IAAtBrH,KAAK0pB,cAIc,EAAnB1pB,KAAK0pB,YACP1pB,KAAK0pB,cACI1pB,KAAKkO,QAAQ4b,OACtB9pB,KAAK0pB,YAAc1pB,KAAKspB,MAAM9oB,OAAS,GAGzCR,KAAK2pB,2BAOArP,cAAKhT,eACgB,IAAtBtH,KAAK0pB,cAILvkB,EAASmC,GACXtH,KAAK0pB,YAAcpiB,EAEnBtH,KAAKspB,MAAMjhB,cAAMpH,EAAGwoB,GAClB,GAAIA,EAAI5d,KAAOvE,EAEb,OADAtH,EAAK0pB,aACE,IAKb1pB,KAAK2pB,2BAOAnC,mCACC+C,EAAWvqB,KAAKspB,MAChBkB,EAAWxqB,KAAKuS,SAASyC,SAAS,KAClCyV,EAAiBF,EAAShgB,MAC1BmgB,EAAiBF,EAASjgB,MAEhC,IAAKigB,EAAShqB,OAKZ,OAJAR,KAAK0pB,aAAe,EACpB1pB,KAAKspB,MAAQkB,OACbxqB,KAAK4pB,uBAMPY,EAASniB,cAAMf,EAAOmiB,GAEhBgB,EAAe9gB,QAAQ8f,GAAO,IAChCzpB,EAAK6pB,aAAaJ,IAEQ,IAAtBzpB,EAAK0pB,YACP1pB,EAAK0pB,YAAc,EACVpiB,GAAStH,EAAK0pB,aACvB1pB,EAAK0pB,iBAMXa,EAASliB,cAAMf,EAAOmiB,GAEhBiB,EAAe/gB,QAAQ8f,GAAO,IAC5BniB,EAAQtH,EAAK0pB,YACf1pB,EAAK0pB,cACIpiB,IAAUtH,EAAK0pB,cACxB1pB,EAAK0pB,YAAc,MAKzB1pB,KAAKspB,MAAQkB,EAEbxqB,KAAK2pB,aAIT1f,EAAKof,IAAMA,GC7WXniB,IAAMsY,GAAa,WAEnB9W,aACEuB,EAAKyR,aAAa8D,OAAe,WAC/B,IAAIvV,EAAKof,IAAIrpB,KAAMse,GAAate,KAAMwf,SCsFxC,SA1BImL,GA2BFhiB,EACAuF,6BAAmB,IAnBdlO,aAAmBmM,EAAO,GAAIyR,IAK7B5d,cAAU,EAgBhBA,KAAKuS,SAAW7J,EAAEC,GAAU0B,QAE5B8B,EAAOnM,KAAKkO,QAASA,GAErBlO,KAAKmH,SAAWnH,KAAKuS,SAASkF,SAAS,qBACnC,QACA,OAEAzX,KAAKuS,SAASkF,SAAS,qBACzBzX,KAAKmf,MAAQ,SACJnf,KAAKuS,SAASkF,SAAS,qBAEvBzX,KAAK4qB,YADd5qB,KAAKmf,MAAQ,SAIbnf,KAAKmf,MAAQ,SAIfjB,GAAQjL,GACN,SACAvK,EAAEiU,oBACI3c,EAAK4qB,aAGH5qB,EAAK6qB,UAAY7qB,EAAKkO,QAAQ2c,UAChCniB,EAAEyT,cACFnc,EAAK6qB,SAAU,EACfniB,EAAEgU,gBAIC1c,EAAKuS,SAASkF,SAAS,uBAC1BzX,EAAKmf,MAAQ,WAELnf,EAAK6qB,SAA0B,WAAf7qB,EAAKmf,QAE3Bnf,EAAKuS,SAASkF,SAAS,qBACzB/O,EAAEoT,cACF9b,EAAK6qB,SAAU,EACfniB,EAAE4T,aAEF5T,EAAE,iBAAiBwK,IAAI,0BAAelT,EAAKogB,WAE3CpgB,EAAKmf,MAAQ,WAGhB,MAILnf,KAAKuS,SAAS/H,KAAK,uBAAuBnC,cAAMlC,EAAGia,GACjD1X,EAAE0X,GAAOnN,GAAG,0BAAejT,EAAKogB,YAGlCpgB,KAAK8qB,eA1FT5jB,IAAM0W,GAA2B,CAC/BiN,SAAS,EACTE,OAAO,gBA8FCH,qBACN,OAA0B,MAAnB1M,GAAQzB,sBAMTqO,wBAEN5jB,IAGI8jB,EACA3H,EACAE,EACA0H,EANE/P,EAAOlb,KAOTkrB,EAAwC,KACxCC,GAAe,EACb5O,EAAQ7T,EAAE,QAGV0iB,EAAiB,GAEvB,SAASC,EAAYC,GACnBpkB,IACMqkB,EAAe,cAClB,GAF8C,UAAlBrQ,EAAK/T,UAAwB,EAAI,GAEhCmkB,uBAIhCpQ,EAAK3I,SAASiE,IACZ,wBACc+U,wCAIlB,SAASC,IACPtQ,EAAK3I,SAAS,GAAGuC,MAAMyG,UAAY,GACnCL,EAAK3I,SAAS,GAAGuC,MAAM0G,gBAAkB,GACzCN,EAAK3I,SAAS,GAAGuC,MAAM+F,WAAa,GACpCK,EAAK3I,SAAS,GAAGuC,MAAM2W,iBAAmB,GAG5C,SAASC,IACP,OAAOxQ,EAAK3I,SAASkK,QAAU,GAGjC,SAASkP,EAAcC,GACrB,OAAOvV,KAAK0P,IACV1P,KAAKC,IACS,YAAZ4U,EACID,EAAcW,EACdF,IAAqBT,EAAcW,EACvC,GAEFF,KAIJ,SAASG,EAAe5f,GACtB,GAAIif,EAAS,CACX3jB,IAAIukB,EAAU7f,EAAqB8f,eAAe,GAAGzI,MAC/B,UAAlBpI,EAAK/T,WACP2kB,EAASvP,EAAME,QAAUqP,GAG3B5kB,IAAM8kB,EAAiBL,EAAcG,GAAUJ,IAE/CP,GAAe,EACfjkB,IAAM+kB,EAAef,EACrBA,EAAU,KAEW,YAAjBe,EACED,EAAiB,KACnBR,IACAtQ,EAAKlL,QAELwb,IAGmB,IAAjBQ,GACFR,IACAtQ,EAAKkF,SAELoL,IAIJ9iB,EAAEgU,oBAEFyO,GAAe,EAGjB5O,EAAMvJ,IAAI,CAERkZ,UAAWC,EACXC,SAAUP,EAEVQ,YAAaF,IAIjB,SAASA,EAAgBlgB,GACvB1E,IAAIukB,EAAU7f,EAAqB8W,QAAQ,GAAGO,MACxB,UAAlBpI,EAAK/T,WACP2kB,EAASvP,EAAME,QAAUqP,GAG3B5kB,IAAMolB,EAAUrgB,EAAqB8W,QAAQ,GAAGS,MAEhD,GAAI0H,EACFG,EAAYM,EAAcG,SACrB,GAAIX,EAAc,CACvBjkB,IAAMqlB,EAAQlW,KAAK0I,IAAI+M,EAASzI,GAC1BmJ,EAAQnW,KAAK0I,IAAIuN,EAAS/I,GACd,EAEdgJ,GAAqBC,GAFP,GAGhBvB,EAAca,EACdZ,EAAyB,WAAfhQ,EAAKiE,MAAqB,UAAY,UAChDzW,EAAE4T,aACF+O,EAAYM,EAAcG,KACjBS,GAPO,GAAA,EAOeC,GAC/BX,KAKN,SAASY,EAAiBxgB,GACxBoX,EAAepX,EAAqB8W,QAAQ,GAAGO,MACzB,UAAlBpI,EAAK/T,WACPkc,EAAc9G,EAAME,QAAU4G,GAGhCE,EAAetX,EAAqB8W,QAAQ,GAAGS,MAE5B,WAAftI,EAAKiE,QAESiM,EAAd/H,GACA2H,IAAwByB,KAM5BtB,GAAe,EAEf5O,EAAMtJ,GAAG,CACPiZ,UAAWC,EACXC,SAAUP,EACVQ,YAAaF,KAWbnsB,KAAKkO,QAAQ6c,QANVC,IACHzO,EAAMtJ,GAAG,aAAcwZ,GACvBzB,EAAsByB,kBAapBvN,sBAAavY,GACnBoW,GAAepW,EAAM,SAAU3G,KAAKuS,SAAUvS,oBAMxCib,yBACFjb,KAAKuS,SAASkF,SAAS,qBACzBzX,KAAKmf,MAAQ,SACbnf,KAAKkf,aAAa,YAElBlf,KAAKmf,MAAQ,SACbnf,KAAKkf,aAAa,yBAOdmB,kBACN,MAAsB,YAAfrgB,KAAKmf,OAAsC,WAAfnf,KAAKmf,oBAMnCnP,2BACDhQ,KAAKqgB,WAITrgB,KAAKmf,MAAQ,UACbnf,KAAKkf,aAAa,QAEblf,KAAKkO,QAAQ2c,SAChBniB,EAAE,QAAQsB,6BAA6BhK,eAGzCA,KAAKuS,SACF8J,YAAY,qBACZrS,SAAS,oBACTiR,gCAAoBjb,EAAKib,kBAEvBjb,KAAK4qB,cAAe5qB,KAAKkO,QAAQ2c,UACpC7qB,KAAK6qB,SAAU,EACfniB,EAAEoT,cAAc5I,IAAI,0BAAelT,EAAKogB,UACxC1X,EAAE4T,6BAOC8D,4BACApgB,KAAKqgB,WAIVrgB,KAAKmf,MAAQ,UACbnf,KAAKkf,aAAa,SAEblf,KAAKkO,QAAQ2c,SAChBniB,EAAE,QAAQ2T,gCAAgCrc,eAG5CA,KAAKuS,SACFvI,SAAS,qBACTqS,YAAY,oBACZpB,gCAAoBjb,EAAKib,kBAExBjb,KAAK6qB,UACPniB,EAAEyT,cACFnc,KAAK6qB,SAAU,EACfniB,EAAEgU,+BAOChC,kBACL1a,KAAKqgB,SAAWrgB,KAAKogB,QAAUpgB,KAAKgQ,qBAM/BuP,oBACL,OAAOvf,KAAKmf,OAIhBlV,EAAK0gB,OAASA,GChadzjB,IAAMsY,GAAa,cAQnB9W,aACEuB,EAAKyR,aAAa8D,OAAe,WAC/BtY,IAAMqL,EAAW7J,EAAE1I,MACbkO,EAAUoQ,GAAate,KAAMwf,IAC7B7W,EAAWuF,EAAQjJ,cAElBiJ,EAAQjJ,OAEfiC,IAAMwlB,EAAUhkB,EAAEC,GAAU0B,QACtB4S,EAAW,IAAIhT,EAAK0gB,OAAO+B,EAASxe,GAE1CqE,EAASU,GAAG,0BAAegK,EAASvC,eCtBxCxT,IAAMiD,GAAiC,GAevC,SAASwiB,GAAMhmB,EAAc8E,GAK3B,GAJIrG,EAAY+E,GAAUxD,MACxBwD,GAAUxD,GAAQ,IAGhBvB,EAAYqG,GACd,OAAOtB,GAAUxD,GAGnBwD,GAAUxD,GAAMjD,KAAK+H,GAOvB,SAASmhB,GAAQjmB,GACXvB,EAAY+E,GAAUxD,KAIrBwD,GAAUxD,GAAMnG,QAIR2J,GAAUxD,GAAMqL,OAE7BvG,GC2EA,SArBIohB,GAsBFlkB,EACAuF,6BAAmB,IAddlO,aAAmBmM,EAAO,GAAIyR,IAK9B5d,WAAe,SAKdA,aAAS,EAMfA,KAAKuS,SAAW7J,EAAEC,GAAU0B,QAGvBH,EAASxK,SAASoJ,KAAM9I,KAAKuS,SAAS,MACzCvS,KAAKymB,QAAS,EACd/d,EAAE,QAAQ+d,OAAOzmB,KAAKuS,WAGxBpG,EAAOnM,KAAKkO,QAASA,GAGrBlO,KAAKuS,SAAS/H,KAAK,wBAAwBnC,cAAMlC,EAAG2mB,GAClDpkB,EAAEokB,GAAQ7Z,GAAG,mBACXjT,EAAKkf,aAAa,UAEdlf,EAAKkO,QAAQ6e,eACf/sB,EAAKogB,YAMXpgB,KAAKuS,SAAS/H,KAAK,yBAAyBnC,cAAMlC,EAAG6mB,GACnDtkB,EAAEskB,GAAS/Z,GAAG,mBACZjT,EAAKkf,aAAa,WAEdlf,EAAKkO,QAAQ+e,gBACfjtB,EAAKogB,YAMXpgB,KAAKuS,SAAS/H,KAAK,uBAAuBnC,cAAMlC,EAAGia,GACjD1X,EAAE0X,GAAOnN,GAAG,0BAAejT,EAAKogB,YAzFtClZ,IA4BI8U,GA5BE4B,GAA2B,CAC/BsP,SAAS,EACTrC,SAAS,EACTsC,OAAO,EACPC,YAAY,EACZL,eAAe,EACfE,gBAAgB,EAChBI,iBAAiB,GAMfC,GAA6B,KAK3BC,GAAY,eAKdC,IAAe,eA0ETtO,sBAAavY,GACnBoW,GAAepW,EAAM,SAAU3G,KAAKuS,SAAUvS,oBAMxCytB,oBACN,GAAKH,GAAL,CAIApmB,IAAMqL,EAAW+a,GAAY/a,SACvBmb,EAASnb,EAASyC,SAAS,sBAC3BuL,EAAWhO,EAASyC,SAAS,wBAC7B2Y,EAAWpb,EAASyC,SAAS,wBAGnCzC,EAASiO,OAAO,IAChBD,EAASC,OAAO,IAEhBtZ,IAAM4gB,EAAgBvV,EAASiO,SAC/BjO,EAASiE,IAAI,CACXE,KAASwH,GAAQsC,SAAWsH,GAAiB,OAC7CtH,OAAWsH,SAIbvH,EAASkD,YACPqE,GACG4F,EAAOjK,eAAiB,IACxBkK,EAASlK,eAAiB,mBAOzBmK,2BACFtsB,OAAO6N,SAASqa,KAAK9L,UAAU,GAAG/T,QAAQ,eAAiB,GAC7D2jB,GAAalN,OAAM,iBAQfyN,sBAAa5hB,GAEjBvD,EAAEuD,EAAMhH,QAAuBwS,SAAS,iBACxC6V,IAEAA,GAAYlN,sBAORnF,yBACFjb,KAAKuS,SAASkF,SAAS,qBACzBzX,KAAKmf,MAAQ,SACbnf,KAAKkf,aAAa,YAElBlf,KAAKmf,MAAQ,SACbnf,KAAKkf,aAAa,UAClBlf,KAAKuS,SAAS0F,OAGT0U,GAAMY,IAAW/sB,QAAW8sB,KAAeE,KAC9C9kB,EAAEgU,eACF8Q,IAAe,GAGjBtP,GAAQlL,IAAI,SAAUtK,EAAEiU,SAAS3c,KAAKytB,SAAU,MAE5CztB,KAAKkO,QAAQmf,iBACfrtB,KAAK8tB,yBAQHC,6BAmCN,GAlCAT,GAActtB,KAETwtB,KACH9kB,EAAE4T,aACFkR,IAAe,GAGjBxtB,KAAKuS,SAAS+H,OACdta,KAAKytB,WAELvP,GAAQjL,GAAG,SAAUvK,EAAEiU,SAAS3c,KAAKytB,SAAU,MAG/CztB,KAAKmf,MAAQ,UACbnf,KAAKkf,aAAa,QAClBlf,KAAKuS,SACFvI,SAAS,oBACTiR,gCAAoBjb,EAAKib,kBAI1Be,GADGA,IACQtT,EAAEoT,YAAY,MAIvB9b,KAAKkO,QAAQif,MACfnR,GAAShJ,IAAI,QAAShT,KAAK6tB,cAE3B7R,GAAS/I,GAAG,QAASjT,KAAK6tB,cAI5B7R,GAASxF,IAAI,UAAWxW,KAAKkO,QAAQ2c,QAAU,GAAK,GAEhD7qB,KAAKkO,QAAQgf,QAAS,CAGxB3lB,IAAIiiB,EAAOloB,OAAO6N,SAASqa,KAAK9L,UAAU,IACP,EAA/B8L,EAAK7f,QAAQ,iBACf6f,EAAOA,EAAKtjB,QAAQ,oBAAqB,KAKzC5E,OAAO6N,SAASqa,KADdA,EACwBA,IACH,EAArBA,EAAK7f,QAAQ,KAAY,IAAM,mBAGV,cAGzBuU,GAAQjL,GAAG,aAAcjT,KAAK4tB,gCAO1BvN,kBACN,MAAsB,YAAfrgB,KAAKmf,OAAsC,WAAfnf,KAAKmf,oBAMnCnP,2BACDhQ,KAAKqgB,WAMNiN,KACwB,YAAtBA,GAAYnO,OAA6C,WAAtBmO,GAAYnO,QAClDwN,GAAMY,IAAW/sB,OAEjBmsB,GAAMY,qBAAiBvtB,EAAK+tB,WAK9B/tB,KAAK+tB,wBAMA3N,eAAM4N,8BAAc,GAOzB5rB,sBACOpC,EAAKqgB,WAIViN,GAAc,KAEdttB,EAAKmf,MAAQ,UACbnf,EAAKkf,aAAa,UAGbyN,GAAMY,IAAW/sB,QAAUwb,KAC9BtT,EAAEyT,cACFH,GAAW,KAGXtT,EAAE,iBAAiB8N,IAAI,UAAW,MAGpCxW,EAAKuS,SACF8J,YAAY,oBACZpB,gCAAoBjb,EAAKib,kBAExBjb,EAAKkO,QAAQgf,UAAYP,GAAMY,IAAW/sB,SACvCwtB,GACH1sB,OAAO4rB,QAAQe,OAGjB/P,GAAQlL,IAAI,aAAchT,EAAK4tB,kBAKjCxrB,sBACEwqB,GAAQW,KACP,sBAOA7S,kBACL1a,KAAKqgB,SAAWrgB,KAAKogB,QAAUpgB,KAAKgQ,qBAM/BuP,oBACL,OAAOvf,KAAKmf,oBAMP2O,mBACD9tB,KAAKymB,QACPzmB,KAAKuS,SAAShH,SAGXohB,GAAMY,IAAW/sB,QAAW8sB,KAC3BtR,KACFtT,EAAEyT,cACFH,GAAW,MAGTwR,KACF9kB,EAAEgU,eACF8Q,IAAe,kBAQdhG,wBACLxnB,KAAKytB,YC/YTxP,GAAUhL,GAAG,mBAAYhH,GAErBqhB,IACAA,GAAYpf,QAAQkf,YACE,WAAtBE,GAAYnO,OACyB,KAApClT,EAAwBiiB,SAEzBZ,GAAYlN,UAIhBnW,EAAK4iB,OAASA,GC9Bd3lB,IAAMsY,GAAa,cACbgD,GAAW,eAajB9Z,aACEuV,GAAUhL,GAAG,YAAauM,OAAe,WACvCtY,IAAMgH,EAAUoQ,GAAate,KAAqBwf,IAC5C7W,EAAWuF,EAAQjJ,cAElBiJ,EAAQjJ,OAEfiC,IAAMinB,EAAUzlB,EAAEC,GAAU0B,QACxB4S,EAAWkR,EAAQ5f,KAAKiU,IAEvBvF,IACHA,EAAW,IAAIhT,EAAK4iB,OAAOsB,EAASjgB,GACpCigB,EAAQ5f,KAAKiU,GAAUvF,IAGzBA,EAASjN,WC4Eb9I,IAAMknB,GAAyB,CAC7B/I,KAAM,GACNgJ,MAAM,EACNjO,OAAO,EAEPkO,sBAGI1Q,GAA2B,CAC/B2Q,MAAO,GACP9Z,QAAS,GACT+Z,QAAS,GACTC,gBAAgB,EAChBC,SAAU,GACVxB,SAAS,EACTrC,SAAS,EACTsC,OAAO,EACPC,YAAY,EACZC,iBAAiB,EAEjBsB,oBAEAC,sBAEAC,qBAEAC,uBC7EIlR,GAA2B,CAC/BmR,YAAa,KACb7B,SAAS,EACTC,QD6EFljB,EAAK+kB,OAAS,SAAU9gB,WAItB7F,GAFA6F,EAAU/B,EAAO,GAAIyR,GAAiB1P,IAEzBsgB,iBAAWvtB,EAAGc,GACzBmM,EAAQsgB,QAASvtB,GAAKkL,EAAO,GAAIiiB,GAAgBrsB,KAInDwF,IAAI0nB,EAAc,aACd/gB,EAAQsgB,wBAAShuB,SACnByuB,EAAc,mCACZ/gB,EAAQugB,eAAiB,+BAAiC,SAG5DpmB,EAAK6F,EAAQsgB,iBAAUroB,EAAGpE,GACxBktB,GACE,qFAEEltB,EAAOssB,KAAO,gBAAkB,SAC7BtsB,gBAGTktB,GAAe,UAIjB/nB,IAAMgoB,EACJ,2BAA2BhhB,iBAC1BA,EAAQqgB,wCAC6BrgB,iBAClC,KACHA,EAAQuG,4CAC+BvG,mBACpC,IACJ+gB,EACA,SAGIhS,EAAW,IAAIhT,EAAK4iB,OAAOqC,EAAM,CACrChC,QAAShf,EAAQgf,QACjBrC,QAAS3c,EAAQ2c,QACjBsC,MAAOjf,EAAQif,MACfC,WAAYlf,EAAQkf,WACpBC,gBAAiBnf,EAAQmf,kBAmC3B,iBA/BInf,EAAQsgB,wBAAShuB,QACnByc,EAAS1K,SACN/H,KAAK,kCACLnC,cAAMf,EAAOvF,GACZ2G,EAAE3G,GAAQkR,GAAG,mBACX/E,EAAQsgB,QAASlnB,GAAOgnB,QAASrR,GAE7B/O,EAAQsgB,QAASlnB,GAAO8Y,OAC1BnD,EAASmD,YAOnBnD,EAAS1K,SACNU,GAAG,8BACF/E,EAAQygB,OAAQ1R,KAEjBhK,GAAG,gCACF/E,EAAQ0gB,SAAU3R,KAEnBhK,GAAG,+BACF/E,EAAQ2gB,QAAS5R,KAElBhK,GAAG,gCACF/E,EAAQ4gB,SAAU7R,KAGtBA,EAASjN,OAEFiN,IC3JPmQ,YAAY,EACZH,gBAAgB,GCSZrP,GAA2B,CAC/BmR,YAAa,KACbI,WAAY,SACZjC,SAAS,EACTC,QDVFljB,EAAKmlB,MAAQ,SACX/J,EACAkJ,EACAc,EACAnhB,GAmBA,OAjBIlJ,EAAWupB,KACbrgB,EAAUmhB,EACVA,EAAYd,EACZA,EAAQ,IAGNnpB,EAAYiqB,KAEdA,gBAGEjqB,EAAY8I,KACdA,EAAU,IAGZA,EAAU/B,EAAO,GAAIyR,GAAiB1P,GAE/BjE,EAAK+kB,OAAO,CACjBT,MAAOA,EACP9Z,QAAS4Q,EACTmJ,QAAS,CACP,CACEnJ,KAAMnX,EAAQ6gB,YACdV,MAAM,EACNjO,MAAOlS,EAAQ+e,eACfqB,QAASe,IAGbX,SAAU,oBACVxB,QAAShf,EAAQgf,QACjBC,MAAOjf,EAAQif,MACfC,WAAYlf,EAAQkf,eC1BtBA,YAAY,EACZL,eAAe,EACfE,gBAAgB,GCiBZrP,GAA2B,CAC/BmR,YAAa,KACbI,WAAY,SACZjC,SAAS,EACTC,QDlBFljB,EAAK+iB,QAAU,SACb3H,EACAkJ,EACAc,EACAC,EACAphB,GAyBA,OAvBIlJ,EAAWupB,KACbrgB,EAAUohB,EACVA,EAAWD,EACXA,EAAYd,EACZA,EAAQ,IAGNnpB,EAAYiqB,KAEdA,gBAGEjqB,EAAYkqB,KAEdA,gBAGElqB,EAAY8I,KACdA,EAAU,IAGZA,EAAU/B,EAAO,GAAIyR,GAAiB1P,GAE/BjE,EAAK+kB,OAAO,CACjBT,MAAOA,EACP9Z,QAAS4Q,EACTmJ,QAAS,CACP,CACEnJ,KAAMnX,EAAQihB,WACdd,MAAM,EACNjO,MAAOlS,EAAQ6e,cACfuB,QAASgB,GAEX,CACEjK,KAAMnX,EAAQ6gB,YACdV,MAAM,EACNjO,MAAOlS,EAAQ+e,eACfqB,QAASe,IAGbX,SAAU,sBACVxB,QAAShf,EAAQgf,QACjBC,MAAOjf,EAAQif,MACfC,WAAYlf,EAAQkf,eC/BtBA,YAAY,EACZL,eAAe,EACfE,gBAAgB,EAChBliB,KAAM,OACNwkB,UAAW,EACXC,aAAc,GACdC,gBAAgB,GAGlBxlB,EAAKylB,OAAS,SACZC,EACApB,EACAc,EACAC,EACAphB,GAEIlJ,EAAWupB,KACbrgB,EAAUohB,EACVA,EAAWD,EACXA,EAAYd,EACZA,EAAQ,IAGNnpB,EAAYiqB,KAEdA,gBAGEjqB,EAAYkqB,KAEdA,gBAGElqB,EAAY8I,KACdA,EAAU,IAKZhH,IAAMuN,EACJ,gCACCkb,yCAA+CA,aAAkB,KAChD,UALpBzhB,EAAU/B,EAAO,GAAIyR,GAAiB1P,IAK3BnD,+DAEHmD,qBAEAA,EAAQqhB,UAAY,cAAgBrhB,EAAQqhB,UAAY,IAAM,SAEhE,KACc,aAAjBrhB,EAAQnD,gDAEHmD,EAAQqhB,UAAY,cAAgBrhB,EAAQqhB,UAAY,IAAM,QAC5DrhB,6BACJ,IACJ,SAYF,OAAOjE,EAAK+kB,OAAO,OACjBT,UACA9Z,EACA+Z,QAAS,CACP,CACEnJ,KAAMnX,EAAQihB,WACdd,MAAM,EACNjO,MAAOlS,EAAQ6e,cACfuB,iBAlBiBU,GACrB9nB,IAAMhH,EAAQ8uB,EAAOzc,SAAS/H,KAAK,yBAAyBtJ,MAC5DouB,EAASpvB,EAAO8uB,KAkBd,CACE3J,KAAMnX,EAAQ6gB,YACdV,MAAM,EACNjO,MAAOlS,EAAQ+e,eACfqB,iBAnBkBU,GACtB9nB,IAAMhH,EAAQ8uB,EAAOzc,SAAS/H,KAAK,yBAAyBtJ,MAC5DmuB,EAAUnvB,EAAO8uB,MAoBjBN,SAAU,qBACVxB,QAAShf,EAAQgf,QACjBC,MAAOjf,EAAQif,MACfC,WAAYlf,EAAQkf,WACpBuB,gBAASK,GAEP9nB,IAAMyd,EAASqK,EAAOzc,SAAS/H,KAAK,yBACpCP,EAAK0c,iBAAiBhC,GAGtBA,EAAO,GAAG+B,QAGW,aAAjBxY,EAAQnD,OAAkD,IAA3BmD,EAAQuhB,gBACzC9K,EAAO1R,GAAG,mBAAYhH,GACpB,GAAyC,KAApCA,EAAwBiiB,QAAgB,CAC3ChnB,IAAMhH,EAAQ8uB,EAAOzc,SAAS/H,KAAK,yBAAyBtJ,MAO5D,OANAmuB,EAAUnvB,EAAO8uB,GAEb9gB,EAAQ+e,gBACV+B,EAAO5O,SAGF,KAQQ,aAAjBlS,EAAQnD,MACV4Z,EAAO1R,GAAG,0BAAe+b,EAAOxH,iBAI9BtZ,EAAQqhB,WACVP,EAAOxH,mBC7Hb,SA1BIoI,GA2BFjnB,EACAuF,kBAAmB,IAddlO,aAAmBmM,EAAO,GAAIyR,IAK7B5d,WAAe,SAKfA,eAAiB,KAMvBA,KAAKyS,QAAU/J,EAAEC,GAAU0B,QAE3B8B,EAAOnM,KAAKkO,QAASA,GAGrBlO,KAAKuS,SAAW7J,mCACmBA,EAAE4U,YACjCtd,KAAKkO,0BAEP+N,SAASvc,SAASoJ,MAIpB5B,IAAMgU,EAAOlb,KACbA,KAAKyS,QACFQ,GAAG,wBAAyB,SAAUhH,GACjCiP,EAAKgL,WAAWlmB,OAIfgjB,GAAQ/W,KAIbgX,GAAShX,GAETiP,EAAKlL,UAENiD,GAAG,sBAAuB,SAAUhH,GAC/BiP,EAAKgL,WAAWlmB,OAIfgjB,GAAQ/W,IAIbiP,EAAKkF,UAENnN,GAAG6P,GAAa,SAAU7W,GACrBiP,EAAKgL,WAAWlmB,OAIpBijB,GAAShX,KAhFjB/E,IAAM0W,GAA2B,CAC/BzW,SAAU,OACVyV,MAAO,EACPnI,QAAS,iBAqFDyR,oBAAWxf,GACjB,OACGA,EAA6ByT,eACErX,IAAhC4F,EAAEhC,GAAS6X,KAAK,0BAOZqM,qBACN,OAAyB,KAAlB1M,GAAQzB,sBAMT4O,uBACN9jB,IAAIsoB,EACAC,EAGEC,EAAc/vB,KAAKyS,QAAQ,GAAG7K,wBAG9BooB,EAAehwB,KAAK4qB,YAAc,GAAK,GAGvCqF,EAAejwB,KAAKuS,SAAS,GAAG2d,YAChCC,EAAgBnwB,KAAKuS,SAAS,GAAG6d,aAGnCjpB,EAAqBnH,KAAKkO,QAAQ/G,SA4BtC,OAzBiB,SAAbA,IASAA,EAPA4oB,EAAYrZ,IACVqZ,EAAYvP,OACZwP,EACAG,EACA,EACFjS,GAAQsC,SAEG,SACFwP,EAAeG,EAAgB,EAAIJ,EAAYrZ,IAC7C,MACFsZ,EAAeC,EAAe,EAAIF,EAAYpZ,KAC5C,OAEXoZ,EAAYtT,MAAQuT,EAAeC,EAAe,EAClD/R,GAAQzB,QAAUsT,EAAYpZ,KAEnB,QAEA,UAKPxP,GACN,IAAK,SACH0oB,EAAmBI,EAAe,GAApB,EACdH,EAAYC,EAAYvP,OAAS,EAAIwP,EACrChwB,KAAKuS,SAAS8I,gBAAgB,cAC9B,MAEF,IAAK,MACHwU,EAAmBI,EAAe,GAApB,EACdH,GACG,GAAKK,EAAgBJ,EAAYvP,OAAS,EAAIwP,GACjDhwB,KAAKuS,SAAS8I,gBAAgB,iBAC9B,MAEF,IAAK,OACHwU,GAAc,GAAKI,EAAeF,EAAYtT,MAAQ,EAAIuT,GAC1DF,EAAkBK,EAAgB,GAArB,EACbnwB,KAAKuS,SAAS8I,gBAAgB,gBAC9B,MAEF,IAAK,QACHwU,EAAaE,EAAYtT,MAAQ,EAAIuT,EACrCF,EAAkBK,EAAgB,GAArB,EACbnwB,KAAKuS,SAAS8I,gBAAgB,eAIlCnU,IAAMmpB,EAAerwB,KAAKyS,QAAQwG,SAElCjZ,KAAKuS,SAASiE,IAAI,CAChBE,IAAQ2Z,EAAa3Z,IAAMqZ,EAAYvP,OAAS,OAChD7J,KAAS0Z,EAAa1Z,KAAOoZ,EAAYtT,MAAQ,OACjD6T,cAAkBT,OAClBhH,aAAiBiH,uBAQb5Q,sBAAavY,GACnBoW,GAAepW,EAAM,UAAW3G,KAAKyS,QAASzS,oBAMxCib,yBACFjb,KAAKuS,SAASkF,SAAS,sBACzBzX,KAAKmf,MAAQ,SACbnf,KAAKkf,aAAa,YAElBlf,KAAKmf,MAAQ,SACbnf,KAAKkf,aAAa,yBAOdmB,kBACN,MAAsB,YAAfrgB,KAAKmf,OAAsC,WAAfnf,KAAKmf,oBAMlC4O,6BACN/tB,KAAKmf,MAAQ,UACbnf,KAAKkf,aAAa,QAElBlf,KAAKuS,SACFvI,SAAS,qBACTiR,gCAAoBjb,EAAKib,gCAOvBjL,cAAK9B,cACV,IAAIlO,KAAKqgB,SAAT,CAIAnZ,IAAMqpB,EAAapkB,EAAO,GAAInM,KAAKkO,SAE/BA,GACF/B,EAAOnM,KAAKkO,QAASA,GAInBqiB,EAAW9b,UAAYzU,KAAKkO,QAAQuG,SACtCzU,KAAKuS,SAASvJ,KAAKhJ,KAAKkO,QAAQuG,SAGlCzU,KAAKqrB,cAEDrrB,KAAKkO,QAAQ0O,MACf5c,KAAKwwB,UAAYpuB,6BAAiBpC,EAAK+tB,UAAU/tB,KAAKkO,QAAQ0O,QAE9D5c,KAAKwwB,UAAY,KACjBxwB,KAAK+tB,yBAOF3N,4BACDpgB,KAAKwwB,YACPngB,aAAarQ,KAAKwwB,WAClBxwB,KAAKwwB,UAAY,MAGdxwB,KAAKqgB,WAIVrgB,KAAKmf,MAAQ,UACbnf,KAAKkf,aAAa,SAElBlf,KAAKuS,SACF8J,YAAY,qBACZpB,gCAAoBjb,EAAKib,iCAMvBP,kBACL1a,KAAKqgB,SAAWrgB,KAAKogB,QAAUpgB,KAAKgQ,qBAM/BuP,oBACL,OAAOvf,KAAKmf,OAIhBlV,EAAK2lB,QAAUA,GChWf1oB,IAAMsY,GAAa,eACbgD,GAAW,gBAEjB9Z,aAEEuV,GAAUhL,GAAG,2BAA4BuM,OAAe,WACtDtY,IAAMuL,EAAU/J,EAAE1I,MACdid,EAAWxK,EAAQlE,KAAKiU,IAEvBvF,IACHA,EAAW,IAAIhT,EAAK2lB,QAClB5vB,KACAse,GAAate,KAAqBwf,KAEpC/M,EAAQlE,KAAKiU,GAAUvF,QCqJ3B,SApBIwT,GAoBeviB,GAZZlO,aAAmBmM,EAAO,GAAIyR,IAK7B5d,WAAe,SAKfA,eAAiB,KAGvBmM,EAAOnM,KAAKkO,QAASA,GAGrB3G,IAAImpB,EAAmB,GACnBC,EAAmB,GAGsB,IAA3C3wB,KAAKkO,QAAQ0iB,YAAajnB,QAAQ,MACW,IAA7C3J,KAAKkO,QAAQ0iB,YAAajnB,QAAQ,OAElC+mB,EAAmB,gBAAgB1wB,KAAKkO,wBACF,KAA7BlO,KAAKkO,QAAQ0iB,cACtBD,EAAmB,mBAAmB3wB,KAAKkO,qBAI7ClO,KAAKuS,SAAW7J,EACd,8DACqC1I,KAAKkO,0BACvClO,KAAKkO,QAAQ2iB,8GACyFF,OAAqBD,MAAoB1wB,KAAKkO,0BACjJ,IACJ,UACF+N,SAASvc,SAASoJ,MAGpB9I,KAAKqrB,YAAY,SAEjBrrB,KAAKuS,SAASoI,SAAS3Q,0BAA0BhK,KAAKkO,kBAjF1DhH,IAAM0W,GAA2B,CAC/BkT,QAAS,GACT5hB,QAAS,IACT/H,SAAU,SACV0pB,WAAY,GACZD,YAAa,GACbG,oBAAoB,EACpBC,qBAAqB,EAErB1C,qBAEA2C,2BAEAtC,oBAEAC,sBAEAC,qBAEAC,uBAMExB,GAA+B,KAK7BC,GAAY,iBC1HlB,SAAS2D,GAAU5pB,GACjB,uBADyC,GAEvC,mCACEA,wBAA8BA,EAAU,iTAmB9C,SAAS6pB,GAASC,GAChBlqB,IAAMmqB,EAAW3oB,EAAE0oB,GAEbE,EAAQD,EAAS5Z,SAAS,yBAC5ByZ,GAAU,GAAKA,GAAU,GAAKA,GAAU,GAAKA,GAAU,GACvDA,KAEJG,EAASroB,KAAKsoB,gBDuJNN,6BAAoB/kB,GAC1B/E,IAAMuL,EAAU/J,EAAEuD,EAAMhH,QAGrBwN,EAAQgF,SAAS,kBACjBhF,EAAQ6C,QAAQ,kBAAkB9U,QAEnC8sB,GAAalN,sBAQTiL,qBAAYlM,GAClBjY,IAGIokB,EACAiG,EAJEC,EAAiBxxB,KAAKuS,SAAS,GAAGkf,aAClCtqB,EAAWnH,KAAKkO,QAAQ/G,SAO5BmkB,EADe,WAAbnkB,GAAsC,QAAbA,EACd,OAEA,IAID,SAAVgY,EACFoS,EAAa,KAEI,WAAbpqB,IACFoqB,EAAaC,GAGE,QAAbrqB,IACFoqB,GAAcC,GAGC,aAAbrqB,GAAwC,cAAbA,IAC7BoqB,GAAcC,EAAiB,IAGhB,gBAAbrqB,GAA2C,iBAAbA,IAChCoqB,EAAaC,EAAiB,KAIlCxxB,KAAKuS,SAASgJ,uBAAuB+P,MAAciG,sBAM9CvhB,2BACc,YAAfhQ,KAAKmf,OAAsC,WAAfnf,KAAKmf,QAKjCmO,GACFX,GAAMY,qBAAiBvtB,EAAKgQ,WAI9Bsd,GAActtB,MAGTmf,MAAQ,UACbnf,KAAKkO,QAAQygB,OAAQ3uB,MAErBA,KAAKqrB,YAAY,QAEjBrrB,KAAKuS,SAAS0I,yBACO,YAAfjb,EAAKmf,QAITnf,EAAKmf,MAAQ,SACbnf,EAAKkO,QAAQ0gB,SAAU5uB,GAGnBA,EAAKkO,QAAQ2iB,YACf7wB,EAAKuS,SAAS/H,KAAK,yBAAyByI,GAAG,mBAC7CjT,EAAKkO,QAAQ+iB,cAAejxB,GACxBA,EAAKkO,QAAQ6iB,oBACf/wB,EAAKogB,UAMXpgB,EAAKuS,SAASU,GAAG,iBAAUhH,GACpBvD,EAAEuD,EAAMhH,QAAuBwS,SAAS,yBAC3CzX,EAAKkO,QAAQogB,QAAStuB,KAKtBA,EAAKkO,QAAQ8iB,qBACf/S,GAAUhL,GAAGyP,GAAY1iB,EAAKgxB,qBAI5BhxB,EAAKkO,QAAQgB,UACflP,EAAKwwB,UAAYpuB,6BAAiBpC,EAAKogB,SAASpgB,EAAKkO,QAAQgB,6BAQ5DkR,4BACc,YAAfpgB,KAAKmf,OAAsC,WAAfnf,KAAKmf,QAIjCnf,KAAKwwB,WACPngB,aAAarQ,KAAKwwB,WAGhBxwB,KAAKkO,QAAQ8iB,qBACf/S,GAAUjL,IAAI0P,GAAY1iB,KAAKgxB,qBAGjChxB,KAAKmf,MAAQ,UACbnf,KAAKkO,QAAQ2gB,QAAS7uB,MAEtBA,KAAKqrB,YAAY,SAEjBrrB,KAAKuS,SAAS0I,yBACO,YAAfjb,EAAKmf,QAITmO,GAAc,KACdttB,EAAKmf,MAAQ,SACbnf,EAAKkO,QAAQ4gB,SAAU9uB,GACvBA,EAAKuS,SAAShH,SACdqhB,GAAQW,SAKdtjB,EAAKynB,SAAW,SAAUZ,EAAc5iB,kBAAe,IACjDhJ,EAAS4rB,GACX5iB,EAAQ4iB,QAAUA,EAElB5iB,EAAU4iB,EAGZ5pB,IAAM+V,EAAW,IAAIwT,GAASviB,GAI9B,OAFA+O,EAASjN,OAEFiN,GE/VTvU,aAEEuV,GAAUhL,GAAG,QAAS,qBAAsB,WAC1C/L,IAAMgZ,EAAQxX,EAAE1I,MACV2xB,EAAazR,EAAMpY,SAEzB6pB,EAAW3c,SAAS,KAAK3M,cAAMf,EAAOmB,GACpCvB,IAAM0qB,EAAS1R,EAAMnO,GAAGtJ,GAEpBmpB,GACF7U,GAAe,SAAU,YAAa4U,EAAW,QAAI7uB,EAAW,OAC9DwE,IAIJsqB,EACIlpB,EAAED,GAAMuB,SAAS,0BACjBtB,EAAED,GAAM4T,YAAY,8BAK5BpS,EAAKyR,SAAS,+BAAgC,WAC5C,IAAIzR,EAAK0T,SAAS3d,KAAM,CACtBoe,YAAa,4BACbC,cAAe,oCDoBrB3V,aAEEuB,EAAKyR,SAAS,gBAAiB,WAC7ByV,GAASnxB,UEuFX,SA/BI6xB,GAgCFC,EACAC,EACA7jB,cAMA,kBANmB,IApBdlO,aAAmBmM,EAAO,GAAIyR,IAK7B5d,WAAe,SAiBrBA,KAAKgyB,QAAUtpB,EAAEopB,GAAgBznB,QACjCrK,KAAKuS,SAAW7J,EAAEqpB,GAAc1nB,SAG3BrK,KAAKgyB,QAAQlqB,SAASiK,GAAG/R,KAAKuS,SAASzK,UAC1C,MAAM,IAAI/C,MAAM,oDAGlBoH,EAAOnM,KAAKkO,QAASA,GAGrBlO,KAAKiyB,UAAYjyB,KAAKuS,SAASkF,SAAS,qBAGxCzX,KAAKkyB,UACsB,SAAzBlyB,KAAKkO,QAAQikB,SAAsBnyB,KAAKiyB,UAAYjyB,KAAKkO,QAAQikB,QAGnEnyB,KAAKgyB,QAAQ/e,GAAG,0BAAejT,EAAK0a,WAGpCuD,GAAUhL,GAAG,4BAAqBhH,GAChC/E,IAAMuL,EAAU/J,EAAEuD,EAAMhH,SAGtBjF,EAAKqgB,UACJ5N,EAAQV,GAAG/R,EAAKuS,WAChBrI,EAASlK,EAAKuS,SAAS,GAAIE,EAAQ,KACnCA,EAAQV,GAAG/R,EAAKgyB,UAChB9nB,EAASlK,EAAKgyB,QAAQ,GAAIvf,EAAQ,KAEnCzS,EAAKogB,UAMTlZ,IAAMgU,EAAOlb,KACbie,GAAUhL,GAAG,QAAS,kBAAmB,WACvC/L,IAAMgZ,EAAQxX,EAAE1I,MAGbkgB,EAAM1V,KAAK,cAAchK,aACCsC,IAA3Bod,EAAM3B,KAAK,aAEXrD,EAAKkF,UAKTpgB,KAAKoyB,mBAGLlU,GAAQjL,GACN,SACAvK,EAAEiU,2BAAe3c,EAAKytB,YAAY,MArGxCvmB,IAAM0W,GAA2B,CAC/BzW,SAAU,OACVkrB,MAAO,OACP5K,OAAQ,GACR6K,QF9CFroB,EAAKsoB,eAAiB,SACpB5pB,IAEkBvD,EAAYuD,GAAYD,EAAE,iBAAmBA,EAAEC,IAEvDN,KAAK,WACb8oB,GAASnxB,UEyCXmyB,QAAS,OACTK,eAAgB,QAChBC,aAAc,kBAqGNpS,kBACN,MAAsB,YAAfrgB,KAAKmf,OAAsC,WAAfnf,KAAKmf,oBAOlCD,sBAAavY,GACnBoW,GAAepW,EAAM,OAAQ3G,KAAKuS,SAAUvS,oBAMtCytB,oBACNlmB,IAAImrB,EACA/J,EAGAxhB,EACAkrB,EAYAM,EACAhL,EAVEE,EAAe3J,GAAQsC,SACvBoS,EAAc1U,GAAQzB,QAGtBgL,EAASznB,KAAKkO,QAAQuZ,OACtByK,EAAYlyB,KAAKkyB,UACjBW,EAAU7yB,KAAKkO,QAAQokB,MAOvBlK,EAAYpoB,KAAKuS,SAASkK,QAC1B4L,EAAaroB,KAAKuS,SAASiO,SAG3BsS,EAAa9yB,KAAKgyB,QAAQ,GAAGpqB,wBAC7BmrB,EAAYD,EAAWpc,IACvBsc,EAAaF,EAAWnc,KACxBsc,EAAeH,EAAWtS,OAC1B0S,EAAcJ,EAAWrW,MACzB0W,EAAetL,EAAekL,EAAYE,EAC1CG,EAAcR,EAAcI,EAAaE,EAGzCG,EAAkBrzB,KAAKgyB,QAAQ,GAAGsB,UAClCC,EAAmBvzB,KAAKgyB,QAAQ,GAAGwB,WAsCzC,GAhCIrsB,EAH0B,SAA1BnH,KAAKkO,QAAQ/G,SACqCkhB,EAAaZ,EAA7D0L,GAAgBjB,EAAYe,EAAe,GAElC,SAGX5K,EAAaZ,EADbsL,GAAab,EAAYe,EAAe,GAI7B,MAGA,SAGFjzB,KAAKkO,QAAQ/G,SAOtBkrB,EAHuB,SAAvBryB,KAAKkO,QAAQmkB,MACiBjK,EAAYX,EAAxC2L,EAAcF,EAER,OAC4B9K,EAAYX,EAAvCuL,EAAaE,EAEd,QAGA,SAGFlzB,KAAKkO,QAAQmkB,MAIN,WAAblrB,EACFwgB,EAAmB,IACnBgB,GACGuJ,EAAY,EAAIe,IAChBJ,EAAUE,EAAYM,QACpB,GAAiB,QAAblsB,EACTwgB,EAAmB,OACnBgB,GACGuJ,EAAYe,EAAe,IAC3BJ,EAAUE,EAAY1K,EAAagL,EAAkBhL,OACnD,CACLV,EAAmB,MAKnBpgB,IAAIksB,EAAiBpL,EAGhBroB,KAAKiyB,WACsBpK,EAA1BQ,EAAsB,EAATZ,IACfgM,EAAiB5L,EAAwB,EAATJ,EAChCznB,KAAKuS,SAASiO,OAAOiT,IAIzB9K,GACGd,EAAe4L,GAAkB,GACjCZ,EAAU,EAAIQ,EAAkBN,GAMrC,GAHA/yB,KAAKuS,SAASiE,IAAI,MAAUmS,QAGd,SAAV0J,EACFM,EAAmB,IACnBD,EAAWG,EAAUG,EAAaO,OAC7B,GAAc,UAAVlB,EACTM,EAAmB,OACnBD,EAAWG,EACPG,EAAaE,EAAc9K,EAC3BmL,EAAmBL,EAAc9K,MAChC,CACLuK,EAAmB,MAInBprB,IAAImsB,EAAgBtL,EAGSwK,EAAzBxK,EAAqB,EAATX,IACdiM,EAAgBd,EAAuB,EAATnL,EAC9BznB,KAAKuS,SAASkK,MAAMiX,IAGtBhB,GACGE,EAAcc,GAAiB,GAC/Bb,EAAU,EAAIU,EAAmBP,GAGtChzB,KAAKuS,SAASiE,IAAI,OAAWkc,QAG7B1yB,KAAKuS,SAAS8I,gBAAmBsX,MAAoBhL,iBAO/CgM,yBAAgBC,GACtB1sB,IAEI2sB,EACAC,EAGA3sB,EACAkrB,EAOAM,EACAhL,EAfEzH,EAAQ0T,EAAS9rB,OAAO,mBAUxB+f,EAAe3J,GAAQsC,SACvBoS,EAAc1U,GAAQzB,QAOtBsX,EAAeH,EAASnX,QACxBuX,EAAgBJ,EAASpT,SAGzByT,EAAW/T,EAAM,GAAGtY,wBACpBssB,EAAYD,EAASxX,MACrBwL,EAAagM,EAASzT,OACtB2T,EAAWF,EAAStd,KACpByd,EAAUH,EAASvd,IAiBvB2b,EAFuC0B,EAArCnB,EAAcuB,EAAWD,EAEnB,OACYH,EAAXI,EAED,QAGA,OAIO,YAtBfhtB,EAF2B6sB,EAAzBnM,EAAeuM,EAEN,SACqBJ,EAAvBI,EAAUnM,EAER,MAGA,UAkBX4L,EADAlM,EAAmB,IAEG,QAAbxgB,IACTwgB,EAAmB,OACnBkM,GAAcG,EAAgB/L,GAGhC2L,EAASpd,IAAI,MAAUqd,QAGT,SAAVxB,GACFM,EAAmB,IACnBmB,EAAcI,GACK,UAAV7B,IACTM,EAAmB,OACnBmB,GAAeC,GAGjBH,EAASpd,IAAI,OAAWsd,QAGxBF,EAASvY,gBAAmBsX,MAAoBhL,iBAO1C0M,qBAAYT,GAClB5zB,KAAK2zB,gBAAgBC,GAErBA,EACG5pB,SAAS,kBACTlC,OAAO,mBACPkC,SAAS,uCAONsqB,sBAAaV,GAEnBA,EACGvX,YAAY,kBACZrS,SAAS,qBACTiR,gCAAoB2Y,EAASvX,YAAY,uBAGzCvU,OAAO,mBACPuU,YAAY,yBAGfuX,EAASppB,KAAK,cAAcnC,cAAMlC,EAAGouB,GACnCrtB,IAAMstB,EAAc9rB,EAAE6rB,GAEtBC,EACGnY,YAAY,kBACZrS,SAAS,qBACTiR,gCAAoBuZ,EAAYnY,YAAY,uBAC5CvU,OAAO,mBACPuU,YAAY,yCAQXoY,uBAAcb,GACpBA,EAASnc,SAAS,kBACdzX,KAAKs0B,aAAaV,GAClB5zB,KAAKq0B,YAAYT,iBAMfxB,4BAENlrB,IAAMgU,EAAOlb,KA8Cb,GA3CAA,KAAKuS,SAASU,GAAG,QAAS,kBAAmB,SAAUhH,GACrD/E,IAAMgZ,EAAQxX,EAAE1I,MACVyS,EAAU/J,EAAEuD,EAAMhH,QAGxB,QAA+BnC,IAA3Bod,EAAM3B,KAAK,cAKX9L,EAAQV,GAAG,gBAAiBU,EAAQV,GAAG,kBAKtCU,EAAQ6C,QAAQ,mBAAmBjL,QAAQ0H,GAAGmO,GAAnD,CAKAhZ,IAAM0sB,EAAW1T,EAAMlL,SAAS,cAGhCkL,EACGpY,OAAO,cACPkN,SAAS,mBACT3M,cAAMlC,EAAGsC,GACRvB,IAAMwtB,EAAchsB,EAAED,GAAMuM,SAAS,eAGnC0f,EAAYl0B,QACVozB,EAASpzB,QAAWk0B,EAAY3iB,GAAG6hB,IAErC1Y,EAAKoZ,aAAaI,KAKpBd,EAASpzB,QACX0a,EAAKuZ,cAAcb,MAIa,UAAhC5zB,KAAKkO,QAAQskB,eAA4B,CAE3CjrB,IAAI2H,EAAe,KACfylB,EAAmB,KAEvB30B,KAAKuS,SAASU,GAAG,qBAAsB,kBAAmB,SACxDhH,GAEA/E,IAAMgZ,EAAQxX,EAAE1I,MACV4kB,EAAY3Y,EAAMlB,KAClB6pB,EAAiBlsB,EACpBuD,EAAqBjK,eAIxB,QAA+Bc,IAA3Bod,EAAM3B,KAAK,YAAf,CAKA,GAAkB,cAAdqG,GACF,IACG1E,EAAMnO,GAAG6iB,IACV1qB,EAASgW,EAAM,GAAI0U,EAAe,IAElC,YAKC,GAAkB,aAAdhQ,IAEL1E,EAAMnO,GAAG6iB,IACT1qB,EAASgW,EAAM,GAAI0U,EAAe,KAElC,OAKJ1tB,IAAM0sB,EAAW1T,EAAMlL,SAAS,cAGhC,GAAkB,cAAd4P,GACF,GAAIgP,EAASpzB,OAAQ,CAEnB0G,IAAM2tB,EAAWjB,EAASrlB,KAAK,0BAM/B,GALIsmB,GACFxkB,aAAawkB,GAIXjB,EAASnc,SAAS,kBACpB,OAIFpH,aAAaskB,GAGbzlB,EAAUylB,EAAcvyB,6BAChB8Y,EAAKmZ,YAAYT,IACvB1Y,EAAKhN,QAAQukB,cAGfmB,EAASrlB,KAAK,wBAAyBW,SAKtC,GAAkB,aAAd0V,GACHgP,EAASpzB,OAAQ,CAEnB0G,IAAM4tB,EAAUlB,EAASrlB,KAAK,yBAC1BumB,GACFzkB,aAAaykB,GAIf5lB,EAAU9M,6BACF8Y,EAAKoZ,aAAaV,IACxB1Y,EAAKhN,QAAQukB,cAGfmB,EAASrlB,KAAK,yBAA0BW,sBAU1C+L,yBACNjb,KAAKuS,SAAS8J,YAAY,qBAEP,YAAfrc,KAAKmf,QACPnf,KAAKmf,MAAQ,SACbnf,KAAKkf,aAAa,WAGD,YAAflf,KAAKmf,QACPnf,KAAKmf,MAAQ,SACbnf,KAAKkf,aAAa,UAGlBlf,KAAKuS,SAASiE,IAAI,CAChBE,IAAK,GACLC,KAAM,GACN8F,MAAO,GACPtV,SAAU,yBAQTuT,kBACL1a,KAAKqgB,SAAWrgB,KAAKogB,QAAUpgB,KAAKgQ,qBAM/BA,2BACDhQ,KAAKqgB,WAITrgB,KAAKmf,MAAQ,UACbnf,KAAKkf,aAAa,QAElBlf,KAAKytB,WAELztB,KAAKuS,SAEFiE,IAAI,WAAYxW,KAAKkO,QAAQokB,MAAQ,QAAU,YAC/CtoB,SAAS,kBACTiR,gCAAoBjb,EAAKib,iCAMvBmF,4BACApgB,KAAKqgB,WAIVrgB,KAAKmf,MAAQ,UACbnf,KAAKkf,aAAa,SAGlBlf,KAAKuS,SAAS/H,KAAK,cAAcnC,cAAMlC,EAAG4uB,GACxC/0B,EAAKs0B,aAAa5rB,EAAEqsB,MAGtB/0B,KAAKuS,SACF8J,YAAY,kBACZrS,SAAS,qBACTiR,gCAAoBjb,EAAKib,oBAIhChR,EAAK4nB,KAAOA,GC1sBZ3qB,IAAMsY,GAAa,YACbgD,GAAW,oBAajB9Z,aACEuV,GAAUhL,GAAG,YAAauM,OAAe,WACvCtY,IAAM2U,EAAQnT,EAAE1I,MACZid,EAAWpB,EAAMtN,KAAKiU,IAE1B,IAAKvF,EAAU,CACb/V,IAAMgH,EAAUoQ,GAAate,KAAqBwf,IAC5CuS,EAAe7jB,EAAQjJ,cAEtBiJ,EAAQjJ,OAEfgY,EAAW,IAAIhT,EAAK4nB,KAAKhW,EAAOkW,EAAc7jB,GAC9C2N,EAAMtN,KAAKiU,GAAUvF,GAErBA,EAASvC"}