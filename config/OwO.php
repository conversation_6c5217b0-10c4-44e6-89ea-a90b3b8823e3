<?php
/**

    _    _  __    _    ____  _   _ ___ 
   / \  | |/ /   / \  / ___|| | | |_ _|
  / _ \ | ' /   / _ \ \___ \| |_| || | 
 / ___ \| . \  / ___ \ ___) |  _  || | 
/_/   \_\_|\_\/_/   \_\____/|_| |_|___|

 * [Romanticism]
 * OwO.php 评论区表情文件
 * @version 2.1
**/
?>

<?php if (!defined('__TYPECHO_ROOT_DIR__')) exit; ?>

    <div class="OwO">
        <div class="OwO-body" id="OwO-body">
            <ul class="OwO-items" style="max-height: 0;padding: 0px;">
                <li class="OwO-item" onclick="Smilies.grin('@OwO(chuibao)');"><img src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/chuibao.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwO(good)');"><img src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/good.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwO(hehe)');"><img src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/hehe.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwO(doge)');"><img src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/doge.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwO(wow)');"><img src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/wow.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwO(huaji)');"><img src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/huaji.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwO(azhe)');"><img src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/azhe.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwO(cry)');"><img src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/cry.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwO(fighting)');"><img src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/fighting.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwO(bixin)');"><img src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/bixin.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwO(yygq)');"><img src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/yygq.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwO(chigua)');"><img src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/chigua.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwO(mojing)');"><img src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/mojing.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwO(hahaha)');"><img src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/hahaha.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwO(aojiao)');"><img src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/aojiao.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwO(kunhuo)');"><img src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/kunhuo.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwO(wosuanle)');"><img src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/wosuanle.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwO(happy)');"><img src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/happy.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwO(mengxin)');"><img src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/mengxin.webp" loading="lazy"></li> 
                <li class="OwO-item" onclick="Smilies.grin('@OwO(baofu)');"><img src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/baofu.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwO(yeah)');"><img src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/yeah.webp" loading="lazy"></li> 
                <li class="OwO-item" onclick="Smilies.grin('@OwO(jingxi)');"><img src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/jingxi.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwO(wukongDoge)');"><img src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/wukongDoge.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwO(wuyan)');"><img src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/wuyan.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwO(baoquan)');"><img src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/baoquan.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwO(zanghu)');"><img src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/zanghu.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwO(ganbei)');"><img src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/ganbei.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwO(luotianyi)');"><img src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/luotianyi.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwO(aixin)');"><img src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/aixin.webp" loading="lazy"></li>
                <br>
                <li class="OwO-item" onclick="Smilies.grin('@OwOb(sanlian)');"><img style="width:45px;height:auto;" src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/sanlian.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwOb(haojiahuo)');"><img style="width:45px;height:auto;" src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/haojiahuo.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwOb(haoye)');"><img style="width:45px;height:auto;" src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/haoye.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwOb(miao)');"><img style="width:45px;height:auto;" src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/miao.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwOb(bucuoo)');"><img style="width:45px;height:auto;" src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/bucuoo.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwOb(youya)');"><img style="width:45px;height:auto;" src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/youya.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwOb(mangqu)');"><img style="width:45px;height:auto;" src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/mangqu.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwOb(pofang)');"><img style="width:45px;height:auto;" src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/pofang.webp" loading="lazy"></li>
                <br>
                <li class="OwO-item" onclick="Smilies.grin('@OwOb(haixiu)');"><img style="width:45px;height:auto;" src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/haixiu.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwOb(rana)');"><img style="width:45px;height:auto;" src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/rana.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwOb(anon)');"><img style="width:45px;height:auto;" src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/anon.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwOb(rwkk)');"><img style="width:45px;height:auto;" src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/rwkk.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwOb(chuji)');"><img style="width:45px;height:auto;" src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/chuji.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwOb(wtzn)');"><img style="width:45px;height:auto;" src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/wtzn.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwOb(offer)');"><img style="width:45px;height:auto;" src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/offer.webp" loading="lazy"></li>
                <li class="OwO-item" onclick="Smilies.grin('@OwOb(jingya)');"><img style="width:45px;height:auto;" src="<?php $this->options->themeUrl(''); ?>/config/style/img/bili/jingya.webp" loading="lazy"></li>

                <p style="opacity: .2"><small>&copy;<?php echo date('Y');?> bilibili.com</small></p> 
             </ul>
        </div>
    </div>