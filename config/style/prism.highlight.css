/* PrismJS 1.29.0
https://prismjs.com/download.html#themes=prism-tomorrow&languages=markup+css+clike+javascript+c+csharp+cpp+go+java+markup-templating+php+python&plugins=toolbar+copy-to-clipboard */

/* 自定义样式：支持没有语言类的代码块 - One Dark主题 */
pre.language-none,
pre:not([class*="language-"]):not([class*="lang-"]) {
    background: #282c34 !important;
    color: #abb2bf !important;
    padding: 1em !important;
    margin: 0.5em 0 !important;
    overflow: auto !important;
    border-radius: 0.375rem !important;
    font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace !important;
    font-size: 0.875em !important;
    line-height: 1.5 !important;
    border: none !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

code.language-none,
pre:not([class*="language-"]):not([class*="lang-"]) > code {
    background: none !important;
    color: inherit !important;
    font-family: inherit !important;
    font-size: inherit !important;
    padding: 0 !important;
    border-radius: 0 !important;
    border: none !important;
}

/* 深色模式下保持一致的One Dark样式 */
.mdui-theme-layout-dark pre.language-none,
.mdui-theme-layout-dark pre:not([class*="language-"]):not([class*="lang-"]) {
    background: #282c34 !important;
    color: #abb2bf !important;
}

/* 确保代码块在文章中的正确显示 */
.article pre.language-none,
.article pre:not([class*="language-"]):not([class*="lang-"]) {
    white-space: pre-wrap !important;
    word-wrap: break-word !important;
}

/* 语言类型标签样式 */
.code-language-label {
    position: absolute !important;
    top: 0.3em !important;
    right: 4em !important;
    background: rgba(0, 0, 0, 0.6) !important;
    color: #fff !important;
    padding: 0.2em 0.5em !important;
    border-radius: 0.3em !important;
    font-size: 0.75em !important;
    font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace !important;
    z-index: 5 !important;
    opacity: 0.8 !important;
    transition: opacity 0.3s ease-in-out !important;
    pointer-events: none !important;
    user-select: none !important;
    font-weight: normal !important;
    line-height: 1.2 !important;
}

/* 深色主题下的语言标签 */
.mdui-theme-layout-dark .code-language-label {
    background: rgba(255, 255, 255, 0.2) !important;
    color: #f7fafc !important;
}

/* 确保语言标签不与工具栏冲突 */
div.code-toolbar .code-language-label {
    right: 4.5em !important;
}

/* 当没有工具栏时，语言标签可以更靠右 */
pre:not(.code-toolbar) .code-language-label {
    right: 0.5em !important;
}

/* 鼠标悬停时的效果 */
pre:hover .code-language-label {
    opacity: 1 !important;
}
/* One Dark Theme for PrismJS */
code[class*=language-],
pre[class*=language-] {
    color: #abb2bf;
    background: none;
    font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
    font-size: 1em;
    text-align: left;
    white-space: pre;
    word-spacing: normal;
    word-break: normal;
    word-wrap: normal;
    line-height: 1.5;
    -moz-tab-size: 4;
    -o-tab-size: 4;
    tab-size: 4;
    -webkit-hyphens: none;
    -moz-hyphens: none;
    -ms-hyphens: none;
    hyphens: none;
}

pre[class*=language-] {
    padding: 1em;
    margin: .5em 0;
    overflow: auto;
}

:not(pre) > code[class*=language-],
pre[class*=language-] {
    background: #282c34;
}

:not(pre) > code[class*=language-] {
    padding: .1em;
    border-radius: .3em;
    white-space: normal;
}

/* One Dark Color Scheme */
.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
    color: #5c6370;
    font-style: italic;
}

.token.punctuation {
    color: #abb2bf;
}

.token.selector,
.token.tag {
    color: #e06c75;
}

.token.property,
.token.boolean,
.token.number,
.token.constant,
.token.symbol,
.token.attr-name,
.token.deleted {
    color: #d19a66;
}

.token.string,
.token.char,
.token.attr-value,
.token.builtin,
.token.inserted {
    color: #98c379;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string {
    color: #56b6c2;
}

.token.atrule,
.token.keyword {
    color: #c678dd;
}

.token.function {
    color: #61afef;
}

.token.class-name {
    color: #e5c07b;
}

.token.regex,
.token.important,
.token.variable {
    color: #e06c75;
}

.token.important,
.token.bold {
    font-weight: bold;
}

.token.italic {
    font-style: italic;
}

.token.entity {
    cursor: help;
}

/* Namespace */
.token.namespace {
    opacity: .7;
}
div.code-toolbar{position:relative;border-radius: 15px;}div.code-toolbar>.toolbar{position:absolute;z-index:10;top:.3em;right:.2em;transition:opacity .3s ease-in-out;opacity:0}div.code-toolbar:hover>.toolbar{opacity:1}div.code-toolbar:focus-within>.toolbar{opacity:1}div.code-toolbar>.toolbar>.toolbar-item{display:inline-block}div.code-toolbar>.toolbar>.toolbar-item>a{cursor:pointer}div.code-toolbar>.toolbar>.toolbar-item>button{background:0 0;border:0;color:inherit;font:inherit;line-height:normal;overflow:visible;padding:0;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none}div.code-toolbar>.toolbar>.toolbar-item>a,div.code-toolbar>.toolbar>.toolbar-item>button,div.code-toolbar>.toolbar>.toolbar-item>span{color:#bbb;font-size:.8em;padding:0 .5em;background:#f5f2f0;background:rgba(224,224,224,.2);box-shadow:0 2px 0 0 rgba(0,0,0,.2);border-radius:.5em}div.code-toolbar>.toolbar>.toolbar-item>a:focus,div.code-toolbar>.toolbar>.toolbar-item>a:hover,div.code-toolbar>.toolbar>.toolbar-item>button:focus,div.code-toolbar>.toolbar>.toolbar-item>button:hover,div.code-toolbar>.toolbar>.toolbar-item>span:focus,div.code-toolbar>.toolbar>.toolbar-item>span:hover{color:inherit;text-decoration:none}
