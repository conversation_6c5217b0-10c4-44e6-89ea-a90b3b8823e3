/**
 * 现代化彩色图标样式
 * 为主题菜单提供彩色现代化图标支持
 * 
 * 功能：
 * 1. 彩色渐变图标
 * 2. 现代化设计风格
 * 3. 明暗主题适配
 * 4. 动画效果
 */

/* 现代图标基础样式 */
.modern-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.modern-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 50%);
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modern-icon:hover::before {
    opacity: 1;
}

.modern-icon:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 主页图标 - 蓝色渐变 */
.modern-icon.home {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.modern-icon.home:hover {
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);
}

/* 分类图标 - 橙色渐变 */
.modern-icon.category {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    box-shadow: 0 2px 8px rgba(245, 87, 108, 0.3);
}

.modern-icon.category:hover {
    box-shadow: 0 4px 16px rgba(245, 87, 108, 0.4);
}

/* 页面图标 - 绿色渐变 */
.modern-icon.page {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);
}

.modern-icon.page:hover {
    box-shadow: 0 4px 16px rgba(79, 172, 254, 0.4);
}

/* 搜索图标 - 紫色渐变 */
.modern-icon.search {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    box-shadow: 0 2px 8px rgba(168, 237, 234, 0.3);
}

.modern-icon.search:hover {
    box-shadow: 0 4px 16px rgba(168, 237, 234, 0.4);
}

/* 标签图标 - 黄色渐变 */
.modern-icon.tag {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    box-shadow: 0 2px 8px rgba(252, 182, 159, 0.3);
}

.modern-icon.tag:hover {
    box-shadow: 0 4px 16px rgba(252, 182, 159, 0.4);
}

/* 归档图标 - 青色渐变 */
.modern-icon.archive {
    background: linear-gradient(135deg, #a8caba 0%, #5d4e75 100%);
    box-shadow: 0 2px 8px rgba(168, 202, 186, 0.3);
}

.modern-icon.archive:hover {
    box-shadow: 0 4px 16px rgba(168, 202, 186, 0.4);
}

/* 关于图标 - 粉色渐变 */
.modern-icon.about {
    background: linear-gradient(135deg, #fbc2eb 0%, #a6c1ee 100%);
    box-shadow: 0 2px 8px rgba(251, 194, 235, 0.3);
}

.modern-icon.about:hover {
    box-shadow: 0 4px 16px rgba(251, 194, 235, 0.4);
}

/* 联系图标 - 红色渐变 */
.modern-icon.contact {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    box-shadow: 0 2px 8px rgba(255, 154, 158, 0.3);
}

.modern-icon.contact:hover {
    box-shadow: 0 4px 16px rgba(255, 154, 158, 0.4);
}

/* 友链图标 - 蓝绿渐变 */
.modern-icon.links {
    background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
    box-shadow: 0 2px 8px rgba(132, 250, 176, 0.3);
}

.modern-icon.links:hover {
    box-shadow: 0 4px 16px rgba(132, 250, 176, 0.4);
}

/* 深色主题适配 */
.mdui-theme-layout-dark .modern-icon {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
}

.mdui-theme-layout-dark .modern-icon:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.5);
}

/* 菜单项中的现代图标样式 */
.mdui-list-item .modern-icon {
    margin-right: 0;
    flex-shrink: 0;
}

/* 图标内的文字/符号 */
.modern-icon .icon-text {
    font-size: 14px;
    font-weight: 700;
    line-height: 1;
}

/* 特殊图标符号 */
.modern-icon.home .icon-text::before { content: "🏠"; }
.modern-icon.category .icon-text::before { content: "📚"; }
.modern-icon.page .icon-text::before { content: "📄"; }
.modern-icon.search .icon-text::before { content: "🔍"; }
.modern-icon.tag .icon-text::before { content: "🏷️"; }
.modern-icon.archive .icon-text::before { content: "📦"; }
.modern-icon.about .icon-text::before { content: "👤"; }
.modern-icon.contact .icon-text::before { content: "📧"; }
.modern-icon.links .icon-text::before { content: "🔗"; }

/* 备用文字图标（如果不支持emoji） */
.no-emoji .modern-icon.home .icon-text::before { content: "H"; }
.no-emoji .modern-icon.category .icon-text::before { content: "C"; }
.no-emoji .modern-icon.page .icon-text::before { content: "P"; }
.no-emoji .modern-icon.search .icon-text::before { content: "S"; }
.no-emoji .modern-icon.tag .icon-text::before { content: "T"; }
.no-emoji .modern-icon.archive .icon-text::before { content: "A"; }
.no-emoji .modern-icon.about .icon-text::before { content: "i"; }
.no-emoji .modern-icon.contact .icon-text::before { content: "M"; }
.no-emoji .modern-icon.links .icon-text::before { content: "L"; }

/* 动画效果 */
@keyframes iconPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.modern-icon.pulse {
    animation: iconPulse 2s ease-in-out infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .modern-icon {
        width: 22px;
        height: 22px;
        font-size: 14px;
    }
    
    .modern-icon .icon-text {
        font-size: 12px;
    }
}

/* 菜单项悬停效果增强 */
.mdui-list-item:hover .modern-icon {
    transform: translateY(-1px) scale(1.05);
}

/* 活动状态 */
.mdui-list-item.active .modern-icon {
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
}

.mdui-theme-layout-dark .mdui-list-item.active .modern-icon {
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2);
}
