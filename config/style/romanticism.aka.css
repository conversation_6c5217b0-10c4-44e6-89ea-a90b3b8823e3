/**

    _    _  __    _    ____  _   _ ___ 
   / \  | |/ /   / \  / ___|| | | |_ _|
  / _ \ | ' /   / _ \ \___ \| |_| || | 
 / ___ \| . \  / ___ \ ___) |  _  || | 
/_/   \_\_|\_\/_/   \_\____/|_| |_|___|

 * [Romanticism]
 * romanticism.aka.css 主样式表文件
 * @version 2.1
 * @link https://imakashi.eu.org/
**/
body{
    font-family: 'Noto Serif SC', serif;
    font-weight:400;
    font-display: swap;
}
html::-webkit-scrollbar {
    display: none;
}

.mdui-theme-layout-light .LDtrans{
    background-color: Seashell;
    transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out;
}
.mdui-theme-layout-dark .LDtrans{
    background-color: #303030;
}
.mdui-appbar{
    transition: background-color 0.3s ease-in-out;
}
.mdui-card{
    transition: all 0.3s ease-in-out;
}

/*高斯模糊效果*/
.blur{
    backdrop-filter: saturate(180%) blur(15px);
    -webkit-backdrop-filter: saturate(180%) blur(15px);
}
.mdui-theme-layout-light .blur{
    background: rgba(255, 255, 255, 0.72);
}
.mdui-theme-layout-dark .blur{
    background: rgba(58, 58, 58, 0.72);
}
.mdui-overlay{
    backdrop-filter: saturate(100%) blur(20px);
    -webkit-backdrop-filter: saturate(100%) blur(20px);
}
.mdui-tooltip, .mdui-snackbar{
    backdrop-filter: saturate(180%) blur(15px);
    -webkit-backdrop-filter: saturate(180%) blur(15px);
    box-shadow: 0 0 2px black; 
}
.mdui-theme-layout-light .mdui-tooltip{
    background: rgba(255, 255, 255, 0.72);
    color:#252525;
}
.mdui-theme-layout-dark .mdui-tooltip{
    background: rgba(69, 69, 69, 0.72);
    color:#f0f0f0;
}
.mdui-theme-layout-light .mdui-snackbar{
    background: rgba(255, 255, 255, 0.72);
    color:#252525;
    font-weight: bold;
}
.mdui-theme-layout-dark .mdui-snackbar{
    background: rgba(69, 69, 69, 0.72);
    color:#f0f0f0;
    font-weight: bold;
}

/*加载动画*/
#loading {
    width: 100%;
    height: 100%;
    font-size: 30px;
    margin: 0;
    text-align: center; 
    border-radius: 0; 
    position: fixed; 
    left: 0;
    right: 0;
    top: 0;
    z-index: 9999
}
.loader {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
}
.loader-box {
    transform: translateY(-50%);
    top: 50%;
    position: absolute;
    width: calc(100% - 200px);
    padding: 0 100px;
    text-align: center;
}
.loader-box .mdui-spinner{
    width: 50px;
    height: 50px;
}

/*圆角设置*/
.yuan, .mdui-snackbar{
    border-radius: 14px;
}
.btnyuan{
    border-radius: 8px;
}
.mdui-tooltip{
    border-radius: 8px;
}
@media(max-width:420px){
    .yuan, .mdui-snackbar{
        border-radius: 10px;
    }
    .btnyuan{
        border-radius: 6px;
    }
    .mdui-tooltip{
    border-radius: 8px;
    }
}

/*进入动画*/
.toup{
    animation-name: toup;
    animation-duration: .5s;
}
 @keyframes toup{
    from {filter: blur(20px);}
    to {filter: blur(0);}
}
.show{
    animation-name: show;
    animation-duration: .5s;
}
 @keyframes show{
    from {filter: blur(20px);}
    to {filter: blur(0);}
}

/*首页主题图*/
.indeximg{
    background-repeat:no-repeat; 
    background-position: center;
    background-size: cover;
    -webkit-background-size: cover;
    -o-background-size: cover;
}
.indeximgcard{
    width:auto; 
    height:85vh;
    min-height:420px;
}

/* 置顶容器 */
.sticky-container {
    position: relative;
    width: 100%;
    height:200px;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
}
.sticky-container::-webkit-scrollbar {
    height: 8px;
}
.sticky-container::-webkit-scrollbar-thumb {
    background: rgba(128, 128, 128, 0.3);
    border-radius: 5px;
}
.sticky-item {
    display: inline-block;
    width: 320px;
    height: 100%;
    margin-right: 8px;
    background-color: #f0f0f0;
    text-align: center;
}
.sticky-item .mdui-card-media-covered{
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
}
.sticky-item img{
    object-fit: cover;
    object-position: center;
    width: 320px;
    height:170px;
}
.sticky-item .mdui-card-primary {
    margin-top: -20px;
}
.sticky-item .mdui-card-primary-title{
    white-space: normal;
    word-wrap: break-word;
    word-break: break-word;
    overflow-wrap: break-word;
    line-height: 24px;
}
.sticky-item .mdui-card-primary-title h5{
    margin-bottom:5px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
}
.sticky-item .mdui-card-media-covered{
    height: 100%;
}

.sticky-wrapper {
    position: relative;
    width: 100%;
    isolation: isolate;
}
.sticky-badge {
    position: absolute;
    top: -10px;
    right: -10px;
    width: auto;
    min-width: 40px;
    height: 40px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 99;
    
}
.sticky-badge i{
    transform: rotate(-30deg);
}

@media (max-width:500px){
    .sticky-item {
        width: 200px;
    }
    .sticky-item img{
        width: 200px;
    }
}


/*主页文章列表容器*/
.indexlistbox{
    margin-top:-180px;
}
.indexlistbox h3{
    margin-top: 5px;
    margin-bottom: 5px;
}
.indexlistbox h4{
    margin-top: 5px;
    margin-bottom: 5px;
}
.indexlistbox h5{
    margin-top: 10px;
}
@media (max-width:420px){
    .indexlistbox{
        margin-top: -180px;
    }
}
@media (max-height:800px){
    .indexlistbox{
        margin-top: -150px;
    }
}

.indexinfobox{
    margin-bottom: 20px;
    padding: 10px;
    padding-top: 15px;
    padding-bottom: 15px;
    line-height: 20px;
}


/*主页文章列表卡片*/
.articlelistcard{
    width:auto; 
    height:auto;
    min-height:250px;
}
.articlelistimg{
    background-repeat:no-repeat; 
    background-position: center;
    background-size: cover;
    -webkit-background-size: cover;
    -o-background-size: cover;
}
.articlelistcard .mdui-card-primary-title{
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 4;
    overflow: hidden;
}
.articlelistcard .mdui-card-primary-title span{
    padding-left: 100px;
}
.articlelistcard .mdui-card-primary-title h4{
    line-height: 30px;
}
.articlesms .mdui-card-content{
    margin-top: -20px;
}
.mdui-theme-layout-dark .articlesms img{
    filter: brightness(80%);
}

.titlegap{
    letter-spacing: 2px;
    font-weight:900;
}

#mainsidebar .mdui-grid-tile{
    height: 170px;
    width: auto;
}
.sidebarimg{
    background-repeat:no-repeat; 
    background-position: center;
    background-size: cover;
    -webkit-background-size: cover;
    -o-background-size: cover;
}
#mainsidebar .headicon{
    position: absolute; 
    top: 15%; 
    left: 24px; 
    width: 60px; 
    height: 60px; 
    border: 3px solid rgb(255, 255, 255);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.53);
    border-radius: 50%;
}

/* 改进的无序列表样式 - 多级标识符 */
/* 使用最高特异性来强制覆盖所有默认样式 */

/* 重置所有列表样式 */
.mdui-typo ul,
.mdui-typo ul ul,
.mdui-typo ul ul ul,
.mdui-typo ul ul ul ul,
.mdui-typo div ul,
.mdui-typo div ul ul,
.mdui-typo div ul ul ul,
.mdui-typo div ul ul ul ul {
    list-style: none !important;
    list-style-type: none !important;
    padding-left: 2em !important;
    margin-left: 0 !important;
}

/* 重置所有列表项样式 - 更精确的选择器 */
.mdui-typo ul > li,
.mdui-typo ul ul > li,
.mdui-typo ul ul ul > li,
.mdui-typo ul ul ul ul > li,
.mdui-typo div ul > li,
.mdui-typo div ul ul > li,
.mdui-typo div ul ul ul > li,
.mdui-typo div ul ul ul ul > li {
    position: relative !important;
    padding-left: 1.5em !important;
    margin: 0.4em 0 !important;
    list-style: none !important;
    list-style-type: none !important;
    background: none !important;
}

/* 第一级：实心小圆点 - 使用最高特异性和精确选择器 */
.mdui-typo ul > li::before,
.mdui-typo div ul > li::before,
body .mdui-typo ul > li::before {
    content: "●" !important;
    position: absolute !important;
    left: 0 !important;
    top: 0.1em !important;
    color: currentColor !important;
    font-size: 0.8em !important;
    line-height: 1.5 !important;
    font-weight: bold !important;
    display: block !important;
    width: 1em !important;
    text-align: center !important;
    z-index: 1 !important;
    height: 1.2em !important;
}

/* 第二级：空心小圆点 - 使用最高特异性和精确选择器 */
.mdui-typo ul > li ul > li::before,
.mdui-typo div ul > li ul > li::before,
body .mdui-typo ul > li ul > li::before {
    content: "○" !important;
    font-weight: normal !important;
    font-size: 0.9em !important;
    top: 0.1em !important;
    line-height: 1.5 !important;
    height: 1.2em !important;
}

/* 第三级：空心小方块 - 使用最高特异性和精确选择器 */
.mdui-typo ul > li ul > li ul > li::before,
.mdui-typo div ul > li ul > li ul > li::before,
body .mdui-typo ul > li ul > li ul > li::before {
    content: "□" !important;
    font-weight: normal !important;
    font-size: 0.8em !important;
    top: 0.1em !important;
    line-height: 1.5 !important;
    height: 1.2em !important;
}

/* 第四级及以后：回到实心圆点 - 使用最高特异性和精确选择器 */
.mdui-typo ul > li ul > li ul > li ul > li::before,
.mdui-typo div ul > li ul > li ul > li ul > li::before,
body .mdui-typo ul > li ul > li ul > li ul > li::before {
    content: "●" !important;
    font-weight: bold !important;
    font-size: 0.8em !important;
    top: 0.1em !important;
    line-height: 1.5 !important;
    height: 1.2em !important;
}

/* 确保嵌套列表的间距 - 使用最高特异性 */
.mdui-typo li ul,
.mdui-typo li ol,
.mdui-typo div li ul,
.mdui-typo div li ol,
body .mdui-typo li ul,
body .mdui-typo li ol {
    margin: 0.5em 0 !important;
    padding-left: 2em !important;
    list-style: none !important;
    list-style-type: none !important;
}

/* 深色主题下的列表标识符颜色 - 使用最高特异性 */
.mdui-theme-layout-dark .mdui-typo ul li::before,
.mdui-theme-layout-dark .mdui-typo div ul li::before,
body.mdui-theme-layout-dark .mdui-typo ul li::before {
    color: #abb2bf !important;
}

.mdui-theme-layout-light .mdui-typo ul li::before,
.mdui-theme-layout-light .mdui-typo div ul li::before,
body.mdui-theme-layout-light .mdui-typo ul li::before {
    color: #2c3e50 !important;
}

/* 强制覆盖MDUI的默认列表样式 */
.mdui-typo li ul,
.mdui-typo div li ul,
body .mdui-typo li ul {
    list-style: none !important;
    list-style-type: none !important;
}

/* 额外的强制样式重置 */
.mdui-typo ul li,
.mdui-typo div ul li,
body .mdui-typo ul li {
    list-style-image: none !important;
    list-style-position: outside !important;
}

/* 当JavaScript增强器添加了自定义标识符时，隐藏CSS伪元素以避免重复 */
.mdui-typo ul li.list-enhanced:has(.custom-bullet)::before,
.mdui-typo div ul li.list-enhanced:has(.custom-bullet)::before,
body .mdui-typo ul li.list-enhanced:has(.custom-bullet)::before {
    display: none !important;
    content: none !important;
}

/* 备用方案：如果浏览器不支持:has()，使用类名控制 */
.mdui-typo ul li.js-bullet-active::before,
.mdui-typo div ul li.js-bullet-active::before,
body .mdui-typo ul li.js-bullet-active::before {
    display: none !important;
    content: none !important;
}

/* 确保JavaScript生成的标识符有正确的样式 */
.mdui-typo ul li .custom-bullet,
.mdui-typo div ul li .custom-bullet,
body .mdui-typo ul li .custom-bullet {
    position: absolute !important;
    left: 0 !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    width: 1em !important;
    height: 1em !important;
    text-align: center !important;
    font-weight: bold !important;
    z-index: 1 !important;
    pointer-events: none !important;
    user-select: none !important;
    line-height: 1 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* 保持原有的通用li样式，但仅适用于非.mdui-typo内的li */
li:not(.mdui-typo li) {
    list-style: none;
}

/* 终极解决方案：使用最高优先级强制覆盖 */
html body .mdui-typo ul,
html body .mdui-typo ul ul,
html body .mdui-typo ul ul ul,
html body .mdui-typo ul ul ul ul {
    list-style: none !important;
    list-style-type: none !important;
    list-style-image: none !important;
    list-style-position: outside !important;
}

html body .mdui-typo ul li,
html body .mdui-typo ul ul li,
html body .mdui-typo ul ul ul li,
html body .mdui-typo ul ul ul ul li {
    list-style: none !important;
    list-style-type: none !important;
    list-style-image: none !important;
    position: relative !important;
    padding-left: 1.5em !important;
    margin: 0.4em 0 !important;
    line-height: 1.6 !important;
    min-height: 1.2em !important;
}

/* 使用属性选择器确保样式应用 */
.mdui-typo ul[style*="list-style"] {
    list-style: none !important;
}

.mdui-typo ul li[style*="list-style"] {
    list-style: none !important;
}

/* 防止非列表元素显示标识符 */
.mdui-typo p::before,
.mdui-typo div::before,
.mdui-typo span::before,
.mdui-typo h1::before,
.mdui-typo h2::before,
.mdui-typo h3::before,
.mdui-typo h4::before,
.mdui-typo h5::before,
.mdui-typo h6::before,
.mdui-typo blockquote::before,
.mdui-typo pre::before,
.mdui-typo code::before {
    content: none !important;
    display: none !important;
}

/* 确保只有真正的列表项才有标识符 */
.mdui-typo *:not(ul > li)::before {
    content: none !important;
}

/* 特殊处理：当列表项包含代码块时，调整标识符位置 */
.mdui-typo ul > li:has(pre)::before,
.mdui-typo ul > li:has(code)::before {
    top: 0 !important;
    margin-top: 0.2em !important;
}

/* 对于不支持:has()的浏览器，使用JavaScript处理 */
.mdui-typo ul > li.has-code-block::before {
    top: 0 !important;
    margin-top: 0.2em !important;
}

/* 确保列表项内的代码块不影响标识符位置 */
.mdui-typo ul > li pre,
.mdui-typo ul > li code {
    margin-top: 0.5em !important;
}

/* 列表项内第一个元素如果是文本，确保标识符对齐 */
.mdui-typo ul > li > *:first-child {
    margin-top: 0 !important;
}

/* 更精确的标识符定位 - 基于第一行文本 */
.mdui-typo ul > li::before {
    /* 使用line-height来确保与文本第一行对齐 */
    top: 0.05em !important;
    line-height: 1.6 !important;
}

/* 当列表项以文本开始时的特殊处理 */
.mdui-typo ul > li:not(.has-code-block)::before {
    top: 0.1em !important;
}

/* 当列表项包含代码块时的特殊处理 */
.mdui-typo ul > li.has-code-block::before {
    top: 0.05em !important;
    position: absolute !important;
    z-index: 10 !important;
}

/* 超链接颜色设置 - 蓝色未访问，紫色已访问 */
a {
    text-decoration: none;
}

a:hover {
    text-decoration: none;
}

/* 覆盖MDUI默认的超链接颜色 - 使用最高特异性 */
html body .mdui-typo a,
html body .mdui-typo a:link,
body .mdui-typo a,
body .mdui-typo a:link,
.mdui-typo a,
.mdui-typo a:link {
    color: #2196F3 !important; /* 蓝色 - 未访问 */
}

html body .mdui-typo a:visited,
body .mdui-typo a:visited,
.mdui-typo a:visited {
    color: #9C27B0 !important; /* 紫色 - 已访问 */
}

html body .mdui-typo a:hover,
html body .mdui-typo a:focus,
body .mdui-typo a:hover,
body .mdui-typo a:focus,
.mdui-typo a:hover,
.mdui-typo a:focus {
    color: #1976D2 !important; /* 深蓝色 - 悬停 */
}

html body .mdui-typo a:visited:hover,
body .mdui-typo a:visited:hover,
.mdui-typo a:visited:hover {
    color: #7B1FA2 !important; /* 深紫色 - 已访问且悬停 */
}

/* 确保下划线颜色与文字颜色一致 */
html body .mdui-typo a::before,
body .mdui-typo a::before,
.mdui-typo a::before {
    background-color: currentColor !important;
}

/* 覆盖所有可能的MDUI主题颜色 */
html body .mdui-theme-accent-pink .mdui-typo a,
html body .mdui-theme-accent-red .mdui-typo a,
html body .mdui-theme-accent-purple .mdui-typo a,
html body .mdui-theme-accent-deep-purple .mdui-typo a,
html body .mdui-theme-accent-indigo .mdui-typo a,
html body .mdui-theme-accent-blue .mdui-typo a,
html body .mdui-theme-accent-light-blue .mdui-typo a,
html body .mdui-theme-accent-cyan .mdui-typo a,
html body .mdui-theme-accent-teal .mdui-typo a,
html body .mdui-theme-accent-green .mdui-typo a,
html body .mdui-theme-accent-light-green .mdui-typo a,
html body .mdui-theme-accent-lime .mdui-typo a,
html body .mdui-theme-accent-yellow .mdui-typo a,
html body .mdui-theme-accent-amber .mdui-typo a,
html body .mdui-theme-accent-orange .mdui-typo a,
html body .mdui-theme-accent-deep-orange .mdui-typo a,
body .mdui-theme-accent-pink .mdui-typo a,
body .mdui-theme-accent-red .mdui-typo a,
body .mdui-theme-accent-purple .mdui-typo a,
body .mdui-theme-accent-deep-purple .mdui-typo a,
body .mdui-theme-accent-indigo .mdui-typo a,
body .mdui-theme-accent-blue .mdui-typo a,
body .mdui-theme-accent-light-blue .mdui-typo a,
body .mdui-theme-accent-cyan .mdui-typo a,
body .mdui-theme-accent-teal .mdui-typo a,
body .mdui-theme-accent-green .mdui-typo a,
body .mdui-theme-accent-light-green .mdui-typo a,
body .mdui-theme-accent-lime .mdui-typo a,
body .mdui-theme-accent-yellow .mdui-typo a,
body .mdui-theme-accent-amber .mdui-typo a,
body .mdui-theme-accent-orange .mdui-typo a,
body .mdui-theme-accent-deep-orange .mdui-typo a {
    color: #2196F3 !important; /* 强制蓝色 - 未访问 */
}

/* 深色主题下的超链接颜色 - 使用最高特异性 */
html body.mdui-theme-layout-dark .mdui-typo a,
html body.mdui-theme-layout-dark .mdui-typo a:link,
body.mdui-theme-layout-dark .mdui-typo a,
body.mdui-theme-layout-dark .mdui-typo a:link,
.mdui-theme-layout-dark .mdui-typo a,
.mdui-theme-layout-dark .mdui-typo a:link {
    color: #64B5F6 !important; /* 浅蓝色 - 未访问 */
}

html body.mdui-theme-layout-dark .mdui-typo a:visited,
body.mdui-theme-layout-dark .mdui-typo a:visited,
.mdui-theme-layout-dark .mdui-typo a:visited {
    color: #CE93D8 !important; /* 浅紫色 - 已访问 */
}

html body.mdui-theme-layout-dark .mdui-typo a:hover,
html body.mdui-theme-layout-dark .mdui-typo a:focus,
body.mdui-theme-layout-dark .mdui-typo a:hover,
body.mdui-theme-layout-dark .mdui-typo a:focus,
.mdui-theme-layout-dark .mdui-typo a:hover,
.mdui-theme-layout-dark .mdui-typo a:focus {
    color: #42A5F5 !important; /* 中蓝色 - 悬停 */
}

html body.mdui-theme-layout-dark .mdui-typo a:visited:hover,
body.mdui-theme-layout-dark .mdui-typo a:visited:hover,
.mdui-theme-layout-dark .mdui-typo a:visited:hover {
    color: #BA68C8 !important; /* 中紫色 - 已访问且悬停 */
}

/* 深色主题下覆盖所有可能的MDUI主题颜色 */
html body.mdui-theme-layout-dark .mdui-theme-accent-pink .mdui-typo a,
html body.mdui-theme-layout-dark .mdui-theme-accent-red .mdui-typo a,
html body.mdui-theme-layout-dark .mdui-theme-accent-purple .mdui-typo a,
html body.mdui-theme-layout-dark .mdui-theme-accent-deep-purple .mdui-typo a,
html body.mdui-theme-layout-dark .mdui-theme-accent-indigo .mdui-typo a,
html body.mdui-theme-layout-dark .mdui-theme-accent-blue .mdui-typo a,
html body.mdui-theme-layout-dark .mdui-theme-accent-light-blue .mdui-typo a,
html body.mdui-theme-layout-dark .mdui-theme-accent-cyan .mdui-typo a,
html body.mdui-theme-layout-dark .mdui-theme-accent-teal .mdui-typo a,
html body.mdui-theme-layout-dark .mdui-theme-accent-green .mdui-typo a,
html body.mdui-theme-layout-dark .mdui-theme-accent-light-green .mdui-typo a,
html body.mdui-theme-layout-dark .mdui-theme-accent-lime .mdui-typo a,
html body.mdui-theme-layout-dark .mdui-theme-accent-yellow .mdui-typo a,
html body.mdui-theme-layout-dark .mdui-theme-accent-amber .mdui-typo a,
html body.mdui-theme-layout-dark .mdui-theme-accent-orange .mdui-typo a,
html body.mdui-theme-layout-dark .mdui-theme-accent-deep-orange .mdui-typo a,
body.mdui-theme-layout-dark .mdui-theme-accent-pink .mdui-typo a,
body.mdui-theme-layout-dark .mdui-theme-accent-red .mdui-typo a,
body.mdui-theme-layout-dark .mdui-theme-accent-purple .mdui-typo a,
body.mdui-theme-layout-dark .mdui-theme-accent-deep-purple .mdui-typo a,
body.mdui-theme-layout-dark .mdui-theme-accent-indigo .mdui-typo a,
body.mdui-theme-layout-dark .mdui-theme-accent-blue .mdui-typo a,
body.mdui-theme-layout-dark .mdui-theme-accent-light-blue .mdui-typo a,
body.mdui-theme-layout-dark .mdui-theme-accent-cyan .mdui-typo a,
body.mdui-theme-layout-dark .mdui-theme-accent-teal .mdui-typo a,
body.mdui-theme-layout-dark .mdui-theme-accent-green .mdui-typo a,
body.mdui-theme-layout-dark .mdui-theme-accent-light-green .mdui-typo a,
body.mdui-theme-layout-dark .mdui-theme-accent-lime .mdui-typo a,
body.mdui-theme-layout-dark .mdui-theme-accent-yellow .mdui-typo a,
body.mdui-theme-layout-dark .mdui-theme-accent-amber .mdui-typo a,
body.mdui-theme-layout-dark .mdui-theme-accent-orange .mdui-typo a,
body.mdui-theme-layout-dark .mdui-theme-accent-deep-orange .mdui-typo a {
    color: #64B5F6 !important; /* 强制浅蓝色 - 深色主题未访问 */
}

::-moz-selection{background:#ecf0f1; color:CornflowerBlue;}
::selection {background:#ecf0f1; color:CornflowerBlue;}

.chameleon {
    transition: color 0.3s ease-in-out;
}

.chameleon:hover {
    color: CornflowerBlue;
}
.akarom-alter-button-valign{
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
}
.akarom-alter-button{
    border: 2px solid rgba(158, 158, 158, 0.3);
    padding:6px;
    transition: all 0.3s ease-in-out;
    background: none;
    padding-left: 10px;
    padding-right: 12px;
}
.akarom-alter-button:hover{
    filter: blur(2px);
    transform: scale(1.01);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.3);
    position: relative;
}
.akarom-alter-button-disabled{
    opacity: .5;
}
.akarom-alter-button-disabled:hover{
    filter: none;
    transform: none;
    box-shadow: none;
    position: none;
}
.akarom-alter-button i{
    margin-bottom: 3px;
    margin-right: 8px;
    opacity: .8;
}
.akarom-hoverable{
    padding: 6px;
    transition: all 0.3s ease-in-out;
    border-radius: 6px;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
}
.akarom-hoverable:hover{
    background-color: rgba(158, 158, 158, 0.2);
}

.title{
    font-family: 'Noto Serif SC', serif;
    font-weight:900;
    text-decoration: none;
}

/* 页脚版权信息样式 */
.title.footer-copyright {
    font-size: 16px !important;
    line-height: 1.8 !important;
    padding: 20px 0 !important;
}

/* PC端：隐藏移动端换行，显示分隔符 */
@media (min-width: 768px) {
    .footer-mobile-break {
        display: none;
    }
    .footer-separator {
        display: inline;
    }
}

/* 移动端：显示换行，隐藏分隔符 */
@media (max-width: 767px) {
    .footer-mobile-break {
        display: inline;
    }
    .footer-separator {
        display: none;
    }
}

.subtitle{
    font-size: 15px;
}

.underline{
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease-in-out;
}

.underline:hover {
    border-bottom: 2px solid;
}

.easysee{
    text-shadow: 0 1px 3px black; 
}

/*上下翻页颜色*/
.mdui-theme-layout-light .prev{
    color:#252525;
}
.mdui-theme-layout-dark .prev{
    color:white;
}

.mdui-theme-layout-light .next{
    color:#252525;
}
.mdui-theme-layout-dark .next{
    color:white;
}

/*独立页面主题图*/
.articlecard{
    width:auto; 
    height:auto;
    display: block;
    min-height: 600px;
    padding-top: 20px;
    color: white;
    position: relative;
    background: none;
}
.mdui-theme-layout-dark .articlecard{
    background: none;
}
.articlecard .articlecardimg{
    background-repeat:no-repeat; 
    background-position: center;
    background-size: cover;
    -webkit-background-size: cover;
    -o-background-size: cover;
    width: 100%;
    height: 100%;
    position: absolute;
    top:0;
    left: 0;
    z-index: -2;
}
.articlecard .articlecardshadow{
    background-color: #252525;
    width: 100%;
    height: 100%;
    position: absolute;
    top:0;
    left: 0;
    z-index: -1;
    opacity: .2;
}
@media (max-width:420px){
    .articlecard{
        min-height: 400px;
    }
}
.articlecard .mdui-card-primary-title{
    line-height: 45px;
}

/*文章内排版格式*/
p {
    font-size: 18px;
}

.mdui-typo {
    font-size: 18px;
}

.mdui-typo p,
.mdui-typo li,
.mdui-typo td,
.mdui-typo th,
.mdui-typo blockquote {
    font-size: inherit;
}
.mdui-typo h1{
    position: relative;
    white-space: nowrap;
    font-weight:900;
    z-index:9 !important;
}
.mdui-typo h1::before {
    content: "";
    position: absolute;
    height: 14px;
    width: 66px;
    bottom: 2px;
    z-index:-1;
    background-color: CornflowerBlue;
    opacity: 0.5;
    transform: skew(-35deg);
    transition: opacity .2s ease-in-out;
    border-radius: 3px 8px 10px 6px;
    transition: 0.1s ease background-color;
}
.mdui-typo h3{
     font-weight:900;
}
.mdui-typo h4{
     font-weight:900;
}
.mdui-typo h2{
    position: relative;
    white-space: nowrap;
    font-weight:900;
    z-index:9 !important;
}
.mdui-typo h2::before {
    content: "";
    position: absolute;
    height: 12px;
    width: 66px;
    bottom: 0;
    z-index:-1;
    background-color: lightblue;
    opacity: 0.5;
    transform: skew(-35deg);
    transition: opacity .2s ease-in-out;
    border-radius: 3px 8px 10px 6px;
    transition: 0.1s ease background-color;
}
.mdui-typo h3{
    font-weight:900;
}
.article hr{
    width:66%;
    border:none;
    border-top:6px dashed rgba(158, 158, 158, 0.2);;
}
.hr hr{
    width:100%;
    margin: 0 auto; 
    border:none;
    border-top:2px dashed rgba(158, 158, 158, 0.2);;
}
.article img{
    height:anto;
    width:100%;
}
.mdui-theme-layout-dark .article img{
    filter: brightness(80%);
}
/* Markdown 表格样式 */
.mdui-typo table{
    border-collapse: collapse;
    width: 100%;
}
.mdui-typo table th,
.mdui-typo table td{
    border: 1px solid rgba(158,158,158,0.35);
    padding: 8px 10px;
}
.mdui-theme-layout-dark .mdui-typo table th,
.mdui-theme-layout-dark .mdui-typo table td{
    border-color: rgba(255,255,255,0.18);
}
.mdui-typo table thead th{
    background: rgba(0,0,0,0.03);
}
.mdui-theme-layout-dark .mdui-typo table thead th{
    background: rgba(255,255,255,0.06);
}
.mdui-theme-layout-dark .mdui-panel-item{
    background-color:#3c3c3c;
}
.copyright{
    font-size: 14px;
}

#comments .headicon{
    border: 2px solid white;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.53);
}
#comments .adminsign{
    display:inline-block;
    padding: 0;
    padding-left:3px;
    padding-right:3px;
    margin-left: 8px;
    font-size: 13px;
    border: 3px solid rgba(136, 132, 132, 0.3);
}
#comments .comment-parent{
    margin-top: 30px;
}

.fancybox {
    text-decoration: none;
}

.outlineborder{
    padding:10px;
    border-radius:10px;
    display:inline-block;
    border: 2px solid rgba(136, 132, 132, 0.199);
    line-height: 25px;
    margin-bottom: 5px;
}

.link img{
    height:70%;
    width:70%;
    border: 5px solid rgb(255, 255, 255);
}

.comment-ua-second{
    margin-right: -5px;
}

/* 文章tag筛选样式 */
.akarom-articletag{
    position: fixed; 
    bottom: 30px; 
    left: 30px; 
    opacity: 1;
    z-index: 99;
    width:40px;
    transition: all 0.3s ease-in-out;
    height:40px;
    border-radius: 20px;
    overflow:hidden;
    display: flex;
    align-items: center;
    cursor: pointer;
}
.akarom-articletag:hover{
    width:205px;
}
.akarom-articletag .mdui-icon{
    padding-left: 8px;
}

.akarom-articletag-options{
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    font-size: 16px;
    white-space: nowrap;
    transform: translateX(-100%);
    opacity: 0;
    filter: blur(15px);
    transition: all 0.45s ease-in-out;
}
.akarom-articletag:hover .akarom-articletag-options{
    transform: translateX(0);
    opacity: 1;
    filter: blur(0);
    margin-left: 8px;
}
.akarom-articletag input[type="radio"] {
    display: none;
}

.akarom-articletag input[type="radio"]:checked + .filter-btn {
    color: #6495ed;
    font-weight: bold;
}
@media (max-width:1024px){
    .akarom-articletag{
        bottom: 15px; 
        left: 15px; 
    }
}
.tagnotice{
    text-shadow: 0 0 3px #fff;
}
.taglist,
.tagnotice{
    display: none; 
    animation: tagfadein 0.3s;
}
/* 首页文章筛选 */
body:has(#filterall:checked) .LDtrans .taglist,
body:has(#filterarticle:checked) .LDtrans .taglist.tagarticle,
body:has(#filtersms:checked) .LDtrans .taglist.tagsms{
    display: block;
    animation: tagfadein 0.3s;
}
body:has(#filterarticle:checked) .LDtrans .tagnotice-article,
body:has(#filtersms:checked) .LDtrans .tagnotice-sms{
    display: block;
    animation: tagfadein 0.3s;
}
@keyframes tagfadein {
  from { opacity: 0; filter:blur(20px);}
  to { opacity: 1; filter:blur(0px);}
}

.notfoundpage{
    height:80vh;
}
.notfound-box{
    height:60vh;
    width:auto;
}
.notfoundpage span{
    opacity: .1;
    font-size: 114px;
    position:absolute;
    margin-top:-50px;
    font-weight: bold;
}

.akarom-rewardbox{
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    height: auto;
    width: auto;
}
.akarom-rewardbox img{
    max-height: 78vh;
    margin: 0 auto;
    display: block;
}

/*aplayer适配深色模式*/
.mdui-theme-layout-light .aplayer{
    background-color: #ffffff;
    transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out;
}
.mdui-theme-layout-dark .aplayer{
    background-color: #404040;
}
.aplayer{
    border-radius:10px;
}
.aplayer-lrc::before{
    opacity: 0.01;
}
.mdui-theme-layout-dark .aplayer-lrc::after{
     background: linear-gradient(rgba(235, 229, 229, 0) 0px, rgba(59, 59, 59, 0.8));
}
/*aplayer适配深色模式*/

/*musenxi livephoto*/
.lpk-live-photo-player{
    border-radius:8px;
    overflow: hidden;
}
/*musenxi livephoto*/

/* 右侧浮动目录样式 */
.article-toc-fixed {
  position: fixed;
  top: 100px;
  right: 40px;
  min-width: 200px;
  max-width: 320px;
  width: auto;
  max-height: 60vh;
  overflow-y: auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
  z-index: 100;
  padding: 18px 20px 12px 20px;
  transition: all 0.3s ease;
}

.mdui-theme-layout-dark .article-toc-fixed {
  background: #232323;
  color: #eee;
}

/* 隐藏状态 */
.article-toc-fixed.toc-hidden {
  transform: translateX(calc(100% - 40px));
}
.mdui-theme-layout-dark .article-toc-list li.active > a {
  color: #66bb6a;
}
.mdui-theme-layout-dark .article-toc-list li > a:hover {
  color: #81c784;
  text-decoration: underline;
  font-weight: bold;
}
.article-toc-title {
  font-weight: bold;
  font-size: 18px;
  margin-bottom: 10px;
  cursor: pointer;
  user-select: none;
  display: flex;
  align-items: center;
}

/* 隐藏按钮样式 */
.toc-hide-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  margin-left: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
  color: #666;
}

.toc-hide-btn:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: #333;
}

.mdui-theme-layout-dark .toc-hide-btn {
  color: #ccc;
}

.mdui-theme-layout-dark .toc-hide-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
}
.article-toc-list {
  list-style: none;
  margin: 0;
  padding-left: 0;
}
.article-toc-list li {
  margin: 4px 0;
  padding-left: 10px;
  transition: font-weight 0.2s, color 0.2s;
}
.article-toc-list li.active > a {
  font-weight: bold;
  color: #4caf50;
}
.article-toc-list li > a {
  color: #2196f3;
  text-decoration: none;
  transition: color 0.2s;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  line-height: 1.4;
}
.article-toc-list li > a:hover {
  color: #66bb6a;
  text-decoration: underline;
  font-weight: bold;
}
.article-toc-collapsed .article-toc-list {
  display: none;
}
/* 当屏幕宽度小于1200px时隐藏目录，避免遮挡文章内容 */
@media (max-width: 1200px) {
  .article-toc-fixed {
    display: none;
  }
}

/* 移动端文章内容字体优化 */
@media (max-width: 767px) {
  .article.mdui-typo {
    font-size: 14px;
    line-height: 1.6;
  }

  .article.mdui-typo h1 {
    font-size: 24px;
  }

  .article.mdui-typo h2 {
    font-size: 20px;
  }

  .article.mdui-typo h3 {
    font-size: 18px;
  }

  .article.mdui-typo h4 {
    font-size: 16px;
  }

  .article.mdui-typo h5,
  .article.mdui-typo h6 {
    font-size: 14px;
  }

  /* 防止页面水平滚动 */
  body {
    overflow-x: hidden;
  }

  .mdui-container {
    max-width: 100%;
    padding-left: 15px;
    padding-right: 15px;
  }
}