/**
 * 现代化图标增强器
 * 为主题菜单的现代化图标提供增强功能
 * 
 * 功能：
 * 1. 智能图标类型检测
 * 2. 动态图标分配
 * 3. 主题适配
 * 4. 动画效果
 */

(function() {
    'use strict';
    
    // 等待DOM加载完成
    function initModernIcons() {
        console.log('ModernIcons: Initializing modern icon enhancer...');
        enhanceIcons();
        
        // 监听主题切换事件
        document.addEventListener('themeChanged', function() {
            setTimeout(updateIconTheme, 100);
        });
        
        // 监听DOM变化
        if (typeof MutationObserver !== 'undefined') {
            const observer = new MutationObserver(function(mutations) {
                let shouldUpdate = false;
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        // 检查是否有新的菜单项
                        mutation.addedNodes.forEach(function(node) {
                            if (node.nodeType === 1 && (node.classList.contains('mdui-list-item') || node.querySelector('.mdui-list-item'))) {
                                shouldUpdate = true;
                            }
                        });
                    }
                });
                
                if (shouldUpdate) {
                    setTimeout(enhanceIcons, 100);
                }
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
    }
    
    function enhanceIcons() {
        console.log('ModernIcons: Starting to enhance icons...');
        
        // 智能检测页面类型并分配图标
        detectAndAssignPageIcons();
        
        // 添加交互效果
        addInteractiveEffects();
        
        // 检测emoji支持
        detectEmojiSupport();
        
        console.log('ModernIcons: Icon enhancement completed');
    }
    
    function detectAndAssignPageIcons() {
        // 查找所有页面链接，智能分配图标类型
        const pageLinks = document.querySelectorAll('.mdui-list-item');
        
        pageLinks.forEach(function(link) {
            const iconElement = link.querySelector('.modern-icon.page');
            if (!iconElement) return;
            
            const titleElement = link.querySelector('.mdui-list-item-content');
            if (!titleElement) return;
            
            const title = titleElement.textContent.toLowerCase().trim();
            
            // 根据页面标题智能分配图标类型
            let iconType = 'page'; // 默认
            
            if (title.includes('关于') || title.includes('about')) {
                iconType = 'about';
            } else if (title.includes('联系') || title.includes('contact')) {
                iconType = 'contact';
            } else if (title.includes('友链') || title.includes('友情链接') || title.includes('links')) {
                iconType = 'links';
            } else if (title.includes('归档') || title.includes('archive')) {
                iconType = 'archive';
            } else if (title.includes('标签') || title.includes('tag')) {
                iconType = 'tag';
            }
            
            // 更新图标类型
            if (iconType !== 'page') {
                iconElement.classList.remove('page');
                iconElement.classList.add(iconType);
                console.log(`ModernIcons: Assigned ${iconType} icon to "${title}"`);
            }
        });
    }
    
    function addInteractiveEffects() {
        // 为所有现代图标添加交互效果
        const modernIcons = document.querySelectorAll('.modern-icon');
        
        modernIcons.forEach(function(icon) {
            // 添加点击波纹效果
            icon.addEventListener('click', function(e) {
                createRippleEffect(icon, e);
            });
            
            // 添加长按效果
            let pressTimer;
            icon.addEventListener('mousedown', function() {
                pressTimer = setTimeout(function() {
                    icon.classList.add('pulse');
                    setTimeout(function() {
                        icon.classList.remove('pulse');
                    }, 2000);
                }, 500);
            });
            
            icon.addEventListener('mouseup', function() {
                clearTimeout(pressTimer);
            });
            
            icon.addEventListener('mouseleave', function() {
                clearTimeout(pressTimer);
            });
        });
    }
    
    function createRippleEffect(element, event) {
        const ripple = document.createElement('span');
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;
        
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s ease-out;
            pointer-events: none;
        `;
        
        // 添加ripple动画CSS（如果还没有）
        if (!document.querySelector('#ripple-animation')) {
            const style = document.createElement('style');
            style.id = 'ripple-animation';
            style.textContent = `
                @keyframes ripple {
                    to {
                        transform: scale(2);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }
        
        element.style.position = 'relative';
        element.style.overflow = 'hidden';
        element.appendChild(ripple);
        
        setTimeout(function() {
            ripple.remove();
        }, 600);
    }
    
    function detectEmojiSupport() {
        // 检测浏览器是否支持emoji
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        if (!ctx) {
            document.body.classList.add('no-emoji');
            return;
        }
        
        canvas.width = 20;
        canvas.height = 20;
        
        ctx.fillStyle = '#000';
        ctx.textBaseline = 'middle';
        ctx.textAlign = 'center';
        ctx.font = '16px Arial';
        
        // 测试绘制emoji
        ctx.fillText('🏠', 10, 10);
        const imageData = ctx.getImageData(0, 0, 20, 20).data;
        
        // 检查是否有非透明像素（表示emoji被渲染）
        let hasPixels = false;
        for (let i = 3; i < imageData.length; i += 4) {
            if (imageData[i] > 0) {
                hasPixels = true;
                break;
            }
        }
        
        if (!hasPixels) {
            document.body.classList.add('no-emoji');
            console.log('ModernIcons: Emoji not supported, using fallback text icons');
        } else {
            console.log('ModernIcons: Emoji support detected');
        }
    }
    
    function updateIconTheme() {
        // 主题切换时更新图标样式
        const isDark = document.body.classList.contains('mdui-theme-layout-dark');
        const modernIcons = document.querySelectorAll('.modern-icon');
        
        modernIcons.forEach(function(icon) {
            // 可以在这里添加主题特定的样式调整
            if (isDark) {
                icon.style.filter = 'brightness(0.9)';
            } else {
                icon.style.filter = 'brightness(1)';
            }
        });
        
        console.log('ModernIcons: Updated theme for', modernIcons.length, 'icons');
    }
    
    // 添加特殊页面的图标动画
    function addSpecialAnimations() {
        // 为主页图标添加特殊动画
        const homeIcon = document.querySelector('.modern-icon.home');
        if (homeIcon) {
            homeIcon.addEventListener('mouseenter', function() {
                this.style.animation = 'iconPulse 1s ease-in-out';
            });
            
            homeIcon.addEventListener('animationend', function() {
                this.style.animation = '';
            });
        }
        
        // 为搜索图标添加特殊效果
        const searchIcons = document.querySelectorAll('.modern-icon.search');
        searchIcons.forEach(function(icon) {
            icon.addEventListener('click', function() {
                this.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 200);
            });
        });
    }
    
    // 导出函数供外部调用
    window.ModernIcons = {
        init: initModernIcons,
        enhance: enhanceIcons,
        updateTheme: updateIconTheme,
        addAnimations: addSpecialAnimations
    };
    
    // 自动初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            console.log('ModernIcons: DOM loaded, initializing...');
            initModernIcons();
            addSpecialAnimations();
        });
    } else {
        console.log('ModernIcons: DOM already loaded, initializing immediately...');
        initModernIcons();
        addSpecialAnimations();
    }
    
    // 监听主题切换事件
    document.addEventListener('themeChanged', updateIconTheme);
    
})();
