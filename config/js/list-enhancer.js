/**
 * 列表增强器
 * 用于确保无序列表的多级标识符正确显示
 * 
 * 功能：
 * 1. 强制应用自定义列表样式
 * 2. 移除默认的list-style
 * 3. 添加自定义的伪元素标识符
 */

(function() {
    'use strict';
    
    // 等待DOM加载完成
    function initListEnhancer() {
        console.log('ListEnhancer: Initializing list enhancer...');
        enhanceLists();
        
        // 监听主题切换事件
        document.addEventListener('themeChanged', function() {
            setTimeout(enhanceLists, 100);
        });
        
        // 监听DOM变化
        if (typeof MutationObserver !== 'undefined') {
            const observer = new MutationObserver(function(mutations) {
                let shouldUpdate = false;
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        // 检查是否有新的列表元素
                        mutation.addedNodes.forEach(function(node) {
                            if (node.nodeType === 1 && (node.tagName === 'UL' || node.querySelector('ul'))) {
                                shouldUpdate = true;
                            }
                        });
                    }
                });
                
                if (shouldUpdate) {
                    setTimeout(enhanceLists, 100);
                }
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
    }
    
    function enhanceLists() {
        console.log('ListEnhancer: Starting to enhance lists...');

        // 首先清理可能存在的重复标识符
        cleanupDuplicateBullets();

        // 查找所有在.mdui-typo容器内的无序列表，使用更精确的选择器
        const containers = document.querySelectorAll('.mdui-typo, .article');
        let totalLists = 0;
        let totalItems = 0;

        containers.forEach(function(container) {
            // 只选择直接的ul元素，避免误选其他元素
            const lists = container.querySelectorAll('ul');

            lists.forEach(function(list) {
                // 验证这确实是一个列表元素
                if (list.tagName.toLowerCase() === 'ul') {
                    totalLists++;
                    enhanceSingleList(list);
                    const items = list.querySelectorAll('li');
                    totalItems += items.length;
                }
            });
        });

        console.log(`ListEnhancer: Enhanced ${totalLists} lists with ${totalItems} items`);

        // 清理不应该有标识符的元素
        setTimeout(function() {
            removeInvalidBullets();
        }, 200);

        // 处理包含代码块的列表项
        setTimeout(function() {
            adjustCodeBlockListItems();
        }, 300);

        // 最后检查：确保所有列表项都有标识符
        setTimeout(function() {
            ensureAllBulletsVisible();
        }, 500);
    }

    function cleanupDuplicateBullets() {
        console.log('ListEnhancer: Cleaning up duplicate bullets...');

        // 查找所有列表项
        const listItems = document.querySelectorAll('.mdui-typo li, .article li');
        let cleanedCount = 0;

        listItems.forEach(function(item) {
            // 检查CSS伪元素是否存在
            const beforeContent = getComputedStyle(item, '::before').content;
            const hasCSSBullet = beforeContent &&
                                beforeContent !== 'none' &&
                                beforeContent !== '""' &&
                                beforeContent !== 'normal' &&
                                (beforeContent.includes('●') ||
                                 beforeContent.includes('○') ||
                                 beforeContent.includes('□') ||
                                 beforeContent.includes('•') ||
                                 beforeContent.includes('◦') ||
                                 beforeContent.includes('▫'));

            // 如果CSS伪元素存在，移除JavaScript生成的标识符
            if (hasCSSBullet) {
                const customBullets = item.querySelectorAll('.custom-bullet');
                customBullets.forEach(function(bullet) {
                    bullet.remove();
                    cleanedCount++;
                });

                // 重置增强标记，允许重新处理
                item.classList.remove('list-enhanced');
                item.classList.remove('js-bullet-active');
            }
        });

        if (cleanedCount > 0) {
            console.log(`ListEnhancer: Removed ${cleanedCount} duplicate JavaScript bullets`);
        }
    }

    function removeInvalidBullets() {
        console.log('ListEnhancer: Removing invalid bullets...');

        // 查找所有可能错误添加了标识符的元素
        const allElements = document.querySelectorAll('.mdui-typo *');
        let removedCount = 0;

        allElements.forEach(function(element) {
            // 检查是否有JavaScript生成的标识符
            const customBullet = element.querySelector('.custom-bullet');
            if (customBullet) {
                // 验证这个元素是否应该有标识符
                const isValidListItem = element.tagName.toLowerCase() === 'li' &&
                                       element.parentElement &&
                                       element.parentElement.tagName.toLowerCase() === 'ul';

                if (!isValidListItem) {
                    console.log('ListEnhancer: Removing invalid bullet from:', element);
                    customBullet.remove();
                    element.classList.remove('list-enhanced');
                    element.classList.remove('js-bullet-active');
                    removedCount++;
                }
            }
        });

        if (removedCount > 0) {
            console.log(`ListEnhancer: Removed ${removedCount} invalid bullets`);
        }
    }

    function adjustCodeBlockListItems() {
        console.log('ListEnhancer: Adjusting list items with code blocks...');

        // 查找所有包含代码块的列表项
        const listItems = document.querySelectorAll('.mdui-typo ul > li');
        let adjustedCount = 0;

        listItems.forEach(function(item) {
            const hasCodeBlock = item.querySelector('pre') || item.querySelector('code');

            if (hasCodeBlock) {
                // 添加特殊类标记
                if (!item.classList.contains('has-code-block')) {
                    item.classList.add('has-code-block');
                }

                // 检查列表项的第一个子元素
                const firstChild = item.firstElementChild;
                const firstTextNode = getFirstTextNode(item);

                // 如果第一个元素是代码块，需要特殊处理
                if (firstChild && (firstChild.tagName === 'PRE' || firstChild.tagName === 'CODE')) {
                    console.log('ListEnhancer: List item starts with code block, adjusting:', item);

                    // 在代码块前添加一个文本节点来承载标识符
                    if (!item.querySelector('.list-text-anchor')) {
                        const textAnchor = document.createElement('span');
                        textAnchor.className = 'list-text-anchor';
                        textAnchor.style.cssText = `
                            display: inline-block;
                            width: 0;
                            height: 1.2em;
                            position: relative;
                        `;
                        item.insertBefore(textAnchor, firstChild);
                    }
                    adjustedCount++;
                } else if (firstTextNode) {
                    // 如果有文本内容，确保标识符与文本对齐
                    console.log('ListEnhancer: List item has text content, ensuring alignment:', item);
                }
            }
        });

        if (adjustedCount > 0) {
            console.log(`ListEnhancer: Adjusted ${adjustedCount} list items with code blocks`);
        }
    }

    function getFirstTextNode(element) {
        for (let node of element.childNodes) {
            if (node.nodeType === Node.TEXT_NODE && node.textContent.trim()) {
                return node;
            } else if (node.nodeType === Node.ELEMENT_NODE) {
                const textNode = getFirstTextNode(node);
                if (textNode) return textNode;
            }
        }
        return null;
    }

    function ensureAllBulletsVisible() {
        console.log('ListEnhancer: Ensuring all bullets are visible...');

        // 只检查真正的列表项（在ul元素内的li元素）
        const listItems = document.querySelectorAll('.mdui-typo ul > li, .article ul > li');
        let missingCount = 0;
        let fixedCount = 0;

        listItems.forEach(function(item) {
            // 验证这确实是一个li元素且父元素是ul
            if (item.tagName.toLowerCase() !== 'li' ||
                !item.parentElement ||
                item.parentElement.tagName.toLowerCase() !== 'ul') {
                return;
            }

            // 检查是否有可见的标识符
            const beforeContent = getComputedStyle(item, '::before').content;
            const beforeDisplay = getComputedStyle(item, '::before').display;
            const hasCSSBullet = beforeContent &&
                                beforeContent !== 'none' &&
                                beforeContent !== '""' &&
                                beforeContent !== 'normal' &&
                                beforeDisplay !== 'none' &&
                                (beforeContent.includes('●') ||
                                 beforeContent.includes('○') ||
                                 beforeContent.includes('□') ||
                                 beforeContent.includes('•') ||
                                 beforeContent.includes('◦') ||
                                 beforeContent.includes('▫'));

            const hasJSBullet = item.querySelector('.custom-bullet') !== null;

            // 如果既没有CSS标识符也没有JavaScript标识符，强制添加JavaScript标识符
            if (!hasCSSBullet && !hasJSBullet) {
                missingCount++;
                console.log('ListEnhancer: Missing bullet detected, adding JavaScript bullet for:', item);

                // 移除可能阻止添加的标记
                item.classList.remove('list-enhanced');

                // 强制添加JavaScript标识符
                const level = getListLevel(item);
                addCustomBullet(item, level);
                fixedCount++;
            }
        });

        if (missingCount > 0) {
            console.log(`ListEnhancer: Found ${missingCount} items without bullets, fixed ${fixedCount}`);
        } else {
            console.log('ListEnhancer: All list items have visible bullets');
        }
    }
    
    function enhanceSingleList(listElement) {
        // 验证这确实是一个ul元素
        if (listElement.tagName.toLowerCase() !== 'ul') {
            return;
        }

        // 强制移除默认样式
        listElement.style.listStyle = 'none';
        listElement.style.listStyleType = 'none';
        listElement.style.listStyleImage = 'none';

        // 处理直接的列表项（使用>选择器避免处理嵌套列表的项）
        const items = listElement.querySelectorAll(':scope > li');
        items.forEach(function(item) {
            // 验证这确实是一个li元素
            if (item.tagName.toLowerCase() === 'li') {
                enhanceListItem(item);
            }
        });
    }
    
    function enhanceListItem(item) {
        // 检查是否包含代码块
        const hasCodeBlock = item.querySelector('pre') || item.querySelector('code');
        if (hasCodeBlock) {
            item.classList.add('has-code-block');
            console.log('ListEnhancer: List item contains code block:', item);
        }

        // 强制移除默认样式
        item.style.listStyle = 'none';
        item.style.listStyleType = 'none';
        item.style.listStyleImage = 'none';
        item.style.position = 'relative';

        // 确保有足够的左边距
        if (!item.style.paddingLeft || item.style.paddingLeft === '0px') {
            item.style.paddingLeft = '1.5em';
        }

        // 检查是否已经有自定义标识符
        if (item.classList.contains('list-enhanced')) {
            return;
        }

        // 检查CSS伪元素是否已经生效
        const beforeContent = getComputedStyle(item, '::before').content;
        const beforeDisplay = getComputedStyle(item, '::before').display;
        const hasCSSBullet = beforeContent &&
                            beforeContent !== 'none' &&
                            beforeContent !== '""' &&
                            beforeContent !== 'normal' &&
                            beforeDisplay !== 'none' &&
                            (beforeContent.includes('●') ||
                             beforeContent.includes('○') ||
                             beforeContent.includes('□') ||
                             beforeContent.includes('•') ||
                             beforeContent.includes('◦') ||
                             beforeContent.includes('▫'));

        // 检查是否已经有JavaScript标识符
        const hasJSBullet = item.querySelector('.custom-bullet') !== null;

        // 如果CSS伪元素已经工作且没有JavaScript标识符，就不添加JavaScript标识符
        if (hasCSSBullet && !hasJSBullet) {
            console.log('ListEnhancer: CSS bullet detected and working, skipping JavaScript bullet for:', item);
            item.classList.add('list-enhanced');
            return;
        }

        // 如果已经有JavaScript标识符且CSS也工作，移除JavaScript标识符避免重复
        if (hasCSSBullet && hasJSBullet) {
            console.log('ListEnhancer: Both CSS and JS bullets detected, removing JS bullet for:', item);
            const existingBullet = item.querySelector('.custom-bullet');
            if (existingBullet) {
                existingBullet.remove();
            }
            item.classList.add('list-enhanced');
            return;
        }

        // 标记为已处理
        item.classList.add('list-enhanced');

        // 确定层级
        const level = getListLevel(item);

        // 添加自定义标识符（仅在CSS失效时）
        addCustomBullet(item, level);

        console.log('ListEnhancer: Added JavaScript bullet for level', level, ':', item);
    }
    
    function getListLevel(item) {
        let level = 1;
        let parent = item.parentElement;
        
        while (parent) {
            if (parent.tagName === 'LI') {
                level++;
            }
            parent = parent.parentElement;
        }
        
        return level;
    }
    
    function addCustomBullet(item, level) {
        // 移除现有的伪元素（如果有的话）
        const existingBullet = item.querySelector('.custom-bullet');
        if (existingBullet) {
            existingBullet.remove();
        }
        
        // 创建自定义标识符元素
        const bullet = document.createElement('span');
        bullet.className = 'custom-bullet';
        bullet.style.cssText = `
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 1em;
            height: 1em;
            text-align: center;
            font-weight: bold;
            z-index: 1;
            pointer-events: none;
            user-select: none;
            line-height: 1;
            display: flex;
            align-items: center;
            justify-content: center;
        `;
        
        // 根据层级设置不同的标识符（使用统一的居中对齐）
        switch (level % 4) {
            case 1:
                bullet.textContent = '●';
                bullet.style.fontSize = '0.8em';
                bullet.style.fontWeight = 'bold';
                break;
            case 2:
                bullet.textContent = '○';
                bullet.style.fontSize = '0.9em';
                bullet.style.fontWeight = 'normal';
                break;
            case 3:
                bullet.textContent = '□';
                bullet.style.fontSize = '0.8em';
                bullet.style.fontWeight = 'normal';
                break;
            case 0: // 第4级及以后
                bullet.textContent = '●';
                bullet.style.fontSize = '0.8em';
                bullet.style.fontWeight = 'bold';
                break;
        }
        
        // 设置主题相关的颜色
        const isDark = document.body.classList.contains('mdui-theme-layout-dark');
        bullet.style.color = isDark ? '#abb2bf' : '#2c3e50';
        
        // 插入到列表项的开头
        item.insertBefore(bullet, item.firstChild);

        // 添加类名以隐藏CSS伪元素（备用方案）
        item.classList.add('js-bullet-active');

        console.log(`ListEnhancer: Added level ${level} bullet to list item`);
    }
    
    // 主题切换时更新颜色
    function updateBulletColors() {
        const bullets = document.querySelectorAll('.custom-bullet');
        const isDark = document.body.classList.contains('mdui-theme-layout-dark');
        const color = isDark ? '#abb2bf' : '#2c3e50';
        
        bullets.forEach(function(bullet) {
            bullet.style.color = color;
        });
    }
    
    // 导出函数供外部调用
    window.ListEnhancer = {
        init: initListEnhancer,
        enhance: enhanceLists,
        updateColors: updateBulletColors,
        cleanup: cleanupDuplicateBullets,
        ensure: ensureAllBulletsVisible,
        removeInvalid: removeInvalidBullets,
        adjustCodeBlocks: adjustCodeBlockListItems
    };
    
    // 自动初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            console.log('ListEnhancer: DOM loaded, initializing...');
            initListEnhancer();
        });
    } else {
        console.log('ListEnhancer: DOM already loaded, initializing immediately...');
        initListEnhancer();
    }
    
    // 监听主题切换事件
    document.addEventListener('themeChanged', updateBulletColors);
    
})();
