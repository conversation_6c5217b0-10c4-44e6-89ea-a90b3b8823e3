/**
 * 代码块增强器
 * 用于处理没有指定语言类型的Markdown代码块，确保它们能正确显示为代码段
 * 
 * 功能：
 * 1. 检测没有语言类的代码块
 * 2. 为它们添加默认样式和类名
 * 3. 确保在明暗主题切换时样式正确
 * 4. 在代码块右上角显示语言类型标签
 */

(function() {
    'use strict';
    
    // 等待DOM加载完成
    function initCodeBlockEnhancer() {
        enhanceCodeBlocks();
        
        // 监听主题切换事件（如果有的话）
        if (typeof MutationObserver !== 'undefined') {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && 
                        (mutation.attributeName === 'class' || mutation.attributeName === 'data-theme')) {
                        // 主题可能发生了变化，重新应用样式
                        setTimeout(enhanceCodeBlocks, 100);
                    }
                });
            });
            
            // 观察body的class变化（主题切换通常会改变body的class）
            observer.observe(document.body, {
                attributes: true,
                attributeFilter: ['class', 'data-theme']
            });
        }
    }
    
    function enhanceCodeBlocks() {
        console.log('CodeBlockEnhancer: Starting to enhance code blocks...');

        // 查找所有代码块（包括有语言类和没有语言类的）
        const selectors = [
            'pre > code:not([class*="language-"]):not([class*="lang-"])', // 没有语言类的code
            'pre:not([class*="language-"]):not([class*="lang-"]) > code', // 没有语言类的pre下的code
            'pre:not([class*="language-"]):not([class*="lang-"])', // 没有语言类的pre
            'pre > code[class*="language-"]', // 有语言类的code
            'pre > code[class*="lang-"]', // 有lang-类的code
            'pre[class*="language-"] > code', // 有语言类的pre下的code
            'pre[class*="lang-"] > code' // 有lang-类的pre下的code
        ];

        const processedElements = new Set();
        let totalFound = 0;

        selectors.forEach(function(selector) {
            const elements = document.querySelectorAll(selector);
            console.log(`CodeBlockEnhancer: Found ${elements.length} elements for selector: ${selector}`);
            totalFound += elements.length;

            elements.forEach(function(element) {
                // 避免重复处理同一个元素
                if (!processedElements.has(element)) {
                    processedElements.add(element);
                    enhanceSingleCodeBlock(element);
                }
            });
        });

        console.log(`CodeBlockEnhancer: Total code blocks found: ${totalFound}, processed: ${processedElements.size}`);
    }
    
    function enhanceSingleCodeBlock(element) {
        console.log('CodeBlockEnhancer: Processing element:', element);

        let codeElement, preElement;

        if (element.tagName.toLowerCase() === 'pre') {
            preElement = element;
            codeElement = preElement.querySelector('code');
        } else if (element.tagName.toLowerCase() === 'code') {
            codeElement = element;
            preElement = codeElement.parentElement;
        }

        if (!preElement || !codeElement) {
            console.log('CodeBlockEnhancer: Missing pre or code element, skipping');
            return;
        }

        console.log('CodeBlockEnhancer: Found pre and code elements:', {pre: preElement, code: codeElement});
        
        // 检查是否已经处理过
        if (preElement.classList.contains('enhanced-code-block')) {
            return;
        }

        // 标记为已处理
        preElement.classList.add('enhanced-code-block');

        // 检查是否有语言类
        const hasLanguageClass = codeElement.className.match(/language-|lang-/) ||
                                preElement.className.match(/language-|lang-/);

        if (!hasLanguageClass) {
            // 没有语言类的代码块，添加默认语言类和样式
            if (!codeElement.classList.contains('language-none')) {
                codeElement.classList.add('language-none');
            }
            if (!preElement.classList.contains('language-none')) {
                preElement.classList.add('language-none');
            }

            // 应用基础样式
            applyCodeBlockStyles(preElement, codeElement);
        }

        // 为所有代码块添加语言类型显示
        addLanguageLabel(preElement, codeElement);

        // 如果存在工具栏插件，确保它能正常工作
        if (typeof Prism !== 'undefined' && Prism.plugins && Prism.plugins.toolbar) {
            // 触发工具栏插件
            Prism.plugins.toolbar.hook({
                element: codeElement,
                language: 'none',
                grammar: null,
                code: codeElement.textContent
            });
        }
    }
    
    function addLanguageLabel(preElement, codeElement) {
        // 检查是否已经有语言标签
        if (preElement.querySelector('.code-language-label')) {
            return;
        }

        // 获取语言类型
        let language = 'text';
        const classList = codeElement.classList;

        // 从class中提取语言类型
        for (let className of classList) {
            if (className.startsWith('language-')) {
                language = className.replace('language-', '');
                break;
            } else if (className.startsWith('lang-')) {
                language = className.replace('lang-', '');
                break;
            }
        }

        // 语言名称映射
        const languageNames = {
            'none': 'Text',
            'text': 'Text',
            'plain': 'Text',
            'plaintext': 'Text',
            'txt': 'Text',
            'javascript': 'JavaScript',
            'js': 'JavaScript',
            'typescript': 'TypeScript',
            'ts': 'TypeScript',
            'python': 'Python',
            'py': 'Python',
            'java': 'Java',
            'c': 'C',
            'cpp': 'C++',
            'csharp': 'C#',
            'cs': 'C#',
            'php': 'PHP',
            'html': 'HTML',
            'css': 'CSS',
            'scss': 'SCSS',
            'sass': 'Sass',
            'json': 'JSON',
            'xml': 'XML',
            'yaml': 'YAML',
            'yml': 'YAML',
            'markdown': 'Markdown',
            'md': 'Markdown',
            'bash': 'Bash',
            'shell': 'Shell',
            'sh': 'Shell',
            'sql': 'SQL',
            'go': 'Go',
            'rust': 'Rust',
            'swift': 'Swift',
            'kotlin': 'Kotlin',
            'dart': 'Dart',
            'ruby': 'Ruby',
            'perl': 'Perl',
            'lua': 'Lua',
            'r': 'R',
            'matlab': 'MATLAB',
            'docker': 'Docker',
            'dockerfile': 'Dockerfile',
            'nginx': 'Nginx',
            'apache': 'Apache',
            'ini': 'INI',
            'toml': 'TOML',
            'properties': 'Properties',
            'makefile': 'Makefile',
            'cmake': 'CMake',
            'powershell': 'PowerShell',
            'ps1': 'PowerShell',
            'batch': 'Batch',
            'bat': 'Batch',
            'vim': 'Vim',
            'git': 'Git',
            'diff': 'Diff',
            'patch': 'Patch',
            'log': 'Log',
            'conf': 'Config',
            'config': 'Config',
            'env': 'Environment',
            'htaccess': '.htaccess',
            'robots': 'robots.txt'
        };

        const displayName = languageNames[language.toLowerCase()] || language.toUpperCase();

        // 创建语言标签
        const languageLabel = document.createElement('div');
        languageLabel.className = 'code-language-label';
        languageLabel.textContent = displayName;

        // 检查是否有工具栏（复制按钮等）
        const hasToolbar = preElement.classList.contains('code-toolbar') ||
                          preElement.querySelector('.toolbar') ||
                          preElement.parentElement.classList.contains('code-toolbar');

        // 根据是否有工具栏调整位置
        const rightPosition = hasToolbar ? '4.5em' : '0.5em';

        // 检查主题
        const isDarkTheme = document.body.classList.contains('mdui-theme-layout-dark');
        const backgroundColor = isDarkTheme ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.6)';
        const textColor = isDarkTheme ? '#f7fafc' : '#fff';

        // 设置样式
        languageLabel.style.cssText = `
            position: absolute;
            top: 0.3em;
            right: ${rightPosition};
            background: ${backgroundColor};
            color: ${textColor};
            padding: 0.2em 0.5em;
            border-radius: 0.3em;
            font-size: 0.75em;
            font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace;
            font-weight: normal;
            line-height: 1.2;
            z-index: 10;
            opacity: 0.8;
            transition: opacity 0.3s ease-in-out;
            pointer-events: none;
            user-select: none;
            white-space: nowrap;
        `;

        // 确保父元素有相对定位
        if (getComputedStyle(preElement).position === 'static') {
            preElement.style.position = 'relative';
        }

        // 添加到代码块
        preElement.appendChild(languageLabel);

        // 鼠标悬停时显示
        preElement.addEventListener('mouseenter', function() {
            languageLabel.style.opacity = '1';
        });

        preElement.addEventListener('mouseleave', function() {
            languageLabel.style.opacity = '0.8';
        });

        // 监听工具栏变化，动态调整位置
        setTimeout(function() {
            adjustLanguageLabelPosition(preElement, languageLabel);
        }, 500);

        // 使用MutationObserver监听工具栏的添加
        if (typeof MutationObserver !== 'undefined') {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' || mutation.type === 'attributes') {
                        adjustLanguageLabelPosition(preElement, languageLabel);
                    }
                });
            });

            observer.observe(preElement, {
                childList: true,
                attributes: true,
                attributeFilter: ['class']
            });

            // 也观察父元素的变化
            if (preElement.parentElement) {
                observer.observe(preElement.parentElement, {
                    childList: true,
                    attributes: true,
                    attributeFilter: ['class']
                });
            }
        }
    }

    function adjustLanguageLabelPosition(preElement, languageLabel) {
        // 检查是否有工具栏
        const hasToolbar = preElement.classList.contains('code-toolbar') ||
                          preElement.querySelector('.toolbar') ||
                          preElement.querySelector('.code-toolbar') ||
                          preElement.parentElement.classList.contains('code-toolbar') ||
                          preElement.querySelector('[class*="toolbar"]') ||
                          preElement.querySelector('.copy-to-clipboard-button');

        // 根据工具栏存在情况调整位置
        const rightPosition = hasToolbar ? '4.5em' : '0.5em';

        if (languageLabel.style.right !== rightPosition) {
            languageLabel.style.right = rightPosition;
            console.log(`CodeBlockEnhancer: Adjusted language label position to ${rightPosition} (toolbar: ${hasToolbar})`);
        }
    }

    function applyCodeBlockStyles(preElement, codeElement) {
        // 检查当前主题
        const isDarkTheme = document.body.classList.contains('mdui-theme-layout-dark') ||
                           document.documentElement.classList.contains('mdui-theme-layout-dark');
        
        // 基础样式
        const baseStyles = {
            fontFamily: 'Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace',
            fontSize: '0.875em',
            lineHeight: '1.5',
            padding: '1em',
            margin: '0.5em 0',
            overflow: 'auto',
            borderRadius: '0.375rem',
            border: 'none',
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
            whiteSpace: 'pre',
            wordWrap: 'break-word',
            tabSize: '4'
        };
        
        // One Dark主题样式
        const themeStyles = isDarkTheme ? {
            background: '#282c34',
            color: '#abb2bf'
        } : {
            background: '#282c34',
            color: '#abb2bf'
        };
        
        // 应用到pre元素
        Object.assign(preElement.style, baseStyles, themeStyles);
        
        // code元素样式
        const codeStyles = {
            background: 'none',
            color: 'inherit',
            fontFamily: 'inherit',
            fontSize: 'inherit',
            padding: '0',
            borderRadius: '0',
            border: 'none'
        };
        
        Object.assign(codeElement.style, codeStyles);
        
        // 确保在文章容器中的正确显示
        if (preElement.closest('.article') || preElement.closest('.mdui-typo')) {
            preElement.style.whiteSpace = 'pre-wrap';
            preElement.style.wordWrap = 'break-word';
        }
    }
    
    // 主题切换时重新应用样式
    function handleThemeChange() {
        const enhancedBlocks = document.querySelectorAll('.enhanced-code-block');
        enhancedBlocks.forEach(function(preElement) {
            const codeElement = preElement.querySelector('code');
            if (codeElement) {
                applyCodeBlockStyles(preElement, codeElement);
            }
        });
    }
    
    // 导出函数供外部调用
    window.CodeBlockEnhancer = {
        init: initCodeBlockEnhancer,
        enhance: enhanceCodeBlocks,
        handleThemeChange: handleThemeChange
    };
    
    // 等待Prism加载完成后再初始化
    function waitForPrismAndInit() {
        if (typeof Prism !== 'undefined') {
            console.log('CodeBlockEnhancer: Prism loaded, setting up hooks...');

            // 添加Prism钩子，在高亮完成后运行我们的增强器
            if (Prism.hooks) {
                Prism.hooks.add('complete', function(env) {
                    console.log('CodeBlockEnhancer: Prism highlighting complete for element:', env.element);
                    // 为这个特定元素添加语言标签
                    if (env.element && env.element.parentElement) {
                        addLanguageLabel(env.element.parentElement, env.element);
                    }
                });
            }

            // 先让Prism完成高亮
            if (Prism.highlightAll) {
                Prism.highlightAll();
            }

            // 然后运行我们的增强器处理没有被Prism处理的代码块
            setTimeout(function() {
                initCodeBlockEnhancer();
            }, 200);
        } else {
            console.log('CodeBlockEnhancer: Waiting for Prism to load...');
            setTimeout(waitForPrismAndInit, 100);
        }
    }

    // 自动初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            console.log('CodeBlockEnhancer: DOM loaded, waiting for Prism...');
            waitForPrismAndInit();
        });
    } else {
        console.log('CodeBlockEnhancer: DOM already loaded, waiting for Prism...');
        waitForPrismAndInit();
    }
    
    // 监听主题切换（如果有相关事件）
    document.addEventListener('themeChanged', handleThemeChange);
    
})();
