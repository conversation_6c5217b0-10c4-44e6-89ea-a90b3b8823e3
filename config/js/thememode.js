 if(localStorage.romanticismTheme)
        document.body.classList.add("mdui-theme-layout-dark");
 
        if(!localStorage.romanticismTheme)
        document.body.classList.add("mdui-theme-layout-light");

    document.getElementById("switch-theme").addEventListener(
        "click",
        () => {
            const isDark = document.body.classList.toggle("mdui-theme-layout-dark");
            if (isDark) {
                localStorage.romanticismTheme = true;
            } else {
                delete localStorage.romanticismTheme;
            }

            // 触发代码块样式更新
            if (window.CodeBlockEnhancer && window.CodeBlockEnhancer.handleThemeChange) {
                setTimeout(window.CodeBlockEnhancer.handleThemeChange, 100);
            }

            // 触发列表样式更新
            if (window.ListEnhancer && window.ListEnhancer.updateColors) {
                setTimeout(window.ListEnhancer.updateColors, 100);
            }

            // 触发链接颜色更新
            if (window.LinkEnhancer && window.LinkEnhancer.updateColors) {
                setTimeout(window.LinkEnhancer.updateColors, 100);
            }

            // 触发文章目录主题更新
            if (window.ArticleTOC && window.ArticleTOC.updateTheme) {
                setTimeout(window.ArticleTOC.updateTheme, 100);
            }

            // 触发自定义主题切换事件
            document.dispatchEvent(new CustomEvent('themeChanged', {
                detail: { isDark: isDark }
            }));
        }
    );