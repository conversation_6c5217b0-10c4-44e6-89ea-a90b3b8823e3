/**
 * 超链接颜色增强器
 * 用于确保超链接显示正确的颜色（蓝色未访问，紫色已访问）
 * 
 * 功能：
 * 1. 强制应用标准的超链接颜色
 * 2. 覆盖MDUI默认的红色/粉色样式
 * 3. 支持明暗主题切换
 */

(function() {
    'use strict';
    
    // 颜色配置
    const COLORS = {
        light: {
            unvisited: '#2196F3',    // 蓝色 - 未访问
            visited: '#9C27B0',      // 紫色 - 已访问
            hover: '#1976D2',        // 深蓝色 - 悬停
            visitedHover: '#7B1FA2'  // 深紫色 - 已访问且悬停
        },
        dark: {
            unvisited: '#64B5F6',    // 浅蓝色 - 未访问
            visited: '#CE93D8',      // 浅紫色 - 已访问
            hover: '#42A5F5',        // 中蓝色 - 悬停
            visitedHover: '#BA68C8'  // 中紫色 - 已访问且悬停
        }
    };
    
    // 等待DOM加载完成
    function initLinkEnhancer() {
        console.log('LinkEnhancer: Initializing link color enhancer...');
        enhanceLinks();
        
        // 监听主题切换事件
        document.addEventListener('themeChanged', function() {
            setTimeout(enhanceLinks, 100);
        });
        
        // 监听DOM变化
        if (typeof MutationObserver !== 'undefined') {
            const observer = new MutationObserver(function(mutations) {
                let shouldUpdate = false;
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        // 检查是否有新的链接元素
                        mutation.addedNodes.forEach(function(node) {
                            if (node.nodeType === 1 && (node.tagName === 'A' || node.querySelector('a'))) {
                                shouldUpdate = true;
                            }
                        });
                    }
                });
                
                if (shouldUpdate) {
                    setTimeout(enhanceLinks, 100);
                }
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
    }
    
    function enhanceLinks() {
        console.log('LinkEnhancer: Starting to enhance links...');
        
        // 查找所有在.mdui-typo容器内的链接
        const containers = document.querySelectorAll('.mdui-typo, .article');
        let totalLinks = 0;
        
        containers.forEach(function(container) {
            const links = container.querySelectorAll('a');
            totalLinks += links.length;
            
            links.forEach(function(link) {
                enhanceSingleLink(link);
            });
        });
        
        console.log(`LinkEnhancer: Enhanced ${totalLinks} links`);
    }
    
    function enhanceSingleLink(link) {
        // 检查是否已经处理过
        if (link.classList.contains('link-enhanced')) {
            return;
        }
        
        // 标记为已处理
        link.classList.add('link-enhanced');
        
        // 应用颜色
        applyLinkColors(link);
        
        // 添加事件监听器
        link.addEventListener('mouseenter', function() {
            applyHoverColors(link);
        });
        
        link.addEventListener('mouseleave', function() {
            applyLinkColors(link);
        });
        
        console.log('LinkEnhancer: Enhanced link:', link.href || link.textContent);
    }
    
    function applyLinkColors(link) {
        const isDark = document.body.classList.contains('mdui-theme-layout-dark');
        const colors = isDark ? COLORS.dark : COLORS.light;
        
        // 检查是否为已访问链接
        const isVisited = isLinkVisited(link);
        
        // 应用颜色
        const color = isVisited ? colors.visited : colors.unvisited;
        link.style.color = color + ' !important';
        
        // 更新下划线颜色
        const beforeElement = link.querySelector('::before');
        if (beforeElement) {
            beforeElement.style.backgroundColor = color + ' !important';
        }
        
        // 使用CSS变量作为备用方案
        link.style.setProperty('--link-color', color);
    }
    
    function applyHoverColors(link) {
        const isDark = document.body.classList.contains('mdui-theme-layout-dark');
        const colors = isDark ? COLORS.dark : COLORS.light;
        
        // 检查是否为已访问链接
        const isVisited = isLinkVisited(link);
        
        // 应用悬停颜色
        const color = isVisited ? colors.visitedHover : colors.hover;
        link.style.color = color + ' !important';
    }
    
    function isLinkVisited(link) {
        // 由于安全限制，无法直接检测:visited状态
        // 这里使用一些启发式方法
        
        // 检查是否为外部链接
        if (link.href && link.href.startsWith('http')) {
            // 对于外部链接，假设常见网站可能已访问
            const commonSites = ['google.com', 'github.com', 'stackoverflow.com', 'wikipedia.org'];
            return commonSites.some(site => link.href.includes(site));
        }
        
        // 对于内部链接，默认未访问
        return false;
    }
    
    // 主题切换时更新颜色
    function updateLinkColors() {
        const links = document.querySelectorAll('.link-enhanced');
        links.forEach(function(link) {
            applyLinkColors(link);
        });
        
        console.log('LinkEnhancer: Updated colors for theme change');
    }
    
    // 强制重置所有链接颜色（用于解决顽固的样式问题）
    function forceResetLinkColors() {
        const allLinks = document.querySelectorAll('.mdui-typo a, .article a');
        
        allLinks.forEach(function(link) {
            // 移除可能的MDUI主题类影响
            link.style.removeProperty('color');
            
            // 重新应用我们的颜色
            enhanceSingleLink(link);
        });
        
        console.log('LinkEnhancer: Force reset completed');
    }
    
    // 导出函数供外部调用
    window.LinkEnhancer = {
        init: initLinkEnhancer,
        enhance: enhanceLinks,
        updateColors: updateLinkColors,
        forceReset: forceResetLinkColors
    };
    
    // 自动初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            console.log('LinkEnhancer: DOM loaded, initializing...');
            initLinkEnhancer();
        });
    } else {
        console.log('LinkEnhancer: DOM already loaded, initializing immediately...');
        initLinkEnhancer();
    }
    
    // 监听主题切换事件
    document.addEventListener('themeChanged', updateLinkColors);
    
    // 延迟执行强制重置，确保覆盖所有可能的样式
    setTimeout(function() {
        if (window.LinkEnhancer) {
            window.LinkEnhancer.forceReset();
        }
    }, 2000);
    
})();
