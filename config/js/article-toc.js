/**
 * 文章目录生成器
 * 自动生成文章目录并提供导航功能
 */
(function() {
    'use strict';
    
    let tocContainer = null;
    let tocList = null;
    let isCollapsed = false;
    let headings = [];
    
    function initTOC() {
        console.log('ArticleTOC: Initializing table of contents...');
        
        // 只在文章页面生成目录
        if (!isArticlePage()) {
            console.log('ArticleTOC: Not an article page, skipping TOC generation');
            return;
        }
        
        // 查找文章内容中的标题
        headings = findHeadings();
        
        if (headings.length === 0) {
            console.log('ArticleTOC: No headings found, skipping TOC generation');
            return;
        }
        
        console.log(`ArticleTOC: Found ${headings.length} headings`);
        
        // 为标题添加ID
        addHeadingIds();
        
        // 创建目录容器
        createTOCContainer();
        
        // 生成目录列表
        generateTOCList();
        
        // 绑定事件
        bindEvents();
        
        // 初始化滚动监听
        initScrollSpy();
        
        console.log('ArticleTOC: Table of contents initialized successfully');
    }
    
    function isArticlePage() {
        // 检查是否在文章页面（包含.article容器）
        return document.querySelector('.article.mdui-typo') !== null;
    }
    
    function findHeadings() {
        const articleContent = document.querySelector('.article.mdui-typo');
        if (!articleContent) return [];
        
        // 查找h1-h6标题
        const headingElements = articleContent.querySelectorAll('h1, h2, h3, h4, h5, h6');
        const headingList = [];
        
        headingElements.forEach(function(heading, index) {
            const level = parseInt(heading.tagName.charAt(1));
            const text = heading.textContent.trim();
            
            if (text) {
                headingList.push({
                    element: heading,
                    level: level,
                    text: text,
                    id: `heading-${index + 1}`
                });
            }
        });
        
        return headingList;
    }
    
    function addHeadingIds() {
        headings.forEach(function(heading) {
            heading.element.id = heading.id;
        });
    }
    
    function createTOCContainer() {
        tocContainer = document.createElement('div');
        tocContainer.className = 'article-toc-fixed';
        tocContainer.innerHTML = `
            <div class="article-toc-title">
                <i class="mdui-icon material-icons" style="margin-right: 8px;">list</i>
                <span>目录</span>
                <button class="toc-hide-btn" style="margin-left: auto;">
                    <i class="mdui-icon material-icons">keyboard_arrow_right</i>
                </button>
                <i class="mdui-icon material-icons toc-toggle" style="cursor: pointer;">keyboard_arrow_up</i>
            </div>
            <ul class="article-toc-list"></ul>
        `;
        
        document.body.appendChild(tocContainer);
        tocList = tocContainer.querySelector('.article-toc-list');
    }
    
    function generateTOCList() {
        let html = '';
        let minLevel = Math.min(...headings.map(h => h.level));
        let maxTextLength = 0;

        // 计算最长标题的长度，用于动态调整目录宽度
        headings.forEach(function(heading) {
            const textLength = calculateTextLength(heading.text);
            if (textLength > maxTextLength) {
                maxTextLength = textLength;
            }
        });

        // 根据最长标题动态确定截断长度
        const maxDisplayLength = Math.min(maxTextLength, 25);

        headings.forEach(function(heading) {
            const indent = (heading.level - minLevel) * 15;
            const truncatedText = truncateText(heading.text, maxDisplayLength);
            html += `
                <li style="padding-left: ${indent}px;" data-heading-id="${heading.id}">
                    <a href="#${heading.id}" title="${heading.text}">${truncatedText}</a>
                </li>
            `;
        });

        tocList.innerHTML = html;

        // 动态调整目录容器宽度
        adjustTOCWidth(maxDisplayLength);
    }

    function calculateTextLength(text) {
        if (!text) return 0;

        let length = 0;
        for (let i = 0; i < text.length; i++) {
            const char = text.charAt(i);
            const charCode = char.charCodeAt(0);

            // 判断是否为中文字符（包括中文标点）
            if ((charCode >= 0x4e00 && charCode <= 0x9fff) ||
                (charCode >= 0x3000 && charCode <= 0x303f) ||
                (charCode >= 0xff00 && charCode <= 0xffef)) {
                length += 1;
            } else {
                length += 0.5;
            }
        }

        return length;
    }

    function adjustTOCWidth(maxDisplayLength) {
        const tocContainer = document.querySelector('.article-toc-fixed');
        if (!tocContainer) return;

        // 根据内容长度动态计算宽度
        // 基础宽度200px + 每个字符约12px + 缩进空间 + 内边距
        const baseWidth = 200;
        const charWidth = 12;
        const maxIndent = 60; // 最大缩进空间
        const padding = 40; // 左右内边距

        const calculatedWidth = Math.min(
            Math.max(baseWidth, maxDisplayLength * charWidth + maxIndent + padding),
            320 // 最大宽度限制
        );

        tocContainer.style.width = calculatedWidth + 'px';
    }

    function truncateText(text, maxLength) {
        if (!text) return '';

        // 计算中文字符长度（中文字符算1个，英文字符算0.5个）
        let length = 0;
        let result = '';

        for (let i = 0; i < text.length; i++) {
            const char = text.charAt(i);
            const charCode = char.charCodeAt(0);

            // 判断是否为中文字符（包括中文标点）
            if ((charCode >= 0x4e00 && charCode <= 0x9fff) ||
                (charCode >= 0x3000 && charCode <= 0x303f) ||
                (charCode >= 0xff00 && charCode <= 0xffef)) {
                length += 1;
            } else {
                length += 0.5;
            }

            if (length > maxLength) {
                return result + '...';
            }

            result += char;
        }

        return result;
    }
    
    function bindEvents() {
        // 目录折叠/展开
        const tocTitle = tocContainer.querySelector('.article-toc-title');
        const toggleIcon = tocContainer.querySelector('.toc-toggle');
        const hideBtn = tocContainer.querySelector('.toc-hide-btn');

        tocTitle.addEventListener('click', function(e) {
            // 如果点击的是隐藏按钮，不执行折叠操作
            if (e.target.classList.contains('toc-hide-btn')) {
                return;
            }
            isCollapsed = !isCollapsed;
            tocContainer.classList.toggle('article-toc-collapsed', isCollapsed);
            toggleIcon.textContent = isCollapsed ? 'keyboard_arrow_down' : 'keyboard_arrow_up';
        });

        // 隐藏按钮点击事件
        hideBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            const isHidden = tocContainer.classList.contains('toc-hidden');
            if (isHidden) {
                tocContainer.classList.remove('toc-hidden');
                tocContainer.style.transform = 'translateX(0)';
            } else {
                tocContainer.classList.add('toc-hidden');
                tocContainer.style.transform = 'translateX(calc(100% - 40px))';
            }
        });
        
        // 目录项点击事件
        tocList.addEventListener('click', function(e) {
            if (e.target.tagName === 'A') {
                e.preventDefault();
                const targetId = e.target.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                
                if (targetElement) {
                    // 平滑滚动到目标位置
                    const offsetTop = targetElement.offsetTop - 100; // 留出一些顶部空间
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                    
                    // 更新活动状态
                    updateActiveHeading(targetId);
                }
            }
        });
    }
    
    function initScrollSpy() {
        let ticking = false;
        
        function updateTOC() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            let activeHeading = null;
            
            // 找到当前可见的标题
            for (let i = headings.length - 1; i >= 0; i--) {
                const heading = headings[i];
                const element = heading.element;
                const offsetTop = element.offsetTop - 150; // 提前一些触发
                
                if (scrollTop >= offsetTop) {
                    activeHeading = heading.id;
                    break;
                }
            }
            
            updateActiveHeading(activeHeading);
            ticking = false;
        }
        
        function requestTick() {
            if (!ticking) {
                requestAnimationFrame(updateTOC);
                ticking = true;
            }
        }
        
        window.addEventListener('scroll', requestTick);
        window.addEventListener('resize', requestTick);
        
        // 初始更新
        updateTOC();
    }
    
    function updateActiveHeading(activeId) {
        // 移除所有活动状态
        const allItems = tocList.querySelectorAll('li');
        allItems.forEach(function(item) {
            item.classList.remove('active');
        });
        
        // 添加当前活动状态
        if (activeId) {
            const activeItem = tocList.querySelector(`[data-heading-id="${activeId}"]`);
            if (activeItem) {
                activeItem.classList.add('active');
            }
        }
    }
    
    // 主题切换时更新目录样式
    function updateTOCTheme() {
        if (!tocContainer) return;
        
        const isDark = document.body.classList.contains('mdui-theme-layout-dark');
        console.log('ArticleTOC: Theme changed to', isDark ? 'dark' : 'light');
    }
    
    // 导出函数供外部调用
    window.ArticleTOC = {
        init: initTOC,
        updateTheme: updateTOCTheme,
        toggle: function() {
            if (tocContainer) {
                const tocTitle = tocContainer.querySelector('.article-toc-title');
                if (tocTitle) {
                    tocTitle.click();
                }
            }
        },
        show: function() {
            if (tocContainer) {
                tocContainer.style.display = 'block';
            }
        },
        hide: function() {
            if (tocContainer) {
                tocContainer.style.display = 'none';
            }
        }
    };
    
    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initTOC);
    } else {
        initTOC();
    }
    
    // 监听主题切换事件
    document.addEventListener('themeChanged', updateTOCTheme);

    // 全局函数：隐藏/显示目录
    window.toggleTocVisibility = function() {
        if (tocContainer) {
            const isHidden = tocContainer.classList.contains('toc-hidden');
            if (isHidden) {
                tocContainer.classList.remove('toc-hidden');
                tocContainer.style.transform = 'translateX(0)';
            } else {
                tocContainer.classList.add('toc-hidden');
                tocContainer.style.transform = 'translateX(calc(100% - 40px))';
            }
        }
    };

})();
