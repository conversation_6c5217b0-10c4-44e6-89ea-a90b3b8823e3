<?php
/**

    _    _  __    _    ____  _   _ ___
   / \  | |/ /   / \  / ___|| | | |_ _|
  / _ \ | ' /   / _ \ \___ \| |_| || |
 / ___ \| . \  / ___ \ ___) |  _  || |
/_/   \_\_|\_\/_/   \_\____/|_| |_|___|

 * [Romanticism]
 * footer.php 页脚文件
 * @version 2.1 - 250202
**/

// 获取最早文章的年份
function getEarliestPostYear() {
    $db = Typecho_Db::get();
    $result = $db->fetchRow($db->select('created')->from('table.contents')
        ->where('type = ?', 'post')
        ->where('status = ?', 'publish')
        ->order('created', Typecho_Db::SORT_ASC)
        ->limit(1));

    if ($result) {
        return date('Y', $result['created']);
    }
    return date('Y'); // 如果没有文章，返回当前年份
}

$earliestYear = getEarliestPostYear();
$currentYear = date('Y');
?>

<div class="mdui-shadow-0 mdui-text-center mdui-card toup">
<br>
      <span class="title footer-copyright">
        &copy;<?php
        if ($earliestYear != $currentYear) {
            echo $earliestYear . '-' . $currentYear;
        } else {
            echo $currentYear;
        }
        ?> <?php $this->options->title(); ?>
        <?php if($this->options->AKAROMfootericp):?>
            <span class="footer-separator">&nbsp;&nbsp;</span><span class="footer-mobile-break"><br></span><?php echo $this->options->AKAROMfootericp; ?>
        <?php endif;?>
        </span>

         <!-- 已经弄得很不显眼了，请不要删除以下信息 -->
      <small style="opacity: .5;"></small>
      <br>
    </div>

    <script>
        document.getElementById("switch-theme").addEventListener("click", () => {
            const isDark = document.body.classList.toggle("mdui-theme-layout-dark");
            if(isDark){
                localStorage.romanticismTheme = true;
            }else{
                delete localStorage.romanticismTheme;
            }
        });
    </script>

    <script src="<?php $this->options->themeUrl('config/mdui/js/mdui.min.js'); ?>"></script>
    <script src="<?php $this->options->themeUrl('config/js/jquery.min.js'); ?>"></script>

    <script src="<?php $this->options->themeUrl('config/js/listLazyload.js?v=2.1'); ?>"></script>
    <script src="<?php $this->options->themeUrl('config/js/tagIcon.js?v=2.1'); ?>"></script>

    <script src="<?php $this->options->themeUrl('config/js/returntop.js?v=2.1'); ?>"></script>
    <script src="<?php $this->options->themeUrl('config/js/prism.highlight.js'); ?>"></script>
    <script src="<?php $this->options->themeUrl('config/js/codeblock-enhancer.js?v=2.2'); ?>"></script>
    <script src="<?php $this->options->themeUrl('config/js/list-enhancer.js?v=2.8'); ?>"></script>
    <script src="<?php $this->options->themeUrl('config/js/link-enhancer.js?v=2.1'); ?>"></script>
    <script src="<?php $this->options->themeUrl('config/js/article-toc.js?v=3.1'); ?>"></script>
    <script src="<?php $this->options->themeUrl('config/js/modern-icons.js?v=1.0'); ?>"></script>
    <!-- Prism additional languages via CDN to reduce theme size -->
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29/components/prism-bash.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29/components/prism-sql.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29/components/prism-yaml.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29/components/prism-nginx.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29/components/prism-json.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29/components/prism-toml.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29/components/prism-docker.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29/components/prism-typescript.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29/components/prism-rust.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29/components/prism-ocaml.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29/components/prism-fsharp.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29/components/prism-erlang.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29/components/prism-elixir.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29/components/prism-r.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29/components/prism-julia.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29/components/prism-matlab.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29/components/prism-markdown.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29/components/prism-apacheconf.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29/components/prism-ini.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29/components/prism-kotlin.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29/components/prism-scala.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29/components/prism-haskell.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29/components/prism-perl.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29/components/prism-ruby.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29/components/prism-latex.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29/components/prism-hcl.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29/components/prism-promql.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29/components/prism-nasm.min.js"></script>


    <script src="<?php $this->options->themeUrl('config/js/jquery.fancybox.min.js'); ?>"></script>
    <script>
        // Ensure Prism re-highlights after loading extra languages
        if (window.Prism && typeof Prism.highlightAll === 'function') {
            Prism.highlightAll();
        }
        $(document).ready(function () {
            $( ".fancybox").fancybox();
        });
    </script>

    <?php if (!empty($this->options->AKAROMfucset) && in_array('AKAROMindexloading', $this->options->AKAROMfucset)): ?>
    <script type="text/javascript" src="<?php $this->options->themeUrl('config/js/loading.js?v=2.1'); ?>"></script>
    <?php endif; ?>

    <script src="<?php $this->options->themeUrl('config/js/OwO.js'); ?>"></script>
    <script>
    function OwO_show() {
        if ($(".OwO-items").css("max-height") == '0px') {
            $(".OwO").addClass("OwO-open");
        } else {
            $(".OwO").removeClass("OwO-open");
        }
    }
    </script>

    <!-- 自定义JS -->
    <?php if(!empty($this->options->AKAROMcustomJs)): ?>
        <script type="text/javascript">
            <?php $this->options->AKAROMcustomJs(); ?>
        </script>
    <?php endif; ?>

    <?php $this->footer(); ?>
</div>
</body>
</html>
