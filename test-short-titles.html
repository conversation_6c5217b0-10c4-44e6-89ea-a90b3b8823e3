<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>短标题目录测试 - 自动宽度调整</title>
    <link rel="stylesheet" href="config/style/romanticism.aka.css?v=3.5">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .article {
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="article mdui-typo">
        <h1>Java教程</h1>
        <p>这是一个使用短标题的测试页面，用来验证目录的自动宽度调整功能。当所有标题都比较短时，目录应该自动收缩宽度，避免右侧留白过多。</p>
        
        <h2>1. 基础</h2>
        <p>这是Java基础知识的介绍。</p>
        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
        
        <h3>1.1 语法</h3>
        <p>Java语法基础。</p>
        <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
        
        <h3>1.2 变量</h3>
        <p>Java变量声明和使用。</p>
        <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
        
        <h3>1.3 方法</h3>
        <p>Java方法定义和调用。</p>
        <p>Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
        
        <h2>2. 面向对象</h2>
        <p>Java面向对象编程概念。</p>
        <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium.</p>
        
        <h3>2.1 类</h3>
        <p>Java类的定义和使用。</p>
        <p>Totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.</p>
        
        <h3>2.2 对象</h3>
        <p>Java对象的创建和操作。</p>
        <p>Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit.</p>
        
        <h3>2.3 继承</h3>
        <p>Java继承机制。</p>
        <p>Sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.</p>
        
        <h2>3. 集合</h2>
        <p>Java集合框架介绍。</p>
        <p>Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit.</p>
        
        <h3>3.1 List</h3>
        <p>List接口和实现类。</p>
        <p>Sed quia non numquam eius modi tempora incidunt ut labore et dolore magnam aliquam quaerat voluptatem.</p>
        
        <h3>3.2 Set</h3>
        <p>Set接口和实现类。</p>
        <p>Ut enim ad minima veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam.</p>
        
        <h3>3.3 Map</h3>
        <p>Map接口和实现类。</p>
        <p>Nisi ut aliquid ex ea commodi consequatur? Quis autem vel eum iure reprehenderit.</p>
        
        <h2>4. IO</h2>
        <p>Java输入输出操作。</p>
        <p>Qui in ea voluptate velit esse quam nihil molestiae consequatur, vel illum qui dolorem eum fugiat quo voluptas nulla pariatur?</p>
        
        <h3>4.1 文件</h3>
        <p>文件读写操作。</p>
        <p>At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque corrupti.</p>
        
        <h3>4.2 流</h3>
        <p>输入输出流的使用。</p>
        <p>Quos dolores et quas molestias excepturi sint occaecati cupiditate non provident.</p>
        
        <h2>5. 多线程</h2>
        <p>Java多线程编程。</p>
        <p>Similique sunt in culpa qui officia deserunt mollitia animi, id est laborum et dolorum fuga.</p>
        
        <h3>5.1 Thread</h3>
        <p>Thread类的使用。</p>
        <p>Et harum quidem rerum facilis est et expedita distinctio.</p>
        
        <h3>5.2 同步</h3>
        <p>线程同步机制。</p>
        <p>Nam libero tempore, cum soluta nobis est eligendi optio cumque nihil impedit quo minus id quod maxime placeat facere possimus.</p>
        
        <br><br>
        <p><strong>测试说明：</strong></p>
        <ul>
            <li>这个页面使用短标题测试目录的自动宽度调整</li>
            <li>目录宽度应该根据最长标题自动调整</li>
            <li>最短宽度200px，最长宽度320px</li>
            <li>短标题页面的目录应该比长标题页面更紧凑</li>
            <li>右侧留白应该适中，不会过多</li>
        </ul>
    </div>

    <script src="config/js/thememode.js?v=3.0"></script>
    <script src="config/js/article-toc.js?v=3.1"></script>
</body>
</html>
