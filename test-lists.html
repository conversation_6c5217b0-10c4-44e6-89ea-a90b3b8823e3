<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>列表样式测试页面</title>
    <link rel="stylesheet" href="config/mdui/css/mdui.min.css">
    <link rel="stylesheet" href="config/style/romanticism.aka.css">
</head>
<body class="mdui-theme-layout-light">
    <div class="mdui-container">
        <div class="mdui-row">
            <div class="mdui-col-md-8 mdui-col-offset-md-2">
                <h1>无序列表样式测试</h1>
                
                <button id="switch-theme" class="mdui-btn mdui-btn-raised">切换主题</button>
                <button id="cleanup-lists" class="mdui-btn mdui-btn-raised mdui-color-orange">清理重复标识符</button>
                <button id="ensure-bullets" class="mdui-btn mdui-btn-raised mdui-color-green">确保标识符可见</button>
                <button id="check-alignment" class="mdui-btn mdui-btn-raised mdui-color-blue">检查对齐</button>
                <button id="test-centering" class="mdui-btn mdui-btn-raised mdui-color-purple">测试居中</button>
                <button id="remove-invalid" class="mdui-btn mdui-btn-raised mdui-color-red">移除无效标识符</button>
                <button id="adjust-codeblocks" class="mdui-btn mdui-btn-raised mdui-color-indigo">调整代码块列表</button>
                
                <div class="article mdui-typo">
                    <h2>多级无序列表测试</h2>
                    <p>以下列表应该显示：第一级为实心小圆点，第二级为空心小圆点，第三级为空心小方块</p>
                    
                    <ul>
                        <li>第一级项目 - 实心小圆点 (•)
                            <ul>
                                <li>第二级项目 - 空心小圆点 (◦)
                                    <ul>
                                        <li>第三级项目 - 空心小方块 (▫)</li>
                                        <li>第三级项目 - 空心小方块 (▫)</li>
                                        <li>第三级项目 - 空心小方块 (▫)
                                            <ul>
                                                <li>第四级项目 - 回到实心小圆点 (•)</li>
                                                <li>第四级项目 - 回到实心小圆点 (•)</li>
                                            </ul>
                                        </li>
                                    </ul>
                                </li>
                                <li>第二级项目 - 空心小圆点 (◦)</li>
                                <li>第二级项目 - 空心小圆点 (◦)</li>
                            </ul>
                        </li>
                        <li>第一级项目 - 实心小圆点 (•)</li>
                        <li>第一级项目 - 实心小圆点 (•)
                            <ul>
                                <li>第二级项目 - 空心小圆点 (◦)</li>
                                <li>第二级项目 - 空心小圆点 (◦)</li>
                            </ul>
                        </li>
                    </ul>

                    <h3>对齐测试专用列表</h3>
                    <p>以下列表用于测试标识符与文本的垂直对齐效果：</p>
                    <ul>
                        <li>短文本</li>
                        <li>中等长度的文本内容</li>
                        <li>这是一个很长的文本内容，用来测试标识符是否与文本的第一行正确对齐，而不是偏上或偏下。</li>
                        <li>包含换行的文本：<br>第二行文本应该正确缩进</li>
                        <li>多行文本测试：这是第一行文本内容，这是第一行文本内容，这是第一行文本内容，这是第一行文本内容，这是第一行文本内容，这是第一行文本内容。</li>
                        <li>嵌套对齐测试：
                            <ul>
                                <li>二级文本对齐测试</li>
                                <li>二级长文本：这是一个较长的二级列表项文本，用来测试二级标识符的对齐效果。</li>
                            </ul>
                        </li>
                    </ul>

                    <h2>功能特性列表</h2>
                    <ul>
                        <li>高度统一的明石设计风格，简洁明快、赏心悦目</li>
                        <li>全局思源宋体，适合文字阅读</li>
                        <li>无插件文章置顶样式</li>
                        <li>代码高亮，为你的代码锦上添花
                            <ul>
                                <li>支持多种编程语言</li>
                                <li>One Dark高亮主题</li>
                                <li>语言类型标签显示
                                    <ul>
                                        <li>智能定位</li>
                                        <li>不遮挡复制按钮</li>
                                        <li>支持主题切换</li>
                                    </ul>
                                </li>
                            </ul>
                        </li>
                        <li>原生自带图片灯箱，快让朋友们来欣赏你发的照片吧</li>
                        <li>评论原生数字验证码，拒绝麦片哥评论</li>
                        <li>评论原生可发送表情，为对话赋予灵魂</li>
                        <li>手动深色模式，夜间阅读保护视力</li>
                    </ul>
                    
                    <h2>混合列表测试</h2>
                    <ul>
                        <li>无序列表项目 1</li>
                        <li>无序列表项目 2
                            <ol>
                                <li>有序子列表项目 1</li>
                                <li>有序子列表项目 2</li>
                            </ol>
                        </li>
                        <li>无序列表项目 3</li>
                    </ul>

                    <h2>非列表元素测试</h2>
                    <p>以下元素不应该显示任何标识符：</p>
                    <div>这是一个div元素，不应该有标识符</div>
                    <p>这是一个段落元素，不应该有标识符</p>
                    <span>这是一个span元素，不应该有标识符</span>
                    <blockquote>这是一个引用元素，不应该有标识符</blockquote>

                    <h3>混合内容测试</h3>
                    <div class="test-content">
                        <p>段落内容 - 不应该有标识符</p>
                        <ul>
                            <li>正常列表项 - 应该有标识符</li>
                            <li>另一个列表项 - 应该有标识符</li>
                        </ul>
                        <p>另一个段落 - 不应该有标识符</p>
                        <div>div内容 - 不应该有标识符</div>
                    </div>

                    <h3>包含代码块的列表项测试</h3>
                    <p>以下列表项包含代码块，标识符应该出现在文本前面，而不是代码块前面：</p>
                    <ul>
                        <li>查看 OrbStack 的日志：
                            <pre><code class="language-bash">orb logs</code></pre>
                        </li>
                        <li>临时取消这个变量：
                            <pre><code class="language-bash">unset DOCKER_HOST</code></pre>
                        </li>
                        <li>然后再运行 <code>docker ps</code>，看看是否能连接到 Docker Desktop。</li>
                        <li>检查环境变量：
                            <pre><code class="language-bash">echo $DOCKER_HOST</code></pre>
                            如果输出是：
                            <pre><code>unix:///Users/<USER>/.orbstack/run/docker.sock</code></pre>
                            说明你的 shell 被配置成了使用 OrbStack。
                        </li>
                    </ul>

                    <h3>嵌套列表与代码块</h3>
                    <ul>
                        <li>第一级项目
                            <ul>
                                <li>第二级项目包含代码：
                                    <pre><code class="language-javascript">console.log('Hello World');</code></pre>
                                </li>
                                <li>第二级普通项目</li>
                            </ul>
                        </li>
                        <li>另一个第一级项目</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <script src="config/js/jquery.min.js"></script>
    <script src="config/mdui/js/mdui.min.js"></script>
    <script src="config/js/list-enhancer.js?v=2.1"></script>
    <script src="config/js/thememode.js"></script>

    <script>
        // 列表功能检查脚本
        setTimeout(function() {
            console.log('=== 列表功能检查报告 ===');

            // 检查列表数量
            const lists = document.querySelectorAll('.mdui-typo ul');
            console.log('找到无序列表数量:', lists.length);

            // 检查列表项数量
            const listItems = document.querySelectorAll('.mdui-typo ul li');
            console.log('找到列表项数量:', listItems.length);

            // 检查自定义标识符
            const customBullets = document.querySelectorAll('.custom-bullet');
            console.log('找到自定义标识符数量:', customBullets.length);

            // 检查CSS样式
            listItems.forEach((li, index) => {
                const computedStyle = getComputedStyle(li);
                const beforeContent = getComputedStyle(li, '::before').content;
                console.log(`列表项 ${index + 1}:`, {
                    listStyle: computedStyle.listStyle,
                    listStyleType: computedStyle.listStyleType,
                    beforeContent: beforeContent,
                    position: computedStyle.position,
                    paddingLeft: computedStyle.paddingLeft
                });
            });

            // 检查ListEnhancer是否加载
            if (window.ListEnhancer) {
                console.log('✅ ListEnhancer 已加载');
            } else {
                console.log('❌ ListEnhancer 未加载');
            }

            console.log('=== 检查完成 ===');
        }, 2000);

        // 清理重复标识符按钮
        document.getElementById('cleanup-lists').addEventListener('click', function() {
            console.log('手动触发列表清理...');
            if (window.ListEnhancer && window.ListEnhancer.cleanup) {
                window.ListEnhancer.cleanup();
                // 重新增强列表
                setTimeout(function() {
                    window.ListEnhancer.enhance();
                }, 100);
                console.log('列表清理和重新增强完成');
            } else {
                console.log('ListEnhancer 未加载或不可用');
            }
        });

        // 确保标识符可见按钮
        document.getElementById('ensure-bullets').addEventListener('click', function() {
            console.log('手动触发标识符可见性检查...');
            if (window.ListEnhancer && window.ListEnhancer.ensure) {
                window.ListEnhancer.ensure();
                console.log('标识符可见性检查完成');
            } else {
                console.log('ListEnhancer 未加载或不可用');
            }
        });

        // 检查对齐按钮
        document.getElementById('check-alignment').addEventListener('click', function() {
            console.log('检查列表标识符对齐...');

            const listItems = document.querySelectorAll('.mdui-typo li');
            listItems.forEach((li, index) => {
                const rect = li.getBoundingClientRect();
                const beforeContent = getComputedStyle(li, '::before').content;
                const customBullet = li.querySelector('.custom-bullet');

                console.log(`列表项 ${index + 1}:`, {
                    text: li.textContent.substring(0, 20) + '...',
                    itemTop: rect.top,
                    itemHeight: rect.height,
                    cssContent: beforeContent,
                    hasJSBullet: customBullet !== null,
                    lineHeight: getComputedStyle(li).lineHeight,
                    paddingLeft: getComputedStyle(li).paddingLeft
                });

                if (customBullet) {
                    const bulletRect = customBullet.getBoundingClientRect();
                    console.log(`  JS标识符位置:`, {
                        top: bulletRect.top,
                        left: bulletRect.left,
                        offsetFromText: bulletRect.top - rect.top
                    });
                }
            });
        });

        // 测试居中按钮
        document.getElementById('test-centering').addEventListener('click', function() {
            console.log('测试标识符垂直居中...');

            const listItems = document.querySelectorAll('.mdui-typo li');
            listItems.forEach((li, index) => {
                const rect = li.getBoundingClientRect();
                const lineHeight = parseFloat(getComputedStyle(li).lineHeight);
                const fontSize = parseFloat(getComputedStyle(li).fontSize);
                const actualLineHeight = isNaN(lineHeight) ? fontSize * 1.6 : lineHeight;

                // 计算理论上的垂直中心位置
                const theoreticalCenter = rect.top + (actualLineHeight / 2);

                console.log(`列表项 ${index + 1}:`, {
                    text: li.textContent.substring(0, 20) + '...',
                    itemTop: rect.top,
                    itemHeight: rect.height,
                    lineHeight: actualLineHeight,
                    theoreticalCenter: theoreticalCenter
                });

                // 检查CSS伪元素
                const beforeContent = getComputedStyle(li, '::before').content;
                if (beforeContent && beforeContent !== 'none' && beforeContent !== '""') {
                    console.log(`  CSS标识符: ${beforeContent} (应该在垂直中心)`);
                }

                // 检查JavaScript标识符
                const customBullet = li.querySelector('.custom-bullet');
                if (customBullet) {
                    const bulletRect = customBullet.getBoundingClientRect();
                    const bulletCenter = bulletRect.top + (bulletRect.height / 2);
                    const centerOffset = Math.abs(bulletCenter - theoreticalCenter);

                    console.log(`  JS标识符中心位置: ${bulletCenter}px`);
                    console.log(`  理论中心位置: ${theoreticalCenter}px`);
                    console.log(`  居中偏差: ${centerOffset.toFixed(2)}px`);

                    if (centerOffset < 2) {
                        console.log(`  ✅ 居中效果良好`);
                    } else {
                        console.log(`  ⚠️ 居中偏差较大`);
                    }
                }
            });
        });

        // 移除无效标识符按钮
        document.getElementById('remove-invalid').addEventListener('click', function() {
            console.log('手动触发无效标识符移除...');
            if (window.ListEnhancer && window.ListEnhancer.removeInvalid) {
                window.ListEnhancer.removeInvalid();
                console.log('无效标识符移除完成');
            } else {
                console.log('ListEnhancer 未加载或不可用');
            }
        });

        // 调整代码块列表按钮
        document.getElementById('adjust-codeblocks').addEventListener('click', function() {
            console.log('手动触发代码块列表调整...');
            if (window.ListEnhancer && window.ListEnhancer.adjustCodeBlocks) {
                window.ListEnhancer.adjustCodeBlocks();
                console.log('代码块列表调整完成');
            } else {
                console.log('ListEnhancer 未加载或不可用');
            }
        });
    </script>
</body>
</html>
