<!DOCTYPE HTML>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, shrink-to-fit=no"/>
    <title>测试页脚和目录功能</title>
    <link rel="stylesheet" href="config/style/mdui.min.css">
    <link rel="stylesheet" href="config/style/romanticism.aka.css?v=3.5">
    <style>
        /* 强制应用页脚样式 */
        .footer-copyright {
            font-size: 16px !important;
            line-height: 1.8 !important;
            padding: 20px 0 !important;
        }

        /* PC端：隐藏移动端换行，显示分隔符 */
        @media (min-width: 768px) {
            .footer-mobile-break {
                display: none !important;
            }
            .footer-separator {
                display: inline !important;
            }
        }

        /* 移动端：显示换行，隐藏分隔符 */
        @media (max-width: 767px) {
            .footer-mobile-break {
                display: inline !important;
            }
            .footer-separator {
                display: none !important;
            }
        }

        /* 强制应用目录隐藏样式 */
        .article-toc-fixed.toc-hidden {
            transform: translateX(calc(100% - 40px)) !important;
        }

        .toc-hide-btn {
            background: none !important;
            border: none !important;
            cursor: pointer !important;
            padding: 4px !important;
            margin-left: 8px !important;
            border-radius: 4px !important;
            transition: background-color 0.2s !important;
            color: #666 !important;
        }

        .toc-hide-btn:hover {
            background-color: rgba(0, 0, 0, 0.1) !important;
            color: #333 !important;
        }
    </style>
</head>
<body>
    <div class="mdui-container">
        <div class="mdui-row">
            <div class="mdui-col-md-8 mdui-col-offset-md-2">
                <div class="article mdui-typo">
                    <h1 id="heading1">测试标题1</h1>
                    <p>这是一段测试文本，用来测试移动端字体大小是否合适。这是一段测试文本，用来测试移动端字体大小是否合适。这是一段测试文本，用来测试移动端字体大小是否合适。</p>
                    
                    <h2 id="heading2">测试标题2</h2>
                    <p>这是另一段测试文本。这是另一段测试文本。这是另一段测试文本。这是另一段测试文本。这是另一段测试文本。</p>
                    
                    <h3 id="heading3">测试标题3</h3>
                    <p>更多测试内容。更多测试内容。更多测试内容。更多测试内容。更多测试内容。更多测试内容。</p>
                    
                    <h2 id="heading4">另一个二级标题</h2>
                    <p>最后一段测试文本。最后一段测试文本。最后一段测试文本。最后一段测试文本。</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 测试页脚 -->
    <div class="mdui-shadow-0 mdui-text-center mdui-card toup">
        <br>
        <span class="title footer-copyright">
            &copy;2020-2025 红发香克斯
            <span class="footer-separator">&nbsp;&nbsp;</span><span class="footer-mobile-break"><br></span>京公网安备110105020328001
        </span>
        <small style="opacity: .5;"></small>
        <br>
    </div>

    <script src="config/js/mdui.min.js"></script>
    <script src="config/js/article-toc.js"></script>
    
    <script>
        // 测试目录功能
        console.log('页面加载完成，测试目录功能');

        // 添加一些调试信息
        setTimeout(() => {
            const tocContainer = document.querySelector('.article-toc-fixed');
            if (tocContainer) {
                console.log('目录容器找到了:', tocContainer);
                const hideBtn = tocContainer.querySelector('.toc-hide-btn');
                if (hideBtn) {
                    console.log('隐藏按钮找到了:', hideBtn);

                    // 手动测试隐藏功能
                    hideBtn.addEventListener('click', function() {
                        console.log('隐藏按钮被点击');
                        const isHidden = tocContainer.classList.contains('toc-hidden');
                        console.log('当前隐藏状态:', isHidden);

                        if (isHidden) {
                            tocContainer.classList.remove('toc-hidden');
                            tocContainer.style.transform = 'translateX(0)';
                            console.log('显示目录');
                        } else {
                            tocContainer.classList.add('toc-hidden');
                            tocContainer.style.transform = 'translateX(calc(100% - 40px))';
                            console.log('隐藏目录');
                        }
                    });
                } else {
                    console.log('隐藏按钮未找到');
                    console.log('目录容器内容:', tocContainer.innerHTML);
                }
            } else {
                console.log('目录容器未找到');
            }
        }, 2000);
    </script>
</body>
</html>
