![主题图](https://mono.imakashi.eu.org/opensource/github-Romanticism2.1Screenshot.webp.webp)
# Romanticism(浪漫主义)
一款简洁的 typecho 主题

当前版本：V2.1

**“在明石的简洁设计中捕捉来自日常生活中的浪漫主义。”**

Demo: [明石博客](https://imakashi.eu.org/blog/)



## 特性

- 高度统一的明石设计风格，简洁明快、赏心悦目；

- 全局思源宋体，适合文字阅读；

- 无插件文章置顶样式；

- 代码高亮，为你的代码锦上添花；
  
- 原生自带图片灯箱，快让朋友们来欣赏你发的照片吧；

- 评论原生数字验证码，拒绝麦片哥评论；

- 评论原生可发送表情，为对话赋予灵魂；
 
- 手动深色模式，夜间阅读保护视力；

- 还有文章归档页、“短讯” 、Gravatar 头像、自带无插件友链页面、评论UA等等功能。

## 使用方法

- 支持 typecho 1.1及以上版本（1.1以下的版本我没测试过）
- 支持 Firefox Chrome Safari 等现代浏览器（当然不支持 IE）

在 Github 下载本主题压缩包，解压后确保文件夹名称为 "Romanticism", 将此文件夹放入主题目录中。

随后在后台/外观中启用 "Romanticism" 主题，在“设置外观”中自定义主题相关功能。

## 感谢

此主题的诞生离不开以下项目的帮助，特此感谢。

[MDUI 框架](https://www.mdui.org/) | [jQuery 库](https://github.com/jquery/jquery)

[Cravatar 头像服务](https://cravatar.cn/)

[OwO.js](https://github.com/DIYgod/OwO) | [谷歌思源宋体](https://fonts.google.com)

[Prismjs](https://prismjs.com/) | [Fancybox](https://fancyapps.com/docs/ui/fancybox/)

[Daydream 主题](https://github.com/Skywt2003/Daydream) | [失眠海峡](https://blog.imalan.cn/)

[Cuckoo 主题](https://github.com/bhaoo/cuckoo) | [MDx 主题](https://flyhigher.top/)

## 功能特色

### 🎨 设计与体验
 1. **高度统一的明石设计风格**：简洁明快、赏心悦目的视觉体验；
 2. **全局思源宋体**：专为中文阅读优化，提供舒适的阅读体验；
 3. **手动深色模式**：夜间阅读保护视力，一键切换明暗主题；
 4. **现代化彩色图标**：菜单使用彩色渐变图标，智能识别页面类型，支持动画效果；

### 💻 代码展示增强
 4. **完善的代码高亮功能**：
    - 使用 ` ``` ` 包裹代码，支持指定或不指定语言类型
    - **One Dark 高亮主题**：专业的代码配色方案，提升代码可读性
    - **语言类型标签显示**：代码块右上角智能显示语言类型，自动避开复制按钮
    - **智能定位系统**：实时检测工具栏位置，动态调整标签位置
    - 支持60+种编程语言的语法高亮和自动识别
    - 即使没有指定语言类型，代码块也会正确显示为标准格式

### 📝 Markdown语法增强
 5. **改进的无序列表样式**：
    - **多级标识符层次**：第一级实心小圆点(•) → 第二级空心小圆点(◦) → 第三级空心小方块(▫)
    - 所有标识符大小一致，仅形状类型不同，保持清晰的视觉层次
    - 完美适配明暗主题的颜色变化

### 🔧 功能配置
 6. 设置文章归档页：请先创建一个空页面，在自定义模板中选择“文章归档页”，正文不必输入内容，设置此页面地址为 archivesbox.html （在标题的下面），然后再选择高级选项 -> 公开度 -> 隐藏；
 7. 设置友情链接页：请先创建一个空页面，在自定义模板中选择“友情链接页”，在正文中输入的内容格式如下：
 ```
 !!!
 [icon]博客的图标
 [link]博客的网址
 [name]博客的标题
 [desc]博客的简短描述
 [end]
 !!!
 ```
 以下是一个例子
 ```html
 !!!
 [icon]https://example.cn/avatar.png
 [link]https://example.cn/
 [name]Example blog
 [desc]A tecnology blog.
 [end]

 [icon]https://mono.imakashi.eu.org/overall/headicon2.webp
 [link]https://imakashi.eu.org/blog
 [name]明石博客
 [desc]一个互联网海洋中的小岛
 [end]
 !!!
 ```
 (如你的博客没有开启 Markdown 解析，请去掉开头与结尾的 “!!!”)
 
 8. **快速登录**：点击侧边栏上你的头像以进入博客后台管理页面。

### 🚀 最新更新 (Latest Updates)

#### v2.1 版本更新内容：
- ✅ **代码块语言标签显示**：所有代码块右上角智能显示语言类型，支持50+种编程语言识别
- ✅ **One Dark 高亮主题**：采用专业的 One Dark 配色方案，提升代码阅读体验
- ✅ **无语言类型代码块支持**：即使不指定编程语言，代码块也能正确显示为标准格式
- ✅ **改进的无序列表样式**：多级标识符层次清晰，智能防重复，完美垂直居中，视觉效果更佳
- ✅ **标准化超链接颜色**：蓝色未访问、紫色已访问，符合网络标准
- ✅ **字体大小一致性优化**：所有文本元素保持统一的字体大小
- ✅ **现代化彩色图标**：菜单使用彩色渐变图标，支持智能识别和动画效果
- ✅ **后台配置优化**：移除废弃选项，新增图标开关，界面更简洁
- ✅ **主题切换增强**：所有样式完美适配明暗主题

#### 技术特点：
- 🎯 **智能定位**：语言标签自动避开复制按钮，不影响用户操作
- 🎨 **视觉一致性**：所有元素都遵循统一的设计语言
- 📱 **响应式设计**：完美适配桌面和移动设备
- ⚡ **性能优化**：轻量级实现，不影响页面加载速度

#### 🔧 功能测试与故障排除：
如果新功能没有生效，请按以下步骤操作：
1. **清除浏览器缓存**（重要！90%的问题都是缓存引起的）
2. **强制刷新页面** (Ctrl+F5 或 Cmd+Shift+R)
3. **检查浏览器控制台**是否有错误信息
4. **使用测试页面**：`test-codeblock.html` 和 `test-lists.html`
5. **查看故障排除指南**：`故障排除指南.md`

## 其他问题

 - “首页加载动画”功能由于是所有数据加载完成后才会消失，在某些复杂环境中如果你觉得加载时间太长，请尝试关闭此功能；
 - 受限于本人的技术，即使我已经进行检查与测试，也仍然会有存在 bug 的可能。如你发现了此主题存在的 bug 请给我留言，谢谢！ 

## 鼓励
喜欢此主题的话请**Star本项目**哦！这是对我最大的认可！

## 版权与声明
**禁止将本主题用于非法网站搭建。由此产生一切后果皆由使用者承担。**
**It is prohibited to use this theme for building illegal websites. Any consequences arising from such use shall be borne solely by the user.**

Romanticism Version 2.1

![GitHub](https://mono.imakashi.eu.org/opensource/GPL3.svg)

Copyright©2021-2025 [Akashi](https://imakashi.eu.org), under GPL-3.0 License
