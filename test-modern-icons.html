<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>现代化彩色图标测试页面</title>
    <link rel="stylesheet" href="config/mdui/css/mdui.min.css">
    <link rel="stylesheet" href="config/style/romanticism.aka.css?v=2.6">
    <link rel="stylesheet" href="config/style/modern-icons.css?v=1.0">
</head>
<body class="mdui-theme-layout-light">
    <div class="mdui-container">
        <div class="mdui-row">
            <div class="mdui-col-md-8 mdui-col-offset-md-2">
                <h1>现代化彩色图标测试</h1>
                
                <button id="switch-theme" class="mdui-btn mdui-btn-raised">切换主题</button>
                <button id="test-animations" class="mdui-btn mdui-btn-raised mdui-color-blue">测试动画</button>
                
                <div class="mdui-typo">
                    <h2>菜单图标展示</h2>
                    <p>以下是主题菜单中使用的现代化彩色图标：</p>
                    
                    <div class="mdui-list yuan">
                        <div class="mdui-list-item">
                            <div class="mdui-list-item-icon modern-icon home">
                                <span class="icon-text"></span>
                            </div>
                            <div class="mdui-list-item-content">主页 - 蓝紫渐变</div>
                        </div>
                        
                        <div class="mdui-list-item">
                            <div class="mdui-list-item-icon modern-icon category">
                                <span class="icon-text"></span>
                            </div>
                            <div class="mdui-list-item-content">分类 - 粉红渐变</div>
                        </div>
                        
                        <div class="mdui-list-item">
                            <div class="mdui-list-item-icon modern-icon page">
                                <span class="icon-text"></span>
                            </div>
                            <div class="mdui-list-item-content">页面 - 蓝色渐变</div>
                        </div>
                        
                        <div class="mdui-list-item">
                            <div class="mdui-list-item-icon modern-icon search">
                                <span class="icon-text"></span>
                            </div>
                            <div class="mdui-list-item-content">搜索 - 青色渐变</div>
                        </div>
                        
                        <div class="mdui-list-item">
                            <div class="mdui-list-item-icon modern-icon tag">
                                <span class="icon-text"></span>
                            </div>
                            <div class="mdui-list-item-content">标签 - 黄色渐变</div>
                        </div>
                        
                        <div class="mdui-list-item">
                            <div class="mdui-list-item-icon modern-icon archive">
                                <span class="icon-text"></span>
                            </div>
                            <div class="mdui-list-item-content">归档 - 青绿渐变</div>
                        </div>
                        
                        <div class="mdui-list-item">
                            <div class="mdui-list-item-icon modern-icon about">
                                <span class="icon-text"></span>
                            </div>
                            <div class="mdui-list-item-content">关于 - 粉蓝渐变</div>
                        </div>
                        
                        <div class="mdui-list-item">
                            <div class="mdui-list-item-icon modern-icon contact">
                                <span class="icon-text"></span>
                            </div>
                            <div class="mdui-list-item-content">联系 - 红粉渐变</div>
                        </div>
                        
                        <div class="mdui-list-item">
                            <div class="mdui-list-item-icon modern-icon links">
                                <span class="icon-text"></span>
                            </div>
                            <div class="mdui-list-item-content">友链 - 绿蓝渐变</div>
                        </div>
                    </div>
                    
                    <h2>图标特性</h2>
                    <ul>
                        <li><strong>彩色渐变</strong>：每个图标都有独特的渐变色彩</li>
                        <li><strong>现代设计</strong>：圆角设计，符合现代UI趋势</li>
                        <li><strong>交互效果</strong>：悬停时有阴影和位移效果</li>
                        <li><strong>主题适配</strong>：自动适配明暗主题</li>
                        <li><strong>智能识别</strong>：根据页面标题自动分配合适的图标</li>
                        <li><strong>动画效果</strong>：点击时有波纹效果</li>
                        <li><strong>Emoji支持</strong>：优先使用Emoji，不支持时使用文字</li>
                    </ul>
                    
                    <h2>单独图标测试</h2>
                    <p>点击以下图标测试交互效果：</p>
                    
                    <div style="display: flex; gap: 16px; flex-wrap: wrap; margin: 20px 0;">
                        <div class="modern-icon home" style="cursor: pointer;">
                            <span class="icon-text"></span>
                        </div>
                        <div class="modern-icon category" style="cursor: pointer;">
                            <span class="icon-text"></span>
                        </div>
                        <div class="modern-icon page" style="cursor: pointer;">
                            <span class="icon-text"></span>
                        </div>
                        <div class="modern-icon search" style="cursor: pointer;">
                            <span class="icon-text"></span>
                        </div>
                        <div class="modern-icon tag" style="cursor: pointer;">
                            <span class="icon-text"></span>
                        </div>
                        <div class="modern-icon archive" style="cursor: pointer;">
                            <span class="icon-text"></span>
                        </div>
                        <div class="modern-icon about" style="cursor: pointer;">
                            <span class="icon-text"></span>
                        </div>
                        <div class="modern-icon contact" style="cursor: pointer;">
                            <span class="icon-text"></span>
                        </div>
                        <div class="modern-icon links" style="cursor: pointer;">
                            <span class="icon-text"></span>
                        </div>
                    </div>
                    
                    <h2>技术实现</h2>
                    <p>现代化图标系统包含以下技术特性：</p>
                    <ul>
                        <li><strong>CSS渐变</strong>：使用 linear-gradient 创建美观的渐变效果</li>
                        <li><strong>CSS Transform</strong>：实现悬停和点击动画</li>
                        <li><strong>Flexbox布局</strong>：确保图标内容完美居中</li>
                        <li><strong>JavaScript增强</strong>：智能图标分配和交互效果</li>
                        <li><strong>主题响应</strong>：监听主题切换事件自动适配</li>
                        <li><strong>渐进增强</strong>：基础功能不依赖JavaScript</li>
                    </ul>
                    
                    <h2>浏览器兼容性</h2>
                    <p>现代化图标支持以下浏览器：</p>
                    <ul>
                        <li>Chrome 60+</li>
                        <li>Firefox 55+</li>
                        <li>Safari 12+</li>
                        <li>Edge 79+</li>
                        <li>移动端浏览器</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <script src="config/js/jquery.min.js"></script>
    <script src="config/mdui/js/mdui.min.js"></script>
    <script src="config/js/modern-icons.js?v=1.0"></script>
    <script src="config/js/thememode.js"></script>
    
    <script>
        // 功能检查脚本
        setTimeout(function() {
            console.log('=== 现代化图标功能检查报告 ===');
            
            // 检查现代图标数量
            const modernIcons = document.querySelectorAll('.modern-icon');
            console.log('找到现代图标数量:', modernIcons.length);
            
            // 检查图标类型分布
            const iconTypes = {};
            modernIcons.forEach(icon => {
                const classes = icon.className.split(' ');
                const type = classes.find(cls => cls !== 'modern-icon' && cls !== 'mdui-list-item-icon');
                if (type) {
                    iconTypes[type] = (iconTypes[type] || 0) + 1;
                }
            });
            
            console.log('图标类型分布:', iconTypes);
            
            // 检查CSS样式
            const testIcon = document.querySelector('.modern-icon.home');
            if (testIcon) {
                const computedStyle = getComputedStyle(testIcon);
                console.log('主页图标样式:', {
                    background: computedStyle.background,
                    borderRadius: computedStyle.borderRadius,
                    boxShadow: computedStyle.boxShadow,
                    transform: computedStyle.transform
                });
            }
            
            // 检查ModernIcons是否加载
            if (window.ModernIcons) {
                console.log('✅ ModernIcons 已加载');
            } else {
                console.log('❌ ModernIcons 未加载');
            }
            
            // 检查Emoji支持
            if (document.body.classList.contains('no-emoji')) {
                console.log('⚠️ 浏览器不支持Emoji，使用文字图标');
            } else {
                console.log('✅ 浏览器支持Emoji图标');
            }
            
            console.log('=== 检查完成 ===');
        }, 1000);
        
        // 测试动画按钮
        document.getElementById('test-animations').addEventListener('click', function() {
            console.log('触发图标动画测试...');
            
            const icons = document.querySelectorAll('.modern-icon');
            icons.forEach((icon, index) => {
                setTimeout(() => {
                    icon.classList.add('pulse');
                    setTimeout(() => {
                        icon.classList.remove('pulse');
                    }, 2000);
                }, index * 200);
            });
        });
    </script>
</body>
</html>
