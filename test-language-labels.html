<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代码块语言标签测试页面</title>
    <link rel="stylesheet" href="config/mdui/css/mdui.min.css">
    <link rel="stylesheet" href="config/style/romanticism.aka.css?v=2.7">
    <link rel="stylesheet" href="config/style/prism.highlight.css?v=2.1">
</head>
<body class="mdui-theme-layout-light">
    <div class="mdui-container">
        <div class="mdui-row">
            <div class="mdui-col-md-10 mdui-col-offset-md-1">
                <h1>代码块语言标签测试</h1>
                
                <button id="switch-theme" class="mdui-btn mdui-btn-raised">切换主题</button>
                <button id="test-labels" class="mdui-btn mdui-btn-raised mdui-color-blue">测试标签显示</button>
                <button id="check-positions" class="mdui-btn mdui-btn-raised mdui-color-green">检查标签位置</button>
                
                <div class="article mdui-typo">
                    <h2>各种编程语言代码块测试</h2>
                    <p>以下代码块应该在右上角显示对应的语言类型标签：</p>
                    
                    <h3>1. Bash 脚本</h3>
                    <pre><code class="language-bash">git reflog expire --expire=now --all
git gc --prune=now --aggressive</code></pre>
                    
                    <h3>2. JavaScript 代码</h3>
                    <pre><code class="language-javascript">function enhanceCodeBlocks() {
    console.log('Enhancing code blocks...');
    const codeBlocks = document.querySelectorAll('pre code');
    codeBlocks.forEach(block => {
        addLanguageLabel(block);
    });
}</code></pre>
                    
                    <h3>3. Python 代码</h3>
                    <pre><code class="language-python">def process_data(data):
    """处理数据的函数"""
    result = []
    for item in data:
        if item.is_valid():
            result.append(item.transform())
    return result</code></pre>
                    
                    <h3>4. C++ 代码</h3>
                    <pre><code class="language-cpp">#include &lt;iostream&gt;
#include &lt;vector&gt;

int main() {
    std::vector&lt;int&gt; numbers = {1, 2, 3, 4, 5};
    for (const auto& num : numbers) {
        std::cout &lt;&lt; num &lt;&lt; " ";
    }
    return 0;
}</code></pre>
                    
                    <h3>5. YAML 配置</h3>
                    <pre><code class="language-yaml">version: '3.8'
services:
  web:
    image: nginx:latest
    ports:
      - "80:80"
    volumes:
      - ./html:/usr/share/nginx/html
    environment:
      - NGINX_HOST=localhost
      - NGINX_PORT=80</code></pre>
                    
                    <h3>6. Nginx 配置</h3>
                    <pre><code class="language-nginx">server {
    listen 80;
    server_name example.com;
    
    location / {
        root /var/www/html;
        index index.html index.htm;
        try_files $uri $uri/ =404;
    }
    
    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        include fastcgi_params;
    }
}</code></pre>
                    
                    <h3>7. JSON 数据</h3>
                    <pre><code class="language-json">{
  "name": "test-project",
  "version": "1.0.0",
  "description": "A test project for language labels",
  "main": "index.js",
  "scripts": {
    "start": "node index.js",
    "test": "jest"
  },
  "dependencies": {
    "express": "^4.18.0",
    "lodash": "^4.17.21"
  }
}</code></pre>
                    
                    <h3>8. SQL 查询</h3>
                    <pre><code class="language-sql">SELECT u.name, u.email, p.title, p.created_at
FROM users u
INNER JOIN posts p ON u.id = p.user_id
WHERE p.status = 'published'
  AND p.created_at >= '2023-01-01'
ORDER BY p.created_at DESC
LIMIT 10;</code></pre>
                    
                    <h3>9. 无语言类型的代码块</h3>
                    <pre><code>这是一个没有指定语言类型的代码块
应该显示为 "Text" 标签
可以包含任意文本内容</code></pre>
                    
                    <h3>10. Go 语言</h3>
                    <pre><code class="language-go">package main

import (
    "fmt"
    "net/http"
)

func handler(w http.ResponseWriter, r *http.Request) {
    fmt.Fprintf(w, "Hello, World!")
}

func main() {
    http.HandleFunc("/", handler)
    http.ListenAndServe(":8080", nil)
}</code></pre>
                    
                    <h3>11. Docker 配置</h3>
                    <pre><code class="language-dockerfile">FROM node:16-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 3000

CMD ["npm", "start"]</code></pre>
                    
                    <h3>12. CSS 样式</h3>
                    <pre><code class="language-css">.code-language-label {
    position: absolute;
    top: 0.3em;
    right: 0.5em;
    background: rgba(0, 0, 0, 0.6);
    color: #fff;
    padding: 0.2em 0.5em;
    border-radius: 0.3em;
    font-size: 0.75em;
    z-index: 10;
}</code></pre>
                    
                    <h2>测试说明</h2>
                    <ul>
                        <li>每个代码块右上角应该显示对应的语言类型标签</li>
                        <li>标签位置应该不遮挡复制按钮（如果有的话）</li>
                        <li>鼠标悬停时标签应该更加明显</li>
                        <li>明暗主题切换时标签颜色应该自动适配</li>
                        <li>无语言类型的代码块应该显示 "Text" 标签</li>
                    </ul>
                    
                    <h2>支持的语言类型</h2>
                    <p>系统支持以下语言类型的智能识别和显示：</p>
                    <ul>
                        <li><strong>编程语言</strong>：JavaScript, TypeScript, Python, Java, C, C++, C#, PHP, Go, Rust, Swift, Kotlin, Dart, Ruby, Perl, Lua, R, MATLAB</li>
                        <li><strong>标记语言</strong>：HTML, XML, Markdown</li>
                        <li><strong>样式语言</strong>：CSS, SCSS, Sass</li>
                        <li><strong>数据格式</strong>：JSON, YAML, TOML, INI, Properties</li>
                        <li><strong>脚本语言</strong>：Bash, Shell, PowerShell, Batch</li>
                        <li><strong>配置文件</strong>：Nginx, Apache, Docker, Git, Vim</li>
                        <li><strong>数据库</strong>：SQL</li>
                        <li><strong>其他</strong>：Diff, Patch, Log, Config, Environment</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <script src="config/js/jquery.min.js"></script>
    <script src="config/mdui/js/mdui.min.js"></script>
    <script src="config/js/prism.min.js"></script>
    <script src="config/js/codeblock-enhancer.js?v=2.1"></script>
    <script src="config/js/thememode.js"></script>
    
    <script>
        // 语言标签功能检查脚本
        setTimeout(function() {
            console.log('=== 语言标签功能检查报告 ===');
            
            // 检查代码块数量
            const codeBlocks = document.querySelectorAll('pre code');
            console.log('找到代码块数量:', codeBlocks.length);
            
            // 检查语言标签数量
            const languageLabels = document.querySelectorAll('.code-language-label');
            console.log('找到语言标签数量:', languageLabels.length);
            
            // 检查每个代码块的语言标签
            codeBlocks.forEach((block, index) => {
                const pre = block.parentElement;
                const label = pre.querySelector('.code-language-label');
                const classList = Array.from(block.classList);
                const languageClass = classList.find(cls => cls.startsWith('language-') || cls.startsWith('lang-'));
                
                console.log(`代码块 ${index + 1}:`, {
                    hasLabel: !!label,
                    labelText: label ? label.textContent : 'N/A',
                    languageClass: languageClass || 'none',
                    classList: classList
                });
            });
            
            // 检查CodeBlockEnhancer是否加载
            if (window.CodeBlockEnhancer) {
                console.log('✅ CodeBlockEnhancer 已加载');
            } else {
                console.log('❌ CodeBlockEnhancer 未加载');
            }
            
            console.log('=== 检查完成 ===');
        }, 1000);
        
        // 测试标签显示按钮
        document.getElementById('test-labels').addEventListener('click', function() {
            console.log('手动触发语言标签添加...');
            
            const codeBlocks = document.querySelectorAll('pre code');
            codeBlocks.forEach(block => {
                const pre = block.parentElement;
                const existingLabel = pre.querySelector('.code-language-label');
                if (existingLabel) {
                    existingLabel.style.opacity = '1';
                    existingLabel.style.background = '#ff6b6b';
                    setTimeout(() => {
                        existingLabel.style.background = '';
                        existingLabel.style.opacity = '0.8';
                    }, 1000);
                }
            });
        });
        
        // 检查标签位置按钮
        document.getElementById('check-positions').addEventListener('click', function() {
            console.log('检查语言标签位置...');
            
            const labels = document.querySelectorAll('.code-language-label');
            labels.forEach((label, index) => {
                const pre = label.parentElement;
                const hasToolbar = pre.classList.contains('code-toolbar') || 
                                  pre.querySelector('.toolbar') ||
                                  pre.querySelector('.copy-to-clipboard-button');
                
                const rect = label.getBoundingClientRect();
                const preRect = pre.getBoundingClientRect();
                
                console.log(`标签 ${index + 1} (${label.textContent}):`, {
                    hasToolbar: hasToolbar,
                    rightPosition: label.style.right,
                    actualPosition: {
                        right: preRect.right - rect.right,
                        top: rect.top - preRect.top
                    },
                    visible: rect.width > 0 && rect.height > 0
                });
            });
        });
    </script>
</body>
</html>
