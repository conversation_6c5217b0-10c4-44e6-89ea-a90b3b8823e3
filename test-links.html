<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>超链接测试页面</title>
    <link rel="stylesheet" href="config/mdui/css/mdui.min.css">
    <link rel="stylesheet" href="config/style/romanticism.aka.css?v=2.1">
    <link rel="stylesheet" href="config/style/prism.highlight.css?v=2.1">
</head>
<body class="mdui-theme-layout-light">
    <div class="mdui-container">
        <div class="mdui-row">
            <div class="mdui-col-md-8 mdui-col-offset-md-2">
                <h1>超链接颜色测试</h1>
                
                <button id="switch-theme" class="mdui-btn mdui-btn-raised">切换主题</button>
                <button id="force-reset-links" class="mdui-btn mdui-btn-raised mdui-color-red">强制重置链接颜色</button>
                

                
                <div class="article mdui-typo">
                    <h2>超链接颜色测试</h2>
                    <p>以下链接应该显示正确的颜色：</p>
                    <ul>
                        <li><a href="https://www.example.com">未访问链接</a> - 应该是蓝色</li>
                        <li><a href="https://www.google.com">可能已访问的链接</a> - 如果访问过应该是紫色</li>
                        <li><a href="https://github.com">GitHub链接</a> - 根据访问状态显示颜色</li>
                        <li><a href="#test-anchor">页面内锚点链接</a> - 测试内部链接</li>
                    </ul>
                    
                    <h3>链接颜色说明</h3>
                    <p>链接颜色规则：</p>
                    <ul>
                        <li><strong>未访问链接</strong>：蓝色 (#2196F3)</li>
                        <li><strong>已访问链接</strong>：紫色 (#9C27B0)</li>
                        <li><strong>悬停状态</strong>：深蓝色或深紫色</li>
                        <li><strong>深色主题</strong>：使用更浅的对应颜色</li>
                    </ul>
                    
                    <h2>字体大小一致性测试</h2>
                    <p>这是一个段落文本，应该与列表项、表格等元素保持相同的字体大小。使用右下角的字体控制工具可以调整大小。</p>
                    
                    <h3>列表项字体测试</h3>
                    <ul>
                        <li>这是第一级列表项，字体大小应该与段落一致</li>
                        <li>这是另一个列表项
                            <ul>
                                <li>这是第二级列表项，字体大小也应该一致</li>
                                <li>嵌套列表项测试</li>
                            </ul>
                        </li>
                        <li>回到第一级列表项</li>
                    </ul>
                    
                    <h3>表格字体测试</h3>
                    <table class="mdui-table">
                        <thead>
                            <tr>
                                <th>列标题1</th>
                                <th>列标题2</th>
                                <th>列标题3</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>表格单元格内容，字体大小应该一致</td>
                                <td>另一个单元格</td>
                                <td>第三个单元格</td>
                            </tr>
                            <tr>
                                <td>第二行内容</td>
                                <td>测试字体大小</td>
                                <td>应该与段落一致</td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <h3>引用文字测试</h3>

                    
                    <h2 id="test-anchor">测试锚点</h2>
                    <p>这是锚点目标位置，用于测试页面内链接。</p>
                </div>
            </div>
        </div>
    </div>
    
    <script src="config/js/jquery.min.js"></script>
    <script src="config/mdui/js/mdui.min.js"></script>

    <script src="config/js/list-enhancer.js?v=2.1"></script>
    <script src="config/js/link-enhancer.js?v=2.1"></script>
    <script src="config/js/thememode.js"></script>
    
    <script>
        // 功能检查脚本
        setTimeout(function() {
            console.log('=== 超链接功能检查报告 ===');

            // 检查超链接颜色
            const links = document.querySelectorAll('.mdui-typo a');
            console.log('找到超链接数量:', links.length);

            links.forEach((link, index) => {
                const computedStyle = getComputedStyle(link);
                console.log(`链接 ${index + 1} (${link.textContent}):`, {
                    color: computedStyle.color,
                    href: link.href,
                    visited: link.matches(':visited')
                });
            });
            
            // 检查标题比例
            console.log('标题字体大小:');
            for (let i = 1; i <= 6; i++) {
                const heading = document.querySelector(`.mdui-typo h${i}`);
                if (heading) {
                    const fontSize = getComputedStyle(heading).fontSize;
                    console.log(`h${i} 字体大小:`, fontSize);
                }
            }
            
            // 检查LinkEnhancer是否加载
            if (window.LinkEnhancer) {
                console.log('✅ LinkEnhancer 已加载');
            } else {
                console.log('❌ LinkEnhancer 未加载');
            }

            console.log('=== 检查完成 ===');
        }, 1000);

        // 强制重置链接颜色按钮
        document.getElementById('force-reset-links').addEventListener('click', function() {
            console.log('手动触发链接颜色重置...');
            if (window.LinkEnhancer && window.LinkEnhancer.forceReset) {
                window.LinkEnhancer.forceReset();
                console.log('链接颜色重置完成');
            } else {
                console.log('LinkEnhancer 未加载或不可用');
            }
        });
    </script>
</body>
</html>
