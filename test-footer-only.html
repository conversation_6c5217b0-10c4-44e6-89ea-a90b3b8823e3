<!DOCTYPE HTML>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, shrink-to-fit=no"/>
    <title>测试页脚功能</title>
    <link rel="stylesheet" href="config/style/mdui.min.css">
    <link rel="stylesheet" href="config/style/romanticism.aka.css?v=3.6">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Noto Serif SC', serif;
        }
        
        /* 强制应用页脚样式 */
        .footer-copyright {
            font-size: 16px !important;
            line-height: 1.8 !important;
            padding: 20px 0 !important;
            border: 2px solid red; /* 调试用边框 */
        }
        
        /* PC端：隐藏移动端换行，显示分隔符 */
        @media (min-width: 768px) {
            .footer-mobile-break {
                display: none !important;
            }
            .footer-separator {
                display: inline !important;
                background-color: yellow; /* 调试用背景色 */
            }
        }
        
        /* 移动端：显示换行，隐藏分隔符 */
        @media (max-width: 767px) {
            .footer-mobile-break {
                display: inline !important;
                background-color: green; /* 调试用背景色 */
            }
            .footer-separator {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <h1>页脚测试页面</h1>
    <p>请调整浏览器窗口大小来测试响应式效果：</p>
    <ul>
        <li>PC端（宽度 ≥ 768px）：版权信息和备案号应该在同一行，中间有空格分隔</li>
        <li>移动端（宽度 < 768px）：版权信息和备案号应该分两行显示</li>
    </ul>
    
    <hr>
    
    <!-- 测试页脚 -->
    <div class="mdui-shadow-0 mdui-text-center mdui-card toup">
        <br>
        <span class="title footer-copyright">
            &copy;2020-2025 红发香克斯
            <span class="footer-separator">&nbsp;&nbsp;</span><span class="footer-mobile-break"><br></span>京公网安备110105020328001
        </span>
        <small style="opacity: .5;"></small>
        <br>
    </div>
    
    <hr>
    
    <h2>调试信息</h2>
    <p>当前窗口宽度：<span id="width"></span>px</p>
    <p>页脚分隔符显示状态：<span id="separator-status"></span></p>
    <p>页脚换行显示状态：<span id="break-status"></span></p>
    
    <script>
        function updateDebugInfo() {
            const width = window.innerWidth;
            const separator = document.querySelector('.footer-separator');
            const breakElement = document.querySelector('.footer-mobile-break');
            
            document.getElementById('width').textContent = width;
            
            const separatorStyle = window.getComputedStyle(separator);
            const breakStyle = window.getComputedStyle(breakElement);
            
            document.getElementById('separator-status').textContent = separatorStyle.display;
            document.getElementById('break-status').textContent = breakStyle.display;
        }
        
        updateDebugInfo();
        window.addEventListener('resize', updateDebugInfo);
    </script>
</body>
</html>
