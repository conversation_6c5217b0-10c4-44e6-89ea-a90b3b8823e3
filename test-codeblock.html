<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代码块测试页面</title>
    <link rel="stylesheet" href="config/mdui/css/mdui.min.css">
    <link rel="stylesheet" href="config/style/romanticism.aka.css">
    <link rel="stylesheet" href="config/style/prism.highlight.css">
</head>
<body class="mdui-theme-layout-light">
    <div class="mdui-container">
        <div class="mdui-row">
            <div class="mdui-col-md-8 mdui-col-offset-md-2">
                <h1>代码块显示测试</h1>
                
                <button id="switch-theme" class="mdui-btn mdui-btn-raised">切换主题</button>
                
                <div class="article mdui-typo">
                    <h2>功能测试检查清单</h2>
                    <p>请检查以下功能是否正常工作：</p>
                    <ul>
                        <li>代码块是否使用One Dark主题（深色背景 #282c34）</li>
                        <li>代码块右上角是否显示语言类型标签</li>
                        <li>语言标签是否不遮挡复制按钮</li>
                        <li>无序列表是否显示正确的多级标识符
                            <ul>
                                <li>第二级应该是空心圆点 (◦)</li>
                                <li>第三级应该是空心方块 (▫)
                                    <ul>
                                        <li>第四级应该回到实心圆点 (•)</li>
                                    </ul>
                                </li>
                            </ul>
                        </li>
                        <li>主题切换是否正常工作</li>
                    </ul>

                    <h2>1. 没有指定语言的代码块</h2>
                    <p>这是一个没有指定语言类型的代码块，右上角应该显示"Text"：</p>
                    <pre><code>function hello() {
    console.log("Hello World!");
    return "success";
}</code></pre>

                    <h2>2. 指定了语言的代码块</h2>
                    <p>这是一个指定了JavaScript语言的代码块，右上角应该显示"JavaScript"：</p>
                    <pre><code class="language-javascript">function hello() {
    console.log("Hello World!");
    return "success";
}</code></pre>

                    <h2>3. 另一个没有语言的代码块</h2>
                    <p>检测请求中可能涉及反序列化的模式，防止利用 PHP 反序列化漏洞 (如 unserialize() ) 执行恶意代码。</p>
                    <p>模式（右上角应该显示"Text"）：</p>
                    <pre><code>(?i:O:[0-9]+:".*";|a:[0-9]+:\{|s:[0-9]+:".*";)</code></pre>

                    <h2>4. Python代码块</h2>
                    <p>右上角应该显示"Python"：</p>
                    <pre><code class="language-python">def hello_world():
    print("Hello, World!")
    return True</code></pre>

                    <h2>5. CSS代码块</h2>
                    <p>右上角应该显示"CSS"：</p>
                    <pre><code class="language-css">.example {
    color: #333;
    background: #fff;
    padding: 1em;
}</code></pre>

                    <h2>6. JSON代码块</h2>
                    <p>右上角应该显示"JSON"：</p>
                    <pre><code class="language-json">{
    "name": "test",
    "version": "1.0.0",
    "description": "A test package"
}</code></pre>

                    <h2>7. 纯文本代码块</h2>
                    <p>右上角应该显示"Text"：</p>
                    <pre><code>这是一段纯文本
没有任何语法高亮
只是展示代码块的基本样式</code></pre>
                </div>
            </div>
        </div>
    </div>
    
    <script src="config/js/jquery.min.js"></script>
    <script src="config/mdui/js/mdui.min.js"></script>
    <script src="config/js/prism.highlight.js"></script>
    <script src="config/js/codeblock-enhancer.js?v=2.1"></script>
    <script src="config/js/thememode.js"></script>

    <script>
        // 功能检查脚本
        setTimeout(function() {
            console.log('=== 功能检查报告 ===');

            // 检查代码块
            const codeBlocks = document.querySelectorAll('pre');
            console.log('找到代码块数量:', codeBlocks.length);

            // 检查语言标签
            const languageLabels = document.querySelectorAll('.code-language-label');
            console.log('找到语言标签数量:', languageLabels.length);

            // 检查One Dark主题
            codeBlocks.forEach((block, index) => {
                const bgColor = getComputedStyle(block).backgroundColor;
                console.log(`代码块 ${index + 1} 背景色:`, bgColor);
            });

            // 检查列表样式
            const lists = document.querySelectorAll('.mdui-typo ul li');
            console.log('找到列表项数量:', lists.length);

            lists.forEach((li, index) => {
                const beforeContent = getComputedStyle(li, '::before').content;
                console.log(`列表项 ${index + 1} 标识符:`, beforeContent);
            });

            // 检查CodeBlockEnhancer是否加载
            if (window.CodeBlockEnhancer) {
                console.log('✅ CodeBlockEnhancer 已加载');
            } else {
                console.log('❌ CodeBlockEnhancer 未加载');
            }

            // 检查Prism是否加载
            if (window.Prism) {
                console.log('✅ Prism 已加载');
            } else {
                console.log('❌ Prism 未加载');
            }

            console.log('=== 检查完成 ===');
        }, 2000);
    </script>
</body>
</html>
